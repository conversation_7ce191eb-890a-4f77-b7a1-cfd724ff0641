<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>快递阶梯费用规则维护管理</title>
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- BoxIcons CSS -->
    <link href="https://cdn.jsdelivr.net/npm/boxicons@2.0.9/css/boxicons.min.css" rel="stylesheet">
    <!-- 预留样式位置 -->
    <style>
        /* 全局样式 */
        
         :root {
            --primary-color: #4361ee;
            --secondary-color: #3f37c9;
            --success-color: #4cc9f0;
            --warning-color: #f72585;
            --info-color: #4895ef;
            --light-bg: #f8f9fa;
            --border-radius: 10px;
            --card-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
        
        body {
            background-color: #f5f7fb;
            font-family: 'Segoe UI', system-ui, -apple-system, sans-serif;
            color: #333;
        }
        /* 页面容器 */
        
        .page-container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 2rem;
        }
        /* 头部样式 */
        
        .dashboard-header {
            margin-bottom: 2rem;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .dashboard-title {
            color: #2b2d42;
            font-weight: 600;
            font-size: 1.8rem;
            margin: 0;
        }
        
        .dashboard-actions {
            display: flex;
            gap: 1rem;
        }
        /* 卡片样式 */
        
        .card {
            border: none;
            border-radius: var(--border-radius);
            box-shadow: var(--card-shadow);
            margin-bottom: 1.5rem;
            background: white;
        }
        
        .card-header {
            background-color: white;
            border-bottom: 1px solid rgba(0, 0, 0, 0.1);
            padding: 1.25rem;
        }
        
        .card-title {
            color: #2b2d42;
            font-weight: 600;
        }
        /* 筛选器样式 */
        
        .rule-filters {
            display: flex;
            gap: 1.5rem;
            align-items: center;
        }
        
        .date-range-filter {
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }
        
        .date-range-filter input {
            width: 140px;
        }
        
        .status-filter select {
            width: 120px;
        }
        /* 表格样式 */
        
        .rule-table {
            margin-bottom: 0;
        }
        
        .rule-table th {
            background-color: var(--light-bg);
            font-weight: 600;
            padding: 1rem;
            white-space: nowrap;
        }
        
        .rule-table td {
            padding: 1rem;
            vertical-align: middle;
        }
        
        .table-responsive {
            margin: 0;
            padding: 1rem;
        }
        /* 状态标签样式 */
        
        .status-badge {
            padding: 0.35rem 0.75rem;
            border-radius: 50px;
            font-size: 0.875rem;
            font-weight: 500;
        }
        
        .status-active {
            background-color: rgba(76, 201, 240, 0.1);
            color: var(--success-color);
        }
        
        .status-pending {
            background-color: rgba(247, 37, 133, 0.1);
            color: var(--warning-color);
        }
        
        .status-expired {
            background-color: rgba(128, 128, 128, 0.1);
            color: #666;
        }
        /* 操作按钮样式 */
        
        .action-buttons {
            display: flex;
            gap: 0.5rem;
        }
        
        .btn-icon {
            padding: 0.375rem;
            line-height: 1;
            border-radius: 4px;
        }
        /* 模态框样式 */
        
        .modal-content {
            border-radius: var(--border-radius);
            border: none;
        }
        
        .modal-header {
            border-bottom: 1px solid rgba(0, 0, 0, 0.1);
            background-color: var(--light-bg);
        }
        
        .modal-footer {
            border-top: 1px solid rgba(0, 0, 0, 0.1);
        }
        /* 表单样式 */
        
        .form-label {
            font-weight: 500;
            margin-bottom: 0.5rem;
        }
        
        .form-control:focus,
        .form-select:focus {
            border-color: var(--primary-color);
            box-shadow: 0 0 0 0.2rem rgba(67, 97, 238, 0.25);
        }
        /* 响应式调整 */
        
        @media (max-width: 768px) {
            .dashboard-header {
                flex-direction: column;
                align-items: flex-start;
                gap: 1rem;
            }
            .rule-filters {
                flex-direction: column;
                align-items: flex-start;
            }
            .date-range-filter {
                flex-wrap: wrap;
            }
        }
    </style>
</head>

<body>
    <div class="page-container">
        <!-- 头部区域 -->
        <header class="dashboard-header">
            <h1 class="dashboard-title">快递阶梯费用规则维护</h1>
            <div class="dashboard-actions">
                <button type="button" class="btn btn-primary" id="addRuleBtn">
                    <i class="bx bx-plus"></i> 新增规则
                </button>
                <button type="button" class="btn btn-success" id="saveRulesBtn">
                    <i class="bx bx-save"></i> 保存所有规则
                </button>
            </div>
        </header>

        <!-- 规则管理区域 -->
        <div class="card rule-management-card">
            <div class="card-header">
                <div class="row align-items-center">
                    <div class="col">
                        <h5 class="card-title mb-0">费用规则配置</h5>
                    </div>
                    <div class="col-auto">
                        <div class="rule-filters">
                            <div class="date-range-filter">
                                <label>生效时间：</label>
                                <input type="date" id="startDate" class="form-control">
                                <span>至</span>
                                <input type="date" id="endDate" class="form-control">
                            </div>
                            <div class="status-filter">
                                <select class="form-select" id="statusFilter">
                                    <option value="all">所有状态</option>
                                    <option value="active">生效中</option>
                                    <option value="pending">待生效</option>
                                    <option value="expired">已失效</option>
                                </select>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="card-body">
                <!-- 规则表格 -->
                <div class="table-responsive">
                    <table class="table table-hover rule-table" id="ruleTable">
                        <thead>
                            <tr>
                                <th>规则编号</th>
                                <th>生效时间</th>
                                <th>失效时间</th>
                                <th>单量区间</th>
                                <th>重量区间</th>
                                <th>价格(元)</th>
                                <th>状态</th>
                                <th>操作</th>
                            </tr>
                        </thead>
                        <tbody id="ruleTableBody">
                            <!-- 规则数据将通过JavaScript动态插入 -->
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>

    <!-- 新增/编辑规则模态框 -->
    <div class="modal fade" id="ruleModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="ruleModalTitle">新增规则</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="ruleForm">
                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label class="form-label">生效时间</label>
                                <input type="date" class="form-control" id="ruleStartDate" required>
                            </div>
                            <div class="col-md-6">
                                <label class="form-label">失效时间</label>
                                <input type="date" class="form-control" id="ruleEndDate" required>
                            </div>
                        </div>
                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label class="form-label">单量区间</label>
                                <div class="input-group">
                                    <input type="number" class="form-control" id="volumeStart" placeholder="起始单量" required>
                                    <span class="input-group-text">-</span>
                                    <input type="number" class="form-control" id="volumeEnd" placeholder="结束单量" required>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <label class="form-label">重量区间</label>
                                <div class="input-group">
                                    <input type="number" class="form-control" id="weightStart" placeholder="起始重量" required>
                                    <span class="input-group-text">-</span>
                                    <input type="number" class="form-control" id="weightEnd" placeholder="结束重量" required>
                                </div>
                            </div>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">价格(元)</label>
                            <input type="number" class="form-control" id="price" step="0.01" required>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-primary" id="saveRuleBtn">保存</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <!-- 预留脚本位置 -->
    <script>
        // 规则数据管理
        class RuleManager {
            constructor() {
                this.rules = [];
                this.currentEditingId = null;
                this.initializeEventListeners();
                this.loadRules();
            }

            // 初始化事件监听
            initializeEventListeners() {
                // 新增规则按钮
                document.getElementById('addRuleBtn').addEventListener('click', () => this.showRuleModal());

                // 保存规则按钮
                document.getElementById('saveRuleBtn').addEventListener('click', () => this.saveRule());

                // 保存所有规则按钮
                document.getElementById('saveRulesBtn').addEventListener('click', () => this.saveAllRules());

                // 筛选器变化事件
                document.getElementById('startDate').addEventListener('change', () => this.filterRules());
                document.getElementById('endDate').addEventListener('change', () => this.filterRules());
                document.getElementById('statusFilter').addEventListener('change', () => this.filterRules());
            }

            // 加载规则数据
            loadRules() {
                // 模拟从后端获取数据
                const mockRules = [{
                    id: 1,
                    startDate: '2025-04-21',
                    endDate: '2025-12-31',
                    volumeStart: 1,
                    volumeEnd: 100,
                    weightStart: 0,
                    weightEnd: 5,
                    price: 10.00,
                    status: 'active'
                }, {
                    id: 2,
                    startDate: '2025-05-01',
                    endDate: '2025-12-31',
                    volumeStart: 101,
                    volumeEnd: 500,
                    weightStart: 0,
                    weightEnd: 5,
                    price: 8.50,
                    status: 'pending'
                }];

                this.rules = mockRules;
                this.renderRules();
            }

            // 渲染规则表格
            renderRules(filteredRules = null) {
                const rules = filteredRules || this.rules;
                const tbody = document.getElementById('ruleTableBody');
                tbody.innerHTML = '';

                rules.forEach(rule => {
                    const tr = document.createElement('tr');
                    tr.innerHTML = `
                <td>${rule.id}</td>
                <td>${rule.startDate}</td>
                <td>${rule.endDate}</td>
                <td>${rule.volumeStart}-${rule.volumeEnd}</td>
                <td>${rule.weightStart}-${rule.weightEnd}</td>
                <td>${rule.price.toFixed(2)}</td>
                <td><span class="status-badge status-${rule.status}">${this.getStatusText(rule.status)}</span></td>
                <td>
                    <div class="action-buttons">
                        <button class="btn btn-sm btn-info btn-icon edit-rule" data-id="${rule.id}">
                            <i class="bx bx-edit"></i>
                        </button>
                        <button class="btn btn-sm btn-danger btn-icon delete-rule" data-id="${rule.id}">
                            <i class="bx bx-trash"></i>
                        </button>
                    </div>
                </td>
            `;

                    // 添加编辑和删除事件监听
                    tr.querySelector('.edit-rule').addEventListener('click', () => this.editRule(rule.id));
                    tr.querySelector('.delete-rule').addEventListener('click', () => this.deleteRule(rule.id));

                    tbody.appendChild(tr);
                });
            }

            // 显示规则模态框
            showRuleModal(ruleId = null) {
                this.currentEditingId = ruleId;
                const modal = new bootstrap.Modal(document.getElementById('ruleModal'));
                const title = document.getElementById('ruleModalTitle');

                if (ruleId) {
                    const rule = this.rules.find(r => r.id === ruleId);
                    title.textContent = '编辑规则';
                    this.fillRuleForm(rule);
                } else {
                    title.textContent = '新增规则';
                    document.getElementById('ruleForm').reset();
                }

                modal.show();
            }

            // 填充规则表单
            fillRuleForm(rule) {
                document.getElementById('ruleStartDate').value = rule.startDate;
                document.getElementById('ruleEndDate').value = rule.endDate;
                document.getElementById('volumeStart').value = rule.volumeStart;
                document.getElementById('volumeEnd').value = rule.volumeEnd;
                document.getElementById('weightStart').value = rule.weightStart;
                document.getElementById('weightEnd').value = rule.weightEnd;
                document.getElementById('price').value = rule.price;
            }

            // 保存规则
            saveRule() {
                const formData = {
                    startDate: document.getElementById('ruleStartDate').value,
                    endDate: document.getElementById('ruleEndDate').value,
                    volumeStart: parseInt(document.getElementById('volumeStart').value),
                    volumeEnd: parseInt(document.getElementById('volumeEnd').value),
                    weightStart: parseFloat(document.getElementById('weightStart').value),
                    weightEnd: parseFloat(document.getElementById('weightEnd').value),
                    price: parseFloat(document.getElementById('price').value)
                };

                if (!this.validateRuleForm(formData)) {
                    return;
                }

                if (this.currentEditingId) {
                    // 更新现有规则
                    const index = this.rules.findIndex(r => r.id === this.currentEditingId);
                    this.rules[index] = {...this.rules[index],
                        ...formData
                    };
                } else {
                    // 添加新规则
                    const newRule = {
                        id: this.rules.length + 1,
                        ...formData,
                        status: this.calculateStatus(formData.startDate, formData.endDate)
                    };
                    this.rules.push(newRule);
                }

                this.renderRules();
                bootstrap.Modal.getInstance(document.getElementById('ruleModal')).hide();
                this.showToast('规则保存成功！');
            }

            // 验证规则表单
            validateRuleForm(formData) {
                if (new Date(formData.startDate) >= new Date(formData.endDate)) {
                    this.showToast('生效时间必须早于失效时间', 'error');
                    return false;
                }

                if (formData.volumeStart >= formData.volumeEnd) {
                    this.showToast('起始单量必须小于结束单量', 'error');
                    return false;
                }

                if (formData.weightStart >= formData.weightEnd) {
                    this.showToast('起始重量必须小于结束重量', 'error');
                    return false;
                }

                if (formData.price <= 0) {
                    this.showToast('价格必须大于0', 'error');
                    return false;
                }

                return true;
            }

            // 编辑规则
            editRule(ruleId) {
                this.showRuleModal(ruleId);
            }

            // 删除规则
            deleteRule(ruleId) {
                if (confirm('确定要删除该规则吗？')) {
                    this.rules = this.rules.filter(rule => rule.id !== ruleId);
                    this.renderRules();
                    this.showToast('规则已删除');
                }
            }

            // 保存所有规则
            saveAllRules() {
                // 模拟向后端保存数据
                console.log('保存所有规则:', this.rules);
                this.showToast('所有规则保存成功！');
            }

            // 筛选规则
            filterRules() {
                const startDate = document.getElementById('startDate').value;
                const endDate = document.getElementById('endDate').value;
                const status = document.getElementById('statusFilter').value;

                let filteredRules = this.rules;

                if (startDate) {
                    filteredRules = filteredRules.filter(rule => rule.startDate >= startDate);
                }

                if (endDate) {
                    filteredRules = filteredRules.filter(rule => rule.endDate <= endDate);
                }

                if (status !== 'all') {
                    filteredRules = filteredRules.filter(rule => rule.status === status);
                }

                this.renderRules(filteredRules);
            }

            // 计算规则状态
            calculateStatus(startDate, endDate) {
                const now = new Date();
                const start = new Date(startDate);
                const end = new Date(endDate);

                if (now < start) return 'pending';
                if (now > end) return 'expired';
                return 'active';
            }

            // 获取状态文本
            getStatusText(status) {
                const statusMap = {
                    active: '生效中',
                    pending: '待生效',
                    expired: '已失效'
                };
                return statusMap[status] || status;
            }

            // 显示提示消息
            showToast(message, type = 'success') {
                const toastDiv = document.createElement('div');
                toastDiv.className = `toast align-items-center text-white bg-${type === 'success' ? 'success' : 'danger'} border-0`;
                toastDiv.setAttribute('role', 'alert');
                toastDiv.setAttribute('aria-live', 'assertive');
                toastDiv.setAttribute('aria-atomic', 'true');

                toastDiv.innerHTML = `
            <div class="d-flex">
                <div class="toast-body">
                    ${message}
                </div>
                <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast"></button>
            </div>
        `;

                document.body.appendChild(toastDiv);
                const toast = new bootstrap.Toast(toastDiv);
                toast.show();

                // 自动移除toast元素
                toastDiv.addEventListener('hidden.bs.toast', () => {
                    document.body.removeChild(toastDiv);
                });
            }
        }

        // 初始化规则管理器
        document.addEventListener('DOMContentLoaded', () => {
            window.ruleManager = new RuleManager();
        });
    </script>
</body>

</html>