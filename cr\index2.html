<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>成本增幅规则管理</title>
    <!-- Bootstrap 5 CDN -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.2.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Bootstrap Icons -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    <!-- Flatpickr 日期选择器 -->
    <link href="https://cdn.jsdelivr.net/npm/flatpickr/dist/flatpickr.min.css" rel="stylesheet">
    <!-- 自定义样式 -->
    <style>
        /* 通用样式 */
        
         :root {
            --primary-color: #3b82f6;
            --primary-light: #eff6ff;
            --success-color: #10b981;
            --warning-color: #f59e0b;
            --danger-color: #ef4444;
            --text-muted: #64748b;
            --border-color: #e2e8f0;
            --border-radius: 0.375rem;
        }
        
        body {
            font-family: 'Segoe UI', system-ui, -apple-system, sans-serif;
            color: #334155;
            font-size: 0.95rem;
        }
        /* 自定义字体大小 */
        
        .fs-14 {
            font-size: 0.875rem;
        }
        
        .fs-12 {
            font-size: 0.75rem;
        }
        
        .fs-16 {
            font-size: 1rem;
        }
        /* 头像样式 */
        
        .avatar-circle {
            width: 32px;
            height: 32px;
            background-color: var(--primary-light);
            color: var(--primary-color);
            font-weight: 600;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 50%;
        }
        /* 卡片样式优化 */
        
        .card {
            border-radius: var(--border-radius);
            transition: transform 0.2s, box-shadow 0.2s;
        }
        
        .card:hover {
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05) !important;
        }
        /* 规则状态徽章 */
        
        .badge-active {
            background-color: rgba(16, 185, 129, 0.1);
            color: var(--success-color);
            font-weight: 500;
        }
        
        .badge-expired {
            background-color: rgba(100, 116, 139, 0.1);
            color: var(--text-muted);
            font-weight: 500;
        }
        /* 平台标签样式 */
        
        .badge-platform {
            display: inline-flex;
            align-items: center;
            padding: 0.25rem 0.5rem;
            background-color: #f8fafc;
            border-radius: 1rem;
            font-weight: normal;
        }
        /* 进度条区间颜色 */
        
        .progress-bar-range-1 {
            background-color: var(--primary-color);
        }
        
        .progress-bar-range-2 {
            background-color: #60a5fa;
        }
        
        .progress-bar-range-3 {
            background-color: #93c5fd;
        }
        
        .progress-bar-range-4 {
            background-color: #bfdbfe;
        }
        
        .progress-bar-range-5 {
            background-color: var(--warning-color);
        }
        /* 进度条区间标签 */
        
        .progress {
            overflow: visible;
        }
        
        .progress-bar {
            position: relative;
            overflow: visible;
            transition: width 0.3s ease;
            color: white;
            text-align: center;
            line-height: 30px;
            font-weight: 500;
            font-size: 0.8rem;
        }
        /* 虚线边框 */
        
        .border-dashed {
            border-style: dashed !important;
        }
        /* 规则卡片样式 */
        
        .rule-card {
            height: 100%;
        }
        
        .rule-card .card-title {
            line-height: 1.3;
        }
        
        .ranges-table th,
        .ranges-table td {
            padding: 0.4rem 0.5rem;
            vertical-align: middle;
        }
        /* 自定义按钮样式 */
        
        .btn-icon {
            width: 32px;
            height: 32px;
            padding: 0;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            border-radius: 50%;
        }
        /* 表单样式优化 */
        
        .form-control:focus,
        .form-select:focus {
            border-color: #93c5fd;
            box-shadow: 0 0 0 0.25rem rgba(59, 130, 246, 0.1);
        }
        
        .input-group-text {
            color: var(--text-muted);
        }
        /* 模态框样式 */
        
        .modal-content {
            border-radius: var(--border-radius);
        }
        
        .modal-header {
            border-top-left-radius: calc(var(--border-radius) - 1px);
            border-top-right-radius: calc(var(--border-radius) - 1px);
        }
        /* 版本信息提示 */
        
        .alert-light {
            background-color: #f8fafc;
        }
        /* Toast通知样式 */
        
        .toast {
            border-radius: var(--border-radius);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        }
        /* 区间表格样式 */
        
        #rangeContainer tr {
            background-color: white;
            margin-bottom: 0.5rem;
            border-radius: var(--border-radius);
        }
        
        #rangeContainer tr td {
            padding: 0.75rem 0.5rem;
            vertical-align: middle;
        }
        /* 动画效果 */
        
        @keyframes fadeIn {
            from {
                opacity: 0;
                transform: translateY(10px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }
        
        .fade-in {
            animation: fadeIn 0.3s ease forwards;
        }
        /* 响应式调整 */
        
        @media (max-width: 768px) {
            .card-title {
                font-size: 0.95rem;
            }
            .table-responsive {
                font-size: 0.9rem;
            }
        }
    </style>
</head>

<body class="bg-light">
    <div class="container-fluid px-0">
        <!-- 顶部导航 -->
        <nav class="navbar navbar-expand-lg navbar-light bg-white border-bottom sticky-top">
            <div class="container-fluid px-4">
                <a class="navbar-brand fw-bold" href="#">
                    <i class="bi bi-shop me-2"></i>电商后台管理系统
                </a>
                <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                    <span class="navbar-toggler-icon"></span>
                </button>
                <div class="collapse navbar-collapse" id="navbarNav">
                    <ul class="navbar-nav me-auto">
                        <li class="nav-item">
                            <a class="nav-link" href="#">数据概览</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="#">商品管理</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="#">订单管理</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link active" href="#">规则配置</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="#">系统设置</a>
                        </li>
                    </ul>
                    <div class="d-flex align-items-center">
                        <div class="dropdown">
                            <a href="#" class="d-flex align-items-center text-decoration-none dropdown-toggle" id="userDropdown" data-bs-toggle="dropdown" aria-expanded="false">
                                <div class="avatar-circle me-2">PM</div>
                                <span>产品经理</span>
                            </a>
                            <ul class="dropdown-menu dropdown-menu-end shadow-sm" aria-labelledby="userDropdown">
                                <li><a class="dropdown-item" href="#"><i class="bi bi-person me-2"></i>个人信息</a></li>
                                <li><a class="dropdown-item" href="#"><i class="bi bi-gear me-2"></i>账号设置</a></li>
                                <li>
                                    <hr class="dropdown-divider">
                                </li>
                                <li><a class="dropdown-item" href="#"><i class="bi bi-box-arrow-right me-2"></i>退出登录</a></li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </nav>

        <!-- 主内容区 -->
        <div class="container-fluid px-4 py-3">
            <!-- 页面头部 -->
            <div class="d-flex justify-content-between align-items-center mb-4">
                <div>
                    <h4 class="mb-1 fw-semibold"><i class="bi bi-sliders me-2"></i>成本增幅规则管理</h4>
                    <p class="text-muted mb-0 fs-14">设置和管理不同电商平台的广告费用成本增幅规则</p>
                </div>
                <button id="addRuleBtn" class="btn btn-primary">
                    <i class="bi bi-plus-lg me-1"></i>新增规则
                </button>
            </div>

            <!-- 筛选区域 -->
            <div class="card shadow-sm border-0 mb-3">
                <div class="card-body p-3">
                    <div class="row g-3 align-items-end">
                        <div class="col-md-2">
                            <label class="form-label text-secondary fs-14 mb-1">规则状态</label>
                            <select id="statusFilter" class="form-select form-select-sm">
                                <option value="active" selected>生效中</option>
                                <option value="expired">已过期</option>
                                <option value="all">全部</option>
                            </select>
                        </div>
                        <div class="col-md-3">
                            <label class="form-label text-secondary fs-14 mb-1">电商平台</label>
                            <select id="platformFilter" class="form-select form-select-sm">
                                <option value="all" selected>全部平台</option>
                                <option value="tmall">天猫</option>
                                <option value="jd">京东</option>
                                <option value="pdd">拼多多</option>
                                <option value="douyin">抖音</option>
                            </select>
                        </div>
                        <div class="col-md-4">
                            <label class="form-label text-secondary fs-14 mb-1">版本名称</label>
                            <input type="text" id="versionFilter" class="form-control form-control-sm" placeholder="输入版本名称搜索">
                        </div>
                        <div class="col-md-3">
                            <div class="d-flex gap-2">
                                <button id="searchBtn" class="btn btn-primary btn-sm flex-grow-1">
                                    <i class="bi bi-search me-1"></i>查询
                                </button>
                                <button id="resetBtn" class="btn btn-light btn-sm">
                                    <i class="bi bi-arrow-counterclockwise"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 规则列表 -->
            <div class="rules-container">
                <!-- 版本信息提示 -->
                <div id="ruleVersionInfo" class="alert alert-light border-start border-4 border-primary d-none mb-3 py-2 px-3">
                    <div class="d-flex align-items-center">
                        <i class="bi bi-info-circle text-primary me-2"></i>
                        <span id="versionInfoText" class="flex-grow-1">您正在查看历史版本规则</span>
                        <button id="backToCurrentBtn" class="btn btn-sm btn-outline-primary">
                            返回当前版本
                        </button>
                    </div>
                </div>

                <!-- 卡片列表容器 -->
                <div class="row g-3" id="rulesCardContainer">
                    <!-- 加载指示器 -->
                    <div class="col-12 text-center py-5" id="loadingIndicator">
                        <div class="spinner-border text-primary" role="status">
                            <span class="visually-hidden">加载中...</span>
                        </div>
                        <p class="mt-2 text-muted">加载数据中，请稍候...</p>
                    </div>
                </div>

                <!-- 空数据状态 -->
                <div id="emptyState" class="text-center py-5 d-none">
                    <i class="bi bi-clipboard-x fs-1 text-muted"></i>
                    <p class="mt-3 text-muted">暂无符合条件的规则数据</p>
                    <button class="btn btn-outline-primary btn-sm mt-2" id="addEmptyRuleBtn">
                        <i class="bi bi-plus-lg me-1"></i>新增规则
                    </button>
                </div>

                <!-- 分页控件 -->
                <div class="d-flex justify-content-between align-items-center mt-4 mb-3">
                    <div class="text-muted fs-14">
                        共 <span id="totalRules" class="fw-semibold">0</span> 条规则
                    </div>
                    <nav aria-label="规则分页">
                        <ul class="pagination pagination-sm mb-0">
                            <li class="page-item disabled">
                                <a class="page-link" href="#" tabindex="-1" aria-disabled="true">
                                    <i class="bi bi-chevron-left"></i>
                                </a>
                            </li>
                            <li class="page-item active"><a class="page-link" href="#">1</a></li>
                            <li class="page-item disabled">
                                <a class="page-link" href="#">
                                    <i class="bi bi-chevron-right"></i>
                                </a>
                            </li>
                        </ul>
                    </nav>
                </div>
            </div>
        </div>
    </div>

    <!-- 新增/编辑规则模态框 -->
    <div class="modal fade" id="ruleModal" tabindex="-1" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content border-0 shadow">
                <div class="modal-header bg-light">
                    <h5 class="modal-title fw-semibold" id="ruleModalTitle">新增成本增幅规则</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body p-4">
                    <form id="ruleForm">
                        <input type="hidden" id="ruleId">
                        <!-- 基本信息 -->
                        <div class="row g-3 mb-4">
                            <div class="col-md-6">
                                <label for="versionName" class="form-label text-secondary fs-14 mb-1">版本名称 <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" id="versionName" required placeholder="例如：2025年Q2成本增幅规则">
                            </div>
                            <div class="col-md-6">
                                <label for="platform" class="form-label text-secondary fs-14 mb-1">电商平台 <span class="text-danger">*</span></label>
                                <select class="form-select" id="platform" required>
                                    <option value="" selected disabled>请选择电商平台</option>
                                    <option value="tmall">天猫</option>
                                    <option value="jd">京东</option>
                                    <option value="pdd">拼多多</option>
                                    <option value="douyin">抖音</option>
                                </select>
                            </div>
                            <div class="col-md-12">
                                <label class="form-label text-secondary fs-14 mb-1">日期范围 <span class="text-danger">*</span></label>
                                <div class="input-group">
                                    <input type="text" class="form-control" id="startDate" required placeholder="开始月份">
                                    <span class="input-group-text border-start-0 border-end-0 bg-transparent">至</span>
                                    <input type="text" class="form-control" id="endDate" required placeholder="结束月份">
                                </div>
                                <div class="form-text">格式：YYYY-MM</div>
                            </div>
                        </div>

                        <!-- 区间设置标题 -->
                        <div class="d-flex justify-content-between align-items-center mb-3">
                            <h6 class="fw-semibold mb-0"><i class="bi bi-sliders me-1"></i>广告费用区间设置</h6>
                            <button type="button" id="addRangeBtn" class="btn btn-sm btn-outline-primary">
                                <i class="bi bi-plus-circle me-1"></i>添加区间
                            </button>
                        </div>
                        <p class="text-muted small mb-3">设置不同广告费用区间的成本增幅比例，区间不可重叠</p>

                        <!-- 直观的区间设置界面 -->
                        <div class="card border-0 bg-light mb-3">
                            <div class="card-body p-0">
                                <div class="table-responsive">
                                    <table class="table table-borderless mb-0">
                                        <thead class="bg-light">
                                            <tr>
                                                <th width="35%">起始金额(含)</th>
                                                <th width="35%">结束金额</th>
                                                <th width="20%">成本增幅比例</th>
                                                <th width="10%">操作</th>
                                            </tr>
                                        </thead>
                                        <tbody id="rangeContainer">
                                            <!-- 区间项会在JS中动态添加 -->
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>

                        <!-- 可视化区间预览 -->
                        <div class="card border border-dashed border-primary-subtle bg-primary-subtle mb-3">
                            <div class="card-body p-3">
                                <h6 class="fw-semibold fs-14 mb-2"><i class="bi bi-eye me-1"></i>区间设置预览</h6>
                                <div id="rangesPreview">
                                    <div class="progress" style="height: 30px;" id="rangesProgressBar">
                                        <!-- 动态生成的区间可视化 -->
                                    </div>
                                    <div class="d-flex justify-content-between text-muted fs-12 mt-1">
                                        <span>¥0</span>
                                        <span id="maxRangeValue">¥0</span>
                                    </div>
                                </div>
                                <div id="noRangesMessage" class="text-center text-muted py-2 d-none">
                                    <i class="bi bi-exclamation-circle me-1"></i>请添加区间以查看预览
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
                <div class="modal-footer bg-light">
                    <button type="button" class="btn btn-light" data-bs-dismiss="modal">取消</button>
                    <button type="button" id="saveRuleBtn" class="btn btn-primary">保存规则</button>
                </div>
            </div>
        </div>
    </div>

    <!-- 删除确认模态框 -->
    <div class="modal fade" id="deleteConfirmModal" tabindex="-1" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content border-0 shadow">
                <div class="modal-header border-0">
                    <h5 class="modal-title fw-semibold">确认删除</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div class="text-center mb-3">
                        <i class="bi bi-exclamation-triangle-fill text-warning fs-1"></i>
                    </div>
                    <p class="mb-1">您确定要删除此规则吗？</p>
                    <p class="text-muted small">删除后数据将无法恢复，请谨慎操作。</p>
                </div>
                <div class="modal-footer border-0">
                    <button type="button" class="btn btn-light" data-bs-dismiss="modal">取消</button>
                    <button type="button" id="confirmDeleteBtn" class="btn btn-danger" data-rule-id="">确认删除</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Toast通知容器 -->
    <div class="toast-container position-fixed top-0 end-0 p-3"></div>

    <!-- 引入JS库 -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.2.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/flatpickr"></script>
    <script src="https://cdn.jsdelivr.net/npm/flatpickr/dist/l10n/zh.js"></script>

    <!-- 自定义JavaScript -->
    <script>
        // 等待DOM加载完成
        document.addEventListener('DOMContentLoaded', function() {
            // 初始化日期选择器
            initDatePickers();

            // 初始化事件监听
            initEventListeners();

            // 加载规则数据
            loadRulesData();

            // 初始化区间容器
            initRangeContainer();
        });

        // 初始化日期选择器
        function initDatePickers() {
            const dateConfig = {
                locale: 'zh',
                dateFormat: 'Y-m',
                mode: "single",
                static: true,
                monthSelectorType: "static"
            };

            flatpickr('#startDate', dateConfig);
            flatpickr('#endDate', dateConfig);
        }

        // 初始化事件监听
        function initEventListeners() {
            // 新增规则按钮
            document.getElementById('addRuleBtn').addEventListener('click', function() {
                openRuleModal('add');
            });

            // 空状态下的新增按钮
            document.getElementById('addEmptyRuleBtn').addEventListener('click', function() {
                openRuleModal('add');
            });

            // 返回当前版本按钮
            document.getElementById('backToCurrentBtn').addEventListener('click', function() {
                loadRulesData();
            });

            // 筛选按钮
            document.getElementById('searchBtn').addEventListener('click', function() {
                loadRulesData();
            });

            // 重置按钮
            document.getElementById('resetBtn').addEventListener('click', function() {
                document.getElementById('statusFilter').value = 'active';
                document.getElementById('platformFilter').value = 'all';
                document.getElementById('versionFilter').value = '';
                loadRulesData();
            });

            // 添加区间按钮
            document.getElementById('addRangeBtn').addEventListener('click', function() {
                addNewRange();
            });

            // 保存规则按钮
            document.getElementById('saveRuleBtn').addEventListener('click', function() {
                saveRule();
            });

            // 确认删除按钮
            document.getElementById('confirmDeleteBtn').addEventListener('click', function() {
                const ruleId = this.getAttribute('data-rule-id');
                deleteRule(ruleId);
            });
        }

        // 初始化区间容器
        function initRangeContainer() {
            const container = document.getElementById('rangeContainer');
            container.innerHTML = '';

            // 添加第一个区间
            addNewRange();

            // 重置预览
            updateRangesPreview();
        }

        // 添加新区间
        function addNewRange() {
            const container = document.getElementById('rangeContainer');
            const rangeCount = container.children.length;

            const newRow = document.createElement('tr');
            newRow.className = 'range-item fade-in';
            newRow.setAttribute('data-index', rangeCount);

            // 获取上一个区间的结束金额
            let startValue = '0';
            if (rangeCount > 0) {
                const lastEndInput = container.querySelector(`tr:nth-child(${rangeCount}) td:nth-child(2) input`);
                if (lastEndInput && lastEndInput.value) {
                    startValue = lastEndInput.value;
                }
            }

            newRow.innerHTML = `
        <td>
            <div class="input-group">
                <span class="input-group-text border-end-0 bg-transparent">¥</span>
                <input type="number" class="form-control border-start-0 start-amount" 
                       placeholder="0" required min="0" step="0.01" value="${startValue}">
            </div>
            <div class="invalid-feedback"></div>
        </td>
        <td>
            <div class="input-group">
                <span class="input-group-text border-end-0 bg-transparent">¥</span>
                <input type="number" class="form-control border-start-0 end-amount" 
                       placeholder="例如：1000" required min="0" step="0.01">
            </div>
            <div class="invalid-feedback"></div>
        </td>
        <td>
            <div class="input-group">
                <input type="number" class="form-control cost-rate" 
                       placeholder="例如：5" required min="0" max="100" step="0.01">
                <span class="input-group-text border-start-0 bg-transparent">%</span>
            </div>
            <div class="invalid-feedback"></div>
        </td>
        <td class="text-center">
            <button type="button" class="btn btn-sm btn-link text-danger remove-range p-0" ${rangeCount === 0 ? 'disabled' : ''}>
                <i class="bi bi-trash"></i>
            </button>
        </td>
    `;

            // 添加到容器
            container.appendChild(newRow);

            // 设置第一个区间的起始值为0且不可编辑
            if (rangeCount === 0) {
                const firstStartInput = newRow.querySelector('.start-amount');
                firstStartInput.value = '0';
                firstStartInput.setAttribute('readonly', true);
            }

            // 绑定删除区间事件
            const removeBtn = newRow.querySelector('.remove-range');
            removeBtn.addEventListener('click', function() {
                removeRange(newRow);
            });

            // 绑定输入事件
            const inputs = newRow.querySelectorAll('input');
            inputs.forEach(input => {
                input.addEventListener('input', function() {
                    validateRanges();
                    updateRangesPreview();

                    // 如果是结束金额，自动更新下一个区间的起始金额
                    if (input.classList.contains('end-amount')) {
                        const currentRow = input.closest('tr');
                        const nextRow = currentRow.nextElementSibling;
                        if (nextRow) {
                            const nextStartInput = nextRow.querySelector('.start-amount');
                            if (nextStartInput) {
                                nextStartInput.value = input.value;
                            }
                        }
                    }
                });
            });

            // 更新预览
            updateRangesPreview();
        }

        // 删除区间
        function removeRange(row) {
            const container = document.getElementById('rangeContainer');

            // 移除当前行
            container.removeChild(row);

            // 重新为所有行设置索引
            const rows = container.querySelectorAll('tr');
            rows.forEach((row, index) => {
                row.setAttribute('data-index', index);

                // 第一行的删除按钮应禁用
                if (index === 0) {
                    const removeBtn = row.querySelector('.remove-range');
                    removeBtn.disabled = true;
                }

                // 如果不是第一行，根据上一行的结束金额设置起始金额
                if (index > 0) {
                    const prevRow = rows[index - 1];
                    const prevEndInput = prevRow.querySelector('.end-amount');
                    const currentStartInput = row.querySelector('.start-amount');

                    if (prevEndInput && prevEndInput.value) {
                        currentStartInput.value = prevEndInput.value;
                    }
                }
            });

            // 更新预览
            validateRanges();
            updateRangesPreview();
        }

        // 验证区间设置
        function validateRanges() {
            const rows = document.querySelectorAll('#rangeContainer tr');
            let isValid = true;

            rows.forEach((row, index) => {
                const startInput = row.querySelector('.start-amount');
                const endInput = row.querySelector('.end-amount');
                const rateInput = row.querySelector('.cost-rate');

                const startValue = parseFloat(startInput.value) || 0;
                const endValue = parseFloat(endInput.value) || 0;
                const rateValue = parseFloat(rateInput.value) || 0;

                // 清除之前的错误
                startInput.classList.remove('is-invalid');
                endInput.classList.remove('is-invalid');
                rateInput.classList.remove('is-invalid');

                // 验证结束金额必须大于起始金额
                if (endValue <= startValue) {
                    endInput.classList.add('is-invalid');
                    endInput.nextElementSibling.textContent = '结束金额必须大于起始金额';
                    isValid = false;
                }

                // 验证增幅比例范围
                if (rateValue < 0 || rateValue > 100) {
                    rateInput.classList.add('is-invalid');
                    rateInput.nextElementSibling.textContent = '比例范围：0-100%';
                    isValid = false;
                }
            });

            return isValid;
        }

        // 更新区间预览
        function updateRangesPreview() {
            const rows = document.querySelectorAll('#rangeContainer tr');
            const progressBar = document.getElementById('rangesProgressBar');
            const maxValueSpan = document.getElementById('maxRangeValue');
            const noRangesMsg = document.getElementById('noRangesMessage');

            // 清空预览
            progressBar.innerHTML = '';

            if (rows.length === 0) {
                noRangesMsg.classList.remove('d-none');
                maxValueSpan.textContent = '¥0';
                return;
            }

            noRangesMsg.classList.add('d-none');

            // 找出最大金额
            let maxValue = 0;
            rows.forEach(row => {
                const endInput = row.querySelector('.end-amount');
                const endValue = parseFloat(endInput.value) || 0;
                if (endValue > maxValue) {
                    maxValue = endValue;
                }
            });

            // 设置最大值显示
            maxValueSpan.textContent = `¥${formatNumber(maxValue)}`;

            // 创建区间预览
            rows.forEach((row, index) => {
                const startInput = row.querySelector('.start-amount');
                const endInput = row.querySelector('.end-amount');
                const rateInput = row.querySelector('.cost-rate');

                const startValue = parseFloat(startInput.value) || 0;
                const endValue = parseFloat(endInput.value) || 0;
                const rateValue = parseFloat(rateInput.value) || 0;

                if (endValue > startValue && endValue > 0) {
                    const range = endValue - startValue;
                    const width = (range / maxValue) * 100;

                    const progressItem = document.createElement('div');
                    progressItem.className = `progress-bar progress-bar-range-${(index % 5) + 1}`;
                    progressItem.style.width = `${width}%`;
                    progressItem.setAttribute('title', `${formatNumber(startValue)}-${formatNumber(endValue)}元: ${rateValue}%`);
                    progressItem.textContent = `${rateValue}%`;

                    progressBar.appendChild(progressItem);
                }
            });
        }

        // 打开规则模态框
        function openRuleModal(mode, ruleData = null) {
            const modal = document.getElementById('ruleModal');
            const modalTitle = document.getElementById('ruleModalTitle');
            const ruleIdInput = document.getElementById('ruleId');
            const form = document.getElementById('ruleForm');

            // 重置表单
            form.reset();

            // 根据模式设置标题和ID
            if (mode === 'add') {
                modalTitle.textContent = '新增成本增幅规则';
                ruleIdInput.value = '';
                initRangeContainer();
            } else if (mode === 'edit' && ruleData) {
                modalTitle.textContent = '编辑成本增幅规则';
                ruleIdInput.value = ruleData.id;

                // 填充数据
                document.getElementById('versionName').value = ruleData.versionName;
                document.getElementById('platform').value = ruleData.platform;
                document.getElementById('startDate').value = ruleData.startDate;
                document.getElementById('endDate').value = ruleData.endDate;

                // 初始化区间
                document.getElementById('rangeContainer').innerHTML = '';

                // 填充区间数据
                ruleData.ranges.forEach((range, index) => {
                    if (index === 0) {
                        // 添加第一个区间
                        addNewRange();
                        const firstRow = document.querySelector('#rangeContainer tr');
                        if (firstRow) {
                            firstRow.querySelector('.end-amount').value = range.end;
                            firstRow.querySelector('.cost-rate').value = range.rate;
                        }
                    } else {
                        // 添加其他区间
                        addNewRange();
                        const rows = document.querySelectorAll('#rangeContainer tr');
                        const currentRow = rows[index];
                        if (currentRow) {
                            currentRow.querySelector('.start-amount').value = range.start;
                            currentRow.querySelector('.end-amount').value = range.end;
                            currentRow.querySelector('.cost-rate').value = range.rate;
                        }
                    }
                });

                // 更新预览
                updateRangesPreview();
            }

            // 显示模态框
            const modalInstance = new bootstrap.Modal(modal);
            modalInstance.show();
        }

        // 保存规则
        function saveRule() {
            // 验证表单
            const form = document.getElementById('ruleForm');
            if (!form.checkValidity()) {
                form.classList.add('was-validated');
                return;
            }

            // 验证区间
            if (!validateRanges()) {
                showToast('区间设置有误，请检查', 'warning');
                return;
            }

            // 收集表单数据
            const ruleId = document.getElementById('ruleId').value;
            const ruleData = {
                id: ruleId || `rule_${Date.now()}`,
                versionName: document.getElementById('versionName').value,
                platform: document.getElementById('platform').value,
                startDate: document.getElementById('startDate').value,
                endDate: document.getElementById('endDate').value,
                status: isRuleActive(document.getElementById('startDate').value, document.getElementById('endDate').value) ? 'active' : 'expired',
                ranges: []
            };

            // 收集区间数据
            const rows = document.querySelectorAll('#rangeContainer tr');
            rows.forEach(row => {
                const startValue = parseFloat(row.querySelector('.start-amount').value) || 0;
                const endValue = parseFloat(row.querySelector('.end-amount').value) || 0;
                const rateValue = parseFloat(row.querySelector('.cost-rate').value) || 0;

                ruleData.ranges.push({
                    start: startValue,
                    end: endValue,
                    rate: rateValue
                });
            });

            // 保存数据 (模拟API调用)
            saveRuleToServer(ruleData).then(() => {
                // 隐藏模态框
                const modal = document.getElementById('ruleModal');
                bootstrap.Modal.getInstance(modal).hide();

                // 重新加载规则
                loadRulesData();

                // 显示成功提示
                showToast(`规则${ruleId ? '更新' : '创建'}成功！`, 'success');
            }).catch(error => {
                showToast(`保存失败：${error.message}`, 'danger');
            });
        }

        // 删除规则
        function deleteRule(ruleId) {
            // 模拟API调用
            deleteRuleFromServer(ruleId).then(() => {
                // 隐藏确认对话框
                const modal = document.getElementById('deleteConfirmModal');
                bootstrap.Modal.getInstance(modal).hide();

                // 重新加载规则
                loadRulesData();

                // 显示成功提示
                showToast('规则已成功删除', 'success');
            }).catch(error => {
                showToast(`删除失败：${error.message}`, 'danger');
            });
        }

        // 加载规则数据
        function loadRulesData() {
            const container = document.getElementById('rulesCardContainer');
            const loadingIndicator = document.getElementById('loadingIndicator');
            const emptyState = document.getElementById('emptyState');
            const totalRulesSpan = document.getElementById('totalRules');
            const versionInfo = document.getElementById('ruleVersionInfo');

            // 显示加载指示器
            container.innerHTML = '';
            loadingIndicator.classList.remove('d-none');
            emptyState.classList.add('d-none');

            // 获取筛选条件
            const status = document.getElementById('statusFilter').value;
            const platform = document.getElementById('platformFilter').value;
            const versionName = document.getElementById('versionFilter').value;

            // 模拟API调用
            fetchRulesData(status, platform, versionName).then(rules => {
                // 隐藏加载指示器
                loadingIndicator.classList.add('d-none');

                // 隐藏版本信息提示
                versionInfo.classList.add('d-none');

                // 更新总数
                totalRulesSpan.textContent = rules.length;

                if (rules.length === 0) {
                    // 显示空状态
                    emptyState.classList.remove('d-none');
                    return;
                }

                // 渲染规则卡片
                rules.forEach(rule => {
                    container.appendChild(createRuleCard(rule));
                });
            }).catch(error => {
                loadingIndicator.classList.add('d-none');
                showToast(`加载失败：${error.message}`, 'danger');
            });
        }

        // 创建规则卡片
        function createRuleCard(rule) {
            const col = document.createElement('div');
            col.className = 'col-md-6 col-lg-4 fade-in';

            // 生成状态标签类和文本
            const statusClass = rule.status === 'active' ? 'bg-success-subtle text-success' : 'bg-secondary-subtle text-secondary';
            const statusText = rule.status === 'active' ? '生效中' : '已过期';

            // 生成平台图标
            const platformIcon = getPlatformIcon(rule.platform);
            const platformText = getPlatformName(rule.platform);

            let rangesHtml = '';
            let chartHtml = '';

            if (rule.ranges && rule.ranges.length > 0) {
                // 计算最大金额
                let maxValue = 0;
                rule.ranges.forEach(range => {
                    if (range.end > maxValue) {
                        maxValue = range.end;
                    }
                });

                // 生成区间图表HTML
                chartHtml = `
            <div class="cost-ranges-chart mt-2 mb-3">
                <div class="progress" style="height: 24px;">
                    ${rule.ranges.map((range, index) => {
                        const width = ((range.end - range.start) / maxValue) * 100;
                        return `<div class="progress-bar progress-bar-range-${(index % 5) + 1}" style="width: ${width}%;" 
                                title="${formatNumber(range.start)}-${formatNumber(range.end)}元: ${range.rate}%">${range.rate}%</div>`;
                    }).join('')}
                </div>
                <div class="d-flex justify-content-between text-muted fs-12 mt-1">
                    <span>¥0</span>
                    <span>¥${formatNumber(maxValue)}</span>
                </div>
            </div>
        `;
        
        // 生成区间表格HTML
        rangesHtml = `
            <div class="table-responsive">
                <table class="table table-sm table-borderless fs-14 mb-0 ranges-table">
                    <thead class="text-muted fs-12">
                        <tr>
                            <th>起始金额</th>
                            <th>结束金额</th>
                            <th class="text-end">增幅比例</th>
                        </tr>
                    </thead>
                    <tbody>
                        ${rule.ranges.map(range => `
                            <tr>
                                <td>¥${formatNumber(range.start)}</td>
                                <td>¥${formatNumber(range.end)}</td>
                                <td class="text-end fw-semibold">${range.rate}%</td>
                            </tr>
                        `).join('')}
                    </tbody>
                </table>
            </div>
        `;
    }
    
    col.innerHTML = `
        <div class="card h-100 shadow-sm border-0 position-relative rule-card" data-rule-id="${rule.id}">
            <!-- 状态标签 -->
            <div class="position-absolute top-0 end-0 mt-2 me-2">
                <span class="badge ${statusClass} px-2 py-1 rounded-pill">${statusText}</span>
            </div>
            
            <div class="card-body p-3">
                <h5 class="card-title fs-16 fw-semibold mb-3 text-truncate" title="${rule.versionName}">
                    ${rule.versionName}
                </h5>
                
                <div class="mb-3">
                    <div class="d-flex justify-content-between text-muted fs-14 mb-1">
                        <span>电商平台</span>
                        <span>生效时间</span>
                    </div>
                    <div class="d-flex justify-content-between">
                        <span class="badge-platform">${platformIcon} ${platformText}</span>
                        <span>${rule.startDate} 至 ${rule.endDate}</span>
                    </div>
                </div>
                
                <!-- 区间明细 -->
                <div class="mt-3">
                    <h6 class="fs-14 fw-semibold d-flex align-items-center">
                        <i class="bi bi-layers me-1"></i>区间设置
                    </h6>
                    
                    ${chartHtml}
                    
                    ${rangesHtml}
                </div>
            </div>
            
            <!-- 卡片底部操作按钮 -->
            <div class="card-footer bg-transparent border-top-0 p-3 pt-0">
                <div class="d-flex justify-content-end gap-2">
                    <button class="btn btn-sm btn-outline-primary edit-rule" data-rule-id="${rule.id}">
                        <i class="bi bi-pencil"></i> 编辑
                    </button>
                    <button class="btn btn-sm btn-outline-danger delete-rule" data-rule-id="${rule.id}">
                        <i class="bi bi-trash"></i> 删除
                    </button>
                </div>
            </div>
        </div>
    `;
    
    // 绑定编辑事件
    const editBtn = col.querySelector('.edit-rule');
    editBtn.addEventListener('click', function() {
        const ruleId = this.getAttribute('data-rule-id');
        editRuleById(ruleId);
    });
    
    // 绑定删除事件
    const deleteBtn = col.querySelector('.delete-rule');
    deleteBtn.addEventListener('click', function() {
        const ruleId = this.getAttribute('data-rule-id');
        confirmDelete(ruleId);
    });
    
    return col;
}

// 确认删除
function confirmDelete(ruleId) {
    const modal = document.getElementById('deleteConfirmModal');
    const confirmBtn = document.getElementById('confirmDeleteBtn');
    
    confirmBtn.setAttribute('data-rule-id', ruleId);
    
    const modalInstance = new bootstrap.Modal(modal);
    modalInstance.show();
}

// 通过ID编辑规则
function editRuleById(ruleId) {
    // 模拟API调用获取规则详情
    getRuleById(ruleId).then(rule => {
        openRuleModal('edit', rule);
    }).catch(error => {
        showToast(`获取规则详情失败：${error.message}`, 'danger');
    });
}

// 显示toast通知
function showToast(message, type = 'info') {
    const toastContainer = document.querySelector('.toast-container');
    
    const toastId = `toast-${Date.now()}`;
    const toastHTML = `
        <div id="${toastId}" class="toast align-items-center border-0" role="alert" aria-live="assertive" aria-atomic="true">
            <div class="d-flex">
                <div class="toast-body">
                    <div class="d-flex align-items-center">
                        ${getToastIcon(type)}
                        <span class="ms-2">${message}</span>
                    </div>
                </div>
                <button type="button" class="btn-close me-2 m-auto" data-bs-dismiss="toast" aria-label="Close"></button>
            </div>
        </div>
    `;
    
    toastContainer.insertAdjacentHTML('beforeend', toastHTML);
    
    const toast = document.getElementById(toastId);
    const toastInstance = new bootstrap.Toast(toast, {
        autohide: true,
        delay: 3000
    });
    
    toast.addEventListener('hidden.bs.toast', function() {
        toast.remove();
    });
    
    toastInstance.show();
}

// 获取Toast图标
function getToastIcon(type) {
    switch (type) {
        case 'success':
            return '<i class="bi bi-check-circle-fill text-success"></i>';
        case 'danger':
        case 'error':
            return '<i class="bi bi-exclamation-circle-fill text-danger"></i>';
        case 'warning':
            return '<i class="bi bi-exclamation-triangle-fill text-warning"></i>';
        case 'info':
        default:
            return '<i class="bi bi-info-circle-fill text-primary"></i>';
    }
}

// 获取平台图标
function getPlatformIcon(platform) {
    switch (platform) {
        case 'tmall':
            return '<i class="bi bi-bag-fill text-danger"></i>';
        case 'jd':
            return '<i class="bi bi-shop text-primary"></i>';
        case 'pdd':
            return '<i class="bi bi-basket-fill text-warning"></i>';
        case 'douyin':
            return '<i class="bi bi-camera-video-fill text-dark"></i>';
        default:
            return '<i class="bi bi-globe"></i>';
    }
}

// 获取平台名称
function getPlatformName(platform) {
    switch (platform) {
        case 'tmall': return '天猫';
        case 'jd': return '京东';
        case 'pdd': return '拼多多';
        case 'douyin': return '抖音';
        default: return '未知平台';
    }
}

// 格式化数字
function formatNumber(num) {
    return new Intl.NumberFormat('zh-CN').format(num);
}

// 判断规则是否生效
function isRuleActive(startDate, endDate) {
    const now = new Date();
    const currentYearMonth = `${now.getFullYear()}-${String(now.getMonth() + 1).padStart(2, '0')}`;
    
    return startDate <= currentYearMonth && currentYearMonth <= endDate;
}

// ========== API 模拟函数 ==========

// 模拟获取规则数据
function fetchRulesData(status, platform, versionName) {
    return new Promise((resolve) => {
        setTimeout(() => {
            let rules = getMockRules();
            
            // 应用筛选
            if (status && status !== 'all') {
                rules = rules.filter(rule => rule.status === status);
            }
            
            if (platform && platform !== 'all') {
                rules = rules.filter(rule => rule.platform === platform);
            }
            
            if (versionName) {
                rules = rules.filter(rule => rule.versionName.includes(versionName));
            }
            
            resolve(rules);
        }, 500);
    });
}

// 模拟获取规则详情
function getRuleById(ruleId) {
    return new Promise((resolve, reject) => {
        setTimeout(() => {
            const rules = getMockRules();
            const rule = rules.find(r => r.id === ruleId);
            
            if (rule) {
                resolve(rule);
            } else {
                reject(new Error('规则不存在'));
            }
        }, 300);
    });
}

// 模拟保存规则
function saveRuleToServer(ruleData) {
    return new Promise((resolve) => {
        setTimeout(() => {
            console.log('保存规则:', ruleData);
            resolve({ success: true });
        }, 600);
    });
}

// 模拟删除规则
function deleteRuleFromServer(ruleId) {
    return new Promise((resolve) => {
        setTimeout(() => {
            console.log('删除规则:', ruleId);
            resolve({ success: true });
        }, 400);
    });
}

// 获取模拟数据
function getMockRules() {
    return [
        {
            id: 'rule_1',
            versionName: '2025年Q2成本增幅规则',
            platform: 'tmall',
            startDate: '2025-04',
            endDate: '2025-06',
            status: 'active',
            ranges: [
                { start: 0, end: 5000, rate: 5 },
                { start: 5000, end: 10000, rate: 8 },
                { start: 10000, end: 50000, rate: 10 },
                { start: 50000, end: 100000, rate: 12 }
            ]
        },
        {
            id: 'rule_2',
            versionName: '京东小家电广告成本调整',
            platform: 'jd',
            startDate: '2025-01',
            endDate: '2025-12',
            status: 'active',
            ranges: [
                { start: 0, end: 3000, rate: 3 },
                { start: 3000, end: 8000, rate: 5 },
                { start: 8000, end: 20000, rate: 8 },
                { start: 20000, end: 50000, rate: 10 }
            ]
        },
        {
            id: 'rule_3',
            versionName: '拼多多促销活动专项规则',
            platform: 'pdd',
            startDate: '2025-03',
            endDate: '2025-05',
            status: 'active',
            ranges: [
                { start: 0, end: 2000, rate: 4 },
                { start: 2000, end: 5000, rate: 6 },
                { start: 5000, end: 15000, rate: 9 }
            ]
        },
        {
            id: 'rule_4',
            versionName: '抖音短视频带货增幅规则',
            platform: 'douyin',
            startDate: '2025-04',
            endDate: '2025-09',
            status: 'active',
            ranges: [
                { start: 0, end: 10000, rate: 6 },
                { start: 10000, end: 30000, rate: 8 },
                { start: 30000, end: 80000, rate: 10 },
                { start: 80000, end: 200000, rate: 15 }
            ]
        },
        {
            id: 'rule_5',
            versionName: '2024年Q4成本增幅规则',
            platform: 'tmall',
            startDate: '2024-10',
            endDate: '2024-12',
            status: 'expired',
            ranges: [
                { start: 0, end: 5000, rate: 4 },
                { start: 5000, end: 15000, rate: 7 },
                { start: 15000, end: 50000, rate: 9 }
            ]
        }
    ];
}
    </script>
</body>

</html>