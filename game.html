<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>星际突袭 - 飞机大战</title>
    <style>
        body {
            margin: 0;
            display: flex;
            justify-content: center;
            align-items: center;
            height: 100vh;
            background-color: #333;
            font-family: Arial, sans-serif;
        }
        
        #gameCanvas {
            border: 2px solid #fff;
            background-color: #000;
            cursor: none;
        }
        
        #uiContainer {
            position: absolute;
            top: 20px;
            left: 20px;
            color: white;
            font-size: 18px;
        }
        
        #uiContainer p {
            margin: 5px 0;
        }
        
        #startScreen,
        #gameOverScreen,
        #levelCompleteScreen {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background-color: rgba(0, 0, 0, 0.8);
            color: white;
            padding: 30px;
            border-radius: 10px;
            text-align: center;
            display: none;
            /* Initially hidden */
            z-index: 10;
        }
        
        #startScreen h1,
        #gameOverScreen h1,
        #levelCompleteScreen h1 {
            margin-top: 0;
        }
        
        button {
            padding: 10px 20px;
            font-size: 16px;
            background-color: #4CAF50;
            color: white;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            margin-top: 20px;
        }
        
        button:hover {
            background-color: #45a049;
        }
    </style>
</head>

<body>
    <div id="uiContainer">
        <p>得分: <span id="score">0</span></p>
        <p>生命: <span id="lives">3</span></p>
        <p>关卡: <span id="level">1</span></p>
        <p>武器: <span id="weaponType">普通</span></p>
        <p id="bossHealthBar" style="display:none;">Boss HP: <span id="bossHp">0</span></p>
    </div>

    <canvas id="gameCanvas"></canvas>

    <div id="startScreen" style="display: flex; flex-direction: column; align-items: center;">
        <h1>星际突袭</h1>
        <p>使用 方向键/WASD 移动, 空格键/J 射击</p>
        <button id="startButton">开始游戏</button>
    </div>

    <div id="gameOverScreen">
        <h1>游戏结束</h1>
        <p>最终得分: <span id="finalScore">0</span></p>
        <button id="restartButton">重新开始</button>
    </div>

    <div id="levelCompleteScreen">
        <h1>关卡完成!</h1>
        <button id="nextLevelButton">下一关</button>
    </div>

    <script>
        const canvas = document.getElementById('gameCanvas');
        const ctx = canvas.getContext('2d');

        // UI Elements
        const scoreDisplay = document.getElementById('score');
        const livesDisplay = document.getElementById('lives');
        const levelDisplay = document.getElementById('level');
        const weaponTypeDisplay = document.getElementById('weaponType');
        const bossHealthBar = document.getElementById('bossHealthBar');
        const bossHpDisplay = document.getElementById('bossHp');
        const startScreen = document.getElementById('startScreen');
        const gameOverScreen = document.getElementById('gameOverScreen');
        const levelCompleteScreen = document.getElementById('levelCompleteScreen');
        const finalScoreDisplay = document.getElementById('finalScore');
        const startButton = document.getElementById('startButton');
        const restartButton = document.getElementById('restartButton');
        const nextLevelButton = document.getElementById('nextLevelButton');


        let canvasWidth = 800;
        let canvasHeight = 600;
        canvas.width = canvasWidth;
        canvas.height = canvasHeight;

        let player, bullets, enemies, enemyBullets, powerUps, boss;
        let score, lives, gameLevel;
        let gameRunning = false;
        let keys = {};
        let shootCooldown = 0;
        let enemySpawnTimer = 0;
        let powerUpSpawnTimer = 0;
        const ENEMIES_PER_LEVEL = 10; // Number of enemies before boss
        let enemiesDefeatedThisLevel = 0;
        let currentWeapon = 'normal'; // 'normal', 'spread'
        let weaponTimer = 0; // Duration for special weapon

        const PLAYER_SPEED = 5;
        const BULLET_SPEED = 7;
        const ENEMY_BULLET_SPEED = 4;
        const POWERUP_DURATION = 10 * 60; // 10 seconds at 60fps

        // --- Game Object Classes (simple structs) ---
        function Player(x, y, width, height, color) {
            this.x = x;
            this.y = y;
            this.width = width;
            this.height = height;
            this.color = color;
            this.speed = PLAYER_SPEED;
            this.draw = function() {
                ctx.fillStyle = this.color;
                // Simple triangle shape for player
                ctx.beginPath();
                ctx.moveTo(this.x, this.y - this.height / 2);
                ctx.lineTo(this.x - this.width / 2, this.y + this.height / 2);
                ctx.lineTo(this.x + this.width / 2, this.y + this.height / 2);
                ctx.closePath();
                ctx.fill();
            }
        }

        function Bullet(x, y, width, height, color, speed, dy) {
            this.x = x;
            this.y = y;
            this.width = width;
            this.height = height;
            this.color = color;
            this.speed = speed;
            this.dy = dy; // direction: -1 for up, 1 for down
            this.draw = function() {
                ctx.fillStyle = this.color;
                ctx.fillRect(this.x - this.width / 2, this.y - this.height / 2, this.width, this.height);
            }
            this.update = function() {
                this.y += this.speed * this.dy;
            }
        }

        function Enemy(x, y, width, height, color, speed, hp, type = 'normal') {
            this.x = x;
            this.y = y;
            this.width = width;
            this.height = height;
            this.color = color;
            this.speed = speed;
            this.hp = hp;
            this.type = type; // 'normal', 'shooter'
            this.shootCooldown = Math.random() * 100 + 50; // Random initial cooldown

            this.draw = function() {
                ctx.fillStyle = this.color;
                // Simple rectangle for enemy
                ctx.fillRect(this.x - this.width / 2, this.y - this.height / 2, this.width, this.height);
            }
            this.update = function() {
                this.y += this.speed;
                if (this.type === 'shooter') {
                    this.shootCooldown--;
                    if (this.shootCooldown <= 0) {
                        enemyBullets.push(new Bullet(this.x, this.y + this.height / 2, 5, 10, 'orange', ENEMY_BULLET_SPEED, 1));
                        this.shootCooldown = 120; // Reset cooldown
                    }
                }
            }
        }

        function Boss(x, y, width, height, color, speed, hp) {
            this.x = x;
            this.y = y;
            this.width = width;
            this.height = height;
            this.color = color;
            this.speed = speed;
            this.hp = hp;
            this.initialHp = hp;
            this.targetY = 100; // Position to move to
            this.attackPattern = 0;
            this.attackCooldown = 120;
            this.phase = 'entering'; // 'entering', 'fighting'

            this.draw = function() {
                ctx.fillStyle = this.color;
                ctx.fillRect(this.x - this.width / 2, this.y - this.height / 2, this.width, this.height);
                // Simple health bar for boss
                ctx.fillStyle = 'red';
                ctx.fillRect(this.x - this.width / 2, this.y - this.height / 2 - 10, this.width, 5);
                ctx.fillStyle = 'green';
                ctx.fillRect(this.x - this.width / 2, this.y - this.height / 2 - 10, this.width * (this.hp / this.initialHp), 5);
                bossHpDisplay.textContent = `${this.hp}/${this.initialHp}`;
            }
            this.update = function() {
                if (this.phase === 'entering') {
                    if (this.y < this.targetY) {
                        this.y += this.speed / 2;
                    } else {
                        this.phase = 'fighting';
                    }
                } else if (this.phase === 'fighting') {
                    this.attackCooldown--;
                    if (this.attackCooldown <= 0) {
                        this.performAttack();
                        this.attackPattern = (this.attackPattern + 1) % 2; // Cycle patterns
                        this.attackCooldown = 90 - gameLevel * 5; // Faster attacks at higher levels
                    }
                }
            }
            this.performAttack = function() {
                if (this.attackPattern === 0) { // Spread shot
                    for (let i = -2; i <= 2; i++) {
                        let angle = Math.atan2(player.y - (this.y + this.height / 2), player.x - (this.x + i * 20));
                        // Simplified angled shot for this example
                        enemyBullets.push(new Bullet(this.x, this.y + this.height / 2, 8, 8, 'magenta', ENEMY_BULLET_SPEED * 0.8, 1)); // Simple downward
                        enemyBullets.push(new Bullet(this.x - 30, this.y + this.height / 2, 8, 8, 'magenta', ENEMY_BULLET_SPEED * 0.8, 1));
                        enemyBullets.push(new Bullet(this.x + 30, this.y + this.height / 2, 8, 8, 'magenta', ENEMY_BULLET_SPEED * 0.8, 1));
                    }
                } else if (this.attackPattern === 1) { // Targeted burst
                    for (let i = 0; i < 3 + gameLevel; i++) {
                        setTimeout(() => {
                            if (!gameRunning || !boss) return; // Check if boss still exists
                            enemyBullets.push(new Bullet(this.x, this.y + this.height / 2, 6, 12, 'cyan', ENEMY_BULLET_SPEED * 1.2, 1));
                        }, i * 100);
                    }
                }
            }
        }


        function PowerUp(x, y, width, height, color, type) {
            this.x = x;
            this.y = y;
            this.width = width;
            this.height = height;
            this.color = color;
            this.type = type; // 'spreadShot'
            this.speed = 2;

            this.draw = function() {
                ctx.fillStyle = this.color;
                ctx.fillRect(this.x - this.width / 2, this.y - this.height / 2, this.width, this.height);
                ctx.fillStyle = 'white';
                ctx.font = "bold 12px Arial";
                ctx.textAlign = "center";
                ctx.fillText(this.type === 'spreadShot' ? 'S' : '?', this.x, this.y + 4);
            }
            this.update = function() {
                this.y += this.speed;
            }
        }


        // --- Game Initialization ---
        function initGame(level = 1) {
            score = 0;
            lives = 3;
            gameLevel = level;
            enemiesDefeatedThisLevel = 0;
            boss = null;

            player = new Player(canvasWidth / 2, canvasHeight - 50, 30, 30, 'lime');
            bullets = [];
            enemies = [];
            enemyBullets = [];
            powerUps = [];

            currentWeapon = 'normal';
            weaponTimer = 0;

            updateUI();
            hideScreens();
            gameRunning = true;
            loop();
        }

        function startGame() {
            initGame(1);
        }

        function nextLevel() {
            initGame(gameLevel + 1);
        }

        // --- Game Loop ---
        function loop() {
            if (!gameRunning) return;

            update();
            draw();
            requestAnimationFrame(loop);
        }

        // --- Update Game State ---
        function update() {
            // Player movement
            if ((keys['ArrowLeft'] || keys['a']) && player.x - player.width / 2 > 0) player.x -= player.speed;
            if ((keys['ArrowRight'] || keys['d']) && player.x + player.width / 2 < canvasWidth) player.x += player.speed;
            if ((keys['ArrowUp'] || keys['w']) && player.y - player.height / 2 > 0) player.y -= player.speed;
            if ((keys['ArrowDown'] || keys['s']) && player.y + player.height / 2 < canvasHeight) player.y += player.speed;

            // Player shooting
            shootCooldown--;
            if ((keys[' '] || keys['j']) && shootCooldown <= 0) {
                shoot();
                shootCooldown = currentWeapon === 'spread' ? 10 : 15; // Faster for spread
            }

            // Weapon timer
            if (weaponTimer > 0) {
                weaponTimer--;
                if (weaponTimer <= 0) {
                    currentWeapon = 'normal';
                    weaponTypeDisplay.textContent = '普通';
                }
            }


            // Update bullets
            bullets.forEach((bullet, index) => {
                bullet.update();
                if (bullet.y < 0) bullets.splice(index, 1);
            });
            enemyBullets.forEach((bullet, index) => {
                bullet.update();
                if (bullet.y > canvasHeight) enemyBullets.splice(index, 1);
            });

            // Update enemies
            if (!boss) { // Only spawn normal enemies if no boss
                enemySpawnTimer--;
                if (enemySpawnTimer <= 0 && enemiesDefeatedThisLevel < ENEMIES_PER_LEVEL * gameLevel) {
                    spawnEnemy();
                    enemySpawnTimer = Math.max(30, 100 - gameLevel * 10); // Spawn faster at higher levels
                }
            }

            enemies.forEach((enemy, eIndex) => {
                enemy.update();
                if (enemy.y > canvasHeight + enemy.height) {
                    enemies.splice(eIndex, 1);
                }
            });

            // Update power-ups
            powerUpSpawnTimer--;
            if (powerUpSpawnTimer <= 0 && !boss) {
                spawnPowerUp();
                powerUpSpawnTimer = 600 + Math.random() * 300; // Spawn every 10-15 seconds
            }
            powerUps.forEach((powerUp, pIndex) => {
                powerUp.update();
                if (powerUp.y > canvasHeight) {
                    powerUps.splice(pIndex, 1);
                }
            });

            // Update Boss
            if (boss) {
                boss.update();
            }


            // Collision detection
            handleCollisions();

            // Check for level complete (all enemies for level defeated, or boss defeated)
            if (!boss && enemiesDefeatedThisLevel >= ENEMIES_PER_LEVEL * gameLevel) {
                spawnBoss();
            }
        }

        function shoot() {
            if (currentWeapon === 'normal') {
                bullets.push(new Bullet(player.x, player.y - player.height / 2, 5, 10, 'yellow', BULLET_SPEED, -1));
            } else if (currentWeapon === 'spread') {
                bullets.push(new Bullet(player.x, player.y - player.height / 2, 5, 10, 'cyan', BULLET_SPEED, -1));
                bullets.push(new Bullet(player.x - 10, player.y - player.height / 2 + 5, 5, 10, 'cyan', BULLET_SPEED, -1));
                bullets.push(new Bullet(player.x + 10, player.y - player.height / 2 + 5, 5, 10, 'cyan', BULLET_SPEED, -1));
            }
        }

        function spawnEnemy() {
            let x = Math.random() * (canvasWidth - 30) + 15;
            let y = -30;
            let speed = 1 + Math.random() * 1.5 + gameLevel * 0.2;
            let hp = 1 + Math.floor(gameLevel / 2);
            let type = Math.random() < 0.3 + gameLevel * 0.1 ? 'shooter' : 'normal'; // More shooters at higher levels
            let color = type === 'shooter' ? 'pink' : 'red';
            enemies.push(new Enemy(x, y, 30, 30, color, speed, hp, type));
        }

        function spawnPowerUp() {
            let x = Math.random() * (canvasWidth - 20) + 10;
            let y = -20;
            // For now, only spread shot power-up
            powerUps.push(new PowerUp(x, y, 20, 20, 'gold', 'spreadShot'));
        }

        function spawnBoss() {
            if (boss) return; // Don't spawn if one already exists
            enemies = []; // Clear remaining small enemies
            enemyBullets = []; // Clear enemy bullets

            let bossHp = 50 + gameLevel * 25;
            let bossSpeed = 1 + gameLevel * 0.1;
            boss = new Boss(canvasWidth / 2, -80, 100 + gameLevel * 10, 60 + gameLevel * 5, 'purple', bossSpeed, bossHp);
            bossHealthBar.style.display = 'block';
            updateUI(); // To show boss HP initially
        }

        function handleCollisions() {
            // Player bullets vs Enemies
            bullets.forEach((bullet, bIndex) => {
                enemies.forEach((enemy, eIndex) => {
                    if (checkCollision(bullet, enemy)) {
                        bullets.splice(bIndex, 1);
                        enemy.hp--;
                        if (enemy.hp <= 0) {
                            enemies.splice(eIndex, 1);
                            score += 10 * gameLevel;
                            enemiesDefeatedThisLevel++;
                            // Small chance to drop a power-up on enemy death, if no boss
                            if (!boss && Math.random() < 0.05) {
                                powerUps.push(new PowerUp(enemy.x, enemy.y, 20, 20, 'gold', 'spreadShot'));
                            }
                        }
                        updateUI();
                        return; // Bullet can only hit one enemy
                    }
                });
                // Player bullets vs Boss
                if (boss && checkCollision(bullet, boss)) {
                    bullets.splice(bIndex, 1);
                    boss.hp--;
                    score += 5 * gameLevel; // Points for hitting boss
                    updateUI();
                    if (boss.hp <= 0) {
                        score += 100 * gameLevel; // Bonus for defeating boss
                        boss = null;
                        levelComplete();
                    }
                    return;
                }
            });

            // Enemy bullets vs Player
            enemyBullets.forEach((bullet, ebIndex) => {
                if (checkCollision(bullet, player)) {
                    enemyBullets.splice(ebIndex, 1);
                    playerHit();
                    return;
                }
            });

            // Enemies vs Player (ramming)
            enemies.forEach((enemy, eIndex) => {
                if (checkCollision(enemy, player)) {
                    enemies.splice(eIndex, 1); // Enemy is destroyed
                    playerHit();
                    return;
                }
            });

            // Boss vs Player (ramming)
            if (boss && checkCollision(boss, player)) {
                playerHit(); // Player takes damage, boss is usually too strong to be affected by ramming
            }

            // Player vs PowerUps
            powerUps.forEach((powerUp, pIndex) => {
                if (checkCollision(player, powerUp)) {
                    powerUps.splice(pIndex, 1);
                    if (powerUp.type === 'spreadShot') {
                        currentWeapon = 'spread';
                        weaponTypeDisplay.textContent = '散射';
                        weaponTimer = POWERUP_DURATION;
                    }
                    // Add other power-up types here
                    score += 50;
                    updateUI();
                }
            });
        }

        function checkCollision(rect1, rect2) {
            // AABB collision detection
            return rect1.x - rect1.width / 2 < rect2.x + rect2.width / 2 &&
                rect1.x + rect1.width / 2 > rect2.x - rect2.width / 2 &&
                rect1.y - rect1.height / 2 < rect2.y + rect2.height / 2 &&
                rect1.y + rect1.height / 2 > rect2.y - rect2.height / 2;
        }

        function playerHit() {
            lives--;
            // Reset weapon on hit for more challenge
            currentWeapon = 'normal';
            weaponTimer = 0;
            weaponTypeDisplay.textContent = '普通';

            updateUI();
            if (lives <= 0) {
                gameOver();
            } else {
                // Brief invincibility and reset position (optional, not implemented here for simplicity)
                player.x = canvasWidth / 2;
                player.y = canvasHeight - 50;
            }
        }

        function updateUI() {
            scoreDisplay.textContent = score;
            livesDisplay.textContent = lives;
            levelDisplay.textContent = gameLevel;
            if (boss) {
                bossHpDisplay.textContent = `${Math.max(0, boss.hp)}/${boss.initialHp}`;
            } else {
                bossHealthBar.style.display = 'none';
            }
        }

        function hideScreens() {
            startScreen.style.display = 'none';
            gameOverScreen.style.display = 'none';
            levelCompleteScreen.style.display = 'none';
        }

        function gameOver() {
            gameRunning = false;
            finalScoreDisplay.textContent = score;
            gameOverScreen.style.display = 'flex';
            gameOverScreen.style.flexDirection = 'column';
            gameOverScreen.style.alignItems = 'center';
        }

        function levelComplete() {
            gameRunning = false;
            boss = null; // Ensure boss is cleared
            bossHealthBar.style.display = 'none';
            levelCompleteScreen.style.display = 'flex';
            levelCompleteScreen.style.flexDirection = 'column';
            levelCompleteScreen.style.alignItems = 'center';
        }

        // --- Draw Everything ---
        function draw() {
            // Clear canvas
            ctx.fillStyle = '#000810'; // Dark blue space background
            ctx.fillRect(0, 0, canvasWidth, canvasHeight);

            // Draw stars (simple)
            drawStars();

            // Draw player
            player.draw();

            // Draw bullets
            bullets.forEach(bullet => bullet.draw());
            enemyBullets.forEach(bullet => bullet.draw());

            // Draw enemies
            enemies.forEach(enemy => enemy.draw());

            // Draw power-ups
            powerUps.forEach(powerUp => powerUp.draw());

            // Draw Boss
            if (boss) {
                boss.draw();
            }
        }

        // Simple starfield background
        let stars = [];

        function createStars(count) {
            for (let i = 0; i < count; i++) {
                stars.push({
                    x: Math.random() * canvasWidth,
                    y: Math.random() * canvasHeight,
                    radius: Math.random() * 1.5,
                    alpha: Math.random() * 0.5 + 0.5, // opacity
                    speed: Math.random() * 0.5 + 0.1 // speed
                });
            }
        }

        function drawStars() {
            ctx.fillStyle = 'white';
            stars.forEach(star => {
                ctx.beginPath();
                ctx.arc(star.x, star.y, star.radius, 0, Math.PI * 2);
                ctx.globalAlpha = star.alpha;
                ctx.fill();
                star.y += star.speed; // Move stars down
                if (star.y > canvasHeight) { // Reset star if it goes off screen
                    star.y = 0;
                    star.x = Math.random() * canvasWidth;
                }
            });
            ctx.globalAlpha = 1.0; // Reset global alpha
        }
        createStars(100); // Create 100 stars


        // --- Event Listeners ---
        window.addEventListener('keydown', (e) => keys[e.key.toLowerCase()] = true); // Use toLowerCase for wasd
        window.addEventListener('keyup', (e) => keys[e.key.toLowerCase()] = false);

        startButton.addEventListener('click', startGame);
        restartButton.addEventListener('click', startGame);
        nextLevelButton.addEventListener('click', nextLevel);

        // Initial setup: Show start screen
        startScreen.style.display = 'flex';
        startScreen.style.flexDirection = 'column';
        startScreen.style.alignItems = 'center';
    </script>
</body>

</html>