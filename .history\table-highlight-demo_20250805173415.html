<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>边界利润表格高亮示例</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: 'PingFang SC', 'Microsoft YaHei', sans-serif;
        }

        :root {
            --primary-color: #1890ff;
            --success-color: #52c41a;
            --warning-color: #faad14;
            --danger-color: #ff4d4f;
            --text-primary: #262626;
            --text-secondary: #595959;
            --border-color: #e8e8e8;
            --bg-color: #f5f5f5;
            --container-bg: #fff;
            --shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
        }

        body {
            background-color: var(--bg-color);
            color: var(--text-primary);
            line-height: 1.5;
            font-size: 14px;
            padding: 20px;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            background: var(--container-bg);
            border-radius: 8px;
            box-shadow: var(--shadow);
            overflow: hidden;
        }

        .header {
            padding: 20px 24px;
            border-bottom: 1px solid var(--border-color);
            background: #fafafa;
        }

        .header h1 {
            font-size: 18px;
            font-weight: 600;
            color: var(--text-primary);
        }

        .table-container {
            overflow-x: auto;
            padding: 0;
        }

        table {
            width: 100%;
            border-collapse: separate;
            border-spacing: 0;
            min-width: 1200px;
        }

        th {
            background: #fafafa;
            font-weight: 600;
            color: var(--text-secondary);
            position: sticky;
            top: 0;
            z-index: 1;
            border-bottom: 1px solid var(--border-color);
            text-align: center;
            font-size: 14px;
            white-space: nowrap;
            padding: 12px 8px;
        }

        td {
            border-bottom: 1px solid var(--border-color);
            color: var(--text-primary);
            text-align: center;
            font-size: 14px;
            vertical-align: middle;
            padding: 12px 8px;
        }

        tr:last-child td {
            border-bottom: none;
        }

        tr:hover td {
            background: rgba(24, 144, 255, 0.04);
        }

        /* 当月边界利润列高亮样式 */
        .highlight-profit-header {
            background-color: rgba(255, 243, 205, 0.9) !important;
            border-left: 3px solid var(--warning-color) !important;
            border-right: 3px solid var(--warning-color) !important;
            font-weight: 600;
            color: #d48806;
        }

        .highlight-profit-column {
            background-color: rgba(255, 243, 205, 0.6) !important;
            border-left: 3px solid var(--warning-color) !important;
            border-right: 3px solid var(--warning-color) !important;
        }

        /* 当月边界比率列高亮样式 */
        .highlight-ratio-header {
            background-color: rgba(230, 247, 255, 0.9) !important;
            border-left: 3px solid var(--primary-color) !important;
            border-right: 3px solid var(--primary-color) !important;
            font-weight: 600;
            color: #0958d9;
        }

        .highlight-ratio-column {
            background-color: rgba(230, 247, 255, 0.6) !important;
            border-left: 3px solid var(--primary-color) !important;
            border-right: 3px solid var(--primary-color) !important;
        }

        /* 悬停效果保持突出显示 */
        tr:hover .highlight-profit-column {
            background-color: rgba(255, 243, 205, 0.8) !important;
        }

        tr:hover .highlight-ratio-column {
            background-color: rgba(230, 247, 255, 0.8) !important;
        }

        /* 排名列样式 */
        .rank-column {
            width: 50px;
            font-weight: 600;
        }

        /* 金额格式化 */
        .amount {
            font-family: 'Consolas', 'Monaco', monospace;
            font-weight: 500;
        }

        /* 百分比样式 */
        .percentage {
            font-weight: 600;
        }

        .percentage.positive {
            color: var(--success-color);
        }

        .percentage.negative {
            color: var(--danger-color);
        }

        /* 总计行样式 */
        .total-row {
            background-color: #f0f2f5 !important;
            font-weight: 600;
        }

        .total-row td {
            border-top: 2px solid var(--border-color);
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>边界利润分析表 - 高亮显示示例</h1>
        </div>
        
        <div class="table-container">
            <table id="profit-table">
                <thead>
                    <tr>
                        <th class="rank-column">排名</th>
                        <th>团队名称</th>
                        <th>总销售额</th>
                        <th>总成本(不含运费)</th>
                        <th>自由现金流</th>
                        <th class="highlight-profit-header">当月边界利润</th>
                        <th class="highlight-ratio-header">当月边界比率</th>
                        <th>预计发货单量</th>
                        <th>处中成交额</th>
                        <th>总价</th>
                        <th>边界利润</th>
                        <th>边界比率</th>
                    </tr>
                </thead>
                <tbody id="table-body">
                    <!-- 数据将通过JavaScript动态生成 -->
                </tbody>
            </table>
        </div>
    </div>

    <script>
        // 模拟数据
        const tableData = [
            {
                rank: 1,
                teamName: "林村直营（原始）",
                totalSales: 34080.38,
                totalCost: 31156.38,
                cashFlow: 31156.38,
                monthlyProfit: 2368.72,
                monthlyRatio: 2,
                expectedOrders: 329,
                dealAmount: 34080.38,
                totalPrice: 0,
                boundaryProfit: 2990.78,
                boundaryRatio: 9.41
            },
            {
                rank: 2,
                teamName: "鑫云直营（原始）",
                totalSales: 27153.5,
                totalCost: 27153.5,
                cashFlow: 27153.5,
                monthlyProfit: 20550.45,
                monthlyRatio: 18,
                expectedOrders: 246,
                dealAmount: 27153.5,
                totalPrice: 0,
                boundaryProfit: 5436.69,
                boundaryRatio: 19.98
            },
            {
                rank: 3,
                teamName: "浩然直营（原始）",
                totalSales: 20227.45,
                totalCost: 19531.25,
                cashFlow: 19531.25,
                monthlyProfit: 9132.14,
                monthlyRatio: 12,
                expectedOrders: 243,
                dealAmount: 20227.45,
                totalPrice: 0,
                boundaryProfit: 3377.2,
                boundaryRatio: 17.29
            },
            {
                rank: 4,
                teamName: "凡竹商贸（全域）",
                totalSales: 19408.38,
                totalCost: 18960.88,
                cashFlow: 18960.88,
                monthlyProfit: 4200.03,
                monthlyRatio: 6,
                expectedOrders: 251,
                dealAmount: 19408.38,
                totalPrice: 0,
                boundaryProfit: 2143.63,
                boundaryRatio: 11.3
            },
            {
                rank: 5,
                teamName: "誉兰商贸（过境）",
                totalSales: 17837.1,
                totalCost: 17837.1,
                cashFlow: 17837.1,
                monthlyProfit: 31986.05,
                monthlyRatio: 24,
                expectedOrders: 300,
                dealAmount: 17837.1,
                totalPrice: 0,
                boundaryProfit: 12339.63,
                boundaryRatio: 69.18
            }
        ];

        // 渲染表格数据
        function renderTable() {
            const tbody = document.getElementById('table-body');
            
            tableData.forEach(item => {
                const row = document.createElement('tr');
                
                // 判断百分比的正负
                const getRatioClass = (ratio) => {
                    return ratio >= 0 ? 'percentage positive' : 'percentage negative';
                };
                
                row.innerHTML = `
                    <td class="rank-column">${item.rank}</td>
                    <td>${item.teamName}</td>
                    <td class="amount">${item.totalSales.toFixed(2)}</td>
                    <td class="amount">${item.totalCost.toFixed(2)}</td>
                    <td class="amount">${item.cashFlow.toFixed(2)}</td>
                    <td class="amount highlight-profit-column">${item.monthlyProfit.toFixed(2)}</td>
                    <td class="highlight-ratio-column ${getRatioClass(item.monthlyRatio)}">${item.monthlyRatio}%</td>
                    <td>${item.expectedOrders}</td>
                    <td class="amount">${item.dealAmount.toFixed(2)}</td>
                    <td>${item.totalPrice}</td>
                    <td class="amount">${item.boundaryProfit.toFixed(2)}</td>
                    <td class="${getRatioClass(item.boundaryRatio)}">${item.boundaryRatio}%</td>
                `;
                
                tbody.appendChild(row);
            });
            
            // 添加总计行
            const totalRow = document.createElement('tr');
            totalRow.className = 'total-row';
            
            const totalSales = tableData.reduce((sum, item) => sum + item.totalSales, 0);
            const totalCost = tableData.reduce((sum, item) => sum + item.totalCost, 0);
            const totalCashFlow = tableData.reduce((sum, item) => sum + item.cashFlow, 0);
            const totalMonthlyProfit = tableData.reduce((sum, item) => sum + item.monthlyProfit, 0);
            const totalOrders = tableData.reduce((sum, item) => sum + item.expectedOrders, 0);
            const totalDealAmount = tableData.reduce((sum, item) => sum + item.dealAmount, 0);
            const totalBoundaryProfit = tableData.reduce((sum, item) => sum + item.boundaryProfit, 0);
            
            totalRow.innerHTML = `
                <td>合计</td>
                <td></td>
                <td class="amount">${totalSales.toFixed(2)}</td>
                <td class="amount">${totalCost.toFixed(2)}</td>
                <td class="amount">${totalCashFlow.toFixed(2)}</td>
                <td class="amount highlight-profit-column">${totalMonthlyProfit.toFixed(2)}</td>
                <td class="highlight-ratio-column"></td>
                <td>${totalOrders}</td>
                <td class="amount">${totalDealAmount.toFixed(2)}</td>
                <td>0</td>
                <td class="amount">${totalBoundaryProfit.toFixed(2)}</td>
                <td></td>
            `;
            
            tbody.appendChild(totalRow);
        }

        // 页面加载完成后渲染表格
        document.addEventListener('DOMContentLoaded', function() {
            renderTable();
        });
    </script>
</body>
</html>
