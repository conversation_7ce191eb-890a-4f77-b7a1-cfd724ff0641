<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8">
    <title>财务管理系统 - 阶梯分摊模块</title>
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/remixicon@3.5.0/fonts/remixicon.css">
    <link rel="icon" href="https://panhan.xin/assets/favicon-DfI5CYqk.ico">
    <style>
         :root {
            /* 主题色 */
            --primary: #4361ee;
            --primary-light: #eef2ff;
            --primary-dark: #3a56d4;
            --secondary: #6c757d;
            --success: #10b981;
            --warning: #f59e0b;
            --danger: #ef4444;
            --info: #3b82f6;
            /* 灰度 */
            --gray-50: #f9fafb;
            --gray-100: #f3f4f6;
            --gray-200: #e5e7eb;
            --gray-300: #d1d5db;
            --gray-400: #9ca3af;
            --gray-500: #6b7280;
            --gray-600: #4b5563;
            --gray-700: #374151;
            --gray-800: #1f2937;
            --gray-900: #111827;
            /* 布局 */
            --sidebar-width: 260px;
            --header-height: 64px;
            --border-radius: 12px;
            --card-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
            --transition: all 0.3s ease;
        }
        
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'PingFang SC', 'Helvetica Neue', Arial, sans-serif;
            background: var(--gray-100);
            color: var(--gray-800);
            line-height: 1.5;
            -webkit-font-smoothing: antialiased;
            -moz-osx-font-smoothing: grayscale;
        }
        
        .app-container {
            display: flex;
            min-height: 100vh;
        }
        /* 侧边栏 */
        
        .sidebar {
            width: var(--sidebar-width);
            background: #fff;
            box-shadow: 0 0 20px rgba(0, 0, 0, 0.05);
            display: flex;
            flex-direction: column;
            position: fixed;
            height: 100vh;
            z-index: 10;
        }
        
        .sidebar .logo {
            display: flex;
            align-items: center;
            padding: 24px 20px;
            gap: 12px;
            border-bottom: 1px solid var(--gray-100);
        }
        
        .sidebar .logo img {
            width: 40px;
            height: 40px;
        }
        
        .sidebar .logo h1 {
            font-size: 18px;
            font-weight: 600;
            color: var(--gray-900);
            margin: 0;
        }
        
        .sidebar .nav-links {
            list-style: none;
            padding: 20px 0;
            flex-grow: 1;
        }
        
        .sidebar .nav-links li a {
            display: flex;
            align-items: center;
            padding: 12px 20px;
            color: var(--gray-600);
            text-decoration: none;
            font-size: 15px;
            font-weight: 500;
            transition: var(--transition);
            border-left: 3px solid transparent;
            gap: 12px;
        }
        
        .sidebar .nav-links li a:hover {
            background: var(--gray-50);
            color: var(--primary);
        }
        
        .sidebar .nav-links li a.active {
            background: var(--primary-light);
            color: var(--primary);
            border-left-color: var(--primary);
        }
        
        .sidebar .nav-links li a i {
            font-size: 18px;
        }
        
        .sidebar-footer {
            padding: 16px 20px;
            border-top: 1px solid var(--gray-100);
            display: flex;
            justify-content: space-between;
        }
        
        .sidebar-footer a {
            color: var(--gray-500);
            text-decoration: none;
            font-size: 14px;
            transition: var(--transition);
            display: flex;
            align-items: center;
            gap: 6px;
        }
        
        .sidebar-footer a:hover {
            color: var(--primary);
        }
        /* 主内容区域 */
        
        .main-content {
            flex-grow: 1;
            margin-left: var(--sidebar-width);
            min-height: 100vh;
            display: flex;
            flex-direction: column;
        }
        
        .top-header {
            height: var(--header-height);
            background: #fff;
            border-bottom: 1px solid var(--gray-200);
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 24px;
            position: sticky;
            top: 0;
            z-index: 5;
        }
        
        .breadcrumb {
            display: flex;
            align-items: center;
            gap: 8px;
            color: var(--gray-500);
            font-size: 14px;
        }
        
        .breadcrumb .current {
            color: var(--gray-700);
            font-weight: 500;
        }
        
        .user-panel {
            display: flex;
            align-items: center;
            gap: 24px;
        }
        
        #current-date {
            color: var(--gray-500);
            font-size: 14px;
        }
        
        .user-info {
            display: flex;
            align-items: center;
            gap: 12px;
        }
        
        #current-user {
            font-weight: 500;
            font-size: 14px;
        }
        
        .user-avatar {
            width: 32px;
            height: 32px;
            border-radius: 50%;
        }
        
        .content-container {
            padding: 24px;
            flex-grow: 1;
        }
        
        .page-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 24px;
        }
        
        .page-header h2 {
            font-size: 24px;
            font-weight: 600;
            color: var(--gray-900);
        }
        
        .page-actions {
            display: flex;
            gap: 12px;
        }
        
        .search-box {
            position: relative;
        }
        
        .search-box i {
            position: absolute;
            left: 12px;
            top: 50%;
            transform: translateY(-50%);
            color: var(--gray-400);
        }
        
        .search-box input {
            padding: 8px 12px 8px 36px;
            border: 1px solid var(--gray-200);
            border-radius: var(--border-radius);
            width: 240px;
            font-size: 14px;
            transition: var(--transition);
        }
        
        .search-box input:focus {
            outline: none;
            border-color: var(--primary);
            box-shadow: 0 0 0 3px rgba(67, 97, 238, 0.15);
        }
        /* 统计卡片 */
        
        .stats-cards {
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            gap: 20px;
            margin-bottom: 24px;
        }
        
        .stat-card {
            background: #fff;
            border-radius: var(--border-radius);
            padding: 20px;
            position: relative;
            box-shadow: var(--card-shadow);
            transition: var(--transition);
            overflow: hidden;
        }
        
        .stat-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
        
        .stat-value {
            font-size: 24px;
            font-weight: 600;
            color: var(--gray-900);
            margin-bottom: 4px;
        }
        
        .stat-label {
            font-size: 14px;
            color: var(--gray-500);
        }
        
        .stat-icon {
            position: absolute;
            top: 20px;
            right: 20px;
            font-size: 24px;
            color: var(--primary);
            opacity: 0.7;
        }
        /* 卡片容器 */
        
        .card {
            background: #fff;
            border-radius: var(--border-radius);
            box-shadow: var(--card-shadow);
            margin-bottom: 24px;
            overflow: hidden;
        }
        /* 表格控制区 */
        
        .table-controls {
            padding: 16px 24px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            border-bottom: 1px solid var(--gray-200);
        }
        
        .table-info {
            font-size: 14px;
            color: var(--gray-600);
        }
        
        .table-filters {
            display: flex;
            gap: 12px;
        }
        
        .table-filters select {
            padding: 8px 12px;
            border: 1px solid var(--gray-200);
            border-radius: var(--border-radius);
            font-size: 14px;
            transition: var(--transition);
            color: var(--gray-700);
            background: #fff;
        }
        
        .table-filters select:focus {
            outline: none;
            border-color: var(--primary);
            box-shadow: 0 0 0 3px rgba(67, 97, 238, 0.15);
        }
        /* 数据表格 */
        
        .table-responsive {
            overflow-x: auto;
            margin: 0 -24px;
            padding: 0 24px;
        }
        
        table {
            width: 100%;
            border-collapse: separate;
            border-spacing: 0;
            min-width: 1000px;
        }
        
        th {
            background: var(--gray-50);
            font-weight: 600;
            color: var(--gray-600);
            position: sticky;
            top: 0;
            z-index: 1;
            border-bottom: 1px solid var(--gray-200);
            text-align: left;
            font-size: 14px;
            white-space: nowrap;
        }
        
        td {
            border-bottom: 1px solid var(--gray-200);
            color: var(--gray-800);
            text-align: left;
            font-size: 14px;
            vertical-align: middle;
        }
        
        tr:last-child td {
            border-bottom: none;
        }
        
        tr:hover td {
            background: var(--gray-50);
        }
        
        td.amount,
        th.amount {
            font-family: 'SF Mono', Consolas, monospace;
            font-variant-numeric: tabular-nums;
            font-weight: 500;
            white-space: nowrap;
            letter-spacing: 0.02em;
            text-align: right;
        }
        /* 状态标签 */
        
        .status-tag {
            display: inline-flex;
            align-items: center;
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 600;
            line-height: 1;
            white-space: nowrap;
        }
        
        .status-active {
            background: #dcfce7;
            color: #16a34a;
        }
        
        .status-history {
            background: #f1f5f9;
            color: #64748b;
        }
        /* 历史记录格式 */
        
        .history-row td {
            background-color: var(--gray-50);
            color: var(--gray-500);
        }
        
        .history-row:hover td {
            background-color: var(--gray-100);
        }
        
        .version-badge {
            margin-left: 4px;
            letter-spacing: 0.5px;
        }
        /* 表格分页 */
        
        .table-pagination {
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 16px;
            gap: 8px;
            border-top: 1px solid var(--gray-200);
        }
        
        .page-numbers {
            font-size: 14px;
            color: var(--gray-700);
        }
        
        .current-page {
            font-weight: 600;
            color: var(--primary);
        }
        /* 按钮样式 */
        
        .btn {
            padding: 8px 16px;
            border: none;
            border-radius: var(--border-radius);
            font-size: 14px;
            font-weight: 500;
            cursor: pointer;
            display: inline-flex;
            align-items: center;
            gap: 8px;
            transition: var(--transition);
        }
        
        .btn-primary {
            background: var(--primary);
            color: #fff;
        }
        
        .btn-primary:hover {
            background: var(--primary-dark);
        }
        
        .btn-secondary {
            background: var(--gray-200);
            color: var(--gray-700);
        }
        
        .btn-secondary:hover {
            background: var(--gray-300);
        }
        
        .btn-icon {
            padding: 8px 10px;
            border-radius: var(--border-radius);
            background: var(--gray-100);
            color: var(--gray-600);
        }
        
        .btn-icon:hover {
            background: var(--gray-200);
            color: var(--gray-800);
        }
        
        .btn-sm {
            padding: 4px 12px;
            font-size: 13px;
        }
        
        .btn-edit {
            color: var(--primary);
            background: var(--primary-light);
            padding: 6px 12px;
            font-weight: 600;
            font-size: 13px;
            border-radius: 6px;
        }
        
        .btn-edit:hover {
            background: #dce7ff;
        }
        
        .btn-end-date {
            color: var(--warning);
            background: #fef3c7;
            padding: 6px 12px;
            font-weight: 600;
            font-size: 13px;
            border-radius: 6px;
        }
        
        .btn-end-date:hover {
            background: #fde68a;
        }
        
        .btn i {
            font-size: 16px;
        }
        /* 显示历史记录按钮 */
        
        .toggle-history {
            padding: 2px 8px;
            font-size: 12px;
            color: var(--primary);
            background: var(--primary-light);
            border: none;
            border-radius: 4px;
            cursor: pointer;
            margin-left: 8px;
            transition: var(--transition);
        }
        
        .toggle-history:hover {
            background: #dce7ff;
        }
        /* 模态框 */
        
        .modal-bg {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0, 0, 0, 0.5);
            z-index: 100;
            align-items: center;
            justify-content: center;
            backdrop-filter: blur(2px);
        }
        
        .modal {
            width: 600px;
            background: #fff;
            border-radius: var(--border-radius);
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
            overflow: hidden;
            animation: modal-in 0.3s ease;
            transform-origin: center;
        }
        
        .modal-sm {
            width: 400px;
        }
        
        @keyframes modal-in {
            from {
                opacity: 0;
                transform: scale(0.95);
            }
            to {
                opacity: 1;
                transform: scale(1);
            }
        }
        
        .modal-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 20px 24px;
            border-bottom: 1px solid var(--gray-200);
        }
        
        .modal-title {
            font-size: 18px;
            font-weight: 600;
            color: var(--gray-900);
            margin: 0;
        }
        
        .btn-close {
            background: none;
            border: none;
            cursor: pointer;
            color: var(--gray-500);
            font-size: 20px;
            padding: 0;
            display: flex;
            align-items: center;
            justify-content: center;
            width: 32px;
            height: 32px;
            border-radius: 50%;
            transition: var(--transition);
        }
        
        .btn-close:hover {
            background: var(--gray-100);
            color: var(--gray-700);
        }
        /* 表单样式 */
        
        form {
            padding: 24px;
        }
        
        .form-row {
            display: flex;
            gap: 16px;
            margin-bottom: 16px;
        }
        
        .form-group {
            flex: 1;
            position: relative;
            margin-bottom: 16px;
        }
        
        label {
            display: block;
            margin-bottom: 8px;
            font-size: 14px;
            font-weight: 500;
            color: var(--gray-700);
        }
        
        .input-with-icon {
            position: relative;
        }
        
        .input-with-icon i {
            position: absolute;
            left: 12px;
            top: 50%;
            transform: translateY(-50%);
            color: var(--gray-400);
        }
        
        .input-with-icon input {
            padding: 10px 12px 10px 36px;
            width: 100%;
            border: 1px solid var(--gray-300);
            border-radius: 8px;
            font-size: 14px;
            transition: var(--transition);
        }
        
        input:focus {
            outline: none;
            border-color: var(--primary);
            box-shadow: 0 0 0 3px rgba(67, 97, 238, 0.15);
        }
        
        .error-message {
            display: none;
            color: var(--danger);
            font-size: 12px;
            margin-top: 4px;
        }
        
        .form-group.error .input-with-icon input {
            border-color: var(--danger);
        }
        
        .form-group.error .error-message {
            display: block;
        }
        
        .form-notice {
            display: flex;
            align-items: flex-start;
            gap: 10px;
            padding: 12px;
            background: var(--primary-light);
            border-radius: 8px;
            margin-bottom: 16px;
            color: var(--primary-dark);
        }
        
        .form-notice i {
            font-size: 18px;
            margin-top: 2px;
        }
        
        .form-footer {
            display: flex;
            justify-content: flex-end;
            gap: 12px;
            padding-top: 16px;
            border-top: 1px solid var(--gray-200);
        }
        /* 表单分隔线 */
        
        .form-divider {
            margin: 24px 0;
            border: none;
            border-top: 1px solid var(--gray-200);
        }
        /* 响应式布局 */
        
        @media (max-width: 1200px) {
            .stats-cards {
                grid-template-columns: repeat(2, 1fr);
            }
        }
        
        @media (max-width: 768px) {
            .sidebar {
                width: 70px;
                overflow: hidden;
            }
            .sidebar .logo h1,
            .sidebar .nav-links li a span,
            .sidebar-footer a span {
                display: none;
            }
            .main-content {
                margin-left: 70px;
            }
            .content-container {
                padding: 16px;
            }
            .stats-cards {
                grid-template-columns: 1fr;
            }
            .modal {
                width: 90%;
            }
            .form-row {
                flex-direction: column;
                gap: 0;
            }
        }
        
        .action-buttons {
            display: flex;
            gap: 8px;
            flex-wrap: nowrap;
            white-space: nowrap;
        }
        
        .btn-edit,
        .btn-end-date {
            padding: 6px 10px;
            min-width: 0;
            white-space: nowrap;
        }
        
        .btn-end-date {
            font-size: 12px;
        }
        
        .status-tag i {
            margin-right: 4px;
            font-size: 14px;
        }
        
        th i {
            margin-right: 6px;
            opacity: 0.8;
        }
        
        td i {
            margin-right: 4px;
            opacity: 0.7;
            font-size: 14px;
        }
        
        .amount {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif !important;
            font-size: 14px;
            font-weight: 500;
            color: var(--gray-800);
            text-align: right;
            white-space: nowrap;
        }
        
        th {
            padding: 16px 20px;
            white-space: nowrap;
        }
        
        td {
            padding: 14px 20px;
            vertical-align: middle;
        }
        
        td:first-child,
        th:first-child {
            padding-left: 24px;
        }
        
        td:last-child,
        th:last-child {
            padding-right: 24px;
        }
        
        .btn {
            display: inline-flex;
            align-items: center;
            gap: 6px;
        }
        
        .btn i {
            font-size: 16px;
        }
        
        .toggle-history {
            margin-left: 8px;
            font-size: 12px;
            padding: 3px 8px;
        }
        /* .toggle-history:before {
            content: "⌄";
            display: inline-block;
            margin-right: 2px;
            font-weight: bold;
        }
        */
        
        .version-badge {
            display: inline-flex;
            align-items: center;
            padding: 2px 8px;
            border-radius: 20px;
            background: var(--gray-200);
            color: var(--gray-700);
            font-size: 11px;
            font-weight: 600;
            margin-left: 8px;
            letter-spacing: 0.5px;
        }
    </style>
</head>

<body>
    <div class="app-container">
        <nav class="sidebar">
            <div class="logo">
                <img src="https://panhan.xin/assets/logo-mini-CMaEea_B.jpg" alt="Logo">
                <h1>财务管理系统</h1>
            </div>
            <ul class="nav-links">
                <li><a href="#" class="active"><i class="ri-pie-chart-line"></i> 阶梯分摊管理</a></li>
                <li><a href="#"><i class="ri-line-chart-line"></i> 财务报表</a></li>
                <li><a href="#"><i class="ri-exchange-funds-line"></i> 交易记录</a></li>
                <li><a href="#"><i class="ri-settings-4-line"></i> 系统设置</a></li>
            </ul>
            <div class="sidebar-footer">
                <a href="#"><i class="ri-question-line"></i> 帮助中心</a>
                <a href="#"><i class="ri-logout-box-line"></i> 退出系统</a>
            </div>
        </nav>

        <main class="main-content">
            <header class="top-header">
                <div class="breadcrumb">
                    <i class="ri-home-line"></i>
                    <span>财务管理</span>
                    <i class="ri-arrow-right-s-line"></i>
                    <span class="current">阶梯分摊管理</span>
                </div>
                <div class="user-panel">
                    <span id="current-date"></span>
                    <div class="user-info">
                        <span id="current-user">加载中...</span>
                        <img src="https://static-legacy.dingtalk.com/media/lALPD2BoQFPD3nDM68zr_235_235.png" alt="User" class="user-avatar">
                    </div>
                </div>
            </header>

            <div class="content-container">
                <div class="page-header">
                    <h2>公司阶梯分摊管理</h2>
                    <div class="page-actions">
                        <div class="search-box">
                            <i class="ri-search-line"></i>
                            <input type="text" placeholder="搜索记录..." id="search-input">
                        </div>
                        <button class="btn btn-primary" id="add-btn">
                            <i class="ri-add-line"></i> 新增记录
                        </button>
                    </div>
                </div>

                <div class="stats-cards">
                    <div class="stat-card">
                        <div class="stat-value">2</div>
                        <div class="stat-label">有效记录</div>
                        <i class="ri-file-list-3-line stat-icon"></i>
                    </div>
                    <div class="stat-card">
                        <div class="stat-value">50,000.01</div>
                        <div class="stat-label">阶梯起点</div>
                        <i class="ri-funds-line stat-icon"></i>
                    </div>
                    <div class="stat-card">
                        <div class="stat-value">200,000.00</div>
                        <div class="stat-label">阶梯终点</div>
                        <i class="ri-bar-chart-2-line stat-icon"></i>
                    </div>
                    <div class="stat-card">
                        <div class="stat-value">4,000.00</div>
                        <div class="stat-label">总分摊金额</div>
                        <i class="ri-money-cny-circle-line stat-icon"></i>
                    </div>
                </div>

                <div class="card">
                    <div class="table-controls">
                        <div class="table-info">
                            <span>总计: <b id="total-records">2</b> 条记录</span>
                        </div>
                        <div class="table-filters">
                            <select id="status-filter">
                                <option value="all">全部状态</option>
                                <option value="active">启用中</option>
                                <option value="history">历史记录</option>
                            </select>
                            <select id="date-filter">
                                <option value="all">全部日期</option>
                                <option value="this-month">本月</option>
                                <option value="last-month">上月</option>
                                <option value="this-year">今年</option>
                            </select>
                        </div>
                    </div>

                    <div class="table-responsive">
                        <table id="finance-table">
                            <thead>
                                <tr>
                                    <th><i class="ri-hashtag"></i> 记录编号</th>
                                    <th class="amount"><i class="ri-funds-line"></i> 起始利润</th>
                                    <th class="amount"><i class="ri-bar-chart-2-line"></i> 结束利润</th>
                                    <th class="amount"><i class="ri-money-cny-circle-line"></i> 分摊金额</th>
                                    <th><i class="ri-calendar-line"></i> 开始日期</th>
                                    <th><i class="ri-calendar-check-line"></i> 结束日期</th>
                                    <th><i class="ri-checkbox-circle-line"></i> 状态</th>
                                    <th><i class="ri-time-line"></i> 操作时间</th>
                                    <th><i class="ri-user-line"></i> 操作人</th>
                                    <th><i class="ri-tools-line"></i> 操作</th>
                                </tr>
                            </thead>
                            <tbody>
                                <!-- 数据行由JS渲染 -->
                            </tbody>
                        </table>
                    </div>

                    <div class="table-pagination">
                        <button class="btn btn-icon" id="prev-page"><i class="ri-arrow-left-s-line"></i></button>
                        <div class="page-numbers">
                            <span class="current-page">1</span> / <span class="total-pages">1</span>
                        </div>
                        <button class="btn btn-icon" id="next-page"><i class="ri-arrow-right-s-line"></i></button>
                    </div>
                </div>
            </div>
        </main>
    </div>

    <!-- 新增/编辑记录弹窗 -->
    <div class="modal-bg" id="record-modal-bg">
        <div class="modal">
            <div class="modal-header">
                <h3 class="modal-title" id="modal-title">新增记录</h3>
                <button class="btn-close" id="close-modal"><i class="ri-close-line"></i></button>
            </div>
            <form id="record-form" novalidate>
                <div class="form-row">
                    <div class="form-group">
                        <label for="start-profit">月起始利润 (¥)</label>
                        <div class="input-with-icon">
                            <i class="ri-money-cny-circle-line"></i>
                            <input type="number" id="start-profit" name="startProfit" min="0" step="0.01" required>
                        </div>
                        <div class="error-message">请输入有效的起始利润</div>
                    </div>
                    <div class="form-group">
                        <label for="end-profit">月结束利润 (¥)</label>
                        <div class="input-with-icon">
                            <i class="ri-money-cny-circle-line"></i>
                            <input type="number" id="end-profit" name="endProfit" min="0" step="0.01" required>
                        </div>
                        <div class="error-message">请输入有效的结束利润</div>
                    </div>
                </div>
                <div class="form-row">
                    <div class="form-group">
                        <label for="amount">公司阶梯分摊金额 (¥)</label>
                        <div class="input-with-icon">
                            <i class="ri-money-cny-circle-line"></i>
                            <input type="number" id="amount" name="amount" min="0" step="0.01" required>
                        </div>
                        <div class="error-message">请输入有效的分摊金额</div>
                    </div>
                </div>
                <div class="form-row">
                    <div class="form-group">
                        <label for="start-date">开始日期</label>
                        <div class="input-with-icon">
                            <i class="ri-calendar-line"></i>
                            <input type="date" id="start-date" name="startDate" required>
                        </div>
                        <div class="error-message">请选择开始日期</div>
                    </div>
                    <div class="form-group">
                        <label for="end-date">结束日期</label>
                        <div class="input-with-icon">
                            <i class="ri-calendar-line"></i>
                            <input type="date" id="end-date" name="endDate" required>
                        </div>
                        <div class="error-message">请选择结束日期</div>
                    </div>
                </div>
                <div class="form-footer">
                    <button type="button" class="btn btn-secondary" id="cancel-btn">取消</button>
                    <button type="submit" class="btn btn-primary" id="save-btn">保存</button>
                </div>
            </form>
        </div>
    </div>

    <!-- 修改结束日期弹窗 -->
    <div class="modal-bg" id="end-date-modal-bg">
        <div class="modal modal-sm">
            <div class="modal-header">
                <h3 class="modal-title">修改结束日期</h3>
                <button class="btn-close" id="close-end-date-modal"><i class="ri-close-line"></i></button>
            </div>
            <form id="end-date-form" novalidate>
                <div class="form-group">
                    <label for="new-end-date">新结束日期</label>
                    <div class="input-with-icon">
                        <i class="ri-calendar-line"></i>
                        <input type="date" id="new-end-date" name="newEndDate" required>
                    </div>
                    <div class="error-message">请选择有效的结束日期</div>
                </div>
                <div class="form-notice">
                    <i class="ri-information-line"></i>
                    <span>修改结束日期会创建历史版本，原记录将保持可追溯。</span>
                </div>
                <div class="form-footer">
                    <button type="button" class="btn btn-secondary" id="end-date-cancel-btn">取消</button>
                    <button type="submit" class="btn btn-primary" id="end-date-save-btn">确认修改</button>
                </div>
            </form>
        </div>
    </div>

    <script>
        // 模拟用户系统
        const users = [{
            id: 1,
            name: '王强',
            role: '系统管理员'
        }, {
            id: 2,
            name: '李婷',
            role: '财务主管'
        }, {
            id: 3,
            name: '陈浩',
            role: '会计'
        }, {
            id: 4,
            name: '赵芳',
            role: '财务专员'
        }, {
            id: 5,
            name: '张明',
            role: '财务经理'
        }];

        // 模拟当前登录用户（随机选择一个用户）
        const currentUser = users[Math.floor(Math.random() * users.length)];

        // 本地存储的键名
        const STORAGE_KEY = 'fentan_records_data';

        // 初始化记录数据（从本地存储加载或使用默认数据）
        let records = loadRecordsFromLocalStorage() || [{
            id: 'PH001',
            version: '00',
            parentId: null,
            startProfit: 0,
            endProfit: 50000,
            amount: 1000,
            startDate: '2025-01-01',
            endDate: '2025-12-31',
            status: 'active',
            operateTime: '2025-01-01 09:30:25',
            operator: '陈浩',
            children: []
        }, {
            id: 'PH002',
            version: '00',
            parentId: null,
            startProfit: 50000.01,
            endProfit: 200000,
            amount: 3000,
            startDate: '2025-01-01',
            endDate: '2025-12-31',
            status: 'active',
            operateTime: '2025-01-01 14:15:40',
            operator: '李婷',
            children: []
        }];

        // DOM 元素
        const addBtn = document.getElementById('add-btn');
        const recordForm = document.getElementById('record-form');
        const recordModalBg = document.getElementById('record-modal-bg');
        const endDateModalBg = document.getElementById('end-date-modal-bg');
        const cancelBtn = document.getElementById('cancel-btn');
        const closeModalBtn = document.getElementById('close-modal');
        const closeEndDateModalBtn = document.getElementById('close-end-date-modal');
        const endDateCancelBtn = document.getElementById('end-date-cancel-btn');
        const endDateSaveBtn = document.getElementById('end-date-save-btn');
        const modalTitle = document.getElementById('modal-title');
        const financeTable = document.getElementById('finance-table').getElementsByTagName('tbody')[0];
        const currentUserDisplay = document.getElementById('current-user');
        const currentDateDisplay = document.getElementById('current-date');
        const searchInput = document.getElementById('search-input');
        const statusFilter = document.getElementById('status-filter');
        const dateFilter = document.getElementById('date-filter');
        const totalRecordsEl = document.getElementById('total-records');

        // 表单字段
        const startProfitInput = document.getElementById('start-profit');
        const endProfitInput = document.getElementById('end-profit');
        const amountInput = document.getElementById('amount');
        const startDateInput = document.getElementById('start-date');
        const endDateInput = document.getElementById('end-date');
        const newEndDateInput = document.getElementById('new-end-date');

        // 全局变量存储当前操作的记录
        let currentRecord = null;
        let currentEndDateRecord = null;
        let filteredRecords = [];

        // 初始化页面
        function initPage() {
            // 显示当前用户
            currentUserDisplay.textContent = `${currentUser.name} (${currentUser.role})`;

            // 显示当前日期
            updateCurrentDate();

            // 初始化过滤记录
            updateFilteredRecords();

            // 渲染记录表格
            renderTable();

            // 绑定事件
            addBtn.addEventListener('click', openAddModal);
            cancelBtn.addEventListener('click', closeModal);
            closeModalBtn.addEventListener('click', closeModal);
            closeEndDateModalBtn.addEventListener('click', closeEndDateModal);
            endDateCancelBtn.addEventListener('click', closeEndDateModal);
            recordForm.addEventListener('submit', saveRecord);
            document.getElementById('end-date-form').addEventListener('submit', saveEndDateChange);

            // 搜索和过滤事件
            searchInput.addEventListener('input', handleSearch);
            statusFilter.addEventListener('change', handleFiltersChange);
            dateFilter.addEventListener('change', handleFiltersChange);
        }

        // 更新当前日期显示
        function updateCurrentDate() {
            const now = new Date();
            const options = {
                weekday: 'long',
                year: 'numeric',
                month: 'long',
                day: 'numeric',
                hour: '2-digit',
                minute: '2-digit'
            };
            currentDateDisplay.textContent = now.toLocaleDateString('zh-CN', options);
        }

        // 处理搜索
        function handleSearch() {
            updateFilteredRecords();
            renderTable();
        }

        // 处理过滤器变化
        function handleFiltersChange() {
            updateFilteredRecords();
            renderTable();
        }

        // 更新过滤后的记录
        function updateFilteredRecords() {
            const searchTerm = searchInput.value.toLowerCase();
            const statusValue = statusFilter.value;
            const dateValue = dateFilter.value;

            // 首先应用搜索过滤
            let filtered = records.filter(record => {
                return record.id.toLowerCase().includes(searchTerm) ||
                    record.operator.toLowerCase().includes(searchTerm) ||
                    record.amount.toString().includes(searchTerm);
            });

            // 然后应用状态过滤
            if (statusValue !== 'all') {
                filtered = filtered.filter(record => record.status === statusValue);
            }

            // 最后应用日期过滤
            if (dateValue !== 'all') {
                const now = new Date();
                const thisYear = now.getFullYear();
                const thisMonth = now.getMonth();

                filtered = filtered.filter(record => {
                    const recordDate = new Date(record.operateTime);

                    if (dateValue === 'this-year') {
                        return recordDate.getFullYear() === thisYear;
                    } else if (dateValue === 'this-month') {
                        return recordDate.getFullYear() === thisYear &&
                            recordDate.getMonth() === thisMonth;
                    } else if (dateValue === 'last-month') {
                        const lastMonth = thisMonth === 0 ? 11 : thisMonth - 1;
                        const lastMonthYear = thisMonth === 0 ? thisYear - 1 : thisYear;
                        return recordDate.getFullYear() === lastMonthYear &&
                            recordDate.getMonth() === lastMonth;
                    }
                    return true;
                });
            }

            filteredRecords = filtered;
            totalRecordsEl.textContent = filteredRecords.length;
        }

        // 渲染数据表格
        function renderTable() {
            // 清空表格
            financeTable.innerHTML = '';

            // 对记录进行排序：活动的记录优先，然后按ID排序
            const sortedRecords = [...filteredRecords].sort((a, b) => {
                if (a.status === b.status) {
                    return a.id.localeCompare(b.id);
                }
                return a.status === 'active' ? -1 : 1;
            });

            // 如果没有记录，显示空状态
            if (sortedRecords.length === 0) {
                const emptyRow = document.createElement('tr');
                emptyRow.innerHTML = `
            <td colspan="10" class="empty-state">
                <div>
                    <i class="ri-inbox-line"></i>
                    <p>没有找到符合条件的记录</p>
                </div>
            </td>
        `;
                financeTable.appendChild(emptyRow);
                return;
            }

            // 填充数据行
            sortedRecords.forEach(record => {
                        const row = document.createElement('tr');
                        row.innerHTML = `
            <td>
                ${record.id}
                ${record.children.length > 0 ? 
                    `<button class="toggle-history" data-id="${record.id}">历史 (${record.children.length})</button>` : ''}
            </td>
            <td class="amount">¥ ${formatNumber(record.startProfit)}</td>
            <td class="amount">¥ ${formatNumber(record.endProfit)}</td>
            <td class="amount">¥ ${formatNumber(record.amount)}</td>
            <td><i class="ri-calendar-line"></i>${formatSimpleDate(record.startDate)}</td>
            <td><i class="ri-calendar-check-line"></i>${formatSimpleDate(record.endDate)}</td>
            <td>
                <span class="status-tag ${record.status === 'active' ? 'status-active' : 'status-history'}">
                    <i class="ri-${record.status === 'active' ? 'check-line' : 'history-line'}"></i> ${record.status === 'active' ? '启用中' : '历史'}
                </span>
            </td>
            <td><i class="ri-time-line"></i>${formatDateTime(record.operateTime)}</td>
            <td><i class="ri-user-line"></i> ${record.operator}</td>
            <td>
                <div class="action-buttons">
                    <button class="btn btn-edit" data-id="${record.id}" ${record.status !== 'active' ? 'disabled' : ''}><i class="ri-edit-line"></i> 编辑</button>
                    <button class="btn btn-end-date" data-id="${record.id}" ${record.status !== 'active' ? 'disabled' : ''}><i class="ri-calendar-event-line"></i>改日期</button>
                </div>
            </td>
        `;
        financeTable.appendChild(row);
        
        // 如果有子记录，添加隐藏的历史行
        if (record.children.length > 0) {
            record.children.forEach(childRecord => {
                const historyRow = document.createElement('tr');
                historyRow.className = 'history-row';
                historyRow.style.display = 'none';
                historyRow.dataset.parentId = record.id;
                
                historyRow.innerHTML = `
                    <td>${childRecord.id}<span class="version-badge">v${childRecord.version}</span></td>
                    <td class="amount">¥ ${formatNumber(childRecord.startProfit)}</td>
                    <td class="amount">¥ ${formatNumber(childRecord.endProfit)}</td>
                    <td class="amount">¥ ${formatNumber(childRecord.amount)}</td>
                    <td><i class="ri-calendar-line"></i>${formatSimpleDate(childRecord.startDate)}</td>
                    <td><i class="ri-calendar-check-line"></i>${formatSimpleDate(childRecord.endDate)}</td>
                    <td>
                        <span class="status-tag status-history"><i class="ri-history-line"></i> 历史</span>
                    </td>
                    <td><i class="ri-time-line"></i>${formatDateTime(childRecord.operateTime)}</td>
                    <td><i class="ri-user-line"></i> ${childRecord.operator}</td>
                    <td></td>
                `;
                financeTable.appendChild(historyRow);
            });
        }
    });
    
    // 绑定历史记录展开/收起按钮
    document.querySelectorAll('.toggle-history').forEach(btn => {
        btn.addEventListener('click', toggleHistory);
    });
    
    // 绑定编辑和修改结束日期按钮
    document.querySelectorAll('.btn-edit').forEach(btn => {
        if (!btn.disabled) {
            btn.addEventListener('click', () => editRecord(btn.dataset.id));
        }
    });
    
    document.querySelectorAll('.btn-end-date').forEach(btn => {
        if (!btn.disabled) {
            btn.addEventListener('click', () => openEndDateModal(btn.dataset.id));
        }
    });
}

// 格式化数字显示
function formatNumber(num) {
    return parseFloat(num).toLocaleString('zh-CN', { minimumFractionDigits: 2, maximumFractionDigits: 2 });
}

// 格式化日期显示
function formatDate(dateStr) {
    const date = new Date(dateStr);
    const year = date.getFullYear();
    const month = date.getMonth() + 1;
    const day = date.getDate();
    return `${year}年${month < 10 ? '0' + month : month}月${day < 10 ? '0' + day : day}日`;
}

// 获取当前日期时间字符串（精确到秒）
function getCurrentDateTime() {
    const now = new Date();
    const year = now.getFullYear();
    const month = String(now.getMonth() + 1).padStart(2, '0');
    const day = String(now.getDate()).padStart(2, '0');
    const hours = String(now.getHours()).padStart(2, '0');
    const minutes = String(now.getMinutes()).padStart(2, '0');
    const seconds = String(now.getSeconds()).padStart(2, '0');
    
    return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
}

// 生成新的记录ID
function generateId() {
    // 获取最大序号
    let maxSeq = 0;
    records.forEach(record => {
        if (record.id.startsWith('PH')) {
            const seq = parseInt(record.id.substring(2), 10);
        if (seq > maxSeq) {
            maxSeq = seq;
            }
        }
    });
    
    // 生成新序号
    const newSeq = maxSeq + 1;
    return `PH${String(newSeq).padStart(3, '0')}`;
}

// 生成新的子记录版本号
function generateVersionNumber(parentId) {
    // 查找该父ID下的最大版本号
    let maxVersion = 0;
    records.forEach(record => {
        if (record.parentId === parentId) {
            const version = parseInt(record.version, 10);
            if (version > maxVersion) {
                maxVersion = version;
            }
        }
    });
    
    // 查找子记录中的最大版本号
    records.forEach(record => {
        record.children.forEach(child => {
            if (child.parentId === parentId) {
                const version = parseInt(child.version, 10);
                if (version > maxVersion) {
                    maxVersion = version;
                }
            }
        });
    });
    
    // 生成新版本号
    return String(maxVersion + 1).padStart(2, '0');
}

// 打开添加模态框
function openAddModal() {
    modalTitle.textContent = '新增记录';
    currentRecord = null;
    
    // 重置表单
    recordForm.reset();
    
    // 设置默认的开始日期为今天
    const today = new Date().toISOString().split('T')[0];
    startDateInput.value = today;
    
    // 显示模态框
    recordModalBg.style.display = 'flex';
}

// 打开编辑模态框
function editRecord(recordId) {
    const record = records.find(r => r.id === recordId);
    if (!record) return;
    
    modalTitle.textContent = '编辑记录';
    currentRecord = record;
    
    // 填充表单数据
    startProfitInput.value = record.startProfit;
    endProfitInput.value = record.endProfit;
    amountInput.value = record.amount;
    startDateInput.value = record.startDate;
    endDateInput.value = record.endDate;
    
    // 显示模态框
    recordModalBg.style.display = 'flex';
}

// 关闭模态框
function closeModal() {
    recordModalBg.style.display = 'none';
    
    // 清除表单验证状态
    document.querySelectorAll('.form-group').forEach(group => {
        group.classList.remove('error');
    });
}

// 打开修改结束日期模态框
function openEndDateModal(recordId) {
    currentEndDateRecord = records.find(r => r.id === recordId);
    if (currentEndDateRecord) {
        // 设置当前结束日期
        newEndDateInput.value = currentEndDateRecord.endDate;
        newEndDateInput.min = currentEndDateRecord.startDate; // 确保不能早于开始日期
        
        // 显示模态框
        endDateModalBg.style.display = 'flex';
    }
}

// 关闭修改结束日期模态框
function closeEndDateModal() {
    endDateModalBg.style.display = 'none';
    currentEndDateRecord = null;
}

// 保存结束日期变更
function saveEndDateChange(e) {
    e.preventDefault();
    
    if (!currentEndDateRecord) return;
    if (!newEndDateInput.value || newEndDateInput.value < currentEndDateRecord.startDate) {
        newEndDateInput.parentElement.classList.add('error');
        return;
    }
    
    // 如果日期没有变化，则不做任何处理
    if (newEndDateInput.value === currentEndDateRecord.endDate) {
        closeEndDateModal();
        return;
    }
    
    // 创建历史记录
    const newVersion = generateVersionNumber(currentEndDateRecord.id);
    const childRecord = {
        id: currentEndDateRecord.id,
        version: newVersion,
        parentId: currentEndDateRecord.id,
        startProfit: currentEndDateRecord.startProfit,
        endProfit: currentEndDateRecord.endProfit,
        amount: currentEndDateRecord.amount,
        startDate: currentEndDateRecord.startDate,
        endDate: currentEndDateRecord.endDate,
        status: 'history',
        operateTime: currentEndDateRecord.operateTime,
        operator: currentEndDateRecord.operator,
        children: []
    };
    
    // 更新当前记录的结束日期
    currentEndDateRecord.endDate = newEndDateInput.value;
    currentEndDateRecord.operateTime = getCurrentDateTime();
    currentEndDateRecord.operator = currentUser.name;
    
    // 添加历史版本记录
    currentEndDateRecord.children.push(childRecord);
    
    // 保存到本地存储
    saveRecordsToLocalStorage();
    
    // 更新过滤记录并重新渲染表格
    updateFilteredRecords();
    renderTable();
    
    // 关闭模态框
    closeEndDateModal();
}

// 切换历史记录的显示/隐藏
function toggleHistory(e) {
    const recordId = e.target.dataset.id;
    const historyRows = document.querySelectorAll(`tr[data-parent-id="${recordId}"]`);
    
    historyRows.forEach(row => {
        row.style.display = row.style.display === 'none' ? 'table-row' : 'none';
    });
    
    // 切换按钮文本
    if (historyRows[0] && historyRows[0].style.display !== 'none') {
        e.target.textContent = `收起 (${historyRows.length})`;
    } else {
        e.target.textContent = `历史 (${historyRows.length})`;
    }
}

// 保存记录
function saveRecord(e) {
    e.preventDefault();
    
    // 表单验证
    let isValid = true;
    
    // 检查起始利润
    if (!startProfitInput.value || isNaN(parseFloat(startProfitInput.value)) || parseFloat(startProfitInput.value) < 0) {
        startProfitInput.parentElement.parentElement.classList.add('error');
        isValid = false;
    } else {
        startProfitInput.parentElement.parentElement.classList.remove('error');
    }
    
    // 检查结束利润
    if (!endProfitInput.value || isNaN(parseFloat(endProfitInput.value)) || parseFloat(endProfitInput.value) <= parseFloat(startProfitInput.value)) {
        endProfitInput.parentElement.parentElement.classList.add('error');
        isValid = false;
    } else {
        endProfitInput.parentElement.parentElement.classList.remove('error');
    }
    
    // 检查分摊金额
    if (!amountInput.value || isNaN(parseFloat(amountInput.value)) || parseFloat(amountInput.value) <= 0) {
        amountInput.parentElement.parentElement.classList.add('error');
        isValid = false;
    } else {
        amountInput.parentElement.parentElement.classList.remove('error');
    }
    
    // 检查开始日期
    if (!startDateInput.value) {
        startDateInput.parentElement.parentElement.classList.add('error');
        isValid = false;
    } else {
        startDateInput.parentElement.parentElement.classList.remove('error');
    }
    
    // 检查结束日期
    if (!endDateInput.value || endDateInput.value < startDateInput.value) {
        endDateInput.parentElement.parentElement.classList.add('error');
        isValid = false;
    } else {
        endDateInput.parentElement.parentElement.classList.remove('error');
    }
    
    if (!isValid) return;
    
    // 获取当前操作时间
    const operateTime = getCurrentDateTime();
    
    // 处理保存记录
    if (currentRecord) {
        // 编辑现有记录
        
        // 首先检查是否有实质性修改
        const hasRealChanges = 
            parseFloat(currentRecord.startProfit) !== parseFloat(startProfitInput.value) ||
            parseFloat(currentRecord.endProfit) !== parseFloat(endProfitInput.value) ||
            parseFloat(currentRecord.amount) !== parseFloat(amountInput.value) ||
            currentRecord.startDate !== startDateInput.value ||
            currentRecord.endDate !== endDateInput.value;
        
        if (hasRealChanges) {
            // 创建子记录（历史版本）
            const newVersion = generateVersionNumber(currentRecord.id);
            const childRecord = {
                id: currentRecord.id,
                version: newVersion,
                parentId: currentRecord.id,
                startProfit: currentRecord.startProfit,
                endProfit: currentRecord.endProfit,
                amount: currentRecord.amount,
                startDate: currentRecord.startDate,
                endDate: currentRecord.endDate,
                status: 'history',
                operateTime: currentRecord.operateTime,
                operator: currentRecord.operator,
                children: []
            };
            
            // 更新当前记录
            currentRecord.startProfit = parseFloat(startProfitInput.value);
            currentRecord.endProfit = parseFloat(endProfitInput.value);
            currentRecord.amount = parseFloat(amountInput.value);
            currentRecord.startDate = startDateInput.value;
            currentRecord.endDate = endDateInput.value;
            currentRecord.operateTime = operateTime;
            currentRecord.operator = currentUser.name;
            
            // 添加到历史记录
            currentRecord.children.push(childRecord);
        }
    } else {
        // 创建新记录
        const newRecord = {
            id: generateId(),
            version: '00',
            parentId: null,
            startProfit: parseFloat(startProfitInput.value),
            endProfit: parseFloat(endProfitInput.value),
            amount: parseFloat(amountInput.value),
            startDate: startDateInput.value,
            endDate: endDateInput.value,
            status: 'active',
            operateTime: operateTime,
            operator: currentUser.name,
            children: []
        };
        
        // 添加到记录数组
        records.push(newRecord);
    }
    
    // 保存到本地存储
    saveRecordsToLocalStorage();
    
    // 更新过滤记录并重新渲染表格
    updateFilteredRecords();
    renderTable();
    
    // 关闭模态框
    closeModal();
}

// 验证重叠区间
function validateOverlap(startProfit, endProfit, recordId) {
    return records.some(record => {
        // 忽略当前记录
        if (record.id === recordId) return false;
        // 只检查活动记录
        if (record.status !== 'active') return false;
        
        // 检查区间重叠
        return (startProfit >= record.startProfit && startProfit <= record.endProfit) ||
               (endProfit >= record.startProfit && endProfit <= record.endProfit) ||
               (startProfit <= record.startProfit && endProfit >= record.endProfit);
    });
}

// 从本地存储加载记录数据
function loadRecordsFromLocalStorage() {
    try {
        const storedData = localStorage.getItem(STORAGE_KEY);
        if (storedData) {
            return JSON.parse(storedData);
        }
        return null;
    } catch (error) {
        console.error('从本地存储加载数据失败:', error);
        return null;
    }
}

// 保存记录数据到本地存储
function saveRecordsToLocalStorage() {
    try {
        localStorage.setItem(STORAGE_KEY, JSON.stringify(records));
        console.log('数据已保存到本地存储');
    } catch (error) {
        console.error('保存数据到本地存储失败:', error);
        alert('保存数据失败，可能是存储空间不足');
    }
}

// 导出/导入功能
function exportData() {
    const dataString = JSON.stringify(records, null, 2);
    const blob = new Blob([dataString], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    
    const a = document.createElement('a');
    a.href = url;
    a.download = `阶梯分摊记录_${new Date().toISOString().slice(0, 10)}.json`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
}

function importData(file) {
    const reader = new FileReader();
    reader.onload = function(event) {
        try {
            const importedRecords = JSON.parse(event.target.result);
            
            if (Array.isArray(importedRecords)) {
                // 验证导入数据的基本结构
                const isValid = importedRecords.every(record => 
                    typeof record === 'object' && 
                    record.id && 
                    typeof record.startProfit === 'number' && 
                    typeof record.endProfit === 'number'
                );
                
                if (isValid) {
                    records = importedRecords;
                    // 保存导入的数据到本地存储
                    saveRecordsToLocalStorage();
                    updateFilteredRecords();
                    renderTable();
                    alert('数据导入成功');
                } else {
                    alert('导入数据格式无效');
                }
            } else {
                alert('导入数据无效：数据不是数组格式');
            }
        } catch (error) {
            alert('导入数据失败：' + error.message);
        }
    };
    reader.readAsText(file);
}

// 添加导入/导出按钮和功能
function setupImportExport() {
    // 创建导入按钮和文件选择器
    const importBtn = document.createElement('button');
    importBtn.className = 'btn btn-secondary';
    importBtn.innerHTML = '<i class="ri-download-line"></i> 导入';
    importBtn.style.marginRight = '8px';

    const fileInput = document.createElement('input');
    fileInput.type = 'file';
    fileInput.accept = '.json';
    fileInput.style.display = 'none';
    
    // 创建导出按钮
    const exportBtn = document.createElement('button');
    exportBtn.className = 'btn btn-secondary';
    exportBtn.innerHTML = '<i class="ri-upload-line"></i> 导出';
    
    // 添加到页面
    const actionDiv = document.querySelector('.page-actions');
    actionDiv.prepend(exportBtn);
    actionDiv.prepend(importBtn);
    document.body.appendChild(fileInput);
    
    // 绑定事件
    importBtn.addEventListener('click', () => fileInput.click());
    fileInput.addEventListener('change', (e) => {
        if (e.target.files.length > 0) {
            importData(e.target.files[0]);
        }
    });
    exportBtn.addEventListener('click', exportData);
}

// 添加清除本地数据的功能
function setupClearData() {
    // 创建清除数据按钮
    const clearDataBtn = document.createElement('button');
    clearDataBtn.className = 'btn btn-secondary';
    clearDataBtn.innerHTML = '<i class="ri-delete-bin-line"></i> 清除数据';
    clearDataBtn.style.marginLeft = 'auto';
    
    // 添加到表格控制区
    const tableControls = document.querySelector('.table-controls');
    tableControls.appendChild(clearDataBtn);
    
    // 绑定事件
    clearDataBtn.addEventListener('click', () => {
        if (confirm('确定要清除所有本地数据吗？此操作不可恢复。')) {
            localStorage.removeItem(STORAGE_KEY);
            window.location.reload(); // 刷新页面
        }
    });
}

// 在初始化页面时设置导入/导出功能
document.addEventListener('DOMContentLoaded', () => {
    initPage();
    setupImportExport();
    setupClearData();
    addStyles();
});

// 添加样式规则
function addStyles() {
    const styleElement = document.createElement('style');
    styleElement.textContent = `
        #finance-table {
            margin-bottom: 0;
        }
        
        .search-box input {
            width: 180px;
        }
        
        .empty-state {
            text-align: center;
            padding: 40px 0;
            color: var(--gray-500);
        }
        
        .empty-state i {
            font-size: 48px;
            margin-bottom: 16px;
            color: var(--gray-300);
        }
        
        .empty-state p {
            font-size: 16px;
            margin: 8px 0 0;
        }
        
        /* 设置最小列宽，确保内容完整显示 */
        #finance-table th, #finance-table td {
            min-width: auto;
            padding: 16px 20px;
        }
        
        #finance-table th:nth-child(1) { min-width: 120px; }  /* 记录编号 */
        #finance-table th:nth-child(2) { min-width: 120px; }  /* 起始利润 */
        #finance-table th:nth-child(3) { min-width: 120px; }  /* 结束利润 */
        #finance-table th:nth-child(4) { min-width: 120px; }  /* 分摊金额 */
        #finance-table th:nth-child(5) { min-width: 130px; }  /* 开始日期 */
        #finance-table th:nth-child(6) { min-width: 130px; }  /* 结束日期 */
        #finance-table th:nth-child(7) { min-width: 120px; }  /* 状态 */
        #finance-table th:nth-child(8) { min-width: 180px; }  /* 操作时间 */
        #finance-table th:nth-child(9) { min-width: 100px; }  /* 操作人 */
        #finance-table th:nth-child(10) { min-width: 180px; } /* 操作 */
        
        /* 让表格行一定在一行显示 */
        #finance-table tr {
            white-space: nowrap;
        }
        
        /* 调整状态标签样式 */
        .status-tag {
            display: inline-flex;
            white-space: nowrap;
            padding: 4px 10px;
            min-width: 80px;
            justify-content: center;
        }
        
        /* 调整操作按钮区域样式 */
        .action-buttons {
            white-space: nowrap;
            display: flex;
            gap: 6px;
        }
        
        /* 调整按钮样式以在一行显示 */
        .btn-edit, .btn-end-date {
            padding: 4px 8px;
            font-size: 12px;
            white-space: nowrap;
        }
        
        /* 改进表格响应式布局 */
        .table-responsive {
            overflow-x: auto;
            scrollbar-width: thin;
        }
        
        /* 让表格内容始终在顶部对齐 */
        td {
            vertical-align: middle;
        }
        
        /* 调整操作时间列的宽度 */
        .time-column {
            min-width: 180px;
        }
        
        /* 滚动条样式美化 */
        .table-responsive::-webkit-scrollbar {
            height: 8px;
        }
        
        .table-responsive::-webkit-scrollbar-track {
            background: var(--gray-100);
            border-radius: 4px;
        }
        
        .table-responsive::-webkit-scrollbar-thumb {
            background-color: var(--gray-300);
            border-radius: 4px;
        }
        
        .table-responsive::-webkit-scrollbar-thumb:hover {
            background-color: var(--gray-400);
        }
    `;
    document.head.appendChild(styleElement);
}

// 添加更简洁的日期格式化函数
function formatSimpleDate(dateStr) {
    const date = new Date(dateStr);
    const year = date.getFullYear();
    const month = date.getMonth() + 1;
    const day = date.getDate();
    return `${year}/${month < 10 ? '0' + month : month}/${day < 10 ? '0' + day : day}`;
}

// 添加简洁的日期时间格式化函数
function formatDateTime(dateTimeStr) {
    // 检查是否包含时间部分
    if (dateTimeStr.includes(':')) {
        const [datePart, timePart] = dateTimeStr.split(' ');
        const [year, month, day] = datePart.split('-');
        return `${year}/${month}/${day} ${timePart}`;
    } else {
        return formatSimpleDate(dateTimeStr);
    }
}

// 模拟服务器响应延迟（仅用于演示）
function simulateServerDelay(callback) {
    setTimeout(callback, 300);
}

// 更新统计卡片数据
function updateStatsCards() {
    // 仅统计活跃记录
    const activeRecords = records.filter(r => r.status === 'active');
    
    // 统计有效记录数
    document.querySelector('.stat-card:nth-child(1) .stat-value').textContent = activeRecords.length;
    
    // 统计阶梯起点（最小起始利润）
    let minStartProfit = Infinity;
    activeRecords.forEach(record => {
        if (record.startProfit < minStartProfit) {
            minStartProfit = record.startProfit;
        }
    });
    document.querySelector('.stat-card:nth-child(2) .stat-value').textContent = 
        minStartProfit === Infinity ? '0.00' : formatNumber(minStartProfit);
    
    // 统计阶梯终点（最大结束利润）
    let maxEndProfit = 0;
    activeRecords.forEach(record => {
        if (record.endProfit > maxEndProfit) {
            maxEndProfit = record.endProfit;
        }
    });
    document.querySelector('.stat-card:nth-child(3) .stat-value').textContent = formatNumber(maxEndProfit);
    
    // 统计总分摊金额
    let totalAmount = 0;
    activeRecords.forEach(record => {
        totalAmount += record.amount;
    });
    document.querySelector('.stat-card:nth-child(4) .stat-value').textContent = formatNumber(totalAmount);
}

// 分页功能
let currentPage = 1;
const recordsPerPage = 10;

function updatePagination() {
    const totalPages = Math.ceil(filteredRecords.length / recordsPerPage);
    document.querySelector('.current-page').textContent = currentPage;
    document.querySelector('.total-pages').textContent = totalPages;
    
    // 更新分页按钮状态
    document.getElementById('prev-page').disabled = currentPage === 1;
    document.getElementById('next-page').disabled = currentPage === totalPages;
}

document.getElementById('prev-page').addEventListener('click', () => {
    if (currentPage > 1) {
        currentPage--;
        renderTable();
    }
});

document.getElementById('next-page').addEventListener('click', () => {
    const totalPages = Math.ceil(filteredRecords.length / recordsPerPage);
    if (currentPage < totalPages) {
        currentPage++;
        renderTable();
    }
});
    </script>
</body>

</html>