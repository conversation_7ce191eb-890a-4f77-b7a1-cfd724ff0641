<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>广告账户数据</title>
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="icon" href="https://panhan.xin/assets/favicon-DfI5CYqk.ico">
    <!-- Litepicker CSS -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/litepicker/dist/css/litepicker.css" />
    <!-- 字体图标 -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css">
    <style>
         :root {
            /* 主要颜色 */
            --primary-color: #2468f2;
            --primary-light: #edf3fe;
            --secondary-color: #6c757d;
            /* 文本颜色 */
            --text-dark: #333333;
            --text-light: #666666;
            --text-muted: #999999;
            /* 状态颜色 */
            --success: #52c41a;
            --success-bg: #f6ffed;
            --warning: #faad14;
            --warning-bg: #fffbe6;
            --danger: #ff4d4f;
            --danger-bg: #fff2f0;
            --info: #2468f2;
            --info-bg: #edf3fe;
            /* 背景色 */
            --bg-light: #f5f7fa;
            --bg-lighter: #fafafa;
            --bg-white: #ffffff;
            /* 边框 */
            --border-color: #e8e8e8;
            --border-light: #f0f0f0;
            /* 阴影 */
            --shadow-sm: 0 1px 2px rgba(0, 0, 0, 0.05);
            --shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
            --shadow-lg: 0 4px 12px rgba(0, 0, 0, 0.12);
            /* 圆角 */
            --border-radius: 2px;
            --border-radius-lg: 4px;
            /* 过渡 */
            --transition: all 0.2s ease-in-out;
        }
        /* 状态筛选样式 */
        
        .status-tabs {
            height: 32px;
        }
        
        .status-tab {
            flex: 1;
            min-width: 80px;
            border-right: 1px solid var(--border-color);
        }
        
        .status-tab:last-child {
            border-right: none;
        }
        
        .status-tab-btn {
            width: 100%;
            height: 100%;
            border: none;
            background: var(--bg-white);
            color: var(--text-light);
            padding: 0 12px;
            font-size: 14px;
            transition: var(--transition);
            outline: none;
            cursor: pointer;
        }
        
        .status-tab-btn:hover {
            background-color: var(--bg-lighter);
        }
        
        .status-tab-btn.active {
            background-color: var(--primary-color);
            color: white;
            font-weight: 500;
        }
        /* 整体页面样式 */
        
        body {
            background-color: var(--bg-light);
            color: var(--text-dark);
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
            min-height: 100vh;
            padding-bottom: 1rem;
            font-size: 14px;
            line-height: 1.5;
        }
        /* 页面容器 */
        
        .main-container {
            max-width: 1280px;
            margin: 0 auto;
            padding: 1rem;
        }
        /* 页面标题 */
        
        .page-title {
            color: var(--text-dark);
            font-weight: 500;
            margin-bottom: 1rem;
            font-size: 16px;
            display: flex;
            align-items: center;
        }
        
        .page-title i {
            margin-right: 0.5rem;
            color: var(--primary-color);
        }
        /* 自定义状态标签样式 */
        
        .status-badge {
            padding: 0 8px;
            height: 22px;
            line-height: 22px;
            font-size: 12px;
            font-weight: normal;
            border-radius: 2px;
            display: inline-flex;
            align-items: center;
        }
        
        .status-badge i {
            margin-right: 4px;
            font-size: 12px;
        }
        
        .status-active {
            background-color: var(--success-bg);
            color: var(--success);
        }
        
        .status-inactive {
            background-color: #f2f2f2;
            color: var(--text-light);
        }
        
        .status-cancelled {
            background-color: var(--danger-bg);
            color: var(--danger);
        }
        /* 表格相关样式 */
        
        .table {
            font-size: 14px;
            width: 100%;
            border-collapse: separate;
            border-spacing: 0;
            border: none;
            box-shadow: none;
            margin-bottom: 0;
        }
        
        .form-select-sm,
        .form-control-sm {
            font-size: 14px;
            border-radius: var(--border-radius);
            padding: 4px 12px;
            height: 32px;
            border-color: var(--border-color);
        }
        
        .form-select-sm:focus,
        .form-control-sm:focus {
            border-color: var(--primary-color);
            box-shadow: 0 0 0 2px rgba(36, 104, 242, 0.1);
        }
        /* 卡片样式优化 */
        
        .card {
            border: none;
            border-radius: var(--border-radius);
            box-shadow: var(--shadow-sm);
            background-color: var(--bg-white);
        }
        
        .card-body {
            padding: 16px;
        }
        /* 表格标题 */
        
        .table-caption {
            font-weight: 500;
            color: var(--text-dark);
            font-size: 14px;
        }
        /* 表格容器 */
        
        .table-responsive {
            overflow-x: auto !important;
            background-color: var(--bg-white);
            padding: 0;
            border-top: 1px solid var(--border-light);
        }
        /* 表格单元格 */
        
        .table th,
        .table td {
            white-space: nowrap;
            vertical-align: middle;
            padding: 12px 16px;
            border-bottom: 1px solid var(--border-light);
            border-top: none;
            border-right: 1px solid var(--border-light);
        }
        
        .table th:last-child,
        .table td:last-child {
            border-right: none;
        }
        /* 表头样式 */
        
        .table thead th {
            background-color: var(--bg-lighter);
            color: var(--text-light);
            font-weight: 500;
            border-bottom: 1px solid var(--border-color);
            font-size: 13px;
            padding-top: 14px;
            padding-bottom: 14px;
        }
        /* 表格行样式 */
        
        .table tbody tr:last-child td {
            border-bottom: none;
        }
        /* 隔行变色 - 增加选择器特异性 */
        
        .table-responsive .table tbody tr.row-even,
        .table tbody tr:nth-child(even) {
            background-color: rgba(0, 0, 0, 0.02);
        }
        /* 悬停效果 - 增加选择器特异性并确保高于隔行变色 */
        
        .table-responsive .table tbody tr:hover,
        .table tbody tr:hover {
            background-color: var(--primary-light) !important;
            /* 使用important确保悬停效果优先 */
            transition: background-color 0.15s ease-in-out;
        }
        /* 数字列右对齐 */
        
        .table td.text-right {
            text-align: right;
        }
        /* 数值格式化 */
        
        .amount {
            font-family: DINPro, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            color: var(--text-dark);
            font-weight: 500;
        }
        /* 分页容器 */
        
        .pagination-container {
            background-color: var(--bg-white);
            padding: 12px 16px;
            border-radius: var(--border-radius);
            box-shadow: var(--shadow-sm);
            font-size: 14px;
        }
        /* 分页控件 */
        
        .pagination {
            font-size: 14px;
            margin-bottom: 0;
            margin-right: 12px;
        }
        
        .page-item .page-link {
            border-radius: var(--border-radius);
            margin: 0 2px;
            color: var(--text-light);
            border: 1px solid var(--border-color);
            min-width: 32px;
            height: 32px;
            line-height: 1.5;
            padding: 4px 8px;
            text-align: center;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .page-item.active .page-link {
            background-color: var(--primary-color);
            border-color: var(--primary-color);
            color: white;
        }
        
        .page-item.disabled .page-link {
            color: var(--text-muted);
            background-color: var(--bg-lighter);
        }
        /* 页数选择器样式 */
        
        .page-size-wrapper {
            margin-left: 0;
        }
        
        .page-size-selector {
            height: 32px;
            border: 1px solid var(--border-color);
            border-radius: var(--border-radius);
            background-color: var(--bg-white);
            display: flex;
            align-items: center;
            position: relative;
            min-width: 100px;
            padding: 0 8px;
            cursor: pointer;
        }
        
        .page-size-selector::after {
            content: '';
            display: inline-block;
            margin-left: auto;
            width: 0;
            height: 0;
            border-left: 4px solid transparent;
            border-right: 4px solid transparent;
            border-top: 4px solid var(--text-light);
        }
        
        .page-size-number {
            font-weight: 500;
            color: var(--text-dark);
            margin-right: 4px;
            font-size: 14px;
        }
        
        .page-size-label {
            color: var(--text-light);
            font-size: 14px;
        }
        
        .page-size-select {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            opacity: 0;
            cursor: pointer;
            z-index: 1;
        }
        /* 按钮样式优化 */
        
        .btn {
            border-radius: var(--border-radius);
            font-weight: 400;
            padding: 4px 16px;
            transition: var(--transition);
            height: 32px;
            font-size: 14px;
            display: inline-flex;
            align-items: center;
            justify-content: center;
        }
        
        .btn i {
            margin-right: 6px;
            font-size: 14px;
        }
        
        .btn-sm {
            padding: 0 12px;
            height: 28px;
            font-size: 14px;
        }
        
        .btn-primary {
            background-color: var(--primary-color);
            border-color: var(--primary-color);
        }
        
        .btn-primary:hover {
            background-color: #1a5ad7;
            border-color: #1a5ad7;
        }
        
        .btn-secondary {
            background-color: #f2f2f2;
            border-color: #e8e8e8;
            color: var(--text-dark);
        }
        
        .btn-secondary:hover {
            background-color: #e8e8e8;
            border-color: #d9d9d9;
            color: var(--text-dark);
        }
        
        .btn-outline-danger {
            color: var(--danger);
            border-color: var(--danger);
            background-color: white;
        }
        
        .btn-outline-danger:hover {
            color: white;
            background-color: var(--danger);
        }
        /* 图标按钮 */
        
        .btn-icon {
            width: 32px;
            height: 32px;
            padding: 0;
            border-radius: var(--border-radius);
            display: inline-flex;
            align-items: center;
            justify-content: center;
            color: var(--text-light);
            background: transparent;
            border: 1px solid transparent;
        }
        
        .btn-icon:hover {
            background-color: #f2f2f2;
            color: var(--text-dark);
        }
        
        .btn-icon i {
            margin-right: 0;
            font-size: 16px;
        }
        /* 搜索框样式 */
        
        .search-container {
            position: relative;
        }
        
        .search-icon {
            top: 50%;
            left: 10px;
            transform: translateY(-50%);
            color: var(--text-light);
            font-size: 14px;
        }
        
        #search-input {
            padding-left: 32px;
            padding-right: 30px;
        }
        
        .btn-clear-search {
            top: 50%;
            right: 10px;
            transform: translateY(-50%);
            border: none;
            background: transparent;
            color: var(--text-light);
            padding: 0;
            cursor: pointer;
            font-size: 14px;
        }
        
        .btn-clear-search:hover {
            color: var(--text-dark);
        }
        /* 搜索结果高亮 */
        
        .highlight-text {
            background-color: rgba(var(--primary-rgb), 0.15);
            border-radius: 2px;
            padding: 0 2px;
        }
    </style>
</head>

<body>
    <div class="main-container">
        <div class="page-header d-flex justify-content-between align-items-center mb-3">
            <h4 class="page-title"><i class="bi bi-database"></i>广告账户数据</h4>
            <div class="d-flex gap-2">
                <button class="btn btn-outline-secondary btn-sm">
                    <i class="bi bi-download"></i>导出
                </button>
                <button class="btn btn-primary btn-sm" id="select-file-btn">
                    <i class="bi bi-upload"></i>导入
                </button>
                <input type="file" id="csv-file-input" accept=".csv,.xls,.xlsx" style="display: none;">
            </div>
        </div>

        <!-- 筛选区域 -->
        <div class="card mb-3">
            <div class="card-body">
                <div class="row g-3 align-items-center">
                    <div class="col-md-4">
                        <label class="form-label small text-muted mb-2">账户状态</label>
                        <div class="status-tabs d-flex border rounded overflow-hidden">
                            <div class="status-tab">
                                <button class="status-tab-btn active" data-value="all">全部</button>
                            </div>
                            <div class="status-tab">
                                <button class="status-tab-btn" data-value="正常">正常</button>
                            </div>
                            <div class="status-tab">
                                <button class="status-tab-btn" data-value="停用">停用</button>
                            </div>
                            <div class="status-tab">
                                <button class="status-tab-btn" data-value="注销">注销</button>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <label class="form-label small text-muted mb-1">综合搜索</label>
                        <div class="search-container position-relative">
                            <input type="text" id="search-input" class="form-control form-control-sm" placeholder="搜索账户ID、名称、归属等">
                            <i class="bi bi-search search-icon position-absolute"></i>
                            <button type="button" class="btn-clear-search position-absolute" style="display: none;">
                                <i class="bi bi-x-circle"></i>
                            </button>
                        </div>
                    </div>
                    <div class="col-auto ms-auto d-flex align-items-end">
                        <button class="btn btn-secondary btn-sm" id="clear-data-btn">
                            <i class="bi bi-trash"></i>清除数据
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- 数据表格 -->
        <div class="card mb-3">
            <div class="table-caption p-3 border-bottom">
                <div class="d-flex justify-content-between align-items-center">
                    <span class="fw-medium">广告账户列表</span>
                    <span class="text-muted small">显示所有符合条件的记录</span>
                </div>
            </div>
            <div class="table-responsive">
                <table class="table align-middle">
                    <thead>
                        <tr>
                            <th style="width: 60px;">#</th>
                            <th>广告账户ID</th>
                            <th>广告账户名称</th>
                            <th>广告户店铺归属</th>
                            <th>广告户公司归属</th>
                            <th>团队归属</th>
                            <th>店铺平台</th>
                            <th>管易店铺ID</th>
                            <th class="text-right">返点比例</th>
                            <th>开始时间</th>
                            <th>结束时间</th>
                            <th>账户状态</th>
                        </tr>
                    </thead>
                    <tbody id="data-table-body">
                        <!-- Data rows will be inserted here by JavaScript -->
                    </tbody>
                </table>
            </div>

            <!-- 分页和总记录数容器 -->
            <div class="d-flex justify-content-between align-items-center p-3 border-top">
                <div class="d-flex align-items-center">
                    <span class="text-muted">共 <span id="total-records" class="fw-medium text-dark">0</span> 条记录</span>
                </div>
                <div class="d-flex align-items-center">
                    <nav aria-label="Page navigation">
                        <ul class="pagination justify-content-end mb-0" id="pagination">
                            <!-- Pagination items will be inserted here by JavaScript -->
                        </ul>
                    </nav>
                    <div class="page-size-wrapper">
                        <div class="page-size-selector">
                            <span class="page-size-number" id="page-size-display">20</span>
                            <span class="page-size-label">条/页</span>
                            <select id="page-size-select" class="page-size-select">
                               <option value="10">10</option>
                               <option value="20" selected>20</option>
                               <option value="50">50</option>
                               <option value="100">100</option>
                            </select>
                        </div>
                    </div>
                </div>
            </div>
        </div>

    </div>

    <!-- 回到顶部按钮 -->
    <button class="btn btn-icon btn-primary position-fixed" id="back-to-top" style="bottom: 24px; right: 24px; display: none;">
        <i class="bi bi-arrow-up"></i>
    </button>

    <!-- Bootstrap JS Bundle -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <!-- jQuery -->

    <script src="https://cdn.jsdelivr.net/npm/jquery@3.6.0/dist/jquery.min.js"></script>
    <!-- colResizable plugin -->

    <script src="https://cdn.jsdelivr.net/npm/colresizable@1.6.0/colResizable-1.6.min.js"></script>
    <!-- Day.js -->
    <script src="https://cdn.jsdelivr.net/npm/dayjs@1/dayjs.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/dayjs@1/plugin/customParseFormat.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/dayjs@1/plugin/isoWeek.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/dayjs@1/plugin/isBetween.js"></script>
    <!-- Litepicker JS -->
    <script src="https://cdn.jsdelivr.net/npm/litepicker/dist/litepicker.js"></script>
    <!-- Custom JS -->
    <script>
        let recordsPerPage = 20; // Default page size
        const LOCAL_STORAGE_KEY = 'financialData';
        let allData = [];
        let currentPage = 1;
        let filteredData = []; // 用于存储搜索筛选后的数据
        let currentSearchQuery = ''; // 当前搜索关键词
        let currentStatusFilter = 'all'; // 当前状态筛选值

        // --- DOM Elements for Import ---
        const csvFileInput = document.getElementById('csv-file-input');
        const selectFileBtn = document.getElementById('select-file-btn');
        const fileNameSpan = document.getElementById('file-name');
        // const importCsvBtn = document.getElementById('import-csv-btn'); // REMOVED
        const pageSizeSelect = document.getElementById('page-size-select'); // Added
        const clearDataBtn = document.getElementById('clear-data-btn'); // 添加清除数据按钮引用

        // --- 模拟数据生成 ---
        function generateMockData() {
            const statuses = ['正常', '停用', '注销'];
            const data = [];

            for (let i = 1; i <= 100; i++) {
                const status = statuses[Math.floor(Math.random() * statuses.length)];

                data.push({
                    id: i,
                    accountName: `广告账户${i}`,
                    platform: ['Facebook', 'Google', 'TikTok'][Math.floor(Math.random() * 3)],
                    status: status,
                    balance: (Math.random() * 10000).toFixed(2),
                    rebateRate: (Math.random() * 0.1).toFixed(2),
                    createTime: new Date(Date.now() - Math.random() * 90 * 24 * 60 * 60 * 1000).toISOString().split('T')[0]
                });
            }
            return data;
        }

        // --- CSV 解析 ---
        function parseCSV(csvText) {
            const lines = csvText.trim().split(/\r?\n/); // Split lines, handle Windows/Unix endings
            if (lines.length < 2) {
                console.error("CSV file appears empty or has no data rows.");
                return null; // Need at least header and one data line (or just one data line if no header assumed)
            }

            // 尝试检测分隔符 (逗号或制表符)
            const firstLine = lines[0];
            let separator = ','; // 默认分隔符
            if (firstLine.includes('\t')) {
                separator = '\t';
                console.log("检测到TSV格式 (制表符分隔)");
            } else if (firstLine.includes(';')) {
                separator = ';';
                console.log("检测到分号分隔CSV格式");
            }

            const parsedData = [];
            // 使用第一行作为表头，检测列位置
            const headers = lines[0].split(separator);

            // 尝试识别关键列的位置
            let colIndexes = {
                id: headers.findIndex(h => h.includes('ID') || h.includes('id') || h.includes('编号')),
                name: headers.findIndex(h => h.includes('名称') || h.includes('账户名') || h.includes('name')),
                storeAffiliation: headers.findIndex(h => h.includes('店铺归属') || h.includes('店铺')),
                companyAffiliation: headers.findIndex(h => h.includes('公司归属') || h.includes('公司')),
                teamAffiliation: headers.findIndex(h => h.includes('团队归属') || h.includes('团队')),
                platform: headers.findIndex(h => h.includes('平台')),
                guanyiId: headers.findIndex(h => h.includes('管易') || h.includes('店铺ID')),
                rebateRate: headers.findIndex(h => h.includes('返点') || h.includes('比例')),
                startTime: headers.findIndex(h => h.includes('开始时间') || h.includes('起始')),
                endTime: headers.findIndex(h => h.includes('结束时间') || h.includes('终止')),
                status: headers.findIndex(h => h.includes('状态'))
            };

            // 检查列映射，如果没有找到匹配，尝试使用索引位置
            for (let key in colIndexes) {
                if (colIndexes[key] === -1) {
                    switch (key) {
                        case 'id':
                            colIndexes[key] = 0;
                            break; // 通常第一列是ID
                        case 'name':
                            colIndexes[key] = 1;
                            break; // 第二列通常是名称
                        case 'status':
                            colIndexes[key] = headers.length - 1;
                            break; // 状态通常是最后一列
                        default:
                            colIndexes[key] = -1; // 保持为-1，表示未找到
                    }
                }
            }

            console.log("列映射:", colIndexes);

            // 跳过标题行，从第二行开始解析
            for (let i = 1; i < lines.length; i++) {
                const line = lines[i];
                if (!line.trim()) continue; // 跳过空行

                // 分割行数据
                const values = line.split(separator);
                if (values.length < 3) continue; // 跳过明显异常的行

                try {
                    // 处理返点率，包括各种可能的格式
                    let rebateRateRaw = '';
                    let rebateRateNum = 0;

                    if (colIndexes.rebateRate >= 0 && colIndexes.rebateRate < values.length) {
                        rebateRateRaw = values[colIndexes.rebateRate].trim();

                        // 处理不同的返点格式 (5%, 0.05, 5等)
                        if (rebateRateRaw.includes('%')) {
                            // 如果包含百分号，去掉百分号并除以100
                            rebateRateNum = parseFloat(rebateRateRaw.replace('%', '')) / 100;
                        } else {
                            rebateRateNum = parseFloat(rebateRateRaw);
                            // 如果值大于1，假设它是以百分比形式输入的 (例如 5 表示 5%)
                            if (rebateRateNum > 1) {
                                rebateRateNum = rebateRateNum / 100;
                            }
                        }

                        if (isNaN(rebateRateNum)) {
                            console.warn(`行 ${i+1} 的返点率格式无效，默认为0: ${rebateRateRaw}`);
                            rebateRateNum = 0;
                        }
                    }

                    // 提取其他字段的值，处理可能的越界
                    const getValue = (index) => {
                        return (index >= 0 && index < values.length) ? values[index].trim() : '';
                    };

                    // 创建数据对象
                    parsedData.push({
                        id: getValue(colIndexes.id),
                        name: getValue(colIndexes.name),
                        storeAffiliation: getValue(colIndexes.storeAffiliation),
                        companyAffiliation: getValue(colIndexes.companyAffiliation),
                        teamAffiliation: getValue(colIndexes.teamAffiliation),
                        platform: getValue(colIndexes.platform),
                        guanyiId: getValue(colIndexes.guanyiId),
                        rebateRate: rebateRateNum,
                        startTime: getValue(colIndexes.startTime),
                        endTime: getValue(colIndexes.endTime),
                        status: getValue(colIndexes.status)
                    });
                } catch (parseError) {
                    console.error(`解析行 ${i+1} 时出错: ${line}`, parseError);
                    continue; // 跳过有问题的行，继续处理其他行
                }
            }

            if (parsedData.length === 0) {
                console.error("CSV解析结果为零条有效数据行。");
                return null;
            }

            return parsedData;
        }


        // --- 本地存储 ---
        function saveDataToLocal(data) {
            try {
                localStorage.setItem(LOCAL_STORAGE_KEY, JSON.stringify(data));
            } catch (e) {
                console.error("Error saving data to localStorage:", e);
                alert("无法保存数据到本地存储，可能是存储已满或浏览器不支持。");
            }
        }

        function loadDataFromLocal() {
            try {
                const storedData = localStorage.getItem(LOCAL_STORAGE_KEY);
                if (storedData) {
                    return JSON.parse(storedData);
                } else {
                    console.log("No data found in localStorage, generating mock data.");
                    const newData = generateMockData();
                    saveDataToLocal(newData);
                    return newData;
                }
            } catch (e) {
                console.error("Error loading data from localStorage:", e);
                alert("无法从本地存储加载数据，将使用默认模拟数据。");
                // Fallback to generating mock data without saving if parsing/loading fails
                return generateMockData();
            }
        }

        // --- 状态标签 ---
        function renderStatusBadge(status) {
            const statusConfig = {
                '正常': {
                    class: 'status-active',
                    icon: 'bi bi-check-circle-fill'
                },
                '停用': {
                    class: 'status-inactive',
                    icon: 'bi bi-x-circle-fill'
                },
                '注销': {
                    class: 'status-cancelled',
                    icon: 'bi bi-ban-fill'
                }
            };

            const config = statusConfig[status] || statusConfig['停用'];
            return `<span class="status-badge ${config.class}"><i class="${config.icon}"></i>${status}</span>`;
        }

        // --- 格式化金额显示 ---
        function formatAmount(value) {
            return new Intl.NumberFormat('zh-CN', {
                minimumFractionDigits: 2,
                maximumFractionDigits: 2
            }).format(value);
        }

        // --- 渲染表格 ---
        function renderTable(page) {
            currentPage = page;
            const tableBody = document.getElementById('data-table-body');
            const totalRecordsSpan = document.getElementById('total-records');
            if (!tableBody || !totalRecordsSpan) {
                console.error("Table body or total records span not found!");
                return;
            }

            tableBody.innerHTML = ''; // Clear existing rows

            // 应用搜索和状态筛选
            filterData();

            totalRecordsSpan.textContent = filteredData.length;

            const startIndex = (page - 1) * recordsPerPage;
            const endIndex = startIndex + recordsPerPage;
            const paginatedData = filteredData.slice(startIndex, endIndex);

            // 如果没有数据，显示空状态
            if (paginatedData.length === 0) {
                let emptyMessage = '暂无数据';
                if (currentSearchQuery) {
                    emptyMessage = '没有找到匹配的搜索结果';
                }

                tableBody.innerHTML = `
                    <tr>
                        <td colspan="13" class="text-center py-5">
                            <div class="d-flex flex-column align-items-center">
                                <i class="bi bi-inbox text-muted mb-3" style="font-size: 2rem;"></i>
                                <p class="text-muted mb-0">${emptyMessage}</p>
                            </div>
                        </td>
                    </tr>`;
                return;
            }

            paginatedData.forEach((item, index) => {
                const row = tableBody.insertRow();
                if (index % 2 === 1) {
                    row.classList.add('row-even');
                }
                const overallIndex = startIndex + index + 1;

                // 格式化日期显示 (YYYY-MM-DD)
                const formatDate = (dateStr) => {
                    if (!dateStr || dateStr === '-') return '-';
                    return dateStr;
                };

                // 返点百分比
                const rebatePercent = (item.rebateRate * 100).toFixed(2);

                // 准备单元格内容，如果有搜索词则高亮显示
                const highlightMatch = (text, field) => {
                    if (!currentSearchQuery || !text || text === '-') return text;
                    return highlightText(text, currentSearchQuery);
                };

                row.innerHTML = `
                    <td class="text-muted">${overallIndex}</td>
                    <td>${highlightMatch(item.id || '-', 'id')}</td>
                    <td>${highlightMatch(item.name || '-', 'name')}</td>
                    <td>${highlightMatch(item.storeAffiliation || '-', 'storeAffiliation')}</td>
                    <td>${highlightMatch(item.companyAffiliation || '-', 'companyAffiliation')}</td>
                    <td>${highlightMatch(item.teamAffiliation || '-', 'teamAffiliation')}</td>
                    <td><i class="bi bi-shop me-1 text-muted"></i>${highlightMatch(item.platform || '-', 'platform')}</td>
                    <td>${highlightMatch(item.guanyiId || '-', 'guanyiId')}</td>
                    <td class="text-right amount">${rebatePercent}%</td>
                    <td>${formatDate(item.startTime)}</td>
                    <td>${formatDate(item.endTime)}</td>
                    <td>${renderStatusBadge(item.status)}</td>
                `;
            });

            renderPagination();
        }

        // --- 高亮显示搜索结果 ---
        function highlightText(text, query) {
            if (!query || !text) return text;

            // 分割搜索关键词
            const keywords = query.trim().split(/\s+/).filter(kw => kw.length > 0);
            if (keywords.length === 0) return text;

            let result = text;

            // 对每个关键词进行高亮处理
            keywords.forEach(keyword => {
                const regex = new RegExp(keyword.replace(/[.*+?^${}()|[\]\\]/g, '\\$&'), 'gi');
                result = result.replace(regex, match => `<span class="highlight-text">${match}</span>`);
            });

            return result;
        }

        // --- 筛选数据 ---
        function filterData() {
            // 如果没有搜索关键词且状态筛选为全部，则不需要筛选
            if (!currentSearchQuery && currentStatusFilter === 'all') {
                filteredData = [...allData];
                return;
            }

            filteredData = allData.filter(item => {
                // 应用状态筛选
                if (currentStatusFilter !== 'all') {
                    if (item.status !== currentStatusFilter) return false;
                }

                // 如果没有搜索关键词，只应用状态筛选
                if (!currentSearchQuery) return true;

                // 分割搜索关键词
                const keywords = currentSearchQuery.trim().split(/\s+/).filter(kw => kw.length > 0);
                if (keywords.length === 0) return true;

                // 对每个关键词进行匹配
                return keywords.every(keyword => {
                    const regex = new RegExp(keyword.replace(/[.*+?^${}()|[\]\\]/g, '\\$&'), 'i');

                    // 在所有可搜索字段中查找匹配
                    return regex.test(item.id) ||
                        regex.test(item.accountName) ||
                        regex.test(item.platform);
                });
            });
        }

        // --- 渲染分页 ---
        function renderPagination() {
            const paginationUl = document.getElementById('pagination');
            if (!paginationUl) {
                console.error("Pagination element not found!");
                return;
            }
            paginationUl.innerHTML = ''; // Clear existing pagination

            const totalPages = Math.ceil(filteredData.length / recordsPerPage);
            if (totalPages <= 1) return; // No pagination needed for 1 or 0 pages

            // Helper to create list items
            const createPageItem = (text, pageNum, isDisabled = false, isActive = false, isEllipsis = false) => {
                const li = document.createElement('li');
                li.className = `page-item ${isDisabled ? 'disabled' : ''} ${isActive ? 'active' : ''}`;

                const a = document.createElement('a');
                a.className = 'page-link';
                a.href = '#';

                // 添加图标到上一页/下一页按钮
                if (text === '上一页') {
                    a.innerHTML = '<i class="bi bi-chevron-left"></i>';
                    a.setAttribute('aria-label', '上一页');
                } else if (text === '下一页') {
                    a.innerHTML = '<i class="bi bi-chevron-right"></i>';
                    a.setAttribute('aria-label', '下一页');
                } else {
                    a.textContent = text;
                }

                if (!isDisabled && !isEllipsis) {
                    a.onclick = (e) => {
                        e.preventDefault();
                        renderTable(pageNum);
                    };
                }

                li.appendChild(a);
                return li;
            };

            // Previous Button
            paginationUl.appendChild(createPageItem('上一页', currentPage - 1, currentPage === 1));

            // Page Number Buttons
            const maxPagesToShow = 5;
            let startPage = Math.max(1, currentPage - Math.floor(maxPagesToShow / 2));
            let endPage = Math.min(totalPages, startPage + maxPagesToShow - 1);

            // Adjust if we are near the end
            if (endPage - startPage + 1 < maxPagesToShow) {
                startPage = Math.max(1, endPage - maxPagesToShow + 1);
            }

            if (startPage > 1) {
                paginationUl.appendChild(createPageItem('1', 1));
                if (startPage > 2) {
                    paginationUl.appendChild(createPageItem('...', 0, true, false, true)); // Ellipsis
                }
            }

            for (let i = startPage; i <= endPage; i++) {
                paginationUl.appendChild(createPageItem(i, i, false, i === currentPage));
            }

            if (endPage < totalPages) {
                if (endPage < totalPages - 1) {
                    paginationUl.appendChild(createPageItem('...', 0, true, false, true)); // Ellipsis
                }
                paginationUl.appendChild(createPageItem(totalPages, totalPages));
            }

            // Next Button
            paginationUl.appendChild(createPageItem('下一页', currentPage + 1, currentPage === totalPages));
        }

        // --- 清理导入状态 ---
        function resetImportState() {
            if (csvFileInput) csvFileInput.value = null; // Clear the file input
            if (fileNameSpan && fileNameSpan.textContent) fileNameSpan.textContent = '';
            if (selectFileBtn) {
                selectFileBtn.disabled = false;
                selectFileBtn.innerHTML = '<i class="bi bi-upload"></i>导入';
            }
        }

        // --- 清空数据 ---
        function clearAllData() {
            if (confirm('确定要清空所有数据吗？此操作不可撤销！')) {
                try {
                    // 清除本地存储
                    localStorage.removeItem(LOCAL_STORAGE_KEY);

                    // 重置数据和页面
                    allData = [];
                    currentPage = 1;

                    // 重新渲染表格
                    renderTable(1);

                    // 使用 Toast 替代 alert
                    showToast('成功', '数据已成功清除！', 'success');
                } catch (err) {
                    console.error('清除数据时发生错误:', err);
                    showToast('错误', '清除数据时发生错误，请稍后再试。', 'danger');
                }
            }
        }

        // --- Toast 消息提示函数 ---
        function showToast(title, message, type = 'info') {
            // 创建Toast容器（如果不存在）
            let toastContainer = document.getElementById('toast-container');
            if (!toastContainer) {
                toastContainer = document.createElement('div');
                toastContainer.id = 'toast-container';
                toastContainer.className = 'position-fixed bottom-0 end-0 p-3';
                toastContainer.style.zIndex = '1080';
                document.body.appendChild(toastContainer);
            }

            // 创建Toast
            const toastId = 'toast-' + Date.now();
            const iconMap = {
                'success': 'bi-check-circle text-success',
                'danger': 'bi-exclamation-circle text-danger',
                'warning': 'bi-exclamation-triangle text-warning',
                'info': 'bi-info-circle text-info'
            };

            const toastHtml = `
                <div id="${toastId}" class="toast" role="alert" aria-live="assertive" aria-atomic="true">
                    <div class="toast-header">
                        <i class="bi ${iconMap[type]} me-2"></i>
                        <strong class="me-auto">${title}</strong>
                        <button type="button" class="btn-close" data-bs-dismiss="toast" aria-label="Close"></button>
                    </div>
                    <div class="toast-body">${message}</div>
                </div>
            `;

            toastContainer.insertAdjacentHTML('beforeend', toastHtml);

            // 显示Toast
            const toastElement = document.getElementById(toastId);
            const toast = new bootstrap.Toast(toastElement, {
                animation: true,
                autohide: true,
                delay: 3000
            });

            toast.show();

            // 删除已消失的Toast
            toastElement.addEventListener('hidden.bs.toast', () => {
                toastElement.remove();
            });
        }

        // --- 初始化 ---
        function initializePage() {
            allData = loadDataFromLocal();
            renderTable(1);

            // --- Import Event Listeners Setup ---
            if (selectFileBtn && csvFileInput) {
                selectFileBtn.addEventListener('click', () => {
                    csvFileInput.click(); // Trigger hidden file input
                });

                csvFileInput.addEventListener('change', (event) => {
                    const file = event.target.files[0];
                    if (file) {
                        // --- Start Import Immediately ---
                        selectFileBtn.disabled = true; // Disable select button during processing
                        selectFileBtn.innerHTML = '<i class="bi bi-hourglass-split"></i>处理中';

                        const reader = new FileReader();

                        reader.onload = (e) => {
                            try {
                                const csvText = e.target.result;
                                const parsedData = parseCSV(csvText);

                                if (parsedData && parsedData.length > 0) {
                                    allData = parsedData;
                                    saveDataToLocal(allData);
                                    renderTable(1); // Render the newly imported data
                                    showToast('导入成功', `共导入 ${allData.length} 条记录`, 'success');
                                } else {
                                    showToast('导入失败', '无法解析文件或文件内容为空', 'danger');
                                }
                            } catch (err) {
                                console.error("Error during file processing:", err);
                                showToast('处理错误', '处理文件时发生意外错误', 'danger');
                            } finally {
                                resetImportState(); // Clean up UI
                            }
                        };

                        reader.onerror = (errorEvent) => {
                            console.error("File reading error:", errorEvent.target.error);
                            showToast('读取错误', '读取文件时发生错误', 'danger');
                            resetImportState(); // Clean up UI
                        };

                        reader.readAsText(file, 'UTF-8'); // Start reading
                    } else {
                        // No file selected or selection cancelled
                        resetImportState();
                    }
                });
            } else {
                console.error("Import related DOM elements not found!");
            }

            // --- Page Size Selector Listener ---
            if (pageSizeSelect) {
                const pageSizeDisplay = document.getElementById('page-size-display');

                // 初始设置页面大小显示
                if (pageSizeDisplay) {
                    pageSizeDisplay.textContent = pageSizeSelect.value;
                }

                pageSizeSelect.addEventListener('change', (event) => {
                    const newSize = parseInt(event.target.value, 10);
                    if (!isNaN(newSize) && newSize > 0) {
                        recordsPerPage = newSize;
                        // 更新页面大小显示
                        if (pageSizeDisplay) {
                            pageSizeDisplay.textContent = newSize;
                        }
                        currentPage = 1; // Reset to first page when size changes
                        renderTable(currentPage);
                    }
                });
            } else {
                console.error("Page size select element not found!");
            }

            // --- 清除数据按钮监听器 ---
            if (clearDataBtn) {
                clearDataBtn.addEventListener('click', clearAllData);
            } else {
                console.error("Clear data button not found!");
            }

            // --- 初始化搜索功能 ---
            const searchInput = document.getElementById('search-input');
            const clearSearchBtn = document.querySelector('.btn-clear-search');

            if (searchInput && clearSearchBtn) {
                // 搜索框输入事件
                searchInput.addEventListener('input', function(e) {
                    currentSearchQuery = e.target.value.trim();
                    clearSearchBtn.style.display = currentSearchQuery ? 'block' : 'none';
                    currentPage = 1; // 重置到第一页
                    renderTable(currentPage);
                });

                // 清除搜索按钮点击事件
                clearSearchBtn.addEventListener('click', function() {
                    searchInput.value = '';
                    currentSearchQuery = '';
                    clearSearchBtn.style.display = 'none';
                    currentPage = 1; // 重置到第一页
                    renderTable(currentPage);
                });

                // 搜索框按下Enter键事件
                searchInput.addEventListener('keydown', function(e) {
                    if (e.key === 'Enter') {
                        e.preventDefault();
                        currentSearchQuery = e.target.value.trim();
                        renderTable(currentPage);
                    }
                });
            } else {
                console.error("搜索框元素未找到!");
            }

            // 状态筛选器功能
            const statusBtns = document.querySelectorAll('.status-tab-btn');
            if (statusBtns.length > 0) {
                statusBtns.forEach(btn => {
                    btn.addEventListener('click', function() {
                        // 移除所有按钮的active类
                        statusBtns.forEach(b => {
                            b.classList.remove('active');
                            b.parentElement.classList.remove('active');
                        });

                        // 给当前点击的按钮添加active类
                        this.classList.add('active');
                        this.parentElement.classList.add('active');

                        // 应用状态筛选逻辑
                        const statusValue = this.getAttribute('data-value');
                        currentStatusFilter = statusValue;
                        currentPage = 1; // 重置到第一页
                        renderTable(currentPage);
                    });
                });
            }
        }

        // --- 入口 ---
        document.addEventListener('DOMContentLoaded', function() {
            initializePage();

            // 回到顶部按钮功能
            const backToTopBtn = document.getElementById('back-to-top');

            if (backToTopBtn) {
                window.addEventListener('scroll', function() {
                    if (window.pageYOffset > 200) {
                        backToTopBtn.style.display = 'flex';
                        backToTopBtn.style.justifyContent = 'center';
                        backToTopBtn.style.alignItems = 'center';
                    } else {
                        backToTopBtn.style.display = 'none';
                    }
                });

                backToTopBtn.addEventListener('click', function() {
                    window.scrollTo({
                        top: 0,
                        behavior: 'smooth'
                    });
                });
            }
        });
    </script>
</body>

</html>