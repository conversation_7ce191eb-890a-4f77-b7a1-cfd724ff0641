<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>商品维护管理系统</title>
    <!-- 引入现代UI框架 -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- 引入Font Awesome图标库 -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- 引入ECharts图表库 -->
    <script src="https://cdn.jsdelivr.net/npm/echarts@5.4.3/dist/echarts.min.js"></script>
    <!-- 引入Swiper轮播插件 -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/swiper@10/swiper-bundle.min.css" />
    <script src="https://cdn.jsdelivr.net/npm/swiper@10/swiper-bundle.min.js"></script>
    <!-- 引入动画库 -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/animate.css/4.1.1/animate.min.css" />
    <style>
         :root {
            /* 主色调 */
            --primary-color: #4361ee;
            --primary-light: #eaefff;
            --primary-dark: #3a56d4;
            /* 辅助色 */
            --success-color: #2ecc71;
            --success-light: #e8f8f0;
            --warning-color: #f39c12;
            --warning-light: #fdf6e9;
            --danger-color: #e74c3c;
            --danger-light: #fdedeb;
            --info-color: #3498db;
            --info-light: #ebf5fb;
            /* 中性色 */
            --text-primary: #2d3748;
            --text-secondary: #64748b;
            --text-light: #94a3b8;
            --border-color: #e2e8f0;
            --bg-light: #f8fafc;
            --bg-white: #ffffff;
            /* 阴影 */
            --shadow-sm: 0 1px 2px rgba(0, 0, 0, 0.05);
            --shadow-md: 0 4px 6px rgba(0, 0, 0, 0.05), 0 1px 3px rgba(0, 0, 0, 0.1);
            --shadow-lg: 0 10px 15px rgba(0, 0, 0, 0.05), 0 4px 6px rgba(0, 0, 0, 0.05);
            /* 边框圆角 */
            --radius-sm: 4px;
            --radius-md: 6px;
            --radius-lg: 8px;
            /* 间距 */
            --spacing-xs: 4px;
            --spacing-sm: 8px;
            --spacing-md: 12px;
            --spacing-lg: 16px;
            --spacing-xl: 20px;
        }
        
        body {
            font-family: "PingFang SC", "Helvetica Neue", Arial, "Microsoft YaHei", sans-serif;
            background-color: var(--bg-light);
            color: var(--text-primary);
            font-size: 13px;
            line-height: 1.5;
        }
        /* 顶部导航栏 */
        
        .app-header {
            background-color: var(--bg-white);
            box-shadow: var(--shadow-sm);
            height: 56px;
            display: flex;
            align-items: center;
            padding: 0 var(--spacing-xl);
            position: sticky;
            top: 0;
            z-index: 1000;
        }
        
        .app-title {
            font-size: 16px;
            font-weight: 600;
            color: var(--text-primary);
            margin: 0;
        }
        
        .app-logo {
            color: var(--primary-color);
            margin-right: var(--spacing-md);
            font-size: 20px;
        }
        /* 主内容区 */
        
        .app-container {
            padding: var(--spacing-lg);
        }
        /* 卡片容器 */
        
        .app-card {
            background-color: var(--bg-white);
            border-radius: var(--radius-md);
            box-shadow: var(--shadow-sm);
            margin-bottom: var(--spacing-lg);
            transition: all 0.3s;
            border: 1px solid var(--border-color);
            overflow: hidden;
        }
        
        .app-card:hover {
            box-shadow: var(--shadow-md);
        }
        
        .app-card-header {
            padding: var(--spacing-md) var(--spacing-lg);
            border-bottom: 1px solid var(--border-color);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .app-card-title {
            font-size: 15px;
            font-weight: 600;
            color: var(--text-primary);
            margin: 0;
            display: flex;
            align-items: center;
        }
        
        .app-card-title i {
            color: var(--primary-color);
            margin-right: var(--spacing-sm);
        }
        
        .app-card-body {
            padding: var(--spacing-lg);
        }
        /* 搜索过滤区 */
        
        .search-form {
            display: flex;
            flex-wrap: wrap;
            gap: var(--spacing-md);
        }
        
        .search-input-group {
            position: relative;
            flex: 1;
            min-width: 200px;
        }
        
        .search-input {
            height: 36px;
            padding-left: 32px;
            border-radius: var(--radius-md);
            border: 1px solid var(--border-color);
            width: 100%;
            transition: all 0.3s;
            font-size: 13px;
        }
        
        .search-input:focus {
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px var(--primary-light);
            outline: none;
        }
        
        .search-icon {
            position: absolute;
            left: var(--spacing-md);
            top: 50%;
            transform: translateY(-50%);
            color: var(--text-light);
        }
        /* 按钮样式 */
        
        .btn-app {
            height: 36px;
            border-radius: var(--radius-md);
            padding: 0 var(--spacing-lg);
            font-weight: 500;
            transition: all 0.3s;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            border: none;
            font-size: 13px;
        }
        
        .btn-app i {
            margin-right: var(--spacing-xs);
        }
        
        .btn-primary-app {
            background-color: var(--primary-color);
            color: white;
        }
        
        .btn-primary-app:hover {
            background-color: var(--primary-dark);
            box-shadow: 0 4px 8px rgba(67, 97, 238, 0.2);
        }
        
        .btn-outline-app {
            background-color: transparent;
            color: var(--text-secondary);
            border: 1px solid var(--border-color);
        }
        
        .btn-outline-app:hover {
            border-color: var(--primary-color);
            color: var(--primary-color);
            background-color: var(--primary-light);
        }
        /* 摘要卡片 */
        
        .summary-card {
            background: linear-gradient(135deg, var(--primary-light), #f8f9ff);
            border-radius: var(--radius-md);
            padding: var(--spacing-lg);
            margin-bottom: var(--spacing-lg);
            position: relative;
            overflow: hidden;
            border-left: 3px solid var(--primary-color);
        }
        
        .summary-card::before {
            content: '';
            position: absolute;
            top: 0;
            right: 0;
            width: 120px;
            height: 120px;
            background: radial-gradient(circle, rgba(67, 97, 238, 0.1) 0%, rgba(255, 255, 255, 0) 70%);
            border-radius: 50%;
        }
        
        .summary-title {
            font-size: 14px;
            font-weight: 600;
            color: var(--text-primary);
            margin-bottom: var(--spacing-md);
            display: flex;
            align-items: center;
        }
        
        .summary-title i {
            color: var(--primary-color);
            margin-right: var(--spacing-sm);
        }
        
        .summary-content {
            display: flex;
            flex-wrap: wrap;
            gap: var(--spacing-lg);
        }
        
        .summary-item {
            display: flex;
            align-items: center;
        }
        
        .summary-icon {
            width: 32px;
            height: 32px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: var(--spacing-sm);
            font-size: 14px;
        }
        
        .summary-icon.success {
            background-color: var(--success-light);
            color: var(--success-color);
        }
        
        .summary-icon.danger {
            background-color: var(--danger-light);
            color: var(--danger-color);
        }
        
        .summary-icon.warning {
            background-color: var(--warning-light);
            color: var(--warning-color);
        }
        
        .summary-icon.info {
            background-color: var(--info-light);
            color: var(--info-color);
        }
        
        .summary-data {
            display: flex;
            flex-direction: column;
        }
        
        .summary-value {
            font-size: 18px;
            font-weight: 700;
            color: var(--text-primary);
            line-height: 1.2;
        }
        
        .summary-label {
            font-size: 12px;
            color: var(--text-secondary);
        }
        /* 团队筛选标签 */
        
        .team-filter-bar {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: var(--spacing-md);
            background-color: var(--bg-white);
            padding: 10px 12px;
            border-radius: var(--radius-md);
            box-shadow: var(--shadow-sm);
            border: 1px solid var(--border-color);
        }
        
        .filter-label {
            font-size: 13px;
            font-weight: 500;
            color: #5f6368;
        }
        
        .btn-sm {
            height: 32px;
            padding: 0 var(--spacing-md);
            font-size: 12px;
        }
        
        .sort-options {
            display: flex;
            align-items: center;
        }
        
        .sort-label {
            font-size: 13px;
            color: var(--text-secondary);
            margin-right: 8px;
            font-weight: 500;
        }
        
        .sort-button-group {
            display: flex;
            align-items: center;
            background-color: var(--bg-light);
            border-radius: var(--radius-md);
            padding: 2px;
            box-shadow: var(--shadow-sm);
            border: 1px solid var(--border-color);
            position: relative;
        }
        
        .sort-btn {
            border: none;
            background: transparent;
            padding: 5px 10px;
            font-size: 12px;
            border-radius: var(--radius-sm);
            color: var(--text-secondary);
            cursor: pointer;
            display: inline-flex;
            align-items: center;
            transition: all 0.2s ease;
            margin: 0 1px;
            height: 28px;
            position: relative;
        }
        
        .sort-btn:not(:last-child)::after {
            content: '';
            position: absolute;
            right: -1px;
            top: 6px;
            height: 16px;
            width: 1px;
            background-color: var(--border-color);
        }
        
        .sort-btn i {
            margin-right: 4px;
            font-size: 11px;
        }
        
        .sort-btn i.sort-icon {
            margin-left: 1px;
            margin-right: 0;
            font-size: 10px;
            opacity: 0.7;
            margin-top: 1px;
        }
        
        .sort-btn:hover {
            color: var(--primary-color);
            background-color: rgba(67, 97, 238, 0.05);
        }
        
        .sort-btn.active {
            background-color: var(--bg-white);
            color: var(--primary-color);
            font-weight: 500;
            box-shadow: var(--shadow-sm);
        }
        /* 团队轮播布局 */
        
        .teams-carousel {
            position: relative;
            padding: 0 28px;
            margin-bottom: var(--spacing-sm);
        }
        
        .swiper-button-prev,
        .swiper-button-next {
            width: 26px;
            height: 26px;
            background-color: var(--bg-white);
            border-radius: 50%;
            box-shadow: var(--shadow-sm);
            color: var(--primary-color);
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.3s;
        }
        
        .swiper-button-prev:hover,
        .swiper-button-next:hover {
            background-color: var(--primary-color);
            color: white;
            box-shadow: var(--shadow-md);
        }
        
        .swiper-button-prev:after,
        .swiper-button-next:after {
            font-size: 12px;
        }
        /* 团队卡片样式 */
        
        .team-card {
            background-color: var(--bg-white);
            border-radius: var(--radius-sm);
            transition: all 0.25s;
            overflow: hidden;
            height: 100%;
            display: flex;
            flex-direction: column;
            max-width: 250px;
            margin: 0 auto;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
            border: 1px solid #eaecef;
        }
        
        .team-card:hover {
            box-shadow: 0 3px 8px rgba(0, 0, 0, 0.15);
            transform: translateY(-1px);
        }
        
        .team-card-header {
            padding: 8px 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-bottom: none;
            background-color: #4361ee;
            position: relative;
        }
        
        .team-card-title {
            font-size: 14px;
            font-weight: 600;
            margin: 0;
            color: white;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            text-align: center;
        }
        
        .team-card-body {
            padding: 0;
            display: flex;
            flex-direction: column;
            flex-grow: 1;
            background-color: #ffffff;
            overflow: hidden;
        }
        /* 数据区块样式 */
        
        .data-section {
            display: flex;
            flex-direction: column;
            margin-bottom: 0;
            padding: 0;
            background-color: #ffffff;
            border-bottom: none;
        }
        
        .data-row {
            display: flex;
            align-items: stretch;
            margin-bottom: 0;
            border-bottom: 1px solid #f0f0f0;
        }
        
        .data-row:last-child {
            margin-bottom: 0;
            border-bottom: none;
        }
        
        .data-block {
            flex: 1;
            padding: 10px 0;
            margin: 0;
            display: flex;
            flex-direction: column;
            align-items: center;
            text-align: center;
            transition: all 0.2s;
            position: relative;
            border: none;
            border-radius: 0;
        }
        
        .data-block:hover {
            background-color: rgba(0, 0, 0, 0.02);
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
        }
        
        .data-block.maintained {
            background-color: #f5fbf7;
            border-right: 1px solid #f0f0f0;
        }
        
        .data-block.unmaintained {
            background-color: #fdf6f5;
        }
        
        .data-block.monthly {
            background-color: #fffbf2;
            border-right: 1px solid #f0f0f0;
        }
        
        .data-block.total {
            background-color: #f5f9ff;
        }
        
        .data-value {
            font-size: 28px;
            font-weight: 700;
            line-height: 1.1;
            margin-bottom: 4px;
        }
        
        .data-value.maintained {
            color: #34a853;
        }
        
        .data-value.unmaintained {
            color: #ea4335;
        }
        
        .data-value.monthly {
            color: #fbbc04;
        }
        
        .data-value.total {
            color: #4285f4;
        }
        
        .data-label {
            font-size: 12px;
            color: #5f6368;
            margin-top: 0;
        }
        /* 进度条区域 */
        
        .progress-section {
            margin: 0;
            padding: 8px 10px;
            border-top: 1px solid rgba(0, 0, 0, 0.05);
            background-color: #f8f9fa;
            border-radius: 0;
        }
        
        .progress-row {
            display: flex;
            align-items: center;
        }
        
        .progress-label {
            font-size: 13px;
            color: #5f6368;
            width: 40px;
            flex-shrink: 0;
            font-weight: 500;
        }
        
        .progress-bar-container {
            flex-grow: 1;
            height: 8px;
            background-color: #e0e0e0;
            border-radius: 4px;
            overflow: hidden;
            position: relative;
            margin: 0 8px;
        }
        
        .progress-value {
            font-size: 13px;
            color: #202124;
            min-width: 40px;
            text-align: right;
            font-weight: 600;
        }
        
        .progress-bar.maintained {
            height: 100%;
            background-color: #34a853;
            border-radius: 4px;
        }
        /* 表格样式 */
        
        .app-table {
            width: 100%;
            border-collapse: separate;
            border-spacing: 0;
        }
        
        .app-table th {
            background-color: var(--bg-light);
            color: var(--text-secondary);
            font-weight: 600;
            text-align: left;
            padding: var(--spacing-sm) var(--spacing-md);
            border-bottom: 1px solid var(--border-color);
            font-size: 12px;
        }
        
        .app-table td {
            padding: var(--spacing-md);
            border-bottom: 1px solid var(--border-color);
            color: var(--text-primary);
            vertical-align: middle;
            font-size: 13px;
        }
        
        .app-table tr:hover td {
            background-color: var(--primary-light);
        }
        
        .app-table tr:last-child td {
            border-bottom: none;
        }
        
        .table-container {
            border: 1px solid var(--border-color);
            border-radius: var(--radius-md);
            overflow: auto;
        }
        
        .new-badge {
            display: inline-flex;
            align-items: center;
            justify-content: center;
            height: 18px;
            padding: 0 4px;
            background-color: var(--danger-color);
            color: white;
            border-radius: var(--radius-sm);
            font-size: 10px;
            font-weight: 600;
            margin-right: var(--spacing-sm);
            vertical-align: middle;
        }
        
        .action-btn {
            width: 28px;
            height: 28px;
            border-radius: var(--radius-sm);
            display: inline-flex;
            align-items: center;
            justify-content: center;
            color: var(--text-secondary);
            background-color: transparent;
            border: 1px solid var(--border-color);
            transition: all 0.3s;
            margin-right: 4px;
        }
        
        .action-btn:hover {
            background-color: var(--primary-light);
            color: var(--primary-color);
            border-color: var(--primary-color);
        }
        /* 分页控件 */
        
        .app-pagination {
            display: flex;
            justify-content: flex-end;
            margin-top: var(--spacing-md);
        }
        
        .page-btn {
            width: 32px;
            height: 32px;
            border-radius: var(--radius-sm);
            display: flex;
            align-items: center;
            justify-content: center;
            background-color: var(--bg-white);
            border: 1px solid var(--border-color);
            color: var(--text-secondary);
            font-weight: 500;
            margin: 0 2px;
            transition: all 0.3s;
            font-size: 12px;
        }
        
        .page-btn.active {
            background-color: var(--primary-color);
            color: white;
            border-color: var(--primary-color);
        }
        
        .page-btn:hover:not(.active):not(.disabled) {
            border-color: var(--primary-color);
            color: var(--primary-color);
        }
        
        .page-btn.disabled {
            color: var(--text-light);
            cursor: not-allowed;
        }
        /* 团队筛选模态框 */
        
        .modal-app {
            border-radius: var(--radius-lg);
            overflow: hidden;
            box-shadow: var(--shadow-lg);
        }
        
        .modal-header-app {
            background-color: var(--bg-light);
            padding: var(--spacing-md) var(--spacing-lg);
            border-bottom: 1px solid var(--border-color);
        }
        
        .modal-title-app {
            font-size: 15px;
            font-weight: 600;
            color: var(--text-primary);
            margin: 0;
        }
        
        .modal-body-app {
            padding: var(--spacing-lg);
        }
        
        .modal-footer-app {
            padding: var(--spacing-md) var(--spacing-lg);
            border-top: 1px solid var(--border-color);
            display: flex;
            justify-content: flex-end;
        }
        
        .team-filter-search {
            position: relative;
            margin-bottom: var(--spacing-md);
        }
        
        .team-filter-search input {
            width: 100%;
            height: 36px;
            padding-left: 32px;
            border-radius: var(--radius-md);
            border: 1px solid var(--border-color);
            transition: all 0.3s;
            font-size: 13px;
        }
        
        .team-filter-search i {
            position: absolute;
            left: var(--spacing-md);
            top: 50%;
            transform: translateY(-50%);
            color: var(--text-light);
        }
        
        .team-checkbox-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: var(--spacing-sm);
            max-height: 300px;
            overflow-y: auto;
            padding: var(--spacing-sm);
            border: 1px solid var(--border-color);
            border-radius: var(--radius-md);
        }
        
        .team-checkbox-item {
            padding: var(--spacing-sm);
            display: flex;
            align-items: center;
            transition: all 0.3s;
            border-radius: var(--radius-sm);
        }
        
        .team-checkbox-item:hover {
            background-color: var(--bg-light);
        }
        
        .team-checkbox-item input[type="checkbox"] {
            width: 16px;
            height: 16px;
            margin-right: var(--spacing-sm);
            accent-color: var(--primary-color);
        }
        
        .team-checkbox-item label {
            margin: 0;
            font-size: 13px;
            color: var(--text-primary);
            cursor: pointer;
        }
        /* 响应式调整 */
        
        @media (max-width: 768px) {
            .app-container {
                padding: var(--spacing-sm);
            }
            .chart-container {
                height: 100px;
                width: 100px;
            }
            .summary-content {
                flex-direction: column;
                gap: var(--spacing-md);
            }
            .search-form {
                flex-direction: column;
            }
            .search-input-group {
                width: 100%;
            }
            .team-checkbox-grid {
                grid-template-columns: 1fr;
            }
            .teams-carousel {
                padding: 0;
            }
            .app-card-header,
            .app-card-body {
                padding: var(--spacing-md);
            }
            .team-filter-bar {
                flex-direction: column;
                align-items: flex-start;
            }
            .sort-options {
                margin-top: var(--spacing-sm);
                width: 100%;
            }
            .sort-button-group {
                flex: 1;
                justify-content: space-between;
            }
            .sort-btn {
                flex: 1;
                justify-content: center;
                padding: 5px 5px;
            }
        }
        
        @media (min-width: 769px) and (max-width: 1199px) {
            .teams-carousel {
                padding: 0;
            }
        }
        
        @media (min-width: 1200px) {
            .teams-carousel {
                padding: 0;
            }
        }
    </style>
</head>

<body>
    <!-- 顶部导航栏 -->
    <header class="app-header">
        <div class="app-logo">
            <i class="fas fa-box-open"></i>
        </div>
        <h1 class="app-title">商品维护管理系统</h1>
    </header>

    <!-- 主内容区 -->
    <div class="app-container">
        <!-- 搜索筛选区域 -->
        <div class="app-card">
            <div class="app-card-header">
                <h2 class="app-card-title">
                    <i class="fas fa-search"></i> 商品搜索
                </h2>
            </div>
            <div class="app-card-body">
                <form class="search-form">
                    <div class="search-input-group">
                        <i class="fas fa-search search-icon"></i>
                        <input type="text" class="search-input" placeholder="名称/平台ID/商品编号/条码">
                    </div>
                    <div class="search-input-group">
                        <i class="fas fa-tag search-icon"></i>
                        <input type="text" class="search-input" placeholder="筛选品牌">
                    </div>
                    <div class="search-input-group">
                        <i class="fas fa-store search-icon"></i>
                        <input type="text" class="search-input" placeholder="筛选平台/店铺">
                    </div>
                    <div class="search-input-group">
                        <i class="fas fa-filter search-icon"></i>
                        <select class="search-input">
                            <option selected>是否维护筛选</option>
                            <option value="1">已维护</option>
                            <option value="2">未维护</option>
                        </select>
                    </div>
                    <button type="button" class="btn-app btn-primary-app">
                        <i class="fas fa-search"></i> 搜索
                    </button>
                </form>
            </div>
        </div>

        <!-- 团队维度统计区域 -->
        <div class="app-card">
            <div class="app-card-header">
                <h2 class="app-card-title">
                    <i class="fas fa-chart-pie"></i> 团队维度商品维护统计
                </h2>
                <div>
                    <button class="btn-app btn-primary-app" data-bs-toggle="modal" data-bs-target="#teamFilterModal">
                        <i class="fas fa-filter"></i> 团队筛选
                    </button>
                </div>
            </div>
            <div class="app-card-body">
                <!-- 汇总信息卡片 -->
                <div class="summary-card">
                    <h3 class="summary-title">
                        <i class="fas fa-chart-bar"></i> 所选团队汇总数据
                    </h3>
                    <div class="summary-content">
                        <div class="summary-item">
                            <div class="summary-icon success">
                                <i class="fas fa-check-circle"></i>
                            </div>
                            <div class="summary-data">
                                <div class="summary-value" id="summary-maintained">182</div>
                                <div class="summary-label">已维护商品数量</div>
                            </div>
                        </div>
                        <div class="summary-item">
                            <div class="summary-icon danger">
                                <i class="fas fa-times-circle"></i>
                            </div>
                            <div class="summary-data">
                                <div class="summary-value" id="summary-unmaintained">56</div>
                                <div class="summary-label">未维护商品数量</div>
                            </div>
                        </div>
                        <div class="summary-item">
                            <div class="summary-icon warning">
                                <i class="fas fa-exclamation-triangle"></i>
                            </div>
                            <div class="summary-data">
                                <div class="summary-value" id="summary-monthly">23</div>
                                <div class="summary-label">当月增幅次数</div>
                            </div>
                        </div>
                        <div class="summary-item">
                            <div class="summary-icon info">
                                <i class="fas fa-history"></i>
                            </div>
                            <div class="summary-data">
                                <div class="summary-value" id="summary-total">399</div>
                                <div class="summary-label">累计增幅次数</div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 团队筛选信息栏 -->
                <div class="team-filter-bar">
                    <span class="filter-label">
                        <i class="fas fa-users"></i> 当前显示: <span id="team-filter-info">全部团队 (20)</span>
                    </span>
                    <div class="sort-options">
                        <span class="sort-label">排序: </span>
                        <div class="sort-button-group">
                            <button class="sort-btn active" data-sort="unmaintained">
                                <i class="fas fa-exclamation-circle"></i> 未维护数量 <i class="fas fa-sort-down sort-icon"></i>
                            </button>
                            <button class="sort-btn" data-sort="monthly">
                                <i class="fas fa-chart-line"></i> 当月增幅 <i class="fas fa-sort-down sort-icon"></i>
                            </button>
                            <button class="sort-btn" data-sort="total">
                                <i class="fas fa-chart-bar"></i> 累计增幅 <i class="fas fa-sort-down sort-icon"></i>
                            </button>
                        </div>
                    </div>
                </div>

                <!-- 团队轮播布局 -->
                <div class="teams-carousel">
                    <div class="swiper teamsSwiper">
                        <div class="swiper-wrapper" id="teams-wrapper">
                            <!-- 团队卡片会在JS中动态生成 -->
                        </div>
                    </div>
                    <!-- 导航按钮 -->
                    <div class="swiper-button-prev"></div>
                    <div class="swiper-button-next"></div>
                </div>
            </div>
        </div>

        <!-- 商品列表区域 -->
        <div class="app-card">
            <div class="app-card-header">
                <h2 class="app-card-title">
                    <i class="fas fa-list"></i> 商品列表
                </h2>
                <div>
                    <button class="btn-app btn-outline-app">
                        <i class="fas fa-file-export"></i> 导出数据
                    </button>
                    <button class="btn-app btn-primary-app">
                        <i class="fas fa-plus"></i> 新增商品
                    </button>
                </div>
            </div>
            <div class="app-card-body">
                <div class="table-container">
                    <table class="app-table">
                        <thead>
                            <tr>
                                <th>平台商品ID</th>
                                <th>主商品代码</th>
                                <th>主商品简称</th>
                                <th>品牌</th>
                                <th>店铺</th>
                                <th>平台</th>
                                <th width="120">操作</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>
                                    <span class="new-badge">NEW</span>700057983922
                                </td>
                                <td>YQ-51</td>
                                <td>YQ 白衣申色净300g</td>
                                <td>YQNY</td>
                                <td>YQ 旗舰店-天猫</td>
                                <td>天猫</td>
                                <td>
                                    <button class="action-btn" title="编辑">
                                        <i class="fas fa-edit"></i>
                                    </button>
                                    <button class="action-btn" title="删除">
                                        <i class="fas fa-trash-alt"></i>
                                    </button>
                                </td>
                            </tr>
                            <tr>
                                <td>
                                    <span class="new-badge">NEW</span>751052186035
                                </td>
                                <td>YQ-91</td>
                                <td>YQ 冰箱专用清洁剂380ml</td>
                                <td>YQNY</td>
                                <td>YQ 旗舰店-天猫</td>
                                <td>天猫</td>
                                <td>
                                    <button class="action-btn" title="编辑">
                                        <i class="fas fa-edit"></i>
                                    </button>
                                    <button class="action-btn" title="删除">
                                        <i class="fas fa-trash-alt"></i>
                                    </button>
                                </td>
                            </tr>
                            <tr>
                                <td>
                                    <span class="new-badge">NEW</span>679064041361
                                </td>
                                <td>套餐3038</td>
                                <td>YQ 衣物翻新染色粉80g+YQ 衣服固色剂120ml（YQ-97黑色，YQ-133）</td>
                                <td>YQNY</td>
                                <td>YQ 旗舰店-天猫</td>
                                <td>天猫</td>
                                <td>
                                    <button class="action-btn" title="编辑">
                                        <i class="fas fa-edit"></i>
                                    </button>
                                    <button class="action-btn" title="删除">
                                        <i class="fas fa-trash-alt"></i>
                                    </button>
                                </td>
                            </tr>
                            <tr>
                                <td>
                                    <span class="new-badge">NEW</span>765382378274
                                </td>
                                <td>YQ-105</td>
                                <td>YQ 内衣内裤洗涤剂380ml</td>
                                <td>YQNY</td>
                                <td>YQ 旗舰店-天猫</td>
                                <td>天猫</td>
                                <td>
                                    <button class="action-btn" title="编辑">
                                        <i class="fas fa-edit"></i>
                                    </button>
                                    <button class="action-btn" title="删除">
                                        <i class="fas fa-trash-alt"></i>
                                    </button>
                                </td>
                            </tr>
                            <tr>
                                <td>
                                    <span class="new-badge">NEW</span>788834639526
                                </td>
                                <td>KISS-03</td>
                                <td>KISS 真皮光亮保养喷雾500ml</td>
                                <td>KISS TST</td>
                                <td>YQ 旗舰店-天猫</td>
                                <td>天猫</td>
                                <td>
                                    <button class="action-btn" title="编辑">
                                        <i class="fas fa-edit"></i>
                                    </button>
                                    <button class="action-btn" title="删除">
                                        <i class="fas fa-trash-alt"></i>
                                    </button>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>

                <!-- 分页控件 -->
                <div class="app-pagination">
                    <button class="page-btn disabled">
                        <i class="fas fa-chevron-left"></i>
                    </button>
                    <button class="page-btn active">1</button>
                    <button class="page-btn">2</button>
                    <button class="page-btn">3</button>
                    <button class="page-btn">
                        <i class="fas fa-chevron-right"></i>
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- 团队筛选模态框 -->
    <div class="modal fade" id="teamFilterModal" tabindex="-1" aria-labelledby="teamFilterModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content modal-app">
                <div class="modal-header modal-header-app">
                    <h5 class="modal-title modal-title-app" id="teamFilterModalLabel">
                        <i class="fas fa-filter me-2"></i>团队筛选
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body modal-body-app">
                    <div class="d-flex justify-content-between mb-3">
                        <button class="btn-app btn-outline-app" id="selectAllTeams">
                            <i class="fas fa-check-square"></i> 全选
                        </button>
                        <button class="btn-app btn-outline-app" id="unselectAllTeams">
                            <i class="fas fa-square"></i> 取消全选
                        </button>
                    </div>
                    <div class="team-filter-search">
                        <i class="fas fa-search"></i>
                        <input type="text" placeholder="搜索团队" id="teamSearchInput">
                    </div>
                    <div class="team-checkbox-grid" id="teamCheckboxList">
                        <!-- 复选框会在JS中动态生成 -->
                    </div>
                </div>
                <div class="modal-footer modal-footer-app">
                    <button type="button" class="btn-app btn-outline-app" data-bs-dismiss="modal">取消</button>
                    <button type="button" class="btn-app btn-primary-app" id="applyTeamFilter">应用筛选</button>
                </div>
            </div>
        </div>
    </div>

    <!-- 引入 Bootstrap JS 和 Popper.js -->
    <script src="https://cdn.jsdelivr.net/npm/@popperjs/core@2.11.6/dist/umd/popper.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.min.js"></script>

    <!-- 引入Javascript代码部分 -->
    <script>
        // 模拟20个团队数据
        const teamsData = {
            '1': {
                name: 'YQ旗舰店团队',
                maintained: 125,
                unmaintained: 37,
                monthlyCost: 15,
                totalCost: 243
            },
            '2': {
                name: 'KISS团队',
                maintained: 87,
                unmaintained: 19,
                monthlyCost: 8,
                totalCost: 156
            },
            '3': {
                name: '美妆团队A',
                maintained: 62,
                unmaintained: 45,
                monthlyCost: 23,
                totalCost: 189
            },
            '4': {
                name: '美妆团队B',
                maintained: 98,
                unmaintained: 12,
                monthlyCost: 7,
                totalCost: 134
            },
            '5': {
                name: '服装团队A',
                maintained: 145,
                unmaintained: 28,
                monthlyCost: 18,
                totalCost: 276
            },
            '6': {
                name: '服装团队B',
                maintained: 76,
                unmaintained: 24,
                monthlyCost: 12,
                totalCost: 187
            },
            '7': {
                name: '家居团队A',
                maintained: 112,
                unmaintained: 33,
                monthlyCost: 20,
                totalCost: 221
            },
            '8': {
                name: '家居团队B',
                maintained: 55,
                unmaintained: 17,
                monthlyCost: 9,
                totalCost: 102
            },
            '9': {
                name: '电子团队A',
                maintained: 132,
                unmaintained: 41,
                monthlyCost: 25,
                totalCost: 267
            },
            '10': {
                name: '电子团队B',
                maintained: 93,
                unmaintained: 22,
                monthlyCost: 14,
                totalCost: 178
            },
            '11': {
                name: '食品团队A',
                maintained: 85,
                unmaintained: 29,
                monthlyCost: 16,
                totalCost: 155
            },
            '12': {
                name: '食品团队B',
                maintained: 103,
                unmaintained: 18,
                monthlyCost: 10,
                totalCost: 198
            },
            '13': {
                name: '母婴团队A',
                maintained: 118,
                unmaintained: 32,
                monthlyCost: 19,
                totalCost: 223
            },
            '14': {
                name: '母婴团队B',
                maintained: 74,
                unmaintained: 15,
                monthlyCost: 8,
                totalCost: 143
            },
            '15': {
                name: '运动团队A',
                maintained: 91,
                unmaintained: 27,
                monthlyCost: 14,
                totalCost: 176
            },
            '16': {
                name: '运动团队B',
                maintained: 68,
                unmaintained: 21,
                monthlyCost: 11,
                totalCost: 129
            },
            '17': {
                name: '图书团队A',
                maintained: 82,
                unmaintained: 14,
                monthlyCost: 6,
                totalCost: 147
            },
            '18': {
                name: '图书团队B',
                maintained: 59,
                unmaintained: 13,
                monthlyCost: 7,
                totalCost: 107
            },
            '19': {
                name: '宠物团队A',
                maintained: 71,
                unmaintained: 23,
                monthlyCost: 12,
                totalCost: 138
            },
            '20': {
                name: '宠物团队B',
                maintained: 65,
                unmaintained: 16,
                monthlyCost: 9,
                totalCost: 124
            }
        };

        // 记录当前选中的团队
        let selectedTeams = Object.keys(teamsData);
        let currentSort = "unmaintained"; // 默认排序方式

        // 页面加载完成后执行
        document.addEventListener('DOMContentLoaded', function() {
            // 初始化团队选择复选框
            initTeamCheckboxes();

            // 初始化团队卡片
            renderTeamCards();
            initTeamsSwiper();

            // 计算并显示汇总数据
            updateSummaryData();
            updateFilterInfo();

            // 绑定全选/取消全选事件
            document.getElementById('selectAllTeams').addEventListener('click', function() {
                selectAllTeamsFunc(true);
            });

            document.getElementById('unselectAllTeams').addEventListener('click', function() {
                selectAllTeamsFunc(false);
            });

            // 绑定应用筛选按钮事件
            document.getElementById('applyTeamFilter').addEventListener('click', function() {
                applyTeamFilter();
            });

            // 绑定排序按钮事件
            const sortButtons = document.querySelectorAll('.sort-btn');
            sortButtons.forEach(button => {
                button.addEventListener('click', function() {
                    // 移除其他按钮的active类
                    sortButtons.forEach(btn => btn.classList.remove('active'));
                    // 为当前按钮添加active类
                    this.classList.add('active');

                    // 更新排序方式
                    currentSort = this.getAttribute('data-sort');

                    // 重新渲染团队卡片
                    renderTeamCards();
                    initTeamsSwiper();
                });
            });

            // 绑定团队搜索框事件
            document.getElementById('teamSearchInput').addEventListener('input', function() {
                const searchTerm = this.value.toLowerCase();
                const items = document.querySelectorAll('.team-checkbox-item');

                items.forEach(item => {
                    const label = item.querySelector('label').textContent.toLowerCase();
                    if (label.includes(searchTerm)) {
                        item.style.display = 'flex';
                    } else {
                        item.style.display = 'none';
                    }
                });
            });
        });

        // 初始化团队选择复选框
        function initTeamCheckboxes() {
            const container = document.getElementById('teamCheckboxList');
            container.innerHTML = '';

            Object.keys(teamsData).forEach(teamId => {
                const team = teamsData[teamId];
                const checkboxItem = document.createElement('div');
                checkboxItem.className = 'team-checkbox-item';

                const checkbox = document.createElement('input');
                checkbox.type = 'checkbox';
                checkbox.id = 'team-checkbox-' + teamId;
                checkbox.className = 'team-checkbox';
                checkbox.value = teamId;
                checkbox.checked = selectedTeams.includes(teamId);

                const label = document.createElement('label');
                label.htmlFor = 'team-checkbox-' + teamId;
                label.textContent = team.name;

                checkboxItem.appendChild(checkbox);
                checkboxItem.appendChild(label);
                container.appendChild(checkboxItem);
            });
        }

        // 全选/取消全选函数
        function selectAllTeamsFunc(selectAll) {
            const checkboxes = document.querySelectorAll('.team-checkbox');
            checkboxes.forEach(checkbox => {
                checkbox.checked = selectAll;
            });
        }

        // 应用团队筛选
        function applyTeamFilter() {
            const checkboxes = document.querySelectorAll('.team-checkbox:checked');
            selectedTeams = Array.from(checkboxes).map(checkbox => checkbox.value);

            // 如果没有选择任何团队，默认全选
            if (selectedTeams.length === 0) {
                selectedTeams = Object.keys(teamsData);
                selectAllTeamsFunc(true);
            }

            // 重新渲染团队卡片（保持当前排序方式）
            renderTeamCards();
            initTeamsSwiper();

            // 更新汇总数据
            updateSummaryData();
            updateFilterInfo();

            // 关闭模态框
            const modalElement = document.getElementById('teamFilterModal');
            const modalInstance = bootstrap.Modal.getInstance(modalElement);
            modalInstance.hide();
        }

        // 更新筛选信息
        function updateFilterInfo() {
            const infoEl = document.getElementById('team-filter-info');
            if (selectedTeams.length === Object.keys(teamsData).length) {
                infoEl.textContent = `全部团队 (${selectedTeams.length})`;
            } else if (selectedTeams.length <= 3) {
                const teamNames = selectedTeams.map(id => teamsData[id].name).join('、');
                infoEl.textContent = teamNames;
            } else {
                infoEl.textContent = `已选 ${selectedTeams.length} 个团队`;
            }
        }

        // 渲染团队卡片
        function renderTeamCards() {
            const container = document.getElementById('teams-wrapper');
            container.innerHTML = '';

            // 根据当前排序方式对团队进行排序
            const sortedTeams = [...selectedTeams].sort((a, b) => {
                const teamA = teamsData[a];
                const teamB = teamsData[b];

                switch (currentSort) {
                    case 'unmaintained':
                        return teamB.unmaintained - teamA.unmaintained;
                    case 'monthly':
                        return teamB.monthlyCost - teamA.monthlyCost;
                    case 'total':
                        return teamB.totalCost - teamA.totalCost;
                    default:
                        return teamB.unmaintained - teamA.unmaintained;
                }
            });

            sortedTeams.forEach(teamId => {
                const team = teamsData[teamId];

                // 创建团队卡片
                const teamCard = document.createElement('div');
                teamCard.className = 'swiper-slide';
                teamCard.setAttribute('data-team-id', teamId);

                // 基于团队数据生成HTML结构
                teamCard.innerHTML = `
                    <div class="team-card">
                        <div class="team-card-header">
                            <h5 class="team-card-title">${team.name}</h5>
                        </div>
                                                    <div class="team-card-body">
                            <div class="data-section">
                                <div class="data-row">
                                    <div class="data-block maintained">
                                        <div class="data-value maintained">${team.maintained}</div>
                                        <div class="data-label">已维护商品</div>
                                    </div>
                                    <div class="data-block unmaintained">
                                        <div class="data-value unmaintained">${team.unmaintained}</div>
                                        <div class="data-label">未维护商品</div>
                                    </div>
                                </div>
                                <div class="data-row">
                                    <div class="data-block monthly">
                                        <div class="data-value monthly">${team.monthlyCost}</div>
                                        <div class="data-label">当月增幅</div>
                                    </div>
                                    <div class="data-block total">
                                        <div class="data-value total">${team.totalCost}</div>
                                        <div class="data-label">累计增幅</div>
                                    </div>
                                </div>
                            </div>
                            <div class="progress-section">
                                <div class="progress-row">
                                    <div class="progress-label">维护率</div>
                                    <div class="progress-bar-container">
                                        <div class="progress-bar maintained" style="width: ${team.maintained / (team.maintained + team.unmaintained) * 100}%"></div>
                                    </div>
                                    <div class="progress-value">${(team.maintained / (team.maintained + team.unmaintained) * 100).toFixed(1)}%</div>
                                </div>
                            </div>
                        </div>
                    </div>
                `;

                container.appendChild(teamCard);
            });
        }

        // 初始化团队轮播
        function initTeamsSwiper() {
            // 如果已经初始化过，先销毁
            if (window.teamsSwiper) {
                window.teamsSwiper.destroy();
            }

            // 计算每行显示的卡片数量 (根据屏幕宽度自动调整)
            let slidesPerView = 1;
            const windowWidth = window.innerWidth;

            if (windowWidth >= 1400) {
                slidesPerView = 6; // 超大屏幕显示6个
            } else if (windowWidth >= 1200) {
                slidesPerView = 5; // 大屏幕显示5个
            } else if (windowWidth >= 992) {
                slidesPerView = 4; // 中等屏幕显示4个
            } else if (windowWidth >= 768) {
                slidesPerView = 3; // 小屏幕显示3个
            } else if (windowWidth >= 576) {
                slidesPerView = 2; // 超小屏幕显示2个
            }

            // 初始化Swiper轮播
            window.teamsSwiper = new Swiper('.teamsSwiper', {
                slidesPerView: slidesPerView,
                spaceBetween: 0,
                navigation: {
                    nextEl: '.swiper-button-next',
                    prevEl: '.swiper-button-prev',
                },
                breakpoints: {
                    // 响应式布局
                    320: {
                        slidesPerView: 1,
                        spaceBetween: 0
                    },
                    576: {
                        slidesPerView: 2,
                        spaceBetween: 0
                    },
                    768: {
                        slidesPerView: 3,
                        spaceBetween: 0
                    },
                    992: {
                        slidesPerView: 4,
                        spaceBetween: 0
                    },
                    1200: {
                        slidesPerView: 5,
                        spaceBetween: 0
                    },
                    1400: {
                        slidesPerView: 6,
                        spaceBetween: 0
                    }
                }
            });
        }

        // 更新汇总数据
        function updateSummaryData() {
            let totalMaintained = 0;
            let totalUnmaintained = 0;
            let totalMonthlyCost = 0;
            let totalCost = 0;

            // 汇总所选团队的数据
            selectedTeams.forEach(teamId => {
                const team = teamsData[teamId];
                totalMaintained += team.maintained;
                totalUnmaintained += team.unmaintained;
                totalMonthlyCost += team.monthlyCost;
                totalCost += team.totalCost;
            });

            // 更新汇总卡片数据
            document.getElementById('summary-maintained').textContent = totalMaintained;
            document.getElementById('summary-unmaintained').textContent = totalUnmaintained;
            document.getElementById('summary-monthly').textContent = totalMonthlyCost;
            document.getElementById('summary-total').textContent = totalCost;

            // 添加动画效果
            const summaryValues = document.querySelectorAll('.summary-value');
            summaryValues.forEach(el => {
                el.classList.add('animate__animated', 'animate__fadeIn');
                setTimeout(() => {
                    el.classList.remove('animate__animated', 'animate__fadeIn');
                }, 1000);
            });
        }

        // 窗口大小变化时更新布局
        window.addEventListener('resize', function() {
            // 重新初始化轮播以适应新的屏幕尺寸
            initTeamsSwiper();
        });
    </script>
</body>

</html>