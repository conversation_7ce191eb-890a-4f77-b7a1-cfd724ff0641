<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>边界利润表格高亮示例</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        :root {
            /* 现代化配色方案 */
            --primary: #4F46E5;
            --primary-light: #EEF2FF;
            --primary-dark: #3730A3;
            --success: #10B981;
            --success-light: #ECFDF5;
            --warning: #F59E0B;
            --warning-light: #FFFBEB;
            --danger: #EF4444;
            --danger-light: #FEF2F2;

            /* 中性色 */
            --gray-50: #F9FAFB;
            --gray-100: #F3F4F6;
            --gray-200: #E5E7EB;
            --gray-300: #D1D5DB;
            --gray-400: #9CA3AF;
            --gray-500: #6B7280;
            --gray-600: #4B5563;
            --gray-700: #374151;
            --gray-800: #1F2937;
            --gray-900: #111827;

            /* 阴影 */
            --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
            --shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
            --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
            --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);

            /* 边框圆角 */
            --radius-sm: 0.375rem;
            --radius: 0.5rem;
            --radius-md: 0.75rem;
            --radius-lg: 1rem;

            /* 字体 */
            --font-sans: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
            --font-mono: ui-monospace, SFMono-Regular, "SF Mono", Consolas, "Liberation Mono", Menlo, monospace;
        }

        body {
            font-family: var(--font-sans);
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 2rem;
            color: var(--gray-900);
            line-height: 1.6;
        }

        .dashboard {
            max-width: 1600px;
            margin: 0 auto;
        }

        .dashboard-header {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border-radius: var(--radius-lg);
            padding: 2rem;
            margin-bottom: 2rem;
            box-shadow: var(--shadow-lg);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .dashboard-title {
            font-size: 2rem;
            font-weight: 700;
            color: var(--gray-900);
            margin-bottom: 0.5rem;
            background: linear-gradient(135deg, var(--primary), var(--primary-dark));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .dashboard-subtitle {
            color: var(--gray-600);
            font-size: 1.1rem;
            font-weight: 400;
        }

        .table-card {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border-radius: var(--radius-lg);
            box-shadow: var(--shadow-lg);
            border: 1px solid rgba(255, 255, 255, 0.2);
            overflow: hidden;
        }

        .table-header {
            padding: 1.5rem 2rem;
            background: linear-gradient(135deg, var(--gray-50), var(--gray-100));
            border-bottom: 1px solid var(--gray-200);
        }

        .table-header h2 {
            font-size: 1.25rem;
            font-weight: 600;
            color: var(--gray-800);
            margin-bottom: 0.25rem;
        }

        .table-header p {
            color: var(--gray-600);
            font-size: 0.875rem;
        }

        .table-wrapper {
            overflow-x: auto;
            scrollbar-width: thin;
            scrollbar-color: var(--gray-300) transparent;
        }

        .table-wrapper::-webkit-scrollbar {
            height: 8px;
        }

        .table-wrapper::-webkit-scrollbar-track {
            background: var(--gray-100);
        }

        .table-wrapper::-webkit-scrollbar-thumb {
            background: var(--gray-300);
            border-radius: var(--radius);
        }

        .table-wrapper::-webkit-scrollbar-thumb:hover {
            background: var(--gray-400);
        }

        table {
            width: 100%;
            border-collapse: separate;
            border-spacing: 0;
            min-width: 1400px;
        }

        thead {
            background: linear-gradient(135deg, var(--gray-50), var(--gray-100));
        }

        th {
            padding: 1rem 1.25rem;
            text-align: left;
            font-weight: 600;
            font-size: 0.875rem;
            color: var(--gray-700);
            text-transform: uppercase;
            letter-spacing: 0.05em;
            border-bottom: 2px solid var(--gray-200);
            position: sticky;
            top: 0;
            z-index: 10;
            white-space: nowrap;
        }

        td {
            padding: 1rem 1.25rem;
            border-bottom: 1px solid var(--gray-100);
            font-size: 0.875rem;
            color: var(--gray-800);
            vertical-align: middle;
        }

        tbody tr {
            transition: all 0.2s ease;
        }

        tbody tr:hover {
            background: var(--primary-light);
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(79, 70, 229, 0.15);
        }

        /* 高亮列样式 - 更现代化设计 */
        .highlight-profit-header {
            background: linear-gradient(135deg, var(--warning-light), #FEF3C7) !important;
            color: var(--warning) !important;
            position: relative;
        }

        .highlight-profit-header::before {
            content: '';
            position: absolute;
            left: 0;
            top: 0;
            bottom: 0;
            width: 4px;
            background: linear-gradient(180deg, var(--warning), #D97706);
            border-radius: 0 var(--radius-sm) var(--radius-sm) 0;
        }

        .highlight-profit-column {
            background: linear-gradient(135deg, var(--warning-light), rgba(254, 243, 199, 0.5)) !important;
            position: relative;
            font-weight: 600;
            color: var(--warning) !important;
        }

        .highlight-profit-column::before {
            content: '';
            position: absolute;
            left: 0;
            top: 0;
            bottom: 0;
            width: 4px;
            background: linear-gradient(180deg, var(--warning), #D97706);
            opacity: 0.8;
        }

        .highlight-ratio-header {
            background: linear-gradient(135deg, var(--primary-light), #E0E7FF) !important;
            color: var(--primary) !important;
            position: relative;
        }

        .highlight-ratio-header::before {
            content: '';
            position: absolute;
            left: 0;
            top: 0;
            bottom: 0;
            width: 4px;
            background: linear-gradient(180deg, var(--primary), var(--primary-dark));
            border-radius: 0 var(--radius-sm) var(--radius-sm) 0;
        }

        .highlight-ratio-column {
            background: linear-gradient(135deg, var(--primary-light), rgba(224, 231, 255, 0.5)) !important;
            position: relative;
            font-weight: 600;
            color: var(--primary) !important;
        }

        .highlight-ratio-column::before {
            content: '';
            position: absolute;
            left: 0;
            top: 0;
            bottom: 0;
            width: 4px;
            background: linear-gradient(180deg, var(--primary), var(--primary-dark));
            opacity: 0.8;
        }

        /* 悬停效果增强 */
        tbody tr:hover .highlight-profit-column {
            background: linear-gradient(135deg, #FEF3C7, var(--warning-light)) !important;
            box-shadow: inset 0 0 0 1px rgba(245, 158, 11, 0.2);
        }

        tbody tr:hover .highlight-ratio-column {
            background: linear-gradient(135deg, #E0E7FF, var(--primary-light)) !important;
            box-shadow: inset 0 0 0 1px rgba(79, 70, 229, 0.2);
        }

        /* 排名徽章样式 */
        .rank-badge {
            display: inline-flex;
            align-items: center;
            justify-content: center;
            width: 2rem;
            height: 2rem;
            border-radius: 50%;
            font-weight: 700;
            font-size: 0.875rem;
        }

        .rank-badge.rank-1 {
            background: linear-gradient(135deg, #FFD700, #FFA500);
            color: white;
            box-shadow: 0 4px 12px rgba(255, 215, 0, 0.4);
        }

        .rank-badge.rank-2 {
            background: linear-gradient(135deg, #C0C0C0, #A8A8A8);
            color: white;
            box-shadow: 0 4px 12px rgba(192, 192, 192, 0.4);
        }

        .rank-badge.rank-3 {
            background: linear-gradient(135deg, #CD7F32, #B8860B);
            color: white;
            box-shadow: 0 4px 12px rgba(205, 127, 50, 0.4);
        }

        .rank-badge.rank-other {
            background: linear-gradient(135deg, var(--gray-200), var(--gray-300));
            color: var(--gray-700);
        }

        /* 团队名称样式 */
        .team-name {
            font-weight: 600;
            color: var(--gray-800);
        }

        .team-type {
            display: inline-block;
            padding: 0.25rem 0.5rem;
            background: var(--gray-100);
            color: var(--gray-600);
            border-radius: var(--radius-sm);
            font-size: 0.75rem;
            margin-left: 0.5rem;
        }

        /* 金额样式 */
        .amount {
            font-family: var(--font-mono);
            font-weight: 600;
            color: var(--gray-800);
        }

        .amount.large {
            font-size: 1rem;
        }

        /* 百分比样式 */
        .percentage {
            display: inline-flex;
            align-items: center;
            padding: 0.25rem 0.75rem;
            border-radius: var(--radius);
            font-weight: 600;
            font-size: 0.875rem;
        }

        .percentage.positive {
            background: var(--success-light);
            color: var(--success);
        }

        .percentage.negative {
            background: var(--danger-light);
            color: var(--danger);
        }

        .percentage.neutral {
            background: var(--gray-100);
            color: var(--gray-600);
        }

        /* 总计行样式 */
        .total-row {
            background: linear-gradient(135deg, var(--gray-50), var(--gray-100)) !important;
            font-weight: 700;
            border-top: 3px solid var(--primary);
        }

        .total-row td {
            padding: 1.25rem;
            font-size: 0.9rem;
            color: var(--gray-800);
        }

        .total-row .total-label {
            color: var(--primary);
            font-size: 1rem;
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            body {
                padding: 1rem;
            }

            .dashboard-header {
                padding: 1.5rem;
            }

            .dashboard-title {
                font-size: 1.5rem;
            }

            th, td {
                padding: 0.75rem;
                font-size: 0.8rem;
            }
        }

        /* 加载动画 */
        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .table-card {
            animation: fadeInUp 0.6s ease-out;
        }

        .dashboard-header {
            animation: fadeInUp 0.4s ease-out;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>边界利润分析表 - 高亮显示示例</h1>
        </div>
        
        <div class="table-container">
            <table id="profit-table">
                <thead>
                    <tr>
                        <th class="rank-column">排名</th>
                        <th>团队名称</th>
                        <th>总销售额</th>
                        <th>总成本(不含运费)</th>
                        <th>自由现金流</th>
                        <th class="highlight-profit-header">当月边界利润</th>
                        <th class="highlight-ratio-header">当月边界比率</th>
                        <th>预计发货单量</th>
                        <th>处中成交额</th>
                        <th>总价</th>
                        <th>边界利润</th>
                        <th>边界比率</th>
                    </tr>
                </thead>
                <tbody id="table-body">
                    <!-- 数据将通过JavaScript动态生成 -->
                </tbody>
            </table>
        </div>
    </div>

    <script>
        // 模拟数据
        const tableData = [
            {
                rank: 1,
                teamName: "林村直营（原始）",
                totalSales: 34080.38,
                totalCost: 31156.38,
                cashFlow: 31156.38,
                monthlyProfit: 2368.72,
                monthlyRatio: 2,
                expectedOrders: 329,
                dealAmount: 34080.38,
                totalPrice: 0,
                boundaryProfit: 2990.78,
                boundaryRatio: 9.41
            },
            {
                rank: 2,
                teamName: "鑫云直营（原始）",
                totalSales: 27153.5,
                totalCost: 27153.5,
                cashFlow: 27153.5,
                monthlyProfit: 20550.45,
                monthlyRatio: 18,
                expectedOrders: 246,
                dealAmount: 27153.5,
                totalPrice: 0,
                boundaryProfit: 5436.69,
                boundaryRatio: 19.98
            },
            {
                rank: 3,
                teamName: "浩然直营（原始）",
                totalSales: 20227.45,
                totalCost: 19531.25,
                cashFlow: 19531.25,
                monthlyProfit: 9132.14,
                monthlyRatio: 12,
                expectedOrders: 243,
                dealAmount: 20227.45,
                totalPrice: 0,
                boundaryProfit: 3377.2,
                boundaryRatio: 17.29
            },
            {
                rank: 4,
                teamName: "凡竹商贸（全域）",
                totalSales: 19408.38,
                totalCost: 18960.88,
                cashFlow: 18960.88,
                monthlyProfit: 4200.03,
                monthlyRatio: 6,
                expectedOrders: 251,
                dealAmount: 19408.38,
                totalPrice: 0,
                boundaryProfit: 2143.63,
                boundaryRatio: 11.3
            },
            {
                rank: 5,
                teamName: "誉兰商贸（过境）",
                totalSales: 17837.1,
                totalCost: 17837.1,
                cashFlow: 17837.1,
                monthlyProfit: 31986.05,
                monthlyRatio: 24,
                expectedOrders: 300,
                dealAmount: 17837.1,
                totalPrice: 0,
                boundaryProfit: 12339.63,
                boundaryRatio: 69.18
            }
        ];

        // 渲染表格数据
        function renderTable() {
            const tbody = document.getElementById('table-body');
            
            tableData.forEach(item => {
                const row = document.createElement('tr');
                
                // 判断百分比的正负
                const getRatioClass = (ratio) => {
                    return ratio >= 0 ? 'percentage positive' : 'percentage negative';
                };
                
                row.innerHTML = `
                    <td class="rank-column">${item.rank}</td>
                    <td>${item.teamName}</td>
                    <td class="amount">${item.totalSales.toFixed(2)}</td>
                    <td class="amount">${item.totalCost.toFixed(2)}</td>
                    <td class="amount">${item.cashFlow.toFixed(2)}</td>
                    <td class="amount highlight-profit-column">${item.monthlyProfit.toFixed(2)}</td>
                    <td class="highlight-ratio-column ${getRatioClass(item.monthlyRatio)}">${item.monthlyRatio}%</td>
                    <td>${item.expectedOrders}</td>
                    <td class="amount">${item.dealAmount.toFixed(2)}</td>
                    <td>${item.totalPrice}</td>
                    <td class="amount">${item.boundaryProfit.toFixed(2)}</td>
                    <td class="${getRatioClass(item.boundaryRatio)}">${item.boundaryRatio}%</td>
                `;
                
                tbody.appendChild(row);
            });
            
            // 添加总计行
            const totalRow = document.createElement('tr');
            totalRow.className = 'total-row';
            
            const totalSales = tableData.reduce((sum, item) => sum + item.totalSales, 0);
            const totalCost = tableData.reduce((sum, item) => sum + item.totalCost, 0);
            const totalCashFlow = tableData.reduce((sum, item) => sum + item.cashFlow, 0);
            const totalMonthlyProfit = tableData.reduce((sum, item) => sum + item.monthlyProfit, 0);
            const totalOrders = tableData.reduce((sum, item) => sum + item.expectedOrders, 0);
            const totalDealAmount = tableData.reduce((sum, item) => sum + item.dealAmount, 0);
            const totalBoundaryProfit = tableData.reduce((sum, item) => sum + item.boundaryProfit, 0);
            
            totalRow.innerHTML = `
                <td>合计</td>
                <td></td>
                <td class="amount">${totalSales.toFixed(2)}</td>
                <td class="amount">${totalCost.toFixed(2)}</td>
                <td class="amount">${totalCashFlow.toFixed(2)}</td>
                <td class="amount highlight-profit-column">${totalMonthlyProfit.toFixed(2)}</td>
                <td class="highlight-ratio-column"></td>
                <td>${totalOrders}</td>
                <td class="amount">${totalDealAmount.toFixed(2)}</td>
                <td>0</td>
                <td class="amount">${totalBoundaryProfit.toFixed(2)}</td>
                <td></td>
            `;
            
            tbody.appendChild(totalRow);
        }

        // 页面加载完成后渲染表格
        document.addEventListener('DOMContentLoaded', function() {
            renderTable();
        });
    </script>
</body>
</html>
