<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>电商后台管理系统 - 手工科目管理</title>
    <style>
        /* 全局样式 */
        
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: 'PingFang SC', 'Microsoft YaHei', sans-serif;
        }
        
         :root {
            --primary-color: #1890ff;
            --primary-hover: #40a9ff;
            --success-color: #52c41a;
            --warning-color: #faad14;
            --danger-color: #ff4d4f;
            --text-primary: #262626;
            --text-secondary: #595959;
            --text-tertiary: #8c8c8c;
            --border-color: #e8e8e8;
            --bg-color: #f5f5f5;
            --header-height: 64px;
            --sidebar-width: 240px;
            --container-bg: #fff;
            --shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
            --radius: 4px;
        }
        
        body {
            background-color: var(--bg-color);
            color: var(--text-primary);
            line-height: 1.5;
            font-size: 14px;
        }
        
        .layout {
            display: flex;
            min-height: 100vh;
        }
        /* 侧边栏样式 */
        
        .sidebar {
            width: var(--sidebar-width);
            background-color: var(--container-bg);
            box-shadow: var(--shadow);
            position: fixed;
            height: 100vh;
            z-index: 10;
            transition: all 0.3s;
        }
        
        .sidebar-logo {
            height: var(--header-height);
            display: flex;
            align-items: center;
            padding: 0 24px;
            box-shadow: 0 1px 0 0 var(--border-color);
        }
        
        .sidebar-logo img {
            height: 32px;
            margin-right: 12px;
        }
        
        .sidebar-logo h1 {
            color: var(--text-primary);
            font-size: 18px;
            font-weight: 600;
            margin: 0;
        }
        
        .menu {
            padding: 16px 0;
        }
        
        .menu-item {
            padding: 12px 24px;
            color: var(--text-secondary);
            display: flex;
            align-items: center;
            cursor: pointer;
            transition: all 0.3s;
        }
        
        .menu-item:hover {
            color: var(--primary-color);
            background-color: rgba(24, 144, 255, 0.05);
        }
        
        .menu-item.active {
            color: var(--primary-color);
            background-color: rgba(24, 144, 255, 0.1);
            border-right: 3px solid var(--primary-color);
            font-weight: 500;
        }
        
        .menu-item i {
            margin-right: 12px;
            font-size: 16px;
        }
        /* 主内容区样式 */
        
        .main-content {
            flex: 1;
            margin-left: var(--sidebar-width);
            display: flex;
            flex-direction: column;
            min-width: 0;
        }
        
        .header {
            height: var(--header-height);
            background-color: var(--container-bg);
            box-shadow: var(--shadow);
            display: flex;
            align-items: center;
            padding: 0 24px;
            justify-content: space-between;
            position: sticky;
            top: 0;
            z-index: 9;
        }
        
        .header-title {
            font-size: 18px;
            font-weight: 500;
        }
        
        .user-info {
            display: flex;
            align-items: center;
        }
        
        .user-avatar {
            width: 32px;
            height: 32px;
            border-radius: 50%;
            background-color: var(--primary-color);
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 14px;
            margin-right: 8px;
        }
        
        .username {
            margin-right: 24px;
            color: var(--text-secondary);
        }
        /* 页面内容区域 */
        
        .page-content {
            padding: 24px;
            flex: 1;
        }
        
        .page-header {
            margin-bottom: 24px;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }
        
        .page-title {
            font-size: 20px;
            font-weight: 500;
            color: var(--text-primary);
        }
        
        .btn {
            padding: 8px 16px;
            border-radius: var(--radius);
            font-size: 14px;
            border: none;
            cursor: pointer;
            display: inline-flex;
            align-items: center;
            transition: all 0.3s;
            outline: none;
        }
        
        .btn i {
            margin-right: 6px;
            font-size: 16px;
        }
        
        .btn-primary {
            background-color: var(--primary-color);
            color: white;
        }
        
        .btn-primary:hover {
            background-color: var(--primary-hover);
        }
        
        .btn-text {
            background: none;
            color: var(--primary-color);
            padding: 4px 8px;
        }
        
        .btn-text:hover {
            background-color: rgba(24, 144, 255, 0.05);
        }
        
        .btn-danger-text {
            color: var(--danger-color);
        }
        
        .btn-danger-text:hover {
            background-color: rgba(255, 77, 79, 0.05);
        }
        
        .card {
            background-color: var(--container-bg);
            border-radius: var(--radius);
            box-shadow: var(--shadow);
            margin-bottom: 24px;
        }
        /* 表格样式 */
        
        .table-container {
            width: 100%;
            overflow-x: auto;
        }
        
        table {
            width: 100%;
            border-collapse: collapse;
            text-align: left;
        }
        
        thead {
            background-color: #fafafa;
            border-bottom: 1px solid var(--border-color);
        }
        
        th {
            padding: 16px;
            color: var(--text-secondary);
            font-weight: 500;
            white-space: nowrap;
        }
        
        td {
            padding: 16px;
            border-bottom: 1px solid var(--border-color);
            color: var(--text-primary);
        }
        
        .table-action-group {
            display: flex;
            gap: 8px;
        }
        /* 标签样式 */
        
        .tag {
            display: inline-block;
            padding: 2px 8px;
            border-radius: var(--radius);
            font-size: 12px;
        }
        
        .tag-blue {
            background-color: rgba(24, 144, 255, 0.1);
            color: var(--primary-color);
            border: 1px solid rgba(24, 144, 255, 0.2);
        }
        
        .tag-green {
            background-color: rgba(82, 196, 26, 0.1);
            color: var(--success-color);
            border: 1px solid rgba(82, 196, 26, 0.2);
        }
        
        .tag-red {
            background-color: rgba(255, 77, 79, 0.1);
            color: var(--danger-color);
            border: 1px solid rgba(255, 77, 79, 0.2);
        }
        /* 加载状态 */
        
        .loading-state {
            padding: 40px 0;
            text-align: center;
            color: var(--text-tertiary);
            display: none;
        }
        /* 分页控件 */
        
        .pagination {
            display: flex;
            justify-content: flex-end;
            align-items: center;
            padding: 16px;
            border-top: 1px solid var(--border-color);
        }
        
        .pagination-info {
            margin-right: 16px;
            color: var(--text-tertiary);
        }
        
        .pagination-btn {
            width: 32px;
            height: 32px;
            display: flex;
            align-items: center;
            justify-content: center;
            border: 1px solid var(--border-color);
            background: none;
            margin: 0 4px;
            border-radius: var(--radius);
            color: var(--text-secondary);
            cursor: pointer;
        }
        
        .pagination-btn.active {
            background-color: var(--primary-color);
            color: white;
            border-color: var(--primary-color);
        }
        
        .pagination-btn:hover:not(.active):not(:disabled) {
            border-color: var(--primary-color);
            color: var(--primary-color);
        }
        
        .pagination-btn:disabled {
            cursor: not-allowed;
            color: var(--text-tertiary);
        }
        /* 对话框样式 */
        
        .modal-backdrop {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-color: rgba(0, 0, 0, 0.45);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 100;
            opacity: 0;
            visibility: hidden;
            transition: all 0.3s;
        }
        
        .modal-backdrop.show {
            opacity: 1;
            visibility: visible;
        }
        
        .modal {
            background-color: var(--container-bg);
            border-radius: var(--radius);
            width: 480px;
            box-shadow: 0 3px 6px -4px rgba(0, 0, 0, 0.12), 0 6px 16px 0 rgba(0, 0, 0, 0.08), 0 9px 28px 8px rgba(0, 0, 0, 0.05);
            transform: translateY(20px);
            transition: transform 0.3s;
            max-height: 80vh;
            display: flex;
            flex-direction: column;
        }
        
        .modal-backdrop.show .modal {
            transform: translateY(0);
        }
        
        .modal-header {
            padding: 16px 24px;
            border-bottom: 1px solid var(--border-color);
            display: flex;
            align-items: center;
            justify-content: space-between;
        }
        
        .modal-title {
            font-size: 16px;
            font-weight: 500;
            color: var(--text-primary);
        }
        
        .modal-close {
            background: none;
            border: none;
            cursor: pointer;
            font-size: 16px;
            color: var(--text-tertiary);
        }
        
        .modal-body {
            padding: 24px;
            overflow-y: auto;
            flex: 1;
        }
        
        .modal-footer {
            padding: 10px 24px;
            border-top: 1px solid var(--border-color);
            display: flex;
            justify-content: flex-end;
            gap: 8px;
        }
        
        .modal-confirm .modal-body {
            display: flex;
            align-items: center;
            gap: 16px;
        }
        
        .modal-confirm-icon {
            font-size: 22px;
            color: var(--warning-color);
        }
        
        .modal-confirm-content {
            flex: 1;
        }
        
        .modal-confirm-title {
            font-size: 16px;
            font-weight: 500;
            margin-bottom: 8px;
            color: var(--text-primary);
        }
        
        .modal-confirm-description {
            color: var(--text-secondary);
        }
        /* 表单样式 */
        
        .form-group {
            margin-bottom: 24px;
        }
        
        .form-label {
            display: block;
            margin-bottom: 8px;
            color: var(--text-secondary);
        }
        
        .form-control {
            width: 100%;
            padding: 8px 12px;
            border: 1px solid var(--border-color);
            border-radius: var(--radius);
            font-size: 14px;
            transition: all 0.3s;
        }
        
        .form-control:focus {
            border-color: var(--primary-color);
            outline: none;
            box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
        }
        
        .form-control:disabled,
        .form-control[readonly] {
            background-color: #f5f5f5;
            cursor: not-allowed;
        }
        
        .form-select {
            appearance: none;
            background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='12' height='12' viewBox='0 0 12 12'%3E%3Cpath fill='%23595959' d='M6 8.825l-4.475-4.5L2.45 3.4 6 6.975 9.55 3.4l.925.925L6 8.825z'/%3E%3C/svg%3E");
            background-repeat: no-repeat;
            background-position: right 12px center;
            padding-right: 32px;
        }
        
        .form-help {
            margin-top: 4px;
            font-size: ca;
            color: var(--text-tertiary);
        }
        
        .form-error {
            color: var(--danger-color);
            font-size: 12px;
            margin-top: 4px;
        }
        /* 状态标签 */
        
        .status-badge {
            display: inline-flex;
            align-items: center;
            padding: 0 8px;
            height: 22px;
            font-size: 12px;
            border-radius: 11px;
        }
        
        .status-active {
            background-color: rgba(82, 196, 26, 0.1);
            color: var(--success-color);
        }
        
        .status-inactive {
            background-color: rgba(140, 140, 140, 0.1);
            color: var(--text-tertiary);
        }
        /* 通知提示 */
        
        .toast-container {
            position: fixed;
            top: 16px;
            right: 16px;
            z-index: 1000;
        }
        
        .toast {
            padding: 12px 16px;
            border-radius: var(--radius);
            background-color: var(--container-bg);
            box-shadow: 0 3px 6px -4px rgba(0, 0, 0, 0.12), 0 6px 16px 0 rgba(0, 0, 0, 0.08);
            margin-bottom: 16px;
            display: flex;
            align-items: center;
            min-width: 300px;
            max-width: 400px;
            animation: slideIn 0.3s;
            opacity: 0;
            transform: translateX(100%);
        }
        
        .toast.show {
            opacity: 1;
            transform: translateX(0);
        }
        
        .toast-icon {
            margin-right: 12px;
            font-size: 16px;
        }
        
        .toast-content {
            flex: 1;
        }
        
        .toast-title {
            font-weight: 500;
            margin-bottom: 2px;
        }
        
        .toast-success .toast-icon {
            color: var(--success-color);
        }
        
        .toast-error .toast-icon {
            color: var(--danger-color);
        }
        
        @keyframes slideIn {
            from {
                opacity: 0;
                transform: translateX(100%);
            }
            to {
                opacity: 1;
                transform: translateX(0);
            }
        }
        
        @keyframes slideOut {
            from {
                opacity: 1;
                transform: translateX(0);
            }
            to {
                opacity: 0;
                transform: translateX(100%);
            }
        }
        /* 空状态 */
        
        .empty-state {
            padding: 40px 0;
            text-align: center;
            color: var(--text-tertiary);
        }
        
        .empty-state-icon {
            font-size: 48px;
            margin-bottom: 16px;
            color: #d9d9d9;
        }
        
        .empty-state-text {
            font-size: 14px;
            margin-bottom: 16px;
        }
    </style>
    <!-- 引入字体图标 -->
    <link href="https://cdn.jsdelivr.net/npm/remixicon@2.5.0/fonts/remixicon.css" rel="stylesheet">
</head>

<body>
    <div class="layout">
        <!-- 侧边栏 -->
        <aside class="sidebar">
            <div class="sidebar-logo">
                <img src="data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='32' height='32' viewBox='0 0 24 24' fill='none' stroke='%231890ff' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpath d='M2 9l10 4.5L22 9'/%3E%3Cpath d='M2 14l10 4.5L22 14'/%3E%3Cpath d='M2 4l10 4.5L22 4'/%3E%3C/svg%3E"
                    alt="Logo">
                <h1>电商管理系统</h1>
            </div>
            <nav class="menu">
                <div class="menu-item">
                    <i class="ri-dashboard-line"></i>
                    <span>仪表盘</span>
                </div>
                <div class="menu-item">
                    <i class="ri-store-2-line"></i>
                    <span>商品管理</span>
                </div>
                <div class="menu-item">
                    <i class="ri-user-line"></i>
                    <span>用户管理</span>
                </div>
                <div class="menu-item">
                    <i class="ri-shopping-cart-line"></i>
                    <span>订单管理</span>
                </div>
                <div class="menu-item active">
                    <i class="ri-book-2-line"></i>
                    <span>财务管理</span>
                </div>
                <div class="menu-item">
                    <i class="ri-settings-line"></i>
                    <span>系统设置</span>
                </div>
            </nav>
        </aside>

        <!-- 主内容区域 -->
        <div class="main-content">
            <header class="header">
                <div class="header-title">财务管理</div>
                <div class="user-info">
                    <div class="user-avatar">管</div>
                    <span class="username">管理员</span>
                    <i class="ri-logout-box-r-line"></i>
                </div>
            </header>

            <main class="page-content">
                <div class="page-header">
                    <h1 class="page-title">手工科目管理</h1>
                    <button id="addSubjectBtn" class="btn btn-primary">
                        <i class="ri-add-line"></i>新增科目
                    </button>
                </div>

                <div class="card">
                    <div class="table-container">
                        <table id="subjectTable">
                            <thead>
                                <tr>
                                    <th>科目ID</th>
                                    <th>收支类型</th>
                                    <th>科目名称</th>
                                    <th>状态</th>
                                    <th>操作时间</th>
                                    <th>操作人员</th>
                                    <th>操作</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>1001</td>
                                    <td><span class="tag tag-blue">收入</span></td>
                                    <td>销售收入</td>
                                    <td><span class="status-badge status-active">启用中</span></td>
                                    <td>2025-06-09 14:32:10</td>
                                    <td>管理员</td>
                                    <td class="table-action-group">
                                        <button class="btn btn-text edit-btn" data-id="1001">
                                            <i class="ri-edit-line"></i>编辑
                                        </button>
                                        <button class="btn btn-text btn-danger-text disable-btn" data-id="1001">
                                            <i class="ri-stop-circle-line"></i>停用
                                        </button>
                                    </td>
                                </tr>
                                <tr>
                                    <td>1002</td>
                                    <td><span class="tag tag-red">支出</span></td>
                                    <td>物流费用</td>
                                    <td><span class="status-badge status-active">启用中</span></td>
                                    <td>2025-06-08 09:17:45</td>
                                    <td>管理员</td>
                                    <td class="table-action-group">
                                        <button class="btn btn-text edit-btn" data-id="1002">
                                            <i class="ri-edit-line"></i>编辑
                                        </button>
                                        <button class="btn btn-text btn-danger-text disable-btn" data-id="1002">
                                            <i class="ri-stop-circle-line"></i>停用
                                        </button>
                                    </td>
                                </tr>
                                <tr>
                                    <td>1003</td>
                                    <td><span class="tag tag-blue">收入</span></td>
                                    <td>会员费</td>
                                    <td><span class="status-badge status-inactive">已停用</span></td>
                                    <td>2025-06-05 16:48:23</td>
                                    <td>财务主管</td>
                                    <td class="table-action-group">
                                        <button class="btn btn-text edit-btn" data-id="1003">
                                            <i class="ri-edit-line"></i>编辑
                                        </button>
                                        <button class="btn btn-text enable-btn" data-id="1003">
                                            <i class="ri-play-circle-line"></i>启用
                                        </button>
                                    </td>
                                </tr>
                            </tbody>
                        </table>

                        <!-- 空状态 -->
                        <div id="emptyState" class="empty-state" style="display: none;">
                            <div class="empty-state-icon">
                                <i class="ri-file-list-3-line"></i>
                            </div>
                            <div class="empty-state-text">暂无科目数据</div>
                            <button class="btn btn-primary">
                                <i class="ri-add-line"></i>新增科目
                            </button>
                        </div>

                        <!-- 加载状态 -->
                        <div id="loadingState" class="loading-state">
                            <div>数据加载中...</div>
                        </div>
                    </div>

                    <!-- 分页控件 -->
                    <div class="pagination">
                        <div class="pagination-info">共 10 条记录，每页显示 10 条</div>
                        <button class="pagination-btn" disabled>
                            <i class="ri-arrow-left-s-line"></i>
                        </button>
                        <button class="pagination-btn active">1</button>
                        <button class="pagination-btn">2</button>
                        <button class="pagination-btn">
                            <i class="ri-arrow-right-s-line"></i>
                        </button>
                    </div>
                </div>
            </main>
        </div>
    </div>

    <!-- 新增/编辑科目模态框 -->
    <div class="modal-backdrop" id="subjectModal">
        <div class="modal">
            <div class="modal-header">
                <h3 class="modal-title" id="modalTitle">新增科目</h3>
                <button class="modal-close" id="closeSubjectModal">
                    <i class="ri-close-line"></i>
                </button>
            </div>
            <div class="modal-body">
                <form id="subjectForm">
                    <input type="hidden" id="subjectId">
                    <div class="form-group">
                        <label class="form-label" for="incomeType">收支类型</label>
                        <select class="form-control form-select" id="incomeType" name="incomeType" required>
                            <option value="income">收入</option>
                            <option value="expense">支出</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label class="form-label" for="subjectName">科目名称</label>
                        <input type="text" class="form-control" id="subjectName" name="subjectName" required placeholder="请输入科目名称">
                        <div id="subjectNameError" class="form-error"></div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button class="btn" id="cancelSubjectBtn">取消</button>
                <button class="btn btn-primary" id="saveSubjectBtn">保存</button>
            </div>
        </div>
    </div>

    <!-- 停用确认模态框 -->
    <div class="modal-backdrop" id="disableConfirmModal">
        <div class="modal modal-confirm">
            <div class="modal-header">
                <h3 class="modal-title">确认操作</h3>
                <button class="modal-close" id="closeDisableModal">
                    <i class="ri-close-line"></i>
                </button>
            </div>
            <div class="modal-body">
                <div class="modal-confirm-icon">
                    <i class="ri-error-warning-line"></i>
                </div>
                <div class="modal-confirm-content">
                    <h4 class="modal-confirm-title">确定要停用此科目吗？</h4>
                    <p class="modal-confirm-description">停用后，此科目将不可用于记账，但历史数据将保留。</p>
                </div>
            </div>
            <div class="modal-footer">
                <button class="btn" id="cancelDisableBtn">取消</button>
                <button class="btn btn-primary" id="confirmDisableBtn">确认停用</button>
            </div>
        </div>
    </div>

    <!-- 启用确认模态框 -->
    <div class="modal-backdrop" id="enableConfirmModal">
        <div class="modal modal-confirm">
            <div class="modal-header">
                <h3 class="modal-title">确认操作</h3>
                <button class="modal-close" id="closeEnableModal">
                    <i class="ri-close-line"></i>
                </button>
            </div>
            <div class="modal-body">
                <div class="modal-confirm-icon" style="color: var(--success-color);">
                    <i class="ri-question-line"></i>
                </div>
                <div class="modal-confirm-content">
                    <h4 class="modal-confirm-title">确定要启用此科目吗？</h4>
                    <p class="modal-confirm-description">启用后，此科目将可用于记账系统。</p>
                </div>
            </div>
            <div class="modal-footer">
                <button class="btn" id="cancelEnableBtn">取消</button>
                <button class="btn btn-primary" id="confirmEnableBtn">确认启用</button>
            </div>
        </div>
    </div>

    <!-- 通知提示容器 -->
    <div class="toast-container" id="toastContainer"></div>

    <!-- JavaScript 代码 -->
    <script>
        // 当前登录用户信息（模拟）
        const currentUser = {
            id: 1001,
            name: '管理员',
            role: '系统管理员'
        };

        // 科目数据（模拟后端数据）
        let subjects = [{
            id: 1001,
            type: 'income',
            name: '销售收入',
            status: 'active',
            updateTime: '2025-06-09 14:32:10',
            operator: '管理员'
        }, {
            id: 1002,
            type: 'expense',
            name: '物流费用',
            status: 'active',
            updateTime: '2025-06-08 09:17:45',
            operator: '管理员'
        }, {
            id: 1003,
            type: 'income',
            name: '会员费',
            status: 'inactive',
            updateTime: '2025-06-05 16:48:23',
            operator: '财务主管'
        }];

        // DOM 元素
        const subjectTable = document.getElementById('subjectTable');
        const emptyState = document.getElementById('emptyState');
        const loadingState = document.getElementById('loadingState');
        const addSubjectBtn = document.getElementById('addSubjectBtn');
        const subjectModal = document.getElementById('subjectModal');
        const subjectForm = document.getElementById('subjectForm');
        const modalTitle = document.getElementById('modalTitle');
        const subjectId = document.getElementById('subjectId');
        const incomeType = document.getElementById('incomeType');
        const subjectName = document.getElementById('subjectName');
        const subjectNameError = document.getElementById('subjectNameError');
        const saveSubjectBtn = document.getElementById('saveSubjectBtn');
        const cancelSubjectBtn = document.getElementById('cancelSubjectBtn');
        const closeSubjectModal = document.getElementById('closeSubjectModal');
        const disableConfirmModal = document.getElementById('disableConfirmModal');
        const closeDisableModal = document.getElementById('closeDisableModal');
        const cancelDisableBtn = document.getElementById('cancelDisableBtn');
        const confirmDisableBtn = document.getElementById('confirmDisableBtn');
        const enableConfirmModal = document.getElementById('enableConfirmModal');
        const closeEnableModal = document.getElementById('closeEnableModal');
        const cancelEnableBtn = document.getElementById('cancelEnableBtn');
        const confirmEnableBtn = document.getElementById('confirmEnableBtn');
        const toastContainer = document.getElementById('toastContainer');

        // 当前操作的科目ID
        let currentSubjectId = null;

        // 页面加载时初始化
        document.addEventListener('DOMContentLoaded', () => {
            // 渲染科目列表
            renderSubjectList();

            // 绑定新增科目按钮点击事件
            addSubjectBtn.addEventListener('click', openAddSubjectModal);

            // 绑定表单提交事件
            subjectForm.addEventListener('submit', (e) => {
                e.preventDefault();
                saveSubject();
            });

            // 绑定保存按钮点击事件
            saveSubjectBtn.addEventListener('click', saveSubject);

            // 绑定取消按钮点击事件
            cancelSubjectBtn.addEventListener('click', closeModal);
            closeSubjectModal.addEventListener('click', closeModal);

            // 绑定停用模态框关闭事件
            closeDisableModal.addEventListener('click', () => {
                disableConfirmModal.classList.remove('show');
            });
            cancelDisableBtn.addEventListener('click', () => {
                disableConfirmModal.classList.remove('show');
            });

            // 绑定确认停用按钮点击事件
            confirmDisableBtn.addEventListener('click', disableSubject);

            // 绑定启用模态框关闭事件
            closeEnableModal.addEventListener('click', () => {
                enableConfirmModal.classList.remove('show');
            });
            cancelEnableBtn.addEventListener('click', () => {
                enableConfirmModal.classList.remove('show');
            });

            // 绑定确认启用按钮点击事件
            confirmEnableBtn.addEventListener('click', enableSubject);

            // 绑定编辑和停用按钮点击事件（委托到表格）
            subjectTable.addEventListener('click', handleTableAction);
        });

        // 渲染科目列表
        function renderSubjectList() {
            const tbody = subjectTable.querySelector('tbody');
            tbody.innerHTML = '';

            // 检查是否有数据
            if (subjects.length === 0) {
                emptyState.style.display = 'block';
                subjectTable.style.display = 'none';
                return;
            }

            emptyState.style.display = 'none';
            subjectTable.style.display = 'table';

            // 渲染每一行数据
            subjects.forEach(subject => {
                const tr = document.createElement('tr');

                // 设置科目类型标签样式
                const typeTag = subject.type === 'income' ?
                    '<span class="tag tag-blue">收入</span>' :
                    '<span class="tag tag-red">支出</span>';

                // 设置状态标签
                const statusBadge = subject.status === 'active' ?
                    '<span class="status-badge status-active">启用中</span>' :
                    '<span class="status-badge status-inactive">已停用</span>';

                // 设置操作按钮
                const actionButtons = subject.status === 'active' ?
                    `<button class="btn btn-text edit-btn" data-id="${subject.id}">
                         <i class="ri-edit-line"></i>编辑
                       </button>
                       <button class="btn btn-text btn-danger-text disable-btn" data-id="${subject.id}">
                         <i class="ri-stop-circle-line"></i>停用
                       </button>` :
                    `<button class="btn btn-text edit-btn" data-id="${subject.id}">
                         <i class="ri-edit-line"></i>编辑
                       </button>
                       <button class="btn btn-text enable-btn" data-id="${subject.id}">
                         <i class="ri-play-circle-line"></i>启用
                       </button>`;

                tr.innerHTML = `
                    <td>${subject.id}</td>
                    <td>${typeTag}</td>
                    <td>${subject.name}</td>
                    <td>${statusBadge}</td>
                    <td>${subject.updateTime}</td>
                    <td>${subject.operator}</td>
                    <td class="table-action-group">${actionButtons}</td>
                `;

                tbody.appendChild(tr);
            });
        }

        // 处理表格中的按钮点击事件
        function handleTableAction(e) {
            const target = e.target.closest('button');
            if (!target) return;

            const id = parseInt(target.getAttribute('data-id'));
            currentSubjectId = id;

            if (target.classList.contains('edit-btn')) {
                openEditSubjectModal(id);
            } else if (target.classList.contains('disable-btn')) {
                openDisableConfirmModal(id);
            } else if (target.classList.contains('enable-btn')) {
                openEnableConfirmModal(id);
            }
        }

        // 打开新增科目模态框
        function openAddSubjectModal() {
            modalTitle.textContent = '新增科目';
            subjectId.value = '';
            subjectForm.reset();
            incomeType.disabled = false;
            subjectNameError.textContent = '';

            subjectModal.classList.add('show');
        }

        // 打开编辑科目模态框
        function openEditSubjectModal(id) {
            const subject = subjects.find(s => s.id === id);
            if (!subject) return;

            modalTitle.textContent = '编辑科目';
            subjectId.value = subject.id;
            incomeType.value = subject.type;
            subjectName.value = subject.name;

            // 编辑模式下收支类型不可修改
            incomeType.disabled = true;
            subjectNameError.textContent = '';

            subjectModal.classList.add('show');
        }

        // 打开停用确认模态框
        function openDisableConfirmModal(id) {
            disableConfirmModal.classList.add('show');
        }

        // 打开启用确认模态框
        function openEnableConfirmModal(id) {
            enableConfirmModal.classList.add('show');
        }

        // 关闭科目模态框
        function closeModal() {
            subjectModal.classList.remove('show');
            subjectNameError.textContent = '';
        }

        // 保存科目
        function saveSubject() {
            // 表单验证
            if (!subjectName.value.trim()) {
                subjectNameError.textContent = '科目名称不能为空';
                return;
            }

            // 检查科目名称是否重复
            const nameExists = subjects.some(s =>
                s.name === subjectName.value.trim() &&
                (!subjectId.value || s.id !== parseInt(subjectId.value))
            );

            if (nameExists) {
                subjectNameError.textContent = '科目名称已存在';
                return;
            }

            // 获取当前时间
            const now = new Date();
            const year = now.getFullYear();
            const month = String(now.getMonth() + 1).padStart(2, '0');
            const day = String(now.getDate()).padStart(2, '0');
            const hours = String(now.getHours()).padStart(2, '0');
            const minutes = String(now.getMinutes()).padStart(2, '0');
            const seconds = String(now.getSeconds()).padStart(2, '0');
            const currentTime = `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;

            if (subjectId.value) {
                // 编辑现有科目
                const index = subjects.findIndex(s => s.id === parseInt(subjectId.value));
                if (index !== -1) {
                    subjects[index].name = subjectName.value.trim();
                    subjects[index].updateTime = currentTime;
                    subjects[index].operator = currentUser.name;

                    showToast('success', '科目更新成功', '科目信息已成功更新');
                }
            } else {
                // 新增科目
                const newId = getNextId();
                const newSubject = {
                    id: newId,
                    type: incomeType.value,
                    name: subjectName.value.trim(),
                    status: 'active',
                    updateTime: currentTime,
                    operator: currentUser.name
                };

                subjects.push(newSubject);
                showToast('success', '科目创建成功', '新科目已成功添加到系统');
            }

            // 关闭模态框并刷新列表
            closeModal();
            renderSubjectList();
        }

        // 停用科目
        function disableSubject() {
            const index = subjects.findIndex(s => s.id === currentSubjectId);
            if (index !== -1) {
                // 获取当前时间
                const now = new Date();
                const year = now.getFullYear();
                const month = String(now.getMonth() + 1).padStart(2, '0');
                const day = String(now.getDate()).padStart(2, '0');
                const hours = String(now.getHours()).padStart(2, '0');
                const minutes = String(now.getMinutes()).padStart(2, '0');
                const seconds = String(now.getSeconds()).padStart(2, '0');
                const currentTime = `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;

                subjects[index].status = 'inactive';
                subjects[index].updateTime = currentTime;
                subjects[index].operator = currentUser.name;

                showToast('success', '操作成功', '科目已成功停用');
            }

            // 关闭模态框并刷新列表
            disableConfirmModal.classList.remove('show');
            renderSubjectList();
        }

        // 启用科目
        function enableSubject() {
            const index = subjects.findIndex(s => s.id === currentSubjectId);
            if (index !== -1) {
                // 获取当前时间
                const now = new Date();
                const year = now.getFullYear();
                const month = String(now.getMonth() + 1).padStart(2, '0');
                const day = String(now.getDate()).padStart(2, '0');
                const hours = String(now.getHours()).padStart(2, '0');
                const minutes = String(now.getMinutes()).padStart(2, '0');
                const seconds = String(now.getSeconds()).padStart(2, '0');
                const currentTime = `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;

                subjects[index].status = 'active';
                subjects[index].updateTime = currentTime;
                subjects[index].operator = currentUser.name;

                showToast('success', '操作成功', '科目已成功启用');
            }

            // 关闭模态框并刷新列表
            enableConfirmModal.classList.remove('show');
            renderSubjectList();
        }

        // 获取下一个ID
        function getNextId() {
            if (subjects.length === 0) return 1001;
            return Math.max(...subjects.map(s => s.id)) + 1;
        }

        // 显示通知提示
        function showToast(type, title, message) {
            const toast = document.createElement('div');
            toast.className = `toast toast-${type}`;

            const icon = type === 'success' ?
                '<i class="ri-check-line"></i>' :
                '<i class="ri-error-warning-line"></i>';

            toast.innerHTML = `
                <div class="toast-icon">${icon}</div>
                <div class="toast-content">
                    <div class="toast-title">${title}</div>
                    <div class="toast-message">${message}</div>
                </div>
            `;

            toastContainer.appendChild(toast);

            // 显示动画
            setTimeout(() => {
                toast.classList.add('show');
            }, 10);

            // 3秒后自动关闭
            setTimeout(() => {
                toast.style.animation = 'slideOut 0.3s forwards';
                toast.addEventListener('animationend', () => {
                    toast.remove();
                });
            }, 3000);
        }

        // 显示加载状态
        function showLoading() {
            loadingState.style.display = 'block';
            subjectTable.style.display = 'none';
            emptyState.style.display = 'none';
        }

        // 隐藏加载状态
        function hideLoading() {
            loadingState.style.display = 'none';
            if (subjects.length === 0) {
                emptyState.style.display = 'block';
            } else {
                subjectTable.style.display = 'table';
            }
        }

        // 模拟API请求
        function simulateApiRequest(delay = 1000) {
            return new Promise(resolve => {
                setTimeout(resolve, delay);
            });
        }
    </script>
</body>

</html>