<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>电商后台管理系统</title>
    <link rel="stylesheet" href="styles.css">
    <!-- 引入字体图标库 -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        /* 全局样式重置 */
        
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: 'PingFang SC', 'Helvetica Neue', 'Arial', sans-serif;
        }
        
        body {
            background-color: #f5f7fa;
            color: #333;
            line-height: 1.6;
            padding: 20px;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
        }
        /* 数据项样式 */
        
        .data-item {
            background-color: #fff;
            border-radius: 8px;
            box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
            margin-bottom: 20px;
            padding: 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .data-info h3 {
            margin-bottom: 15px;
            color: #333;
            font-size: 18px;
            font-weight: 600;
        }
        
        .data-details {
            display: flex;
            flex-wrap: wrap;
            gap: 20px;
        }
        
        .detail-item {
            min-width: 180px;
        }
        
        .label {
            font-size: 14px;
            color: #666;
            margin-right: 6px;
        }
        
        .value {
            font-size: 14px;
            color: #333;
            font-weight: 500;
        }
        /* 按钮样式 */
        
        .btn {
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 14px;
            font-weight: 500;
            display: flex;
            align-items: center;
            gap: 6px;
        }
        
        .btn i {
            font-size: 14px;
        }
        
        .btn-void {
            background-color: #f56c6c;
            color: white;
        }
        
        .btn-void:hover {
            background-color: #e64242;
        }
        
        .btn-cancel {
            background-color: #f2f3f5;
            color: #606266;
        }
        
        .btn-cancel:hover {
            background-color: #e6e8eb;
        }
        
        .btn-confirm {
            background-color: #f56c6c;
            color: white;
        }
        
        .btn-confirm:hover {
            background-color: #e64242;
        }
        /* 弹框样式 */
        
        .modal-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.5);
            display: flex;
            justify-content: center;
            align-items: center;
            z-index: 1000;
            visibility: hidden;
            opacity: 0;
            transition: all 0.3s ease;
        }
        
        .modal-overlay.active {
            visibility: visible;
            opacity: 1;
        }
        
        .modal-container {
            width: 480px;
            background-color: #fff;
            border-radius: 8px;
            box-shadow: 0 4px 24px rgba(0, 0, 0, 0.15);
            overflow: hidden;
            transform: translateY(20px);
            transition: all 0.3s ease;
        }
        
        .modal-overlay.active .modal-container {
            transform: translateY(0);
        }
        
        .modal-header {
            padding: 20px;
            border-bottom: 1px solid #ebeef5;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .modal-header h3 {
            font-size: 18px;
            font-weight: 600;
            color: #333;
        }
        
        .close-btn {
            background: none;
            border: none;
            cursor: pointer;
            font-size: 18px;
            color: #909399;
        }
        
        .close-btn:hover {
            color: #333;
        }
        
        .modal-body {
            padding: 20px;
        }
        
        .warning-icon {
            color: #f56c6c;
            font-size: 24px;
            display: flex;
            justify-content: center;
            margin-bottom: 15px;
        }
        
        .warning-icon i {
            font-size: 48px;
        }
        
        .warning-text {
            text-align: center;
            color: #606266;
            margin-bottom: 20px;
            line-height: 1.6;
            font-size: 14px;
        }
        
        .data-review {
            background-color: #f8f8f8;
            border-radius: 6px;
            padding: 15px;
            margin-top: 10px;
        }
        
        .data-review h4 {
            color: #333;
            font-size: 16px;
            font-weight: 600;
            margin-bottom: 12px;
        }
        
        .review-item {
            display: flex;
            justify-content: space-between;
            margin-bottom: 10px;
            padding-bottom: 10px;
            border-bottom: 1px solid #ebeef5;
        }
        
        .review-item:last-child {
            margin-bottom: 0;
            padding-bottom: 0;
            border-bottom: none;
        }
        
        .review-label {
            color: #606266;
            font-size: 14px;
        }
        
        .review-value {
            color: #333;
            font-weight: 500;
            font-size: 14px;
        }
        
        .modal-footer {
            padding: 20px;
            border-top: 1px solid #ebeef5;
            display: flex;
            justify-content: flex-end;
            gap: 10px;
        }
    </style>
</head>

<body>
    <div class="container">
        <div class="data-item">
            <div class="data-info">
                <h3>订单数据 #12345</h3>
                <div class="data-details">
                    <div class="detail-item">
                        <span class="label">起始利润:</span>
                        <span class="value">¥5,000.00</span>
                    </div>
                    <div class="detail-item">
                        <span class="label">结束利润:</span>
                        <span class="value">¥8,200.00</span>
                    </div>
                    <div class="detail-item">
                        <span class="label">分摊金额:</span>
                        <span class="value">¥1,600.00</span>
                    </div>
                </div>
            </div>
            <div class="data-actions">
                <button class="btn btn-void" id="voidButton">
                    <i class="fas fa-ban"></i> 作废
                </button>
            </div>
        </div>
    </div>

    <!-- 作废确认弹框 -->
    <div class="modal-overlay" id="voidModal">
        <div class="modal-container">
            <div class="modal-header">
                <h3>确认作废</h3>
                <button class="close-btn" id="closeModal">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="modal-body">
                <div class="warning-icon">
                    <i class="fas fa-exclamation-triangle"></i>
                </div>
                <p class="warning-text">您确定要作废此数据吗？作废后此数据将失效，且操作不可逆。</p>
                <div class="data-review">
                    <h4>数据信息</h4>
                    <div class="review-item">
                        <span class="review-label">起始利润:</span>
                        <span class="review-value" id="startProfit">¥5,000.00</span>
                    </div>
                    <div class="review-item">
                        <span class="review-label">结束利润:</span>
                        <span class="review-value" id="endProfit">¥8,200.00</span>
                    </div>
                    <div class="review-item">
                        <span class="review-label">分摊金额:</span>
                        <span class="review-value" id="allocationAmount">¥1,600.00</span>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button class="btn btn-cancel" id="cancelVoid">取消</button>
                <button class="btn btn-confirm" id="confirmVoid">确认作废</button>
            </div>
        </div>
    </div>

    <script>
        // 获取DOM元素
        const voidButton = document.getElementById('voidButton');
        const voidModal = document.getElementById('voidModal');
        const closeModal = document.getElementById('closeModal');
        const cancelVoid = document.getElementById('cancelVoid');
        const confirmVoid = document.getElementById('confirmVoid');

        // 数据字段
        const startProfit = document.getElementById('startProfit');
        const endProfit = document.getElementById('endProfit');
        const allocationAmount = document.getElementById('allocationAmount');

        // 打开弹框
        function openModal() {
            // 获取当前行的数据，这里可以根据实际情况从表格行获取
            // 本例中使用静态数据作为演示
            const currentData = {
                startProfit: '¥5,000.00',
                endProfit: '¥8,200.00',
                allocationAmount: '¥1,600.00'
            };

            // 更新弹框中的数据显示
            startProfit.textContent = currentData.startProfit;
            endProfit.textContent = currentData.endProfit;
            allocationAmount.textContent = currentData.allocationAmount;

            // 显示弹框
            voidModal.classList.add('active');

            // 阻止背景滚动
            document.body.style.overflow = 'hidden';
        }

        // 关闭弹框
        function closeModalFunc() {
            voidModal.classList.remove('active');

            // 恢复背景滚动
            document.body.style.overflow = 'auto';
        }

        // 确认作废操作
        function confirmVoidFunc() {
            // 在这里执行作废操作，可以通过AJAX发送请求到后端
            console.log('数据已作废');

            // 显示成功提示或执行其他操作
            alert('数据已成功作废');

            // 关闭弹框
            closeModalFunc();

            // 刷新数据列表或更新界面，这部分代码根据实际需求编写
            // refreshDataList();
        }

        // 事件监听
        voidButton.addEventListener('click', openModal);
        closeModal.addEventListener('click', closeModalFunc);
        cancelVoid.addEventListener('click', closeModalFunc);
        confirmVoid.addEventListener('click', confirmVoidFunc);

        // 点击弹框外部区域关闭弹框
        voidModal.addEventListener('click', function(event) {
            if (event.target === voidModal) {
                closeModalFunc();
            }
        });

        // 按ESC键关闭弹框
        document.addEventListener('keydown', function(event) {
            if (event.key === 'Escape' && voidModal.classList.contains('active')) {
                closeModalFunc();
            }
        });
    </script>
</body>

</html>