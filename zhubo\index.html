<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>主播经营日报 - 电商后台管理系统</title>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <style>
         :root {
            /* 品牌色系 - 专业蓝调 */
            --brand-primary: #2563eb;
            --brand-primary-light: #3b82f6;
            --brand-primary-dark: #1d4ed8;
            --brand-secondary: #f59e0b;
            /* 中性色系 - 精心调配的灰度 */
            --neutral-0: #ffffff;
            --neutral-50: #fafbfc;
            --neutral-100: #f4f6f8;
            --neutral-200: #e1e5e9;
            --neutral-300: #c7d2da;
            --neutral-400: #9aa5b1;
            --neutral-500: #7b8794;
            --neutral-600: #616e7c;
            --neutral-700: #52606d;
            --neutral-800: #3e4c59;
            --neutral-900: #1f2937;
            /* 功能色系 */
            --success: #059669;
            --success-light: #d1fae5;
            --warning: #d97706;
            --warning-light: #fef3c7;
            --error: #dc2626;
            --error-light: #fee2e2;
            --info: #0891b2;
            --info-light: #cffafe;
            /* 阴影系统 */
            --shadow-xs: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
            --shadow-sm: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px -1px rgba(0, 0, 0, 0.1);
            --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -2px rgba(0, 0, 0, 0.1);
            --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -4px rgba(0, 0, 0, 0.1);
            --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 8px 10px -6px rgba(0, 0, 0, 0.1);
            /* 边角系统 */
            --radius-sm: 6px;
            --radius-md: 8px;
            --radius-lg: 12px;
            --radius-xl: 16px;
            /* 间距系统 */
            --space-1: 4px;
            --space-2: 8px;
            --space-3: 12px;
            --space-4: 16px;
            --space-5: 20px;
            --space-6: 24px;
            --space-8: 32px;
            --space-10: 40px;
            --space-12: 48px;
            --space-16: 64px;
        }
        
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', system-ui, sans-serif;
            background: linear-gradient(135deg, var(--neutral-50) 0%, var(--neutral-100) 100%);
            color: var(--neutral-900);
            line-height: 1.6;
            font-size: 14px;
            font-weight: 400;
            -webkit-font-smoothing: antialiased;
            -moz-osx-font-smoothing: grayscale;
        }
        
        .app-container {
            min-height: 100vh;
            display: flex;
            flex-direction: column;
        }
        /* 顶部导航栏 */
        
        .app-header {
            background: var(--neutral-0);
            border-bottom: 1px solid var(--neutral-200);
            padding: 0 var(--space-6);
            height: 64px;
            display: flex;
            align-items: center;
            justify-content: space-between;
            position: sticky;
            top: 0;
            z-index: 100;
            backdrop-filter: blur(8px);
        }
        
        .header-left {
            display: flex;
            align-items: center;
            gap: var(--space-6);
        }
        
        .app-logo {
            display: flex;
            align-items: center;
            gap: var(--space-3);
            font-weight: 700;
            font-size: 18px;
            color: var(--brand-primary);
        }
        
        .breadcrumb {
            display: flex;
            align-items: center;
            gap: var(--space-2);
            color: var(--neutral-600);
            font-size: 13px;
        }
        
        .breadcrumb-separator {
            color: var(--neutral-400);
        }
        
        .breadcrumb-current {
            color: var(--neutral-900);
            font-weight: 500;
        }
        /* 主要内容区域 */
        
        .app-main {
            flex: 1;
            padding: var(--space-6);
            max-width: 1440px;
            margin: 0 auto;
            width: 100%;
        }
        /* 页面头部 */
        
        .page-header {
            margin-bottom: var(--space-8);
        }
        
        .page-header-top {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: var(--space-4);
        }
        
        .page-title-group {
            display: flex;
            flex-direction: column;
            gap: var(--space-2);
        }
        
        .page-title {
            font-size: 28px;
            font-weight: 700;
            color: var(--neutral-900);
            letter-spacing: -0.02em;
        }
        
        .page-subtitle {
            font-size: 15px;
            color: var(--neutral-600);
            font-weight: 400;
        }
        
        .page-actions {
            display: flex;
            gap: var(--space-3);
            align-items: center;
        }
        /* 按钮系统 */
        
        .btn {
            display: inline-flex;
            align-items: center;
            justify-content: center;
            gap: var(--space-2);
            padding: var(--space-3) var(--space-4);
            border: 1px solid transparent;
            border-radius: var(--radius-md);
            font-size: 14px;
            font-weight: 500;
            line-height: 1.4;
            cursor: pointer;
            transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
            text-decoration: none;
            white-space: nowrap;
            user-select: none;
            position: relative;
            overflow: hidden;
        }
        
        .btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
        }
        
        .btn-primary {
            background: var(--brand-primary);
            color: var(--neutral-0);
            box-shadow: var(--shadow-sm);
        }
        
        .btn-primary:hover:not(:disabled) {
            background: var(--brand-primary-dark);
            box-shadow: var(--shadow-md);
            transform: translateY(-1px);
        }
        
        .btn-secondary {
            background: var(--neutral-0);
            color: var(--neutral-700);
            border-color: var(--neutral-300);
            box-shadow: var(--shadow-xs);
        }
        
        .btn-secondary:hover:not(:disabled) {
            background: var(--neutral-50);
            border-color: var(--neutral-400);
            box-shadow: var(--shadow-sm);
        }
        
        .btn-ghost {
            background: transparent;
            color: var(--neutral-600);
            border-color: transparent;
        }
        
        .btn-ghost:hover:not(:disabled) {
            background: var(--neutral-100);
            color: var(--neutral-700);
        }
        
        .btn-sm {
            padding: var(--space-2) var(--space-3);
            font-size: 13px;
        }
        
        .btn-lg {
            padding: var(--space-4) var(--space-6);
            font-size: 15px;
        }
        
        .btn-icon-only {
            padding: var(--space-3);
            width: 40px;
            height: 40px;
        }
        /* 卡片系统 */
        
        .card {
            background: var(--neutral-0);
            border: 1px solid var(--neutral-200);
            border-radius: var(--radius-lg);
            box-shadow: var(--shadow-sm);
            overflow: hidden;
            transition: all 0.2s ease;
        }
        
        .card:hover {
            box-shadow: var(--shadow-md);
        }
        
        .card-header {
            padding: var(--space-6);
            border-bottom: 1px solid var(--neutral-200);
            background: var(--neutral-50);
        }
        
        .card-body {
            padding: var(--space-6);
        }
        
        .card-footer {
            padding: var(--space-6);
            border-top: 1px solid var(--neutral-200);
            background: var(--neutral-50);
        }
        /* 主要内容卡片 */
        
        .main-card {
            background: var(--neutral-0);
            border-radius: var(--radius-xl);
            box-shadow: var(--shadow-lg);
            overflow: hidden;
            min-height: 600px;
            display: flex;
            flex-direction: column;
        }
        
        .main-card-header {
            padding: var(--space-6) var(--space-8);
            background: linear-gradient(135deg, var(--neutral-50) 0%, var(--neutral-100) 100%);
            border-bottom: 1px solid var(--neutral-200);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .main-card-body {
            padding: var(--space-6);
            display: flex;
            flex-direction: column;
            flex: 1;
        }
        
        .placeholder-content {
            text-align: center;
            max-width: 400px;
        }
        
        .placeholder-icon {
            width: 80px;
            height: 80px;
            background: linear-gradient(135deg, var(--brand-primary) 0%, var(--brand-primary-light) 100%);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto var(--space-6);
            color: var(--neutral-0);
            font-size: 32px;
        }
        
        .placeholder-title {
            font-size: 18px;
            font-weight: 600;
            color: var(--neutral-900);
            margin-bottom: var(--space-3);
        }
        
        .placeholder-description {
            color: var(--neutral-600);
            line-height: 1.6;
            margin-bottom: var(--space-6);
        }
        /* 设置按钮特殊样式 */
        
        .settings-trigger {
            position: relative;
            background: linear-gradient(135deg, var(--brand-primary) 0%, var(--brand-primary-light) 100%);
            color: var(--neutral-0);
            border: none;
            padding: var(--space-3) var(--space-5);
            border-radius: var(--radius-lg);
            font-weight: 500;
            box-shadow: var(--shadow-md);
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }
        
        .settings-trigger:hover {
            transform: translateY(-2px);
            box-shadow: var(--shadow-xl);
        }
        
        .settings-trigger::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(135deg, rgba(255, 255, 255, 0.2) 0%, rgba(255, 255, 255, 0) 100%);
            border-radius: var(--radius-lg);
            opacity: 0;
            transition: opacity 0.2s ease;
        }
        
        .settings-trigger:hover::before {
            opacity: 1;
        }
        /* 模态框系统 */
        
        .modal-overlay {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(15, 23, 42, 0.6);
            backdrop-filter: blur(8px);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1000;
            opacity: 0;
            visibility: hidden;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }
        
        .modal-overlay.active {
            opacity: 1;
            visibility: visible;
        }
        
        .modal {
            background: var(--neutral-0);
            border-radius: var(--radius-xl);
            box-shadow: var(--shadow-xl);
            width: 95%;
            max-width: 1200px;
            max-height: 90vh;
            display: flex;
            flex-direction: column;
            overflow: hidden;
            transform: scale(0.95) translateY(20px);
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }
        
        .modal-overlay.active .modal {
            transform: scale(1) translateY(0);
        }
        
        .modal-header {
            padding: var(--space-8);
            border-bottom: 1px solid var(--neutral-200);
            background: linear-gradient(135deg, var(--neutral-50) 0%, var(--neutral-100) 100%);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .modal-title {
            font-size: 24px;
            font-weight: 700;
            color: var(--neutral-900);
            display: flex;
            align-items: center;
            gap: var(--space-3);
        }
        
        .modal-close {
            background: var(--neutral-100);
            color: var(--neutral-600);
            border: none;
            width: 40px;
            height: 40px;
            border-radius: var(--radius-md);
            cursor: pointer;
            transition: all 0.2s ease;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .modal-close:hover {
            background: var(--neutral-200);
            color: var(--neutral-800);
        }
        /* Tab系统 */
        
        .tab-nav {
            display: flex;
            background: var(--neutral-100);
            border-bottom: 1px solid var(--neutral-200);
            overflow-x: auto;
        }
        
        .tab-button {
            background: none;
            border: none;
            padding: var(--space-4) var(--space-6);
            font-size: 14px;
            font-weight: 500;
            color: var(--neutral-600);
            cursor: pointer;
            border-bottom: 3px solid transparent;
            transition: all 0.2s ease;
            white-space: nowrap;
            position: relative;
        }
        
        .tab-button:hover {
            color: var(--neutral-800);
            background: var(--neutral-50);
        }
        
        .tab-button.active {
            color: var(--brand-primary);
            background: var(--neutral-0);
            border-bottom-color: var(--brand-primary);
        }
        
        .tab-content {
            flex: 1;
            overflow-y: auto;
        }
        
        .tab-panel {
            display: none;
            padding: var(--space-8);
            height: 100%;
        }
        
        .tab-panel.active {
            display: block;
        }
        /* 工具栏 */
        
        .toolbar {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: var(--space-6);
            gap: var(--space-4);
        }
        
        .toolbar-left {
            display: flex;
            align-items: center;
            gap: var(--space-4);
        }
        
        .toolbar-right {
            display: flex;
            align-items: center;
            gap: var(--space-3);
        }
        /* 表格系统 */
        
        .table-container {
            border-radius: var(--radius-lg);
            overflow: hidden;
            border: 1px solid var(--neutral-200);
            background: var(--neutral-0);
        }
        
        .data-table {
            width: 100%;
            border-collapse: collapse;
        }
        
        .data-table th {
            background: var(--neutral-50);
            padding: var(--space-4) var(--space-6);
            text-align: left;
            font-weight: 600;
            font-size: 13px;
            color: var(--neutral-700);
            text-transform: uppercase;
            letter-spacing: 0.05em;
            border-bottom: 1px solid var(--neutral-200);
        }
        
        .data-table td {
            padding: var(--space-5) var(--space-6);
            border-bottom: 1px solid var(--neutral-100);
            font-size: 14px;
            color: var(--neutral-900);
        }
        
        .data-table tr:hover {
            background: var(--neutral-50);
        }
        
        .data-table tr:last-child td {
            border-bottom: none;
        }
        /* 状态标签 */
        
        .status-badge {
            display: inline-flex;
            align-items: center;
            gap: var(--space-1);
            padding: var(--space-1) var(--space-3);
            border-radius: var(--radius-sm);
            font-size: 12px;
            font-weight: 500;
            text-transform: uppercase;
            letter-spacing: 0.05em;
        }
        
        .status-active {
            background: var(--success-light);
            color: var(--success);
        }
        
        .status-inactive {
            background: var(--error-light);
            color: var(--error);
        }
        
        .status-badge::before {
            content: '';
            width: 6px;
            height: 6px;
            border-radius: 50%;
            background: currentColor;
        }
        /* 操作按钮组 */
        
        .action-group {
            display: flex;
            gap: var(--space-2);
        }
        
        .action-btn {
            padding: var(--space-2) var(--space-3);
            border: none;
            border-radius: var(--radius-sm);
            font-size: 12px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.2s ease;
            display: flex;
            align-items: center;
            gap: var(--space-1);
        }
        
        .action-btn-edit {
            background: var(--info-light);
            color: var(--info);
        }
        
        .action-btn-edit:hover {
            color: var(--info);
        }
        
        .action-btn-toggle {
            background: var(--warning-light);
            color: var(--warning);
        }
        
        .action-btn-toggle:hover {
            background: var(--warning);
            color: var(--neutral-0);
        }
        
        .action-btn-delete {
            background: var(--error-light);
            color: var(--error);
        }
        
        .action-btn-delete:hover {
            color: var(--error);
        }
        
        .action-btn-warning:hover {
            color: var(--warning);
        }
        
        .action-btn-success:hover {
            color: var(--success);
        }
        
        .action-tooltip {
            position: absolute;
            bottom: 100%;
            left: 50%;
            transform: translateX(-50%);
            background: var(--neutral-800);
            color: var(--neutral-0);
            padding: 4px 8px;
            border-radius: var(--radius-sm);
            font-size: 11px;
            white-space: nowrap;
            opacity: 0;
            visibility: hidden;
            transition: all 0.2s;
            margin-bottom: 4px;
        }
        
        .action-btn:hover .action-tooltip {
            opacity: 1;
            visibility: visible;
        }
        /* 表单系统 */
        
        .form-modal {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(15, 23, 42, 0.6);
            backdrop-filter: blur(8px);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1001;
            opacity: 0;
            visibility: hidden;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }
        
        .form-modal.active {
            opacity: 1;
            visibility: visible;
        }
        
        .form-container {
            background: var(--neutral-0);
            border-radius: var(--radius-xl);
            box-shadow: var(--shadow-xl);
            width: 95%;
            max-width: 500px;
            max-height: 80vh;
            overflow-y: auto;
            transform: scale(0.95) translateY(20px);
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }
        
        .form-modal.active .form-container {
            transform: scale(1) translateY(0);
        }
        
        .form-header {
            padding: var(--space-6);
            border-bottom: 1px solid var(--neutral-200);
            background: linear-gradient(135deg, var(--neutral-50) 0%, var(--neutral-100) 100%);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .form-title {
            font-size: 18px;
            font-weight: 600;
            color: var(--neutral-900);
        }
        
        .form-body {
            padding: var(--space-6);
        }
        
        .form-group {
            margin-bottom: var(--space-5);
        }
        
        .form-label {
            display: block;
            margin-bottom: var(--space-2);
            font-weight: 500;
            font-size: 14px;
            color: var(--neutral-700);
        }
        
        .form-label .required {
            color: var(--error);
            margin-left: var(--space-1);
        }
        
        .form-input,
        .form-select {
            width: 100%;
            padding: var(--space-3) var(--space-4);
            border: 1px solid var(--neutral-300);
            border-radius: var(--radius-md);
            font-size: 14px;
            background: var(--neutral-0);
            transition: all 0.2s ease;
        }
        
        .form-input:focus,
        .form-select:focus {
            outline: none;
            border-color: var(--brand-primary);
            box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
        }
        
        .form-footer {
            padding: var(--space-6);
            border-top: 1px solid var(--neutral-200);
            background: var(--neutral-50);
            display: flex;
            justify-content: flex-end;
            gap: var(--space-3);
        }
        /* 空状态 */
        
        .empty-state {
            text-align: center;
            padding: var(--space-16) var(--space-6);
        }
        
        .empty-icon {
            width: 64px;
            height: 64px;
            background: var(--neutral-100);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto var(--space-4);
            color: var(--neutral-400);
            font-size: 24px;
        }
        
        .empty-title {
            font-size: 16px;
            font-weight: 600;
            color: var(--neutral-900);
            margin-bottom: var(--space-2);
        }
        
        .empty-description {
            color: var(--neutral-600);
            font-size: 14px;
        }
        /* 通知系统 */
        
        .notification {
            position: fixed;
            top: var(--space-6);
            right: var(--space-6);
            background: var(--neutral-0);
            border: 1px solid var(--neutral-200);
            border-radius: var(--radius-lg);
            box-shadow: var(--shadow-xl);
            padding: var(--space-4) var(--space-5);
            min-width: 300px;
            z-index: 10000;
            transform: translateX(100%);
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }
        
        .notification.show {
            transform: translateX(0);
        }
        
        .notification-success {
            border-left: 4px solid var(--success);
        }
        
        .notification-error {
            border-left: 4px solid var(--error);
        }
        
        .notification-warning {
            border-left: 4px solid var(--warning);
        }
        
        .notification-content {
            display: flex;
            align-items: center;
            gap: var(--space-3);
        }
        
        .notification-icon {
            width: 20px;
            height: 20px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
            color: var(--neutral-0);
        }
        
        .notification-success .notification-icon {
            background: var(--success);
        }
        
        .notification-error .notification-icon {
            background: var(--error);
        }
        
        .notification-warning .notification-icon {
            background: var(--warning);
        }
        
        .notification-message {
            font-size: 14px;
            font-weight: 500;
            color: var(--neutral-900);
        }
        /* 响应式设计 */
        
        @media (max-width: 1024px) {
            .app-main {
                padding: var(--space-4);
            }
            .modal {
                width: 98%;
                max-height: 95vh;
            }
        }
        
        @media (max-width: 768px) {
            .app-header {
                padding: 0 var(--space-4);
            }
            .page-header-top {
                flex-direction: column;
                gap: var(--space-4);
                align-items: stretch;
            }
            .toolbar {
                flex-direction: column;
                gap: var(--space-4);
                align-items: stretch;
            }
            .toolbar-left,
            .toolbar-right {
                justify-content: space-between;
            }
            .tab-nav {
                overflow-x: auto;
            }
            .data-table {
                font-size: 13px;
            }
            .data-table th,
            .data-table td {
                padding: var(--space-3) var(--space-4);
            }
        }
        /* 滚动条美化 */
        
         ::-webkit-scrollbar {
            width: 6px;
            height: 6px;
        }
        
         ::-webkit-scrollbar-track {
            background: var(--neutral-100);
        }
        
         ::-webkit-scrollbar-thumb {
            background: var(--neutral-300);
            border-radius: 3px;
        }
        
         ::-webkit-scrollbar-thumb:hover {
            background: var(--neutral-400);
        }
        /* 加载状态 */
        
        .loading {
            display: inline-flex;
            align-items: center;
            gap: var(--space-2);
        }
        
        .loading-spinner {
            width: 16px;
            height: 16px;
            border: 2px solid var(--neutral-300);
            border-top: 2px solid var(--brand-primary);
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }
        
        @keyframes spin {
            0% {
                transform: rotate(0deg);
            }
            100% {
                transform: rotate(360deg);
            }
        }
        /* 微交互动画 */
        
        @keyframes fadeIn {
            from {
                opacity: 0;
                transform: translateY(10px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }
        
        .fade-in {
            animation: fadeIn 0.3s ease-out;
        }
        /* 高级视觉效果 */
        
        .glass-effect {
            background: rgba(255, 255, 255, 0.8);
            backdrop-filter: blur(12px);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        
        .gradient-border {
            position: relative;
            background: var(--neutral-0);
            border-radius: var(--radius-lg);
        }
        
        .gradient-border::before {
            content: '';
            position: absolute;
            inset: 0;
            padding: 1px;
            background: linear-gradient(135deg, var(--brand-primary), var(--brand-secondary));
            border-radius: inherit;
            mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
            mask-composite: xor;
        }
        /* 添加优化后的表格样式 */
        
        .enhanced-table-container {
            border-radius: var(--radius-lg);
            overflow: hidden;
            border: 1px solid var(--neutral-200);
            background: var(--neutral-0);
            margin-bottom: var(--space-6);
            box-shadow: var(--shadow-sm);
            width: 100%;
            overflow-x: auto;
            position: relative;
        }
        
        .enhanced-data-table {
            width: 100%;
            border-collapse: separate;
            border-spacing: 0;
            table-layout: fixed;
        }
        
        .enhanced-data-table th {
            background: var(--neutral-50);
            padding: var(--space-3) var(--space-2);
            font-weight: 600;
            font-size: 12px;
            color: var(--neutral-600);
            border-bottom: 1px solid var(--neutral-200);
            text-align: center;
            position: sticky;
            top: 0;
            z-index: 10;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            user-select: none;
        }
        
        .enhanced-data-table th.sortable {
            cursor: pointer;
        }
        
        .enhanced-data-table th.sortable:hover {
            background: var(--neutral-100);
        }
        
        .enhanced-data-table th.sortable::after {
            content: '';
            display: none;
        }
        
        .enhanced-data-table th.sorted-asc::after {
            content: '';
            display: none;
        }
        
        .enhanced-data-table th.sorted-desc::after {
            content: '';
            display: none;
        }
        
        .enhanced-data-table td {
            padding: var(--space-3) var(--space-2);
            border-bottom: 1px solid var(--neutral-100);
            text-align: center;
            vertical-align: middle;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }
        /* 列宽调整手柄 */
        
        .column-resizer {
            position: absolute;
            top: 0;
            right: 0;
            width: 8px;
            height: 100%;
            cursor: col-resize;
            z-index: 20;
        }
        /* 拖动指示器 */
        
        .resizer-indicator {
            position: absolute;
            top: 0;
            width: 2px;
            height: 100%;
            background-color: var(--brand-primary);
            z-index: 30;
            display: none;
        }
        /* 表头容器 */
        
        .th-container {
            position: relative;
            padding-right: 8px;
            overflow: hidden;
            text-overflow: ellipsis;
            width: 100%;
            height: 100%;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .enhanced-data-table tr:hover {
            background: var(--neutral-50);
        }
        
        .enhanced-data-table tr:last-child td {
            border-bottom: none;
        }
        
        .enhanced-data-table tr:nth-child(even) {
            background-color: var(--neutral-50);
        }
        
        .enhanced-data-table tr:nth-child(odd) {
            background-color: var(--neutral-0);
        }
        /* 数据单元格样式 */
        
        .cell-primary {
            font-weight: 500;
            color: var(--neutral-900);
        }
        
        .cell-secondary {
            font-size: 12px;
            color: var(--neutral-500);
            display: block;
            margin-top: 2px;
        }
        
        .cell-success {
            font-weight: 600;
            color: var(--success);
        }
        
        .cell-info {
            font-weight: 600;
            color: var(--info);
        }
        
        .cell-highlight {
            font-weight: 600;
            color: var(--brand-primary);
        }
        
        .cell-wrapper {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
        }
        
        .pill {
            display: inline-block;
            padding: 2px 8px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: 500;
            line-height: 1.4;
        }
        
        .pill-blue {
            background: var(--info-light);
            color: var(--info);
        }
        
        .pill-purple {
            background: #f0e7fe;
            color: #7e3af2;
        }
        
        .time-range {
            font-weight: 500;
            text-align: center;
            padding: 3px 8px;
            background: var(--neutral-50);
            border-radius: 4px;
            display: inline-block;
        }
        
        .percentage-bar {
            width: 60px;
            height: 4px;
            background: var(--neutral-200);
            border-radius: 2px;
            overflow: hidden;
            margin: 4px auto 0;
        }
        
        .percentage-value {
            height: 100%;
            border-radius: 2px;
        }
        
        .percentage-low {
            background: var(--error);
        }
        
        .percentage-medium {
            background: var(--warning);
        }
        
        .percentage-high {
            background: var(--success);
        }
        /* 优化操作按钮 */
        
        .action-group {
            display: flex;
            gap: var(--space-1);
            justify-content: center;
        }
        
        .action-btn {
            width: 28px;
            height: 28px;
            display: flex;
            align-items: center;
            justify-content: center;
            border: none;
            border-radius: var(--radius-md);
            background: transparent;
            color: var(--neutral-600);
            transition: all 0.2s;
            position: relative;
            cursor: pointer;
        }
        
        .action-btn:hover {
            background: var(--neutral-100);
            color: var(--neutral-800);
        }
        
        .action-btn-edit:hover {
            color: var(--info);
        }
        
        .action-btn-delete:hover {
            color: var(--error);
        }
        
        .action-btn-warning:hover {
            color: var(--warning);
        }
        
        .action-btn-success:hover {
            color: var(--success);
        }
        
        .action-tooltip {
            position: absolute;
            bottom: 100%;
            left: 50%;
            transform: translateX(-50%);
            background: var(--neutral-800);
            color: var(--neutral-0);
            padding: 4px 8px;
            border-radius: var(--radius-sm);
            font-size: 11px;
            white-space: nowrap;
            opacity: 0;
            visibility: hidden;
            transition: all 0.2s;
            margin-bottom: 4px;
        }
        
        .action-btn:hover .action-tooltip {
            opacity: 1;
            visibility: visible;
        }
        /* 优化筛选区域 */
        
        .filter-section {
            display: flex;
            padding: var(--space-4) var(--space-6);
            background: var(--neutral-50);
            border-radius: var(--radius-lg);
            margin-bottom: var(--space-5);
            border: 1px solid var(--neutral-200);
            align-items: center;
            justify-content: space-between;
            flex-wrap: wrap;
            gap: var(--space-4);
        }
        
        .filter-left {
            display: flex;
            gap: var(--space-4);
            align-items: center;
            flex-wrap: wrap;
        }
        
        .filter-right {
            display: flex;
            gap: var(--space-3);
            align-items: center;
        }
        
        .filter-group {
            display: flex;
            align-items: center;
            gap: var(--space-2);
        }
        
        .filter-label {
            font-weight: 500;
            color: var(--neutral-700);
            white-space: nowrap;
        }
        /* 优化按钮样式 */
        
        .btn-action-primary {
            background: var(--brand-primary);
            color: white;
            border: none;
            padding: var(--space-2) var(--space-4);
            border-radius: var(--radius-md);
            display: inline-flex;
            align-items: center;
            gap: var(--space-2);
            font-weight: 500;
            cursor: pointer;
            transition: all 0.2s;
            box-shadow: var(--shadow-sm);
        }
        
        .btn-action-primary:hover {
            background: var(--brand-primary-dark);
            box-shadow: var(--shadow-md);
        }
        
        .btn-action-secondary {
            background: white;
            color: var(--neutral-700);
            border: 1px solid var(--neutral-300);
            padding: var(--space-2) var(--space-4);
            border-radius: var(--radius-md);
            display: inline-flex;
            align-items: center;
            gap: var(--space-2);
            font-weight: 500;
            cursor: pointer;
            transition: all 0.2s;
        }
        
        .btn-action-secondary:hover {
            background: var(--neutral-50);
            border-color: var(--neutral-400);
        }
        
        .btn-icon {
            width: 32px;
            height: 32px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: var(--radius-md);
            background: transparent;
            color: var(--neutral-600);
            border: 1px solid var(--neutral-300);
            cursor: pointer;
            transition: all 0.2s;
        }
        
        .btn-icon:hover {
            background: var(--neutral-50);
            color: var(--neutral-800);
        }
        /* 优化分页控制 */
        
        .pagination-control {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .pagination {
            display: flex;
            align-items: center;
            gap: var(--space-1);
        }
        
        .page-btn {
            min-width: 32px;
            height: 32px;
            display: flex;
            align-items: center;
            justify-content: center;
            border: 1px solid var(--neutral-200);
            background: var(--neutral-0);
            color: var(--neutral-700);
            border-radius: var(--radius-md);
            cursor: pointer;
            transition: all 0.2s;
        }
        
        .page-btn:hover:not(.page-btn-active):not(:disabled) {
            background: var(--neutral-50);
            border-color: var(--neutral-300);
        }
        
        .page-btn-active {
            background: var(--brand-primary);
            color: white;
            border-color: var(--brand-primary);
        }
        
        .page-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
        }
    </style>
</head>

<body>
    <div class="app-container">
        <!-- 顶部导航栏 -->
        <header class="app-header">
            <div class="header-left">
                <div class="app-logo">
                    <i class="fas fa-chart-line"></i> 电商管理系统
                </div>
                <nav class="breadcrumb">
                    <span>数据分析</span>
                    <i class="fas fa-chevron-right breadcrumb-separator"></i>
                    <span>主播管理</span>
                    <i class="fas fa-chevron-right breadcrumb-separator"></i>
                    <span class="breadcrumb-current">经营日报</span>
                </nav>
            </div>
        </header>

        <!-- 主要内容区域 -->
        <main class="app-main">
            <!-- 页面头部 -->
            <div class="page-header">
                <div class="page-header-top">
                    <div class="page-title-group">
                        <h1 class="page-title">主播经营日报</h1>
                        <p class="page-subtitle">管理主播业务数据，优化运营策略，提升整体绩效</p>
                    </div>
                    <div class="page-actions">
                        <a href="#" class="btn btn-secondary" style="display:flex; align-items:center; gap:8px;">
                            <i class="fas fa-question-circle"></i>
                            <span>使用指南</span>
                        </a>
                        <button class="btn settings-trigger" onclick="openSettingsModal()">
                            <i class="fas fa-cog"></i>
                            基础设置
                        </button>
                    </div>
                </div>
            </div>

            <!-- 主要内容卡片 -->
            <div class="main-card">
                <div class="main-card-header">
                    <div style="display:flex; align-items:center; gap:var(--space-4);">
                        <h2 class="main-card-title" style="display:flex; align-items:center; gap:var(--space-2); font-size:18px; font-weight:600;">
                            <i class="fas fa-chart-bar" style="color:var(--brand-primary);"></i>
                            <span>主播经营数据</span>
                        </h2>
                    </div>
                </div>
                <div class="main-card-body">
                    <!-- 筛选区域 -->
                    <div class="filter-section">
                        <div class="filter-left">
                            <div class="filter-group">
                                <span class="filter-label">日期:</span>
                                <div style="display: flex; align-items: center; gap: 4px;">
                                    <input type="date" class="form-input" id="dateFilterStart" style="width: 140px;">
                                    <span>至</span>
                                    <input type="date" class="form-input" id="dateFilterEnd" style="width: 140px;">
                                </div>
                            </div>
                            <div class="filter-group">
                                <span class="filter-label">主播:</span>
                                <div class="search-dropdown-container" style="position: relative; width: 180px;">
                                    <input type="text" class="form-input" id="anchorFilter" placeholder="搜索主播" style="width: 100%; padding-right: 30px;" onclick="toggleAnchorDropdown()">
                                    <i class="fas fa-search" style="position: absolute; right: 10px; top: 50%; transform: translateY(-50%); color: var(--neutral-400);"></i>
                                    <div class="dropdown-menu" id="anchorDropdown" style="display: none; position: absolute; top: 100%; left: 0; width: 100%; max-height: 300px; overflow-y: auto; background: var(--neutral-0); border: 1px solid var(--neutral-200); border-radius: var(--radius-md); z-index: 1000; margin-top: 4px; box-shadow: var(--shadow-xl);"></div>
                                </div>
                            </div>
                        </div>
                        <div class="filter-right">
                            <button class="btn-action-primary" onclick="openBroadcastForm('add')">
                                <i class="fas fa-plus"></i>
                                新增填报
                            </button>
                            <div class="btn-group" style="display: flex; gap: 1px; border-radius: var(--radius-md); overflow: hidden;">
                                <button class="btn-action-secondary" onclick="openImportModal()" style="border-top-right-radius: 0; border-bottom-right-radius: 0; margin-right: -1px;">
                                    <i class="fas fa-file-import"></i>
                                    导入
                                </button>
                                <button class="btn-action-secondary" onclick="exportData()" style="border-top-left-radius: 0; border-bottom-left-radius: 0;">
                                    <i class="fas fa-file-export"></i>
                                    导出
                                </button>
                            </div>
                            <button class="btn-action-secondary" id="batchDeleteBtn" style="display:none; color: var(--error);" onclick="confirmBatchDelete()">
                                <i class="fas fa-trash-alt"></i>
                                批量删除
                            </button>
                        </div>
                    </div>

                    <!-- 增强表格容器 -->
                    <div class="enhanced-table-container" style="overflow-x: auto; flex: 1; position: relative;">
                        <div class="resizer-indicator"></div>
                        <table class="enhanced-data-table">
                            <thead>
                                <tr>
                                    <th width="40px">
                                        <input type="checkbox" id="selectAllCheckbox" onchange="toggleSelectAll()" style="cursor: pointer;">
                                    </th>
                                    <th width="100px" class="sortable">
                                        <div class="th-container">填报日期
                                            <div class="column-resizer" data-column="1"></div>
                                        </div>
                                    </th>
                                    <th width="90px">
                                        <div class="th-container">团队名称
                                            <div class="column-resizer" data-column="2"></div>
                                        </div>
                                    </th>
                                    <th width="90px">
                                        <div class="th-container">主播人员
                                            <div class="column-resizer" data-column="3"></div>
                                        </div>
                                    </th>
                                    <th width="100px">
                                        <div class="th-container">店铺名称
                                            <div class="column-resizer" data-column="4"></div>
                                        </div>
                                    </th>
                                    <th width="180px">
                                        <div class="th-container">直播时段
                                            <div class="column-resizer" data-column="5"></div>
                                        </div>
                                    </th>
                                    <th width="100px">
                                        <div class="th-container">主推产品
                                            <div class="column-resizer" data-column="6"></div>
                                        </div>
                                    </th>
                                    <th width="120px" class="sortable">
                                        <div class="th-container">销售额(元)
                                            <div class="column-resizer" data-column="7"></div>
                                        </div>
                                    </th>
                                    <th width="100px">
                                        <div class="th-container">最高在线
                                            <div class="column-resizer" data-column="8"></div>
                                        </div>
                                    </th>
                                    <th width="100px">
                                        <div class="th-container">平均在线
                                            <div class="column-resizer" data-column="9"></div>
                                        </div>
                                    </th>
                                    <th width="100px">
                                        <div class="th-container">平均单价
                                            <div class="column-resizer" data-column="10"></div>
                                        </div>
                                    </th>
                                    <th width="100px">
                                        <div class="th-container">平均留存率
                                            <div class="column-resizer" data-column="11"></div>
                                        </div>
                                    </th>
                                    <th width="100px">
                                        <div class="th-container">平均互动率
                                            <div class="column-resizer" data-column="12"></div>
                                        </div>
                                    </th>
                                    <th width="100px">
                                        <div class="th-container">单UV价值
                                            <div class="column-resizer" data-column="13"></div>
                                        </div>
                                    </th>
                                    <th width="90px">
                                        <div class="th-container">操作</div>
                                    </th>
                                </tr>
                            </thead>
                            <tbody id="broadcastTableBody">
                                <!-- 数据将通过JavaScript动态生成 -->
                            </tbody>
                        </table>
                    </div>

                    <!-- 分页控制 -->
                    <div class="pagination-control" style="margin-top: var(--space-4);">
                        <div class="pagination-info">
                            共 <span id="totalRecords" class="cell-highlight">0</span> 条记录
                        </div>
                        <div style="display: flex; align-items: center; gap: var(--space-4);">
                            <div class="pagination">
                                <button class="page-btn" id="prevPageBtn" disabled>
                                    <i class="fas fa-chevron-left"></i>
                                </button>
                                <div id="paginationPages" style="display: flex; gap: var(--space-1);"></div>
                                <button class="page-btn" id="nextPageBtn" disabled>
                                    <i class="fas fa-chevron-right"></i>
                                </button>
                            </div>
                            <div class="filter-group">
                                <span class="filter-label">每页显示:</span>
                                <select class="form-select" id="pageSizeSelect" style="width: 80px;">
                                    <option value="10">10条</option>
                                    <option value="20">20条</option>
                                    <option value="50">50条</option>
                                    <option value="100">100条</option>
                                </select>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </main>
    </div>

    <!-- 设置模态框 -->
    <div class="modal-overlay" id="settingsModal">
        <div class="modal">
            <div class="modal-header">
                <h2 class="modal-title">
                    <i class="fas fa-cog"></i> 主播基础设置
                </h2>
                <button class="modal-close" onclick="closeSettingsModal()">
                    <i class="fas fa-times"></i>
                </button>
            </div>

            <!-- Tab导航 -->
            <div class="tab-nav">
                <button class="tab-button active" onclick="switchTab('income-expense')">
                    <i class="fas fa-dollar-sign"></i>
                    收入支出配置
                </button>
                <button class="tab-button" onclick="switchTab('personnel')">
                    <i class="fas fa-users"></i>
                    人员管理
                </button>
                <button class="tab-button" onclick="switchTab('level')">
                    <i class="fas fa-layer-group"></i>
                    级别维护
                </button>
            </div>

            <!-- Tab内容 -->
            <div class="tab-content">
                <!-- 收入支出配置 -->
                <div class="tab-panel active" id="income-expense-panel">
                    <div class="toolbar">
                        <div class="toolbar-left">
                        </div>
                        <div class="toolbar-right">
                            <button class="btn btn-primary" onclick="openIncomeExpenseForm('add')">
                                <i class="fas fa-plus"></i>
                                新增配置
                            </button>
                        </div>
                    </div>

                    <div class="table-container">
                        <table class="data-table">
                            <thead>
                                <tr>
                                    <th>主播级别</th>
                                    <th>收入时薪（元/小时）</th>
                                    <th>支出时薪（元/小时）</th>
                                    <th>状态</th>
                                    <th>操作</th>
                                </tr>
                            </thead>
                            <tbody id="incomeExpenseTableBody">
                                <!-- 数据将通过JavaScript动态生成 -->
                            </tbody>
                        </table>
                    </div>
                </div>

                <!-- 人员管理 -->
                <div class="tab-panel" id="personnel-panel">
                    <div class="toolbar">
                        <div class="toolbar-left">
                        </div>
                        <div class="toolbar-right">
                            <button class="btn btn-primary" onclick="openPersonnelForm('add')">
                                <i class="fas fa-user-plus"></i>
                                新增人员
                            </button>
                        </div>
                    </div>

                    <div class="table-container">
                        <table class="data-table">
                            <thead>
                                <tr>
                                    <th>主播姓名</th>
                                    <th>主播级别</th>
                                    <th>年限工资（元）</th>
                                    <th>入职时间</th>
                                    <th>状态</th>
                                    <th>操作</th>
                                </tr>
                            </thead>
                            <tbody id="personnelTableBody">
                                <!-- 数据将通过JavaScript动态生成 -->
                            </tbody>
                        </table>
                    </div>
                </div>

                <!-- 级别维护 -->
                <div class="tab-panel" id="level-panel">
                    <div class="toolbar">
                        <div class="toolbar-left">
                        </div>
                        <div class="toolbar-right">
                            <button class="btn btn-primary" onclick="openLevelForm('add')">
                                <i class="fas fa-plus"></i>
                                新增级别
                            </button>
                        </div>
                    </div>

                    <div class="table-container">
                        <table class="data-table">
                            <thead>
                                <tr>
                                    <th>级别名称</th>
                                    <th>创建时间</th>
                                    <th>关联人员数</th>
                                    <th>状态</th>
                                    <th>操作</th>
                                </tr>
                            </thead>
                            <tbody id="levelTableBody">
                                <!-- 数据将通过JavaScript动态生成 -->
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 收入支出表单模态框 -->
    <div class="form-modal" id="incomeExpenseFormModal">
        <div class="form-container">
            <div class="form-header">
                <h3 class="form-title" id="incomeExpenseFormTitle">新增收入支出配置</h3>
                <button class="modal-close" onclick="closeIncomeExpenseForm()">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="form-body">
                <form id="incomeExpenseForm">
                    <div class="form-group">
                        <label class="form-label">
                            主播级别
                            <span class="required">*</span>
                        </label>
                        <select class="form-select" id="incomeExpenseLevel" required>
                            <option value="">请选择主播级别</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label class="form-label">
                            收入时薪（元/小时）
                            <span class="required">*</span>
                        </label>
                        <input type="number" class="form-input" id="incomeHourlyRate" placeholder="请输入收入时薪" step="0.01" min="0" required>
                    </div>
                    <div class="form-group">
                        <label class="form-label">
                            支出时薪（元/小时）
                            <span class="required">*</span>
                        </label>
                        <input type="number" class="form-input" id="expenseHourlyRate" placeholder="请输入支出时薪" step="0.01" min="0" required>
                    </div>
                </form>
            </div>
            <div class="form-footer">
                <button class="btn btn-secondary" onclick="closeIncomeExpenseForm()">取消</button>
                <button class="btn btn-primary" onclick="saveIncomeExpense()">
                    <span class="loading" id="incomeExpenseLoading" style="display: none;">
                        <div class="loading-spinner"></div>
                        保存中...
                    </span>
                    <span id="incomeExpenseSaveText">确定保存</span>
                </button>
            </div>
        </div>
    </div>

    <!-- 人员表单模态框 -->
    <div class="form-modal" id="personnelFormModal">
        <div class="form-container">
            <div class="form-header">
                <h3 class="form-title" id="personnelFormTitle">新增主播人员</h3>
                <button class="modal-close" onclick="closePersonnelForm()">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="form-body">
                <form id="personnelForm">
                    <div class="form-group">
                        <label class="form-label">
                            主播姓名
                            <span class="required">*</span>
                        </label>
                        <input type="text" class="form-input" id="personnelName" placeholder="请输入主播姓名" required>
                    </div>
                    <div class="form-group">
                        <label class="form-label">
                            主播级别
                            <span class="required">*</span>
                        </label>
                        <select class="form-select" id="personnelLevel" required>
                            <option value="">请选择主播级别</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label class="form-label">
                            年限工资（元）
                            <span class="required">*</span>
                        </label>
                        <input type="number" class="form-input" id="personnelSalary" placeholder="请输入年限工资" step="0.01" min="0" required>
                    </div>
                    <div class="form-group">
                        <label class="form-label">入职时间</label>
                        <input type="date" class="form-input" id="personnelJoinDate">
                    </div>
                </form>
            </div>
            <div class="form-footer">
                <button class="btn btn-secondary" onclick="closePersonnelForm()">取消</button>
                <button class="btn btn-primary" onclick="savePersonnel()">
                    <span class="loading" id="personnelLoading" style="display: none;">
                        <div class="loading-spinner"></div>
                        保存中...
                    </span>
                    <span id="personnelSaveText">确定保存</span>
                </button>
            </div>
        </div>
    </div>

    <!-- 级别表单模态框 -->
    <div class="form-modal" id="levelFormModal">
        <div class="form-container">
            <div class="form-header">
                <h3 class="form-title" id="levelFormTitle">新增主播级别</h3>
                <button class="modal-close" onclick="closeLevelForm()">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="form-body">
                <form id="levelForm">
                    <div class="form-group">
                        <label class="form-label">
                            级别名称
                            <span class="required">*</span>
                        </label>
                        <input type="text" class="form-input" id="levelName" placeholder="请输入主播级别名称" required>
                    </div>
                    <div class="form-group">
                        <label class="form-label">级别描述</label>
                        <textarea class="form-input" id="levelDescription" placeholder="请输入级别描述（可选）" rows="3"></textarea>
                    </div>
                </form>
            </div>
            <div class="form-footer">
                <button class="btn btn-secondary" onclick="closeLevelForm()">取消</button>
                <button class="btn btn-primary" onclick="saveLevel()">
                    <span class="loading" id="levelLoading" style="display: none;">
                        <div class="loading-spinner"></div>
                        保存中...
                    </span>
                    <span id="levelSaveText">确定保存</span>
                </button>
            </div>
        </div>
    </div>

    <!-- 主播经营数据表单模态框 -->
    <div class="form-modal" id="broadcastFormModal">
        <div class="form-container" style="max-width: 700px;">
            <div class="form-header">
                <h3 class="form-title" id="broadcastFormTitle">新增主播经营数据</h3>
                <button class="modal-close" onclick="closeBroadcastForm()">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="form-body">
                <form id="broadcastForm">
                    <div style="display: grid; grid-template-columns: 1fr 1fr; gap: var(--space-5);">
                        <div class="form-group">
                            <label class="form-label">
                                填报日期
                                <span class="required">*</span>
                            </label>
                            <input type="date" class="form-input" id="broadcastDate" required>
                        </div>
                        <div class="form-group">
                            <label class="form-label">
                                团队名称
                                <span class="required">*</span>
                            </label>
                            <input type="text" class="form-input" id="teamName" placeholder="请输入团队名称" required>
                        </div>
                        <div class="form-group">
                            <label class="form-label">
                                主播人员
                                <span class="required">*</span>
                            </label>
                            <div class="search-dropdown-container" style="position: relative;">
                                <input type="text" class="form-input" id="anchorName" placeholder="请选择主播人员" required onclick="toggleAnchorSelect()">
                                <input type="hidden" id="anchorId">
                                <i class="fas fa-chevron-down" style="position: absolute; right: 10px; top: 50%; transform: translateY(-50%); color: var(--neutral-400);"></i>
                                <div class="dropdown-menu" id="anchorSelect" style="display: none; position: absolute; top: 100%; left: 0; width: 100%; max-height: 300px; overflow-y: auto; background: var(--neutral-0); border: 1px solid var(--neutral-200); border-radius: var(--radius-md); z-index: 1000; margin-top: 4px; box-shadow: var(--shadow-xl);"></div>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="form-label">
                                店铺名称
                                <span class="required">*</span>
                            </label>
                            <input type="text" class="form-input" id="storeName" placeholder="请输入店铺名称" required>
                        </div>
                        <div class="form-group">
                            <label class="form-label">
                                上播时间
                                <span class="required">*</span>
                            </label>
                            <input type="datetime-local" class="form-input" id="startTime" required>
                        </div>
                        <div class="form-group">
                            <label class="form-label">
                                下播时间
                                <span class="required">*</span>
                            </label>
                            <input type="datetime-local" class="form-input" id="endTime" required>
                        </div>
                        <div class="form-group">
                            <label class="form-label">
                                直播主推产品ERP简称
                                <span class="required">*</span>
                            </label>
                            <input type="text" class="form-input" id="productName" placeholder="请输入主推产品ERP简称" required>
                        </div>
                        <div class="form-group">
                            <label class="form-label">
                                直播销售额(元)
                                <span class="required">*</span>
                            </label>
                            <input type="number" class="form-input" id="salesAmount" placeholder="请输入销售额" step="0.01" min="0" required>
                        </div>
                        <div class="form-group">
                            <label class="form-label">
                                最高在线人数
                                <span class="required">*</span>
                            </label>
                            <input type="number" class="form-input" id="maxOnlineUsers" placeholder="请输入最高在线人数" min="0" required>
                        </div>
                        <div class="form-group">
                            <label class="form-label">
                                平均在线人数
                                <span class="required">*</span>
                            </label>
                            <input type="number" class="form-input" id="avgOnlineUsers" placeholder="请输入平均在线人数" min="0" required>
                        </div>
                        <div class="form-group">
                            <label class="form-label">
                                平均单价(元)
                                <span class="required">*</span>
                            </label>
                            <input type="number" class="form-input" id="avgPrice" placeholder="请输入平均单价" step="0.01" min="0" required>
                        </div>
                        <div class="form-group">
                            <label class="form-label">
                                平均留存率(%)
                                <span class="required">*</span>
                            </label>
                            <input type="number" class="form-input" id="retentionRate" placeholder="请输入平均留存率" step="0.01" min="0" max="100" required>
                        </div>
                        <div class="form-group">
                            <label class="form-label">
                                平均互动率(%)
                                <span class="required">*</span>
                            </label>
                            <input type="number" class="form-input" id="interactionRate" placeholder="请输入平均互动率" step="0.01" min="0" max="100" required>
                        </div>
                        <div class="form-group">
                            <label class="form-label">
                                平均单UV价值(元)
                                <span class="required">*</span>
                            </label>
                            <input type="number" class="form-input" id="uvValue" placeholder="请输入平均单UV价值" step="0.01" min="0" required>
                        </div>
                    </div>
                </form>
            </div>
            <div class="form-footer">
                <button class="btn btn-secondary" onclick="closeBroadcastForm()">取消</button>
                <button class="btn btn-primary" onclick="saveBroadcast()">
                    <span class="loading" id="broadcastLoading" style="display: none;">
                        <div class="loading-spinner"></div>
                        保存中...
                    </span>
                    <span id="broadcastSaveText">确定保存</span>
                </button>
            </div>
        </div>
    </div>

    <!-- 导入数据模态框 -->
    <div class="form-modal" id="importModal">
        <div class="form-container">
            <div class="form-header">
                <h3 class="form-title">导入主播经营数据</h3>
                <button class="modal-close" onclick="closeImportModal()">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="form-body">
                <div class="import-steps" style="margin-bottom: var(--space-5);">
                    <div class="step" style="margin-bottom: var(--space-4);">
                        <div style="font-weight: 500; margin-bottom: var(--space-2); display: flex; align-items: center; gap: var(--space-2);">
                            <div style="width: 24px; height: 24px; border-radius: 50%; background: var(--brand-primary); color: white; display: flex; align-items: center; justify-content: center; font-size: 12px;">1</div>
                            <span>下载数据模板</span>
                        </div>
                        <div style="padding-left: 32px;">
                            <button class="btn btn-secondary btn-sm" onclick="downloadTemplate()">
                                <i class="fas fa-download"></i>
                                下载Excel模板
                            </button>
                        </div>
                    </div>
                    <div class="step" style="margin-bottom: var(--space-4);">
                        <div style="font-weight: 500; margin-bottom: var(--space-2); display: flex; align-items: center; gap: var(--space-2);">
                            <div style="width: 24px; height: 24px; border-radius: 50%; background: var(--brand-primary); color: white; display: flex; align-items: center; justify-content: center; font-size: 12px;">2</div>
                            <span>填写数据后上传文件</span>
                        </div>
                        <div style="padding-left: 32px;">
                            <div class="upload-area" style="border: 2px dashed var(--neutral-300); border-radius: var(--radius-lg); padding: var(--space-8); text-align: center; background: var(--neutral-50);">
                                <i class="fas fa-file-upload" style="font-size: 32px; color: var(--neutral-400); margin-bottom: var(--space-3);"></i>
                                <div style="color: var(--neutral-600); margin-bottom: var(--space-3);">点击选择文件或拖拽文件至此处</div>
                                <input type="file" id="fileInput" style="display: none;" accept=".xlsx,.xls,.csv">
                                <button class="btn btn-secondary" onclick="document.getElementById('fileInput').click()">
                                    <i class="fas fa-folder-open"></i>
                                    选择文件
                                </button>
                                <div id="fileName" style="margin-top: var(--space-3); color: var(--neutral-700);"></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="form-footer">
                <button class="btn btn-secondary" onclick="closeImportModal()">取消</button>
                <button class="btn btn-primary" onclick="uploadFile()" id="uploadButton" disabled>
                    <span class="loading" id="importLoading" style="display: none;">
                        <div class="loading-spinner"></div>
                        上传中...
                    </span>
                    <span id="importButtonText">导入数据</span>
                </button>
            </div>
        </div>
    </div>

    <!-- 删除确认模态框 -->
    <div class="form-modal" id="deleteConfirmModal">
        <div class="form-container" style="max-width: 400px;">
            <div class="form-header">
                <h3 class="form-title" id="deleteModalTitle">确认删除</h3>
                <button class="modal-close" onclick="closeDeleteConfirmModal()">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="form-body">
                <p id="deleteConfirmText" style="margin-bottom: var(--space-5);">确定要删除该条主播经营数据吗？此操作不可逆。</p>
            </div>
            <div class="form-footer">
                <button class="btn btn-secondary" onclick="closeDeleteConfirmModal()">取消</button>
                <button class="btn btn-primary" style="background-color: var(--error); border-color: var(--error);" onclick="executeDelete()">
                    <span class="loading" id="deleteLoading" style="display: none;">
                        <div class="loading-spinner"></div>
                        处理中...
                    </span>
                    <span id="deleteButtonText">确认删除</span>
                </button>
            </div>
        </div>
    </div>

    <script>
        // 数据存储
        let anchorLevels = [{
            id: 1,
            name: '初级主播',
            description: '新入职主播，具备基础直播技能',
            status: 'active',
            createdAt: '2024-01-15',
            personnelCount: 5
        }, {
            id: 2,
            name: '中级主播',
            description: '具备一定经验，能够独立完成直播任务',
            status: 'active',
            createdAt: '2024-01-15',
            personnelCount: 3
        }, {
            id: 3,
            name: '高级主播',
            description: '经验丰富，具备带货能力和粉丝号召力',
            status: 'active',
            createdAt: '2024-01-15',
            personnelCount: 2
        }, {
            id: 4,
            name: '金牌主播',
            description: '顶级主播，具备强大的商业价值',
            status: 'inactive',
            createdAt: '2024-02-01',
            personnelCount: 0
        }];

        let incomeExpenseData = [{
            id: 1,
            levelId: 1,
            levelName: '初级主播',
            incomeRate: 50.00,
            expenseRate: 30.00,
            status: 'active'
        }, {
            id: 2,
            levelId: 2,
            levelName: '中级主播',
            incomeRate: 80.00,
            expenseRate: 50.00,
            status: 'active'
        }, {
            id: 3,
            levelId: 3,
            levelName: '高级主播',
            incomeRate: 120.00,
            expenseRate: 80.00,
            status: 'active'
        }];

        let personnelData = [{
            id: 1,
            name: '张小美',
            levelId: 1,
            levelName: '初级主播',
            salary: 5000.00,
            joinDate: '2024-01-20',
            status: 'active'
        }, {
            id: 2,
            name: '李明星',
            levelId: 2,
            levelName: '中级主播',
            salary: 8000.00,
            joinDate: '2023-12-15',
            status: 'active'
        }, {
            id: 3,
            name: '王大咖',
            levelId: 3,
            levelName: '高级主播',
            salary: 12000.00,
            joinDate: '2023-10-10',
            status: 'inactive'
        }, {
            id: 4,
            name: '刘晓雨',
            levelId: 1,
            levelName: '初级主播',
            salary: 4800.00,
            joinDate: '2024-02-01',
            status: 'active'
        }, {
            id: 5,
            name: '陈思思',
            levelId: 2,
            levelName: '中级主播',
            salary: 7500.00,
            joinDate: '2023-11-20',
            status: 'active'
        }];

        // 当前编辑的数据
        let currentEditData = null;
        let currentEditType = null;

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            updatePersonnelCount();
            renderIncomeExpenseTable();
            renderPersonnelTable();
            renderLevelTable();
            updateLevelOptions();

            // 添加表格行的淡入动画
            setTimeout(() => {
                document.querySelectorAll('.data-table tbody tr').forEach((row, index) => {
                    row.style.animationDelay = `${index * 0.05}s`;
                    row.classList.add('fade-in');
                });
            }, 100);
        });

        // 更新人员统计
        function updatePersonnelCount() {
            anchorLevels.forEach(level => {
                level.personnelCount = personnelData.filter(p =>
                    p.levelId === level.id && p.status === 'active'
                ).length;
            });
        }

        // 打开设置模态框
        function openSettingsModal() {
            document.getElementById('settingsModal').classList.add('active');
            document.body.style.overflow = 'hidden';
        }

        // 关闭设置模态框
        function closeSettingsModal() {
            document.getElementById('settingsModal').classList.remove('active');
            document.body.style.overflow = 'auto';
        }

        // 切换Tab
        function switchTab(tabName) {
            // 移除所有active类
            document.querySelectorAll('.tab-button').forEach(btn => btn.classList.remove('active'));
            document.querySelectorAll('.tab-panel').forEach(panel => panel.classList.remove('active'));

            // 添加active类到当前tab
            event.target.classList.add('active');
            document.getElementById(tabName + '-panel').classList.add('active');
        }

        // 渲染收入支出表格
        function renderIncomeExpenseTable() {
            const tbody = document.getElementById('incomeExpenseTableBody');

            if (incomeExpenseData.length === 0) {
                tbody.innerHTML = `
                    <tr>
                        <td colspan="5" class="empty-state">
                            <div class="empty-icon">
                                <i class="fas fa-inbox"></i>
                            </div>
                            <div class="empty-title">暂无数据</div>
                            <div class="empty-description">还没有配置任何收入支出信息</div>
                        </td>
                    </tr>
                `;
                return;
            }

            tbody.innerHTML = incomeExpenseData.map(item => `
                <tr>
                    <td>
                        <div style="font-weight: 500;">${item.levelName}</div>
                    </td>
                    <td>
                        <div style="font-weight: 600; color: var(--success);">
                            ¥${item.incomeRate.toFixed(2)}
                        </div>
                    </td>
                    <td>
                        <div style="font-weight: 600; color: var(--error);">
                            ¥${item.expenseRate.toFixed(2)}
                        </div>
                    </td>
                    <td>
                        <span class="status-badge ${item.status === 'active' ? 'status-active' : 'status-inactive'}">
                            ${item.status === 'active' ? '启用' : '停用'}
                        </span>
                    </td>
                    <td>
                        <div class="action-group" style="gap: 8px;">
                            <button onclick="openIncomeExpenseForm('edit', ${item.id})" style="background: #cffafe; color: #0891b2; border: none; border-radius: 4px; padding: 6px 12px; font-size: 13px; cursor: pointer; display: flex; align-items: center;">
                                <i class="fas fa-edit" style="margin-right: 4px;"></i>
                                编辑
                            </button>
                            <button onclick="toggleIncomeExpenseStatus(${item.id})" style="background: #fef3c7; color: #d97706; border: none; border-radius: 4px; padding: 6px 12px; font-size: 13px; cursor: pointer; display: flex; align-items: center;">
                                <i class="fas fa-${item.status === 'active' ? 'pause' : 'play'}" style="margin-right: 4px;"></i>
                                ${item.status === 'active' ? '停用' : '启用'}
                            </button>
                        </div>
                    </td>
                </tr>
            `).join('');
        }

        // 渲染人员表格
        function renderPersonnelTable() {
            const tbody = document.getElementById('personnelTableBody');

            if (personnelData.length === 0) {
                tbody.innerHTML = `
                    <tr>
                        <td colspan="6" class="empty-state">
                            <div class="empty-icon">
                                <i class="fas fa-users"></i>
                            </div>
                            <div class="empty-title">暂无人员</div>
                            <div class="empty-description">还没有添加任何主播人员信息</div>
                        </td>
                    </tr>
                `;
                return;
            }

            tbody.innerHTML = personnelData.map(item => `
                <tr>
                    <td>
                        <div style="font-weight: 500;">${item.name}</div>
                    </td>
                    <td>
                        <span class="status-badge status-active" style="background: var(--info-light); color: var(--info);">
                            ${item.levelName}
                        </span>
                    </td>
                    <td>
                        <div style="font-weight: 600; color: var(--success);">
                            ¥${item.salary.toLocaleString()}
                        </div>
                    </td>
                    <td>
                        <div style="color: var(--neutral-600);">
                            ${item.joinDate}
                        </div>
                    </td>
                    <td>
                        <span class="status-badge ${item.status === 'active' ? 'status-active' : 'status-inactive'}">
                            ${item.status === 'active' ? '在职' : '离职'}
                        </span>
                    </td>
                    <td>
                        <div class="action-group" style="gap: 8px;">
                            <button onclick="openPersonnelForm('edit', ${item.id})" style="background: #cffafe; color: #0891b2; border: none; border-radius: 4px; padding: 6px 12px; font-size: 13px; cursor: pointer; display: flex; align-items: center;">
                                <i class="fas fa-edit" style="margin-right: 4px;"></i>
                                编辑
                            </button>
                            <button onclick="togglePersonnelStatus(${item.id})" style="background: #fef3c7; color: #d97706; border: none; border-radius: 4px; padding: 6px 12px; font-size: 13px; cursor: pointer; display: flex; align-items: center;">
                                <i class="fas fa-${item.status === 'active' ? 'user-times' : 'user-check'}" style="margin-right: 4px;"></i>
                                ${item.status === 'active' ? '离职' : '复职'}
                            </button>
                        </div>
                    </td>
                </tr>
            `).join('');
        }

        // 渲染级别表格
        function renderLevelTable() {
            const tbody = document.getElementById('levelTableBody');

            if (anchorLevels.length === 0) {
                tbody.innerHTML = `
                    <tr>
                        <td colspan="5" class="empty-state">
                            <div class="empty-icon">
                                <i class="fas fa-layer-group"></i>
                            </div>
                            <div class="empty-title">暂无级别</div>
                            <div class="empty-description">还没有创建任何主播级别</div>
                        </td>
                    </tr>
                `;
                return;
            }

            tbody.innerHTML = anchorLevels.map(item => `
                <tr>
                    <td>
                        <div>
                            <div style="font-weight: 500; margin-bottom: 4px;">${item.name}</div>
                            <div style="font-size: 12px; color: var(--neutral-500);">${item.description || '暂无描述'}</div>
                        </div>
                    </td>
                    <td>
                        <div style="color: var(--neutral-600);">
                            ${item.createdAt}
                        </div>
                    </td>
                    <td>
                        <div style="font-weight: 600; color: var(--brand-primary);">
                            ${item.personnelCount} 人
                        </div>
                    </td>
                    <td>
                        <span class="status-badge ${item.status === 'active' ? 'status-active' : 'status-inactive'}">
                            ${item.status === 'active' ? '启用' : '停用'}
                        </span>
                    </td>
                    <td>
                        <div class="action-group" style="gap: 8px;">
                            <button onclick="openLevelForm('edit', ${item.id})" style="background: #cffafe; color: #0891b2; border: none; border-radius: 4px; padding: 6px 12px; font-size: 13px; cursor: pointer; display: flex; align-items: center;">
                                <i class="fas fa-edit" style="margin-right: 4px;"></i>
                                编辑
                            </button>
                            <button onclick="toggleLevelStatus(${item.id})" style="background: #fef3c7; color: #d97706; border: none; border-radius: 4px; padding: 6px 12px; font-size: 13px; cursor: pointer; display: flex; align-items: center;">
                                <i class="fas fa-${item.status === 'active' ? 'pause' : 'play'}" style="margin-right: 4px;"></i>
                                ${item.status === 'active' ? '停用' : '启用'}
                            </button>
                        </div>
                    </td>
                </tr>
            `).join('');
        }

        // 更新级别选项
        function updateLevelOptions() {
            const activeLevels = anchorLevels.filter(level => level.status === 'active');

            // 更新收入支出表单的级别选项
            const incomeExpenseLevelSelect = document.getElementById('incomeExpenseLevel');
            incomeExpenseLevelSelect.innerHTML = '<option value="">请选择主播级别</option>' +
                activeLevels.map(level => `<option value="${level.id}">${level.name}</option>`).join('');

            // 更新人员表单的级别选项
            const personnelLevelSelect = document.getElementById('personnelLevel');
            personnelLevelSelect.innerHTML = '<option value="">请选择主播级别</option>' +
                activeLevels.map(level => `<option value="${level.id}">${level.name}</option>`).join('');
        }

        // 收入支出相关函数
        function openIncomeExpenseForm(type, id = null) {
            currentEditType = type;
            currentEditData = id ? incomeExpenseData.find(item => item.id === id) : null;

            const modal = document.getElementById('incomeExpenseFormModal');
            const title = document.getElementById('incomeExpenseFormTitle');
            const form = document.getElementById('incomeExpenseForm');

            title.textContent = type === 'add' ? '新增收入支出配置' : '编辑收入支出配置';

            if (type === 'edit' && currentEditData) {
                document.getElementById('incomeExpenseLevel').value = currentEditData.levelId;
                document.getElementById('incomeHourlyRate').value = currentEditData.incomeRate;
                document.getElementById('expenseHourlyRate').value = currentEditData.expenseRate;
            } else {
                form.reset();
            }

            modal.classList.add('active');
            document.body.style.overflow = 'hidden';
        }

        function closeIncomeExpenseForm() {
            document.getElementById('incomeExpenseFormModal').classList.remove('active');
            document.body.style.overflow = 'auto';
            currentEditData = null;
            currentEditType = null;
        }

        function saveIncomeExpense() {
            const saveButton = document.querySelector('#incomeExpenseFormModal .btn-primary');
            const loadingElement = document.getElementById('incomeExpenseLoading');
            const saveTextElement = document.getElementById('incomeExpenseSaveText');

            // 显示加载状态
            loadingElement.style.display = 'flex';
            saveTextElement.style.display = 'none';
            saveButton.disabled = true;

            // 模拟异步保存
            setTimeout(() => {
                const levelId = parseInt(document.getElementById('incomeExpenseLevel').value);
                const incomeRate = parseFloat(document.getElementById('incomeHourlyRate').value);
                const expenseRate = parseFloat(document.getElementById('expenseHourlyRate').value);

                if (!levelId || !incomeRate || !expenseRate) {
                    showNotification('请填写所有必填字段', 'error');
                    resetSaveButton();
                    return;
                }

                // 检查是否已存在该级别的配置（编辑时排除自己）
                const existingItem = incomeExpenseData.find(item =>
                    item.levelId === levelId &&
                    (currentEditType === 'add' || item.id !== currentEditData.id)
                );

                if (existingItem) {
                    showNotification('该主播级别已存在配置，请选择其他级别', 'error');
                    resetSaveButton();
                    return;
                }

                const levelName = anchorLevels.find(level => level.id === levelId).name;

                if (currentEditType === 'add') {
                    const newId = Math.max(...incomeExpenseData.map(item => item.id)) + 1;
                    incomeExpenseData.push({
                        id: newId,
                        levelId: levelId,
                        levelName: levelName,
                        incomeRate: incomeRate,
                        expenseRate: expenseRate,
                        status: 'active'
                    });
                } else {
                    const index = incomeExpenseData.findIndex(item => item.id === currentEditData.id);
                    incomeExpenseData[index] = {
                        ...incomeExpenseData[index],
                        levelId: levelId,
                        levelName: levelName,
                        incomeRate: incomeRate,
                        expenseRate: expenseRate
                    };
                }

                renderIncomeExpenseTable();
                closeIncomeExpenseForm();
                showNotification(currentEditType === 'add' ? '新增成功' : '编辑成功', 'success');
                resetSaveButton();
            }, 1000);

            function resetSaveButton() {
                loadingElement.style.display = 'none';
                saveTextElement.style.display = 'inline';
                saveButton.disabled = false;
            }
        }

        function toggleIncomeExpenseStatus(id) {
            const item = incomeExpenseData.find(item => item.id === id);
            if (item) {
                item.status = item.status === 'active' ? 'inactive' : 'active';
                renderIncomeExpenseTable();
                showNotification(`${item.status === 'active' ? '启用' : '停用'}成功`, 'success');
            }
        }

        // 人员相关函数
        function openPersonnelForm(type, id = null) {
            currentEditType = type;
            currentEditData = id ? personnelData.find(item => item.id === id) : null;

            const modal = document.getElementById('personnelFormModal');
            const title = document.getElementById('personnelFormTitle');
            const form = document.getElementById('personnelForm');

            title.textContent = type === 'add' ? '新增主播人员' : '编辑主播人员';

            if (type === 'edit' && currentEditData) {
                document.getElementById('personnelName').value = currentEditData.name;
                document.getElementById('personnelLevel').value = currentEditData.levelId;
                document.getElementById('personnelSalary').value = currentEditData.salary;
                document.getElementById('personnelJoinDate').value = currentEditData.joinDate;
            } else {
                form.reset();
                // 设置默认入职时间为今天
                document.getElementById('personnelJoinDate').value = new Date().toISOString().split('T')[0];
            }

            modal.classList.add('active');
            document.body.style.overflow = 'hidden';
        }

        function closePersonnelForm() {
            document.getElementById('personnelFormModal').classList.remove('active');
            document.body.style.overflow = 'auto';
            currentEditData = null;
            currentEditType = null;
        }

        function savePersonnel() {
            const saveButton = document.querySelector('#personnelFormModal .btn-primary');
            const loadingElement = document.getElementById('personnelLoading');
            const saveTextElement = document.getElementById('personnelSaveText');

            // 显示加载状态
            loadingElement.style.display = 'flex';
            saveTextElement.style.display = 'none';
            saveButton.disabled = true;

            // 模拟异步保存
            setTimeout(() => {
                const name = document.getElementById('personnelName').value.trim();
                const levelId = parseInt(document.getElementById('personnelLevel').value);
                const salary = parseFloat(document.getElementById('personnelSalary').value);
                const joinDate = document.getElementById('personnelJoinDate').value;

                if (!name || !levelId || !salary) {
                    showNotification('请填写所有必填字段', 'error');
                    resetSaveButton();
                    return;
                }

                // 检查是否已存在该人员（编辑时排除自己）
                const existingItem = personnelData.find(item =>
                    item.name === name &&
                    (currentEditType === 'add' || item.id !== currentEditData.id)
                );

                if (existingItem) {
                    showNotification('该主播人员已存在，请使用其他姓名', 'error');
                    resetSaveButton();
                    return;
                }

                const levelName = anchorLevels.find(level => level.id === levelId).name;

                if (currentEditType === 'add') {
                    const newId = Math.max(...personnelData.map(item => item.id)) + 1;
                    personnelData.push({
                        id: newId,
                        name: name,
                        levelId: levelId,
                        levelName: levelName,
                        salary: salary,
                        joinDate: joinDate || new Date().toISOString().split('T')[0],
                        status: 'active'
                    });
                } else {
                    const index = personnelData.findIndex(item => item.id === currentEditData.id);
                    personnelData[index] = {
                        ...personnelData[index],
                        name: name,
                        levelId: levelId,
                        levelName: levelName,
                        salary: salary,
                        joinDate: joinDate || personnelData[index].joinDate
                    };
                }

                updatePersonnelCount();
                renderPersonnelTable();
                renderLevelTable();
                closePersonnelForm();
                showNotification(currentEditType === 'add' ? '新增成功' : '编辑成功', 'success');
                resetSaveButton();
            }, 1000);

            function resetSaveButton() {
                loadingElement.style.display = 'none';
                saveTextElement.style.display = 'inline';
                saveButton.disabled = false;
            }
        }

        function togglePersonnelStatus(id) {
            const item = personnelData.find(item => item.id === id);
            if (item) {
                item.status = item.status === 'active' ? 'inactive' : 'active';
                updatePersonnelCount();
                renderPersonnelTable();
                renderLevelTable();
                showNotification(`${item.status === 'active' ? '复职' : '离职'}操作成功`, 'success');
            }
        }

        // 级别相关函数
        function openLevelForm(type, id = null) {
            currentEditType = type;
            currentEditData = id ? anchorLevels.find(item => item.id === id) : null;

            const modal = document.getElementById('levelFormModal');
            const title = document.getElementById('levelFormTitle');
            const form = document.getElementById('levelForm');

            title.textContent = type === 'add' ? '新增主播级别' : '编辑主播级别';

            if (type === 'edit' && currentEditData) {
                document.getElementById('levelName').value = currentEditData.name;
                document.getElementById('levelDescription').value = currentEditData.description || '';
            } else {
                form.reset();
            }

            modal.classList.add('active');
            document.body.style.overflow = 'hidden';
        }

        function closeLevelForm() {
            document.getElementById('levelFormModal').classList.remove('active');
            document.body.style.overflow = 'auto';
            currentEditData = null;
            currentEditType = null;
        }

        function saveLevel() {
            const saveButton = document.querySelector('#levelFormModal .btn-primary');
            const loadingElement = document.getElementById('levelLoading');
            const saveTextElement = document.getElementById('levelSaveText');

            // 显示加载状态
            loadingElement.style.display = 'flex';
            saveTextElement.style.display = 'none';
            saveButton.disabled = true;

            // 模拟异步保存
            setTimeout(() => {
                const name = document.getElementById('levelName').value.trim();
                const description = document.getElementById('levelDescription').value.trim();

                if (!name) {
                    showNotification('请填写主播级别名称', 'error');
                    resetSaveButton();
                    return;
                }

                // 检查是否已存在该级别名称（编辑时排除自己）
                const existingItem = anchorLevels.find(item =>
                    item.name === name &&
                    (currentEditType === 'add' || item.id !== currentEditData.id)
                );

                if (existingItem) {
                    showNotification('该主播级别名称已存在，请使用其他名称', 'error');
                    resetSaveButton();
                    return;
                }

                if (currentEditType === 'add') {
                    const newId = Math.max(...anchorLevels.map(item => item.id)) + 1;
                    anchorLevels.push({
                        id: newId,
                        name: name,
                        description: description,
                        status: 'active',
                        createdAt: new Date().toISOString().split('T')[0],
                        personnelCount: 0
                    });
                } else {
                    const index = anchorLevels.findIndex(item => item.id === currentEditData.id);
                    anchorLevels[index] = {
                        ...anchorLevels[index],
                        name: name,
                        description: description
                    };

                    // 更新其他表中对应的级别名称
                    incomeExpenseData.forEach(item => {
                        if (item.levelId === currentEditData.id) {
                            item.levelName = name;
                        }
                    });

                    personnelData.forEach(item => {
                        if (item.levelId === currentEditData.id) {
                            item.levelName = name;
                        }
                    });
                }

                renderLevelTable();
                renderIncomeExpenseTable();
                renderPersonnelTable();
                updateLevelOptions();
                closeLevelForm();
                showNotification(currentEditType === 'add' ? '新增成功' : '编辑成功', 'success');
                resetSaveButton();
            }, 1000);

            function resetSaveButton() {
                loadingElement.style.display = 'none';
                saveTextElement.style.display = 'inline';
                saveButton.disabled = false;
            }
        }

        function toggleLevelStatus(id) {
            const item = anchorLevels.find(item => item.id === id);
            if (item) {
                // 如果要停用级别，检查是否有关联数据
                if (item.status === 'active') {
                    const hasIncomeExpenseData = incomeExpenseData.some(data => data.levelId === id && data.status === 'active');
                    const hasPersonnelData = personnelData.some(data => data.levelId === id && data.status === 'active');

                    if (hasIncomeExpenseData || hasPersonnelData) {
                        if (!confirm('该级别下还有启用的配置数据，停用级别后相关数据也将被停用，确定要继续吗？')) {
                            return;
                        }

                        // 停用相关的收入支出配置
                        incomeExpenseData.forEach(data => {
                            if (data.levelId === id) {
                                data.status = 'inactive';
                            }
                        });

                        // 停用相关的人员配置
                        personnelData.forEach(data => {
                            if (data.levelId === id) {
                                data.status = 'inactive';
                            }
                        });
                    }
                }

                item.status = item.status === 'active' ? 'inactive' : 'active';
                updatePersonnelCount();
                renderLevelTable();
                renderIncomeExpenseTable();
                renderPersonnelTable();
                updateLevelOptions();
                showNotification(`${item.status === 'active' ? '启用' : '停用'}成功`, 'success');
            }
        }

        // 通知系统
        function showNotification(message, type = 'success') {
            // 移除现有通知
            const existingNotification = document.querySelector('.notification');
            if (existingNotification) {
                existingNotification.remove();
            }

            // 创建新通知
            const notification = document.createElement('div');
            notification.className = `notification notification-${type}`;

            const iconMap = {
                success: 'fas fa-check',
                error: 'fas fa-times',
                warning: 'fas fa-exclamation-triangle',
                info: 'fas fa-info'
            };

            notification.innerHTML = `
                <div class="notification-content">
                    <div class="notification-icon">
                        <i class="${iconMap[type]}"></i>
                    </div>
                    <div class="notification-message">${message}</div>
                </div>
            `;

            document.body.appendChild(notification);

            // 显示动画
            setTimeout(() => {
                notification.classList.add('show');
            }, 100);

            // 自动隐藏
            setTimeout(() => {
                notification.classList.remove('show');
                setTimeout(() => {
                    if (notification.parentNode) {
                        notification.parentNode.removeChild(notification);
                    }
                }, 300);
            }, 3000);
        }

        // 点击模态框外部关闭
        document.getElementById('settingsModal').addEventListener('click', function(e) {
            if (e.target === this) {
                closeSettingsModal();
            }
        });

        document.getElementById('incomeExpenseFormModal').addEventListener('click', function(e) {
            if (e.target === this) {
                closeIncomeExpenseForm();
            }
        });

        document.getElementById('personnelFormModal').addEventListener('click', function(e) {
            if (e.target === this) {
                closePersonnelForm();
            }
        });

        document.getElementById('levelFormModal').addEventListener('click', function(e) {
            if (e.target === this) {
                closeLevelForm();
            }
        });

        // ESC键关闭模态框
        document.addEventListener('keydown', function(e) {
            if (e.key === 'Escape') {
                if (document.getElementById('levelFormModal').classList.contains('active')) {
                    closeLevelForm();
                } else if (document.getElementById('personnelFormModal').classList.contains('active')) {
                    closePersonnelForm();
                } else if (document.getElementById('incomeExpenseFormModal').classList.contains('active')) {
                    closeIncomeExpenseForm();
                } else if (document.getElementById('settingsModal').classList.contains('active')) {
                    closeSettingsModal();
                } else if (document.getElementById('broadcastFormModal').classList.contains('active')) {
                    closeBroadcastForm();
                } else if (document.getElementById('importModal').classList.contains('active')) {
                    closeImportModal();
                } else if (document.getElementById('deleteConfirmModal').classList.contains('active')) {
                    closeDeleteConfirmModal();
                }
            }
        });

        // 表单验证增强
        document.querySelectorAll('.form-input, .form-select').forEach(input => {
            input.addEventListener('blur', function() {
                if (this.hasAttribute('required') && !this.value.trim()) {
                    this.style.borderColor = 'var(--error)';
                    this.style.boxShadow = '0 0 0 3px rgba(220, 38, 38, 0.1)';
                } else {
                    this.style.borderColor = 'var(--neutral-300)';
                    this.style.boxShadow = 'none';
                }
            });

            input.addEventListener('focus', function() {
                this.style.borderColor = 'var(--brand-primary)';
                this.style.boxShadow = '0 0 0 3px rgba(37, 99, 235, 0.1)';
            });
        });

        // 数字输入框格式化
        document.querySelectorAll('input[type="number"]').forEach(input => {
            input.addEventListener('input', function() {
                if (this.value < 0) {
                    this.value = 0;
                }
            });
        });

        // 主播经营数据表格相关功能

        // 模拟数据
        let broadcastData = [{
            id: 1,
            date: '2024-05-15',
            team: '销售一组',
            anchorId: 1,
            anchorName: '张小美',
            store: '旗舰店',
            startTime: '2024-05-15T19:00',
            endTime: '2024-05-15T22:30',
            product: 'XC-876',
            salesAmount: 45600.00,
            maxOnlineUsers: 3500,
            avgOnlineUsers: 1800,
            avgPrice: 560.5,
            retentionRate: 35.8,
            interactionRate: 42.6,
            uvValue: 12.5
        }, {
            id: 2,
            date: '2024-05-16',
            team: '销售二组',
            anchorId: 2,
            anchorName: '李明星',
            store: '官方店',
            startTime: '2024-05-16T18:30',
            endTime: '2024-05-16T21:45',
            product: 'BG-234',
            salesAmount: 32800.00,
            maxOnlineUsers: 2800,
            avgOnlineUsers: 1200,
            avgPrice: 410.0,
            retentionRate: 32.1,
            interactionRate: 38.4,
            uvValue: 10.8
        }, {
            id: 3,
            date: '2024-05-15',
            team: '销售一组',
            anchorId: 4,
            anchorName: '刘晓雨',
            store: '旗舰店',
            startTime: '2024-05-15T14:00',
            endTime: '2024-05-15T17:30',
            product: 'LC-527',
            salesAmount: 28500.00,
            maxOnlineUsers: 2400,
            avgOnlineUsers: 1350,
            avgPrice: 380.0,
            retentionRate: 30.5,
            interactionRate: 36.2,
            uvValue: 9.5
        }, {
            id: 4,
            date: '2024-05-16',
            team: '销售三组',
            anchorId: 5,
            anchorName: '陈思思',
            store: '专卖店',
            startTime: '2024-05-16T19:30',
            endTime: '2024-05-16T23:00',
            product: 'TS-908',
            salesAmount: 52300.00,
            maxOnlineUsers: 4200,
            avgOnlineUsers: 2100,
            avgPrice: 620.5,
            retentionRate: 38.2,
            interactionRate: 45.7,
            uvValue: 14.2
        }, {
            id: 5,
            date: '2024-05-17',
            team: '销售二组',
            anchorId: 2,
            anchorName: '李明星',
            store: '官方店',
            startTime: '2024-05-17T18:30',
            endTime: '2024-05-17T22:00',
            product: 'BG-234',
            salesAmount: 35700.00,
            maxOnlineUsers: 3000,
            avgOnlineUsers: 1450,
            avgPrice: 430.0,
            retentionRate: 33.8,
            interactionRate: 40.2,
            uvValue: 11.4
        }];

        // 分页相关变量
        let currentPage = 1;
        let pageSize = 10;
        let totalPages = 1;

        // 筛选相关变量
        let dateFilterStart = '';
        let dateFilterEnd = '';
        let selectedAnchorId = '';

        // 批量删除相关变量
        let selectedItems = [];

        // 当前编辑的数据
        let currentBroadcastData = null;
        let currentBroadcastEditType = null;
        let deleteItemId = null;
        let isBatchDelete = false;

        // 页面加载完成后初始化主播经营数据表格
        document.addEventListener('DOMContentLoaded', function() {
            // 原有的初始化代码保持不变
            updatePersonnelCount();
            renderIncomeExpenseTable();
            renderPersonnelTable();
            renderLevelTable();
            updateLevelOptions();

            // 初始化主播经营数据表格
            renderBroadcastTable();
            updatePagination();

            // 初始化日期筛选器默认值
            const today = new Date();
            const lastMonth = new Date();
            lastMonth.setMonth(lastMonth.getMonth() - 1);

            document.getElementById('dateFilterEnd').valueAsDate = today;
            document.getElementById('dateFilterStart').valueAsDate = lastMonth;

            // 设置日期筛选器的初始值
            dateFilterStart = document.getElementById('dateFilterStart').value;
            dateFilterEnd = document.getElementById('dateFilterEnd').value;

            // 为日期筛选器添加change事件监听器
            document.getElementById('dateFilterStart').addEventListener('change', function() {
                dateFilterStart = this.value;
                currentPage = 1;
                renderBroadcastTable();
                updatePagination();
            });

            document.getElementById('dateFilterEnd').addEventListener('change', function() {
                dateFilterEnd = this.value;
                currentPage = 1;
                renderBroadcastTable();
                updatePagination();
            });

            // 初始化文件上传监听
            document.getElementById('fileInput').addEventListener('change', function(e) {
                let fileName = '';
                if (e.target.files && e.target.files.length > 0) {
                    fileName = e.target.files[0].name;
                }
                document.getElementById('fileName').textContent = fileName;
                document.getElementById('uploadButton').disabled = !fileName;
            });

            // 拖放文件上传功能
            const uploadArea = document.querySelector('.upload-area');

            uploadArea.addEventListener('dragover', function(e) {
                e.preventDefault();
                this.style.borderColor = 'var(--brand-primary)';
                this.style.background = 'var(--neutral-100)';
            });

            uploadArea.addEventListener('dragleave', function(e) {
                e.preventDefault();
                this.style.borderColor = 'var(--neutral-300)';
                this.style.background = 'var(--neutral-50)';
            });

            uploadArea.addEventListener('drop', function(e) {
                e.preventDefault();
                this.style.borderColor = 'var(--neutral-300)';
                this.style.background = 'var(--neutral-50)';

                const file = e.dataTransfer.files[0];
                if (file) {
                    const fileInput = document.getElementById('fileInput');
                    fileInput.files = e.dataTransfer.files;
                    document.getElementById('fileName').textContent = file.name;
                    document.getElementById('uploadButton').disabled = false;
                }
            });

            // 页面大小选择器监听
            document.getElementById('pageSizeSelect').addEventListener('change', function() {
                pageSize = parseInt(this.value);
                currentPage = 1;
                renderBroadcastTable();
                updatePagination();
            });

            // 初始化表格列宽调整功能
            initColumnResizing();
        });

        // 初始化表格列宽调整功能
        function initColumnResizing() {
            const table = document.querySelector('.enhanced-data-table');
            const resizers = document.querySelectorAll('.column-resizer');
            const indicator = document.querySelector('.resizer-indicator');
            let isResizing = false;
            let currentResizer = null;
            let startX, startWidth;

            // 加载已保存的列宽
            loadColumnWidths();

            // 为每个调整手柄添加事件监听
            resizers.forEach(resizer => {
                resizer.addEventListener('mousedown', function(e) {
                    // 阻止事件冒泡和默认行为
                    e.preventDefault();
                    e.stopPropagation();

                    // 开始调整列宽
                    isResizing = true;
                    currentResizer = resizer;

                    // 获取列索引
                    const columnIndex = parseInt(resizer.getAttribute('data-column'));
                    const th = resizer.closest('th');

                    // 记录起始位置和宽度
                    startX = e.pageX;
                    startWidth = th.offsetWidth;

                    // 显示拖动指示器
                    const thRect = th.getBoundingClientRect();
                    indicator.style.left = (thRect.right - 1) + 'px';
                    indicator.style.display = 'block';

                    // 添加拖动和释放事件
                    document.addEventListener('mousemove', handleMouseMove);
                    document.addEventListener('mouseup', handleMouseUp);
                });
            });

            // 处理鼠标移动
            function handleMouseMove(e) {
                if (!isResizing) return;

                const th = currentResizer.closest('th');
                const columnIndex = parseInt(currentResizer.getAttribute('data-column'));

                // 计算宽度差异
                const diffX = e.pageX - startX;
                let newWidth = startWidth + diffX;

                // 设置最小宽度
                newWidth = Math.max(50, newWidth);

                // 更新指示器位置
                const thRect = th.getBoundingClientRect();
                indicator.style.left = (thRect.left + newWidth - 1) + 'px';

                // 应用新宽度到列
                th.style.width = newWidth + 'px';

                // 同时更新表格单元格
                const allRows = table.querySelectorAll('tbody tr');
                allRows.forEach(row => {
                    const cell = row.cells[columnIndex];
                    if (cell) {
                        cell.style.width = newWidth + 'px';
                    }
                });
            }

            // 处理鼠标释放
            function handleMouseUp() {
                if (!isResizing) return;

                // 结束调整
                isResizing = false;
                indicator.style.display = 'none';

                // 保存列宽配置
                saveColumnWidths();

                // 移除事件监听
                document.removeEventListener('mousemove', handleMouseMove);
                document.removeEventListener('mouseup', handleMouseUp);
            }

            // 保存列宽配置到本地存储
            function saveColumnWidths() {
                const widths = {};

                // 获取所有表头宽度
                document.querySelectorAll('.enhanced-data-table th').forEach((th, index) => {
                    if (index > 0) { // 跳过复选框列
                        widths[index] = th.offsetWidth;
                    }
                });

                // 保存到localStorage
                localStorage.setItem('columnWidths', JSON.stringify(widths));
            }

            // 加载已保存的列宽配置
            function loadColumnWidths() {
                const savedWidths = localStorage.getItem('columnWidths');

                if (savedWidths) {
                    const widths = JSON.parse(savedWidths);

                    // 应用已保存的宽度
                    Object.keys(widths).forEach(index => {
                        const th = document.querySelectorAll('.enhanced-data-table th')[parseInt(index)];
                        if (th) {
                            th.style.width = widths[index] + 'px';
                        }
                    });
                }
            }
        }

        // 刷新表格列宽
        function refreshColumnWidths() {
            const table = document.querySelector('.enhanced-data-table');
            const headers = table.querySelectorAll('th');

            headers.forEach((th, index) => {
                if (index > 0) { // 跳过复选框列
                    const width = th.offsetWidth;

                    // 应用到所有行的单元格
                    const allRows = table.querySelectorAll('tbody tr');
                    allRows.forEach(row => {
                        const cell = row.cells[index];
                        if (cell) {
                            cell.style.width = width + 'px';
                        }
                    });
                }
            });
        }

        // 渲染主播经营数据表格
        function renderBroadcastTable() {
            const tbody = document.getElementById('broadcastTableBody');

            // 筛选数据
            let filteredData = broadcastData;

            if (dateFilterStart) {
                filteredData = filteredData.filter(item => item.date >= dateFilterStart);
            }

            if (dateFilterEnd) {
                filteredData = filteredData.filter(item => item.date <= dateFilterEnd);
            }

            if (selectedAnchorId) {
                filteredData = filteredData.filter(item => item.anchorId == selectedAnchorId);
            }

            // 更新总记录数显示
            document.getElementById('totalRecords').textContent = filteredData.length;

            // 计算总页数
            totalPages = Math.max(1, Math.ceil(filteredData.length / pageSize));

            // 确保当前页在有效范围内
            if (currentPage > totalPages) {
                currentPage = totalPages;
            }

            // 分页
            const startIdx = (currentPage - 1) * pageSize;
            const endIdx = Math.min(startIdx + pageSize, filteredData.length);
            const paginatedData = filteredData.slice(startIdx, endIdx);

            if (paginatedData.length === 0) {
                tbody.innerHTML = `
                    <tr>
                        <td colspan="15" class="empty-state" style="padding: var(--space-12); text-align: center;">
                            <div style="display: flex; flex-direction: column; align-items: center; gap: var(--space-4);">
                                <div style="width: 64px; height: 64px; border-radius: 50%; background: var(--neutral-100); display: flex; align-items: center; justify-content: center;">
                                    <i class="fas fa-chart-bar" style="font-size: 24px; color: var(--neutral-400);"></i>
                                </div>
                                <div style="max-width: 240px;">
                                    <div style="font-size: 16px; font-weight: 600; color: var(--neutral-900); margin-bottom: var(--space-2);">暂无数据</div>
                                    <div style="color: var(--neutral-600);">暂无主播经营数据，您可以点击"新增填报"添加数据或导入现有数据。</div>
                                </div>
                                <button class="btn-action-primary" onclick="openBroadcastForm('add')" style="margin-top: var(--space-2);">
                                    <i class="fas fa-plus"></i>
                                    新增填报
                                </button>
                            </div>
                        </td>
                    </tr>
                `;
                return;
            }

            tbody.innerHTML = paginatedData.map(item => {
                // 格式化日期时间显示
                const formattedStartTime = formatDateTime(item.startTime);
                const formattedEndTime = formatDateTime(item.endTime);

                // 确定百分比颜色
                const getPercentageClass = (value) => {
                    if (value < 30) return 'percentage-low';
                    if (value < 40) return 'percentage-medium';
                    return 'percentage-high';
                };

                // 销售额格式化
                const formattedSalesAmount = new Intl.NumberFormat('zh-CN', {
                    style: 'currency',
                    currency: 'CNY',
                    minimumFractionDigits: 0,
                    maximumFractionDigits: 0
                }).format(item.salesAmount);

                return `
                <tr>
                    <td style="vertical-align: middle;">
                        <input type="checkbox" class="row-checkbox" value="${item.id}" onchange="handleCheckboxChange()" style="cursor: pointer;">
                    </td>
                    <td>
                        <div class="cell-primary">${item.date}</div>
                    </td>
                    <td>
                        <div class="cell-wrapper">
                            <div class="pill pill-purple">
                                ${item.team}
                            </div>
                        </div>
                    </td>
                    <td>
                        <div class="cell-wrapper">
                            <div class="pill pill-blue">
                                ${item.anchorName}
                            </div>
                        </div>
                    </td>
                    <td>
                        <div class="cell-primary">${item.store}</div>
                    </td>
                    <td>
                        <div class="time-range">${formattedStartTime} - ${formattedEndTime}</div>
                    </td>
                    <td>
                        <div class="cell-primary">${item.product}</div>
                    </td>
                    <td>
                        <div class="cell-success">${formattedSalesAmount}</div>
                    </td>
                    <td>
                        <div class="cell-primary">${item.maxOnlineUsers.toLocaleString()}</div>
                    </td>
                    <td>
                        <div class="cell-primary">${item.avgOnlineUsers.toLocaleString()}</div>
                    </td>
                    <td>
                        <div class="cell-primary">¥${item.avgPrice.toFixed(2)}</div>
                    </td>
                    <td>
                        <div class="cell-wrapper">
                            <div class="cell-primary">${item.retentionRate.toFixed(1)}%</div>
                            <div class="percentage-bar">
                                <div class="percentage-value ${getPercentageClass(item.retentionRate)}" style="width: ${Math.min(100, item.retentionRate)}%;"></div>
                            </div>
                        </div>
                    </td>
                    <td>
                        <div class="cell-wrapper">
                            <div class="cell-primary">${item.interactionRate.toFixed(1)}%</div>
                            <div class="percentage-bar">
                                <div class="percentage-value ${getPercentageClass(item.interactionRate)}" style="width: ${Math.min(100, item.interactionRate)}%;"></div>
                            </div>
                        </div>
                    </td>
                    <td>
                        <div class="cell-highlight">¥${item.uvValue.toFixed(2)}</div>
                    </td>
                    <td>
                        <div class="action-group">
                            <button class="action-btn action-btn-edit" onclick="openBroadcastForm('edit', ${item.id})">
                                <i class="fas fa-edit"></i>
                                <span class="action-tooltip">编辑</span>
                            </button>
                            <button class="action-btn action-btn-delete" onclick="openDeleteConfirm(${item.id})">
                                <i class="fas fa-trash-alt"></i>
                                <span class="action-tooltip">删除</span>
                            </button>
                        </div>
                    </td>
                </tr>
                `;
            }).join('');

            // 更新批量删除按钮状态
            document.getElementById('batchDeleteBtn').style.display = 'none';
            selectedItems = [];
            document.getElementById('selectAllCheckbox').checked = false;

            // 添加表格行的淡入动画
            setTimeout(() => {
                document.querySelectorAll('.enhanced-data-table tbody tr').forEach((row, index) => {
                    row.style.animationDelay = `${index * 0.05}s`;
                    row.classList.add('fade-in');
                });

                // 确保列宽一致
                refreshColumnWidths();
            }, 10);
        }

        // 格式化日期时间
        function formatDateTime(dateTimeStr) {
            const dt = new Date(dateTimeStr);
            const hours = dt.getHours().toString().padStart(2, '0');
            const minutes = dt.getMinutes().toString().padStart(2, '0');
            return `${hours}:${minutes}`;
        }

        // 更新分页控件
        function updatePagination() {
            const paginationPages = document.getElementById('paginationPages');
            const prevBtn = document.getElementById('prevPageBtn');
            const nextBtn = document.getElementById('nextPageBtn');

            prevBtn.disabled = currentPage === 1;
            nextBtn.disabled = currentPage === totalPages;

            prevBtn.onclick = () => {
                if (currentPage > 1) {
                    currentPage--;
                    renderBroadcastTable();
                    updatePagination();
                }
            };

            nextBtn.onclick = () => {
                if (currentPage < totalPages) {
                    currentPage++;
                    renderBroadcastTable();
                    updatePagination();
                }
            };

            // 生成页码
            let pagesHTML = '';

            // 显示最多5个页码
            const maxVisiblePages = 5;
            let startPage = Math.max(1, currentPage - Math.floor(maxVisiblePages / 2));
            let endPage = Math.min(totalPages, startPage + maxVisiblePages - 1);

            if (endPage - startPage + 1 < maxVisiblePages) {
                startPage = Math.max(1, endPage - maxVisiblePages + 1);
            }

            if (startPage > 1) {
                pagesHTML += `
                    <button class="page-btn" onclick="goToPage(1)">1</button>
                    ${startPage > 2 ? '<span style="color: var(--neutral-500);">...</span>' : ''}
                `;
            }

            for (let i = startPage; i <= endPage; i++) {
                pagesHTML += `
                    <button class="page-btn ${i === currentPage ? 'page-btn-active' : ''}" onclick="goToPage(${i})">${i}</button>
                `;
            }

            if (endPage < totalPages) {
                pagesHTML += `
                    ${endPage < totalPages - 1 ? '<span style="color: var(--neutral-500);">...</span>' : ''}
                    <button class="page-btn" onclick="goToPage(${totalPages})">${totalPages}</button>
                `;
            }

            paginationPages.innerHTML = pagesHTML;
        }

        // 跳转到指定页
        function goToPage(page) {
            currentPage = page;
            renderBroadcastTable();
            updatePagination();
        }

        // 更新筛选数据
        function updateFilteredData() {
            dateFilterStart = document.getElementById('dateFilterStart').value;
            dateFilterEnd = document.getElementById('dateFilterEnd').value;
            // selectedAnchorId 在选择主播时已经设置

            currentPage = 1;
            renderBroadcastTable();
            updatePagination();
        }

        // 应用筛选条件
        function applyFilters() {
            dateFilterStart = document.getElementById('dateFilterStart').value;
            dateFilterEnd = document.getElementById('dateFilterEnd').value;
            // selectedAnchorId 在选择主播时已经设置

            currentPage = 1;
            renderBroadcastTable();
            updatePagination();

            showNotification('筛选条件已应用', 'success');
        }

        // 重置筛选条件
        function resetFilters() {
            const today = new Date();
            const lastMonth = new Date();
            lastMonth.setMonth(lastMonth.getMonth() - 1);

            document.getElementById('dateFilterEnd').valueAsDate = today;
            document.getElementById('dateFilterStart').valueAsDate = lastMonth;

            document.getElementById('anchorFilter').value = '';
            selectedAnchorId = '';

            dateFilterStart = document.getElementById('dateFilterStart').value;
            dateFilterEnd = document.getElementById('dateFilterEnd').value;

            currentPage = 1;
            renderBroadcastTable();
            updatePagination();

            showNotification('已重置筛选条件', 'info');
        }

        // 显示主播下拉菜单
        function toggleAnchorDropdown() {
            const dropdown = document.getElementById('anchorDropdown');

            if (dropdown.style.display === 'none' || !dropdown.style.display) {
                // 获取在职主播数据
                const activePersonnel = personnelData.filter(p => p.status === 'active');

                dropdown.innerHTML = activePersonnel.map(personnel =>
                    `<div class="dropdown-item" style="padding: var(--space-3); cursor: pointer; border-bottom: 1px solid var(--neutral-100);" onclick="selectAnchorFilter(${personnel.id}, '${personnel.name}')">${personnel.name} (${personnel.levelName})</div>`
                ).join('');

                dropdown.style.display = 'block';

                // 点击外部关闭下拉菜单
                document.addEventListener('click', closeAnchorDropdownOnClick);
            } else {
                closeAnchorDropdown();
            }
        }

        // 关闭主播下拉菜单
        function closeAnchorDropdown() {
            const dropdown = document.getElementById('anchorDropdown');
            dropdown.style.display = 'none';
            document.removeEventListener('click', closeAnchorDropdownOnClick);
        }

        // 点击外部关闭下拉菜单的事件处理器
        function closeAnchorDropdownOnClick(e) {
            const dropdown = document.getElementById('anchorDropdown');
            const input = document.getElementById('anchorFilter');

            if (!dropdown.contains(e.target) && e.target !== input) {
                closeAnchorDropdown();
            }
        }

        // 选择主播筛选
        function selectAnchorFilter(id, name) {
            document.getElementById('anchorFilter').value = name;
            selectedAnchorId = id;
            closeAnchorDropdown();

            // 自动应用筛选
            currentPage = 1;
            renderBroadcastTable();
            updatePagination();
        }

        // 打开主播经营数据表单
        function openBroadcastForm(type, id = null) {
            currentBroadcastEditType = type;
            currentBroadcastData = id ? broadcastData.find(item => item.id === id) : null;

            const modal = document.getElementById('broadcastFormModal');
            const title = document.getElementById('broadcastFormTitle');
            const form = document.getElementById('broadcastForm');

            title.textContent = type === 'add' ? '新增主播经营数据' : '编辑主播经营数据';

            if (type === 'edit' && currentBroadcastData) {
                document.getElementById('broadcastDate').value = currentBroadcastData.date;
                document.getElementById('teamName').value = currentBroadcastData.team;
                document.getElementById('anchorName').value = currentBroadcastData.anchorName;
                document.getElementById('anchorId').value = currentBroadcastData.anchorId;
                document.getElementById('storeName').value = currentBroadcastData.store;
                document.getElementById('startTime').value = currentBroadcastData.startTime;
                document.getElementById('endTime').value = currentBroadcastData.endTime;
                document.getElementById('productName').value = currentBroadcastData.product;
                document.getElementById('salesAmount').value = currentBroadcastData.salesAmount;
                document.getElementById('maxOnlineUsers').value = currentBroadcastData.maxOnlineUsers;
                document.getElementById('avgOnlineUsers').value = currentBroadcastData.avgOnlineUsers;
                document.getElementById('avgPrice').value = currentBroadcastData.avgPrice;
                document.getElementById('retentionRate').value = currentBroadcastData.retentionRate;
                document.getElementById('interactionRate').value = currentBroadcastData.interactionRate;
                document.getElementById('uvValue').value = currentBroadcastData.uvValue;
            } else {
                form.reset();
                const today = new Date().toISOString().split('T')[0];
                document.getElementById('broadcastDate').value = today;
            }

            modal.classList.add('active');
            document.body.style.overflow = 'hidden';
        }

        // 关闭主播经营数据表单
        function closeBroadcastForm() {
            document.getElementById('broadcastFormModal').classList.remove('active');
            document.body.style.overflow = 'auto';
            currentBroadcastData = null;
            currentBroadcastEditType = null;
        }

        // 显示主播选择下拉菜单
        function toggleAnchorSelect() {
            const dropdown = document.getElementById('anchorSelect');
            const input = document.getElementById('anchorName');

            if (dropdown.style.display === 'none' || !dropdown.style.display) {
                // 获取在职主播数据
                const activePersonnel = personnelData.filter(p => p.status === 'active');

                dropdown.innerHTML = activePersonnel.map(personnel =>
                    `<div class="dropdown-item" style="padding: var(--space-3); cursor: pointer; border-bottom: 1px solid var(--neutral-100);" onclick="selectAnchor(${personnel.id}, '${personnel.name}')">${personnel.name} (${personnel.levelName})</div>`
                ).join('');

                dropdown.style.display = 'block';

                // 点击外部关闭下拉菜单
                document.addEventListener('click', function closeDropdown(e) {
                    if (!dropdown.contains(e.target) && e.target !== input) {
                        dropdown.style.display = 'none';
                        document.removeEventListener('click', closeDropdown);
                    }
                });
            } else {
                dropdown.style.display = 'none';
            }
        }

        // 选择主播
        function selectAnchor(id, name) {
            document.getElementById('anchorName').value = name;
            document.getElementById('anchorId').value = id;
            document.getElementById('anchorSelect').style.display = 'none';
        }

        // 保存主播经营数据
        function saveBroadcast() {
            const saveButton = document.querySelector('#broadcastFormModal .btn-primary');
            const loadingElement = document.getElementById('broadcastLoading');
            const saveTextElement = document.getElementById('broadcastSaveText');

            // 显示加载状态
            loadingElement.style.display = 'flex';
            saveTextElement.style.display = 'none';
            saveButton.disabled = true;

            // 模拟异步保存
            setTimeout(() => {
                const date = document.getElementById('broadcastDate').value;
                const team = document.getElementById('teamName').value;
                const anchorId = parseInt(document.getElementById('anchorId').value);
                const anchorName = document.getElementById('anchorName').value;
                const store = document.getElementById('storeName').value;
                const startTime = document.getElementById('startTime').value;
                const endTime = document.getElementById('endTime').value;
                const product = document.getElementById('productName').value;
                const salesAmount = parseFloat(document.getElementById('salesAmount').value);
                const maxOnlineUsers = parseInt(document.getElementById('maxOnlineUsers').value);
                const avgOnlineUsers = parseInt(document.getElementById('avgOnlineUsers').value);
                const avgPrice = parseFloat(document.getElementById('avgPrice').value);
                const retentionRate = parseFloat(document.getElementById('retentionRate').value);
                const interactionRate = parseFloat(document.getElementById('interactionRate').value);
                const uvValue = parseFloat(document.getElementById('uvValue').value);

                if (!date || !team || !anchorId || !anchorName || !store || !startTime || !endTime || !product ||
                    !salesAmount || !maxOnlineUsers || !avgOnlineUsers || !avgPrice ||
                    !retentionRate || !interactionRate || !uvValue) {
                    showNotification('请填写所有必填字段', 'error');
                    resetSaveButton();
                    return;
                }

                // 验证开始时间小于结束时间
                if (new Date(startTime) >= new Date(endTime)) {
                    showNotification('上播时间必须早于下播时间', 'error');
                    resetSaveButton();
                    return;
                }

                if (currentBroadcastEditType === 'add') {
                    const newId = Math.max(0, ...broadcastData.map(item => item.id)) + 1;
                    broadcastData.unshift({
                        id: newId,
                        date,
                        team,
                        anchorId,
                        anchorName,
                        store,
                        startTime,
                        endTime,
                        product,
                        salesAmount,
                        maxOnlineUsers,
                        avgOnlineUsers,
                        avgPrice,
                        retentionRate,
                        interactionRate,
                        uvValue
                    });
                } else {
                    const index = broadcastData.findIndex(item => item.id === currentBroadcastData.id);
                    broadcastData[index] = {
                        ...broadcastData[index],
                        date,
                        team,
                        anchorId,
                        anchorName,
                        store,
                        startTime,
                        endTime,
                        product,
                        salesAmount,
                        maxOnlineUsers,
                        avgOnlineUsers,
                        avgPrice,
                        retentionRate,
                        interactionRate,
                        uvValue
                    };
                }

                renderBroadcastTable();
                updatePagination();
                closeBroadcastForm();
                showNotification(currentBroadcastEditType === 'add' ? '新增成功' : '编辑成功', 'success');
                resetSaveButton();
            }, 1000);

            function resetSaveButton() {
                loadingElement.style.display = 'none';
                saveTextElement.style.display = 'inline';
                saveButton.disabled = false;
            }
        }

        // 打开删除确认框
        function openDeleteConfirm(id) {
            deleteItemId = id;
            isBatchDelete = false;

            document.getElementById('deleteModalTitle').textContent = '确认删除';
            document.getElementById('deleteConfirmText').textContent = '确定要删除该条主播经营数据吗？此操作不可逆。';

            document.getElementById('deleteConfirmModal').classList.add('active');
            document.body.style.overflow = 'hidden';
        }

        // 确认批量删除
        function confirmBatchDelete() {
            isBatchDelete = true;
            document.getElementById('deleteModalTitle').textContent = '批量删除';
            document.getElementById('deleteConfirmText').textContent = `确定要删除选中的 ${selectedItems.length} 条主播经营数据吗？此操作不可逆。`;

            document.getElementById('deleteConfirmModal').classList.add('active');
            document.body.style.overflow = 'hidden';
        }

        // 关闭删除确认框
        function closeDeleteConfirmModal() {
            document.getElementById('deleteConfirmModal').classList.remove('active');
            document.body.style.overflow = 'auto';
            deleteItemId = null;
            isBatchDelete = false;
        }

        // 执行删除
        function executeDelete() {
            const deleteButton = document.querySelector('#deleteConfirmModal .btn-primary');
            const loadingElement = document.getElementById('deleteLoading');
            const textElement = document.getElementById('deleteButtonText');

            // 显示加载状态
            loadingElement.style.display = 'flex';
            textElement.style.display = 'none';
            deleteButton.disabled = true;

            // 模拟异步删除
            setTimeout(() => {
                if (isBatchDelete) {
                    // 批量删除
                    broadcastData = broadcastData.filter(item => !selectedItems.includes(item.id));
                    selectedItems = [];
                    document.getElementById('batchDeleteBtn').style.display = 'none';
                    document.getElementById('selectAllCheckbox').checked = false;

                    showNotification(`已删除 ${selectedItems.length} 条数据`, 'success');
                } else {
                    // 单条删除
                    broadcastData = broadcastData.filter(item => item.id !== deleteItemId);
                    showNotification('删除成功', 'success');
                }

                renderBroadcastTable();
                updatePagination();
                closeDeleteConfirmModal();

                // 重置按钮状态
                loadingElement.style.display = 'none';
                textElement.style.display = 'inline';
                deleteButton.disabled = false;
            }, 1000);
        }

        // 处理勾选变化
        function handleCheckboxChange() {
            const checkboxes = document.querySelectorAll('.row-checkbox:checked');
            selectedItems = Array.from(checkboxes).map(cb => parseInt(cb.value));

            document.getElementById('batchDeleteBtn').style.display = selectedItems.length > 0 ? 'flex' : 'none';

            // 更新全选框状态
            const allCheckboxes = document.querySelectorAll('.row-checkbox');
            document.getElementById('selectAllCheckbox').checked =
                selectedItems.length > 0 && selectedItems.length === allCheckboxes.length;
        }

        // 全选/取消全选
        function toggleSelectAll() {
            const isChecked = document.getElementById('selectAllCheckbox').checked;

            document.querySelectorAll('.row-checkbox').forEach(checkbox => {
                checkbox.checked = isChecked;
            });

            if (isChecked) {
                const allIds = Array.from(document.querySelectorAll('.row-checkbox')).map(cb => parseInt(cb.value));
                selectedItems = allIds;
                document.getElementById('batchDeleteBtn').style.display = 'flex';
            } else {
                selectedItems = [];
                document.getElementById('batchDeleteBtn').style.display = 'none';
            }
        }

        // 打开导入模态框
        function openImportModal() {
            document.getElementById('importModal').classList.add('active');
            document.body.style.overflow = 'hidden';
            document.getElementById('fileName').textContent = '';
            document.getElementById('uploadButton').disabled = true;
            document.getElementById('fileInput').value = '';
        }

        // 关闭导入模态框
        function closeImportModal() {
            document.getElementById('importModal').classList.remove('active');
            document.body.style.overflow = 'auto';
        }

        // 下载模板
        function downloadTemplate() {
            showNotification('模板下载中...', 'info');
            setTimeout(() => {
                showNotification('模板下载完成', 'success');
            }, 1000);
        }

        // 上传文件
        function uploadFile() {
            const uploadButton = document.getElementById('uploadButton');
            const loadingElement = document.getElementById('importLoading');
            const textElement = document.getElementById('importButtonText');

            // 显示加载状态
            loadingElement.style.display = 'flex';
            textElement.style.display = 'none';
            uploadButton.disabled = true;

            // 模拟文件上传和数据导入
            setTimeout(() => {
                // 模拟导入3条新数据
                const lastId = Math.max(0, ...broadcastData.map(item => item.id));

                for (let i = 1; i <= 3; i++) {
                    const newId = lastId + i;
                    const date = new Date();
                    date.setDate(date.getDate() - i);

                    broadcastData.unshift({
                        id: newId,
                        date: date.toISOString().split('T')[0],
                        team: '导入团队' + i,
                        anchorId: i % 2 + 1,
                        anchorName: i % 2 === 0 ? '张小美' : '李明星',
                        store: '导入店铺' + i,
                        startTime: `${date.toISOString().split('T')[0]}T19:00`,
                        endTime: `${date.toISOString().split('T')[0]}T22:00`,
                        product: `IMP-${100 + i}`,
                        salesAmount: 30000 + i * 1000,
                        maxOnlineUsers: 2000 + i * 100,
                        avgOnlineUsers: 1000 + i * 50,
                        avgPrice: 400 + i * 10,
                        retentionRate: 30 + i,
                        interactionRate: 35 + i,
                        uvValue: 9 + i * 0.5
                    });
                }

                renderBroadcastTable();
                updatePagination();
                closeImportModal();
                showNotification('成功导入3条数据', 'success');

                // 重置按钮状态
                loadingElement.style.display = 'none';
                textElement.style.display = 'inline';
                uploadButton.disabled = false;
            }, 2000);
        }

        // 导出数据
        function exportData() {
            showNotification('正在准备导出数据...', 'info');

            setTimeout(() => {
                showNotification('数据导出成功', 'success');
            }, 1500);
        }

        // 更新数据周期文本显示
        function updateDataPeriodText() {
            // 由于相关的DOM元素已移除，此函数不再需要执行任何操作
        }

        // 刷新数据
        function refreshData() {
            // 模拟数据刷新
            setTimeout(() => {
                renderBroadcastTable();
                updatePagination();

                // 显示刷新成功提示
                showNotification('数据已刷新', 'success');
            }, 800);
        }
    </script>
</body>

</html>