<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>商品报备活动管理系统</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/element-ui@2.15.13/lib/theme-chalk/index.css">
    <style>
        /* 全局样式 */
        
        body {
            font-family: "Helvetica Neue", Helvetica, "PingFang SC", "Hiragino Sans GB", "Microsoft YaHei", "微软雅黑", Arial, sans-serif;
            -webkit-font-smoothing: antialiased;
            -moz-osx-font-smoothing: grayscale;
            margin: 0;
            padding: 0;
            color: #333;
            background-color: #f5f7fa;
        }
        
        #app {
            min-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        /* 头部样式 */
        
        .app-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            background: #fff;
            padding: 15px 20px;
            border-radius: 4px;
            box-shadow: 0 1px 4px rgba(0, 0, 0, 0.1);
        }
        
        .app-header h1 {
            font-size: 22px;
            color: #303133;
            margin: 0;
            font-weight: 500;
        }
        
        .header-actions {
            display: flex;
            gap: 10px;
        }
        /* 搜索区域样式 */
        
        .search-container {
            background: #fff;
            padding: 20px;
            margin-bottom: 20px;
            border-radius: 4px;
            box-shadow: 0 1px 4px rgba(0, 0, 0, 0.1);
        }
        
        .search-form {
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
            align-items: center;
        }
        
        .search-form .el-form-item {
            margin-bottom: 10px;
            margin-right: 10px;
        }
        /* 表格区域样式 */
        
        .table-container {
            background: #fff;
            padding: 20px;
            border-radius: 4px;
            box-shadow: 0 1px 4px rgba(0, 0, 0, 0.1);
        }
        
        .table-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
        }
        
        .table-header h3 {
            font-size: 18px;
            margin: 0;
            color: #303133;
            font-weight: 500;
        }
        
        .batch-actions {
            display: flex;
            gap: 10px;
        }
        /* 商品信息样式 */
        
        .product-info {
            display: flex;
            align-items: center;
        }
        
        .product-image {
            width: 60px;
            height: 60px;
            overflow: hidden;
            border-radius: 4px;
            margin-right: 10px;
            cursor: pointer;
            border: 1px solid #ebeef5;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .product-image img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }
        
        .product-details {
            flex: 1;
            min-width: 0;
        }
        
        .product-name {
            font-weight: bold;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
            margin-bottom: 4px;
        }
        
        .product-code,
        .product-store,
        .product-team,
        .product-platform {
            font-size: 12px;
            color: #606266;
            margin-top: 3px;
        }
        /* 迷你版商品信息 */
        
        .product-info.mini .product-image {
            width: 40px;
            height: 40px;
        }
        
        .product-info.mini .product-name {
            font-size: 13px;
        }
        
        .product-info.mini .product-store {
            font-size: 11px;
        }
        /* 活动信息样式 */
        
        .activity-cell {
            border-bottom: 1px solid #EBEEF5;
            padding: 5px 0;
        }
        
        .activity-cell:last-child {
            border-bottom: none;
        }
        
        .activity-name {
            font-weight: bold;
            margin-bottom: 4px;
        }
        
        .activity-date {
            font-size: 12px;
            color: #606266;
            margin-top: 3px;
        }
        /* 状态标签样式 */
        
        .status-tag {
            margin-top: 5px;
        }
        /* 更新信息样式 */
        
        .update-info {
            font-size: 13px;
        }
        
        .update-user {
            font-size: 12px;
            color: #606266;
            margin-top: 4px;
        }
        /* 分页样式 */
        
        .pagination-container {
            margin-top: 20px;
            display: flex;
            justify-content: flex-end;
        }
        /* 操作按钮样式 */
        
        .danger-button {
            color: #F56C6C;
        }
        
        .danger-button:hover {
            color: #ff7c7c;
        }
        
        .operation-buttons {
            display: flex;
            align-items: center;
            flex-wrap: wrap;
            gap: 5px;
        }
        
        .operation-buttons .el-button {
            margin-left: 0;
            margin-right: 10px;
            padding: 7px 8px;
        }
        
        .el-dropdown-link {
            cursor: pointer;
            color: #409EFF;
            font-size: 13px;
        }
        
        .el-dropdown-link:hover {
            color: #66b1ff;
        }
        /* 商品选择样式 */
        
        .product-selection {
            width: 100%;
        }
        
        .selected-products {
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
            align-items: center;
            min-height: 32px;
        }
        
        .product-tag {
            display: flex;
            align-items: center;
            margin-right: 8px;
        }
        
        .add-product-btn {
            height: 32px;
        }
        
        .product-limit-hint {
            color: #F56C6C;
            font-size: 13px;
        }
        
        .product-search {
            margin-bottom: 15px;
        }
        
        .selected-info {
            flex: 1;
            text-align: left;
            color: #606266;
        }
        /* 活动选择样式 */
        
        .activity-select-container {
            display: flex;
            flex-direction: column;
            gap: 15px;
        }
        
        .activity-option {
            display: flex;
            align-items: center;
        }
        /* 图片预览样式 */
        
        .image-preview-container {
            display: flex;
            justify-content: center;
            align-items: center;
        }
        
        .preview-image {
            max-width: 100%;
            max-height: 400px;
        }
        /* 导入对话框样式 */
        
        .import-container {
            text-align: center;
        }
        
        .import-actions {
            margin-top: 15px;
        }
        /* 详情页样式 */
        
        .report-detail {
            padding: 10px;
        }
        
        .detail-section-title {
            margin: 20px 0 10px;
            font-size: 16px;
            color: #303133;
            font-weight: 500;
            border-left: 3px solid #409EFF;
            padding-left: 10px;
        }
        
        .detail-remark {
            background-color: #f8f8f8;
            padding: 10px;
            border-radius: 4px;
            min-height: 60px;
        }
        /* 无数据状态 */
        
        .no-data {
            text-align: center;
            color: #909399;
            padding: 30px 0;
            font-size: 14px;
        }
        /* 活动类型与勾选框样式 */
        
        .activities-container {
            display: flex;
            flex-direction: column;
            gap: 10px;
        }
        
        .activity-item {
            display: flex;
            align-items: center;
            padding: 3px 0;
        }
        
        .custom-checkbox {
            margin-right: 8px;
        }
        
        .custom-checkbox .el-checkbox__input {
            height: 18px;
            width: 18px;
        }
        
        .custom-checkbox .el-checkbox__inner {
            height: 18px;
            width: 18px;
            border-radius: 4px;
            border: 1px solid #dcdfe6;
        }
        
        .custom-checkbox .el-checkbox__inner::after {
            height: 9px;
            width: 4px;
            left: 6px;
            top: 2px;
        }
        
        .custom-checkbox.is-checked .el-checkbox__inner {
            background-color: #409EFF;
            border-color: #409EFF;
        }
        /* 活动日期样式 */
        
        .activities-date-container {
            display: flex;
            flex-direction: column;
            gap: 10px;
        }
        
        .activity-date-item {
            padding: 3px 0;
            height: 24px;
            line-height: 24px;
        }
        
        .no-date {
            color: #909399;
            font-style: italic;
        }
        
        .activity-status-tag {
            margin-left: 5px;
        }
        /* 行高亮和鼠标悬停样式 */
        
        .highlight-row {
            background-color: #f0f9eb !important;
        }
        
        .el-table__row:hover {
            background-color: #f5f7fa;
        }
        /* 自定义下拉菜单样式 */
        
        .el-dropdown-menu__item {
            padding: 8px 16px;
            font-size: 13px;
        }
        /* 时间线样式 */
        
        .el-timeline-item__timestamp {
            font-size: 12px !important;
        }
        /* 表单验证样式 */
        
        .el-form-item__error {
            padding-top: 2px;
        }
    </style>
</head>

<body>
    <div id="app">
        <!-- 页面标题 -->
        <div class="app-header">
            <h1>商品报备活动管理系统</h1>
            <div class="header-actions">
                <el-button type="primary" @click="addReportDialogVisible = true" :disabled="!userPermissions.canCreate">
                    <i class="el-icon-plus"></i> 新增报备
                </el-button>
                <el-button type="success" @click="exportReportData('xlsx')">
                    <i class="el-icon-download"></i> 导出数据
                </el-button>
            </div>
        </div>

        <!-- 搜索区域 -->
        <div class="search-container">
            <el-form :model="searchForm" ref="searchForm" :inline="true" class="search-form">
                <el-form-item label="报备编号">
                    <el-input v-model="searchForm.reportId" placeholder="请输入报备编号" clearable></el-input>
                </el-form-item>
                <el-form-item label="商品名称">
                    <el-input v-model="searchForm.productName" placeholder="请输入商品名称" clearable></el-input>
                </el-form-item>
                <el-form-item label="店铺名称">
                    <el-input v-model="searchForm.storeName" placeholder="请输入店铺名称" clearable></el-input>
                </el-form-item>
                <el-form-item label="活动类型">
                    <el-select v-model="searchForm.activityType" placeholder="请选择活动类型" clearable>
                        <el-option label="新品主推让利" value="promo"></el-option>
                        <el-option label="新品赛马" value="race"></el-option>
                        <el-option label="新品亏损扶持" value="support"></el-option>
                    </el-select>
                </el-form-item>
                <el-form-item label="活动状态">
                    <el-select v-model="searchForm.status" placeholder="请选择状态" clearable>
                        <el-option label="待审核" value="pending"></el-option>
                        <el-option label="已通过" value="approved"></el-option>
                        <el-option label="已拒绝" value="rejected"></el-option>
                    </el-select>
                </el-form-item>
                <el-form-item label="活动日期">
                    <el-date-picker v-model="searchForm.activityDateRange" type="daterange" range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期" value-format="yyyy-MM-dd">
                    </el-date-picker>
                </el-form-item>
                <el-form-item>
                    <el-button type="primary" @click="handleSearch">
                        <i class="el-icon-search"></i> 查询
                    </el-button>
                    <el-button @click="resetSearch">
                        <i class="el-icon-refresh"></i> 重置
                    </el-button>
                </el-form-item>
            </el-form>
        </div>

        <!-- 数据表格 -->
        <div class="table-container">
            <div class="table-header">
                <h3>报备记录列表</h3>
                <div class="batch-actions" v-if="userPermissions.canApprove">
                    <el-button size="small" type="success" @click="batchOperateReports('approve', multipleSelection)" :disabled="multipleSelection.length === 0">
                        批量通过
                    </el-button>
                    <el-button size="small" type="danger" @click="batchOperateReports('reject', multipleSelection)" :disabled="multipleSelection.length === 0">
                        批量拒绝
                    </el-button>
                </div>
            </div>

            <el-table ref="reportTable" v-loading="loading" :data="tableData" border stripe style="width: 100%" @selection-change="handleSelectionChange" :row-style="getTableRowStyle">
                <el-table-column type="selection" width="55"></el-table-column>
                <el-table-column type="index" width="50" label="序号" v-if="false"></el-table-column>
                <el-table-column prop="reportId" label="报备编号" width="180"></el-table-column>
                <el-table-column label="商品信息" width="280">
                    <template slot-scope="scope">
            <div class="product-info">
              <div class="product-image" @click="previewProductImage(scope.row.product.image)">
                <img :src="scope.row.product.image" alt="商品图片">
              </div>
              <div class="product-details">
                <div class="product-name">{{ scope.row.product.name }}</div>
                <div class="product-code">SKU: {{ scope.row.product.code }}</div>
                <div class="product-store">店铺: {{ scope.row.product.store }}</div>
                <div class="product-team">{{ scope.row.product.team }}</div>
                <div class="product-platform">{{ scope.row.product.platform }}</div>
              </div>
            </div>
          </template>
                </el-table-column>

                <!-- 修改后的活动类型列 -->
                <el-table-column label="活动类型" width="200">
                    <template slot-scope="scope">
            <div class="activities-container">
              <div v-for="(activity, index) in activityTypes" :key="index" class="activity-item">
                <el-checkbox 
                  :value="isActivitySelected(scope.row, activity.type)"
                  @change="(val) => handleActivityCheck(val, scope.row, activity)"
                  :disabled="isActivityCheckDisabled(scope.row, activity.type)"
                  class="custom-checkbox">
                  {{ activity.name }}
                </el-checkbox>
                <el-tag v-if="isActivitySelected(scope.row, activity.type)" 
                  size="small" 
                  :type="getStatusTagType(getActivityStatus(scope.row, activity.type))"
                  class="activity-status-tag">
                  {{ getStatusText(getActivityStatus(scope.row, activity.type)) }}
                </el-tag>
              </div>
            </div>
          </template>
                </el-table-column>

                <!-- 新增的活动日期列 -->
                <el-table-column label="活动日期" width="220">
                    <template slot-scope="scope">
            <div class="activities-date-container">
              <div v-for="(activity, index) in activityTypes" :key="index" class="activity-date-item">
                <template v-if="isActivitySelected(scope.row, activity.type)">
                  {{ formatDate(getActivityStartDate(scope.row, activity.type)) }}
                  至
                  {{ formatDate(getActivityEndDate(scope.row, activity.type)) }}
                </template>
                    <template v-else>
                  <span class="no-date">未设置</span>
                </template>
        </div>
    </div>
    </template>
    </el-table-column>

    <el-table-column label="最近更新" width="170">
        <template slot-scope="scope">
            <div class="update-info">
              <div>{{ scope.row.updateTime }}</div>
              <div class="update-user">{{ scope.row.updateUser }}</div>
            </div>
          </template>
    </el-table-column>

    <el-table-column label="操作" fixed="right" width="150">
        <template slot-scope="scope">
            <div class="operation-buttons">
                <el-button
                  @click="viewReportDetail(scope.row)"
                  type="text"
                  size="small">
                  详情
                </el-button>
                <el-button
                  v-if="userPermissions.canEdit"
                  @click="editReport(scope.row)"
                  type="text"
                  size="small">
                  编辑
                </el-button>
                <el-button
                  v-if="userPermissions.canDelete && false"
                  @click="deleteReport(scope.row)"
                  type="text"
                  size="small"
                  class="danger-button">
                  删除
                </el-button>
                <el-dropdown v-if="userPermissions.canApprove && false" trigger="click">
                  <span class="el-dropdown-link">
                    更多<i class="el-icon-arrow-down el-icon--right"></i>
                  </span>
                  <el-dropdown-menu slot="dropdown">
                    <el-dropdown-item @click.native="viewOperationHistory(scope.row.reportId)">操作记录</el-dropdown-item>
                    <el-dropdown-item @click.native="sendNotification('sms', scope.row.reportId, 'approved')">发送短信通知</el-dropdown-item>
                    <el-dropdown-item @click.native="sendNotification('email', scope.row.reportId, 'approved')">发送邮件通知</el-dropdown-item>
                  </el-dropdown-menu>
                </el-dropdown>
            </div>
          </template>
    </el-table-column>
    </el-table>

    <!-- 分页组件 -->
    <div class="pagination-container">
        <el-pagination @size-change="handleSizeChange" @current-change="handleCurrentChange" :current-page="pagination.currentPage" :page-sizes="[10, 20, 50, 100]" :page-size="pagination.pageSize" layout="total, sizes, prev, pager, next, jumper" :total="pagination.total">
        </el-pagination>
    </div>
    </div>

    <!-- 新增报备对话框 -->
    <el-dialog title="新增商品报备" :visible.sync="addReportDialogVisible" width="850px" :before-close="handleAddReportDialogClose">
        <el-form :model="reportForm" ref="reportForm" label-width="100px" :rules="formRules">
            <!-- 商品选择区域 -->
            <el-form-item label="选择商品" prop="products" required>
                <div class="product-selection">
                    <div class="selected-products">
                        <el-tag v-for="product in reportForm.products" :key="product.id" closable @close="removeProduct(product)" class="product-tag">
                            {{ product.name }}
                        </el-tag>
                        <el-button v-if="reportForm.products.length < 5" size="small" @click="productSelectionVisible = true" class="add-product-btn">
                            + 添加商品 ({{ reportForm.products.length }}/5)
                        </el-button>
                        <div class="product-limit-hint" v-else>
                            已达到选择上限 (5个)
                        </div>
                    </div>
                </div>
            </el-form-item>

            <!-- 活动类型选择 -->
            <el-form-item label="活动类型" required>
                <div class="activity-select-container">
                    <div v-for="(activity, index) in activityOptions" :key="activity.type" class="activity-option">
                        <el-checkbox v-model="activity.selected" @change="handleActivityOptionChange(activity)">
                            {{ activity.name }}
                        </el-checkbox>
                        <el-date-picker v-if="activity.selected" v-model="activity.dateRange" type="daterange" range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期" value-format="yyyy-MM-dd" style="width: 340px; margin-left: 15px;">
                        </el-date-picker>
                    </div>
                </div>
            </el-form-item>

            <!-- 备注信息 -->
            <el-form-item label="备注">
                <el-input type="textarea" v-model="reportForm.remark" placeholder="请输入备注信息" maxlength="200" show-word-limit rows="3">
                </el-input>
            </el-form-item>
        </el-form>
        <div slot="footer" class="dialog-footer">
            <el-button @click="addReportDialogVisible = false">取 消</el-button>
            <el-button type="primary" @click="submitReportForm" :loading="submitting">提 交</el-button>
        </div>
    </el-dialog>

    <!-- 商品选择对话框 -->
    <el-dialog title="选择商品" :visible.sync="productSelectionVisible" width="900px" append-to-body>
        <div class="product-search">
            <el-input v-model="productSearchKeyword" placeholder="输入商品名称或编码搜索" prefix-icon="el-icon-search" @input="searchProducts" clearable>
            </el-input>
        </div>
        <el-table :data="filteredProducts" border style="width: 100%" height="350px" @selection-change="handleProductSelectionChange">
            <el-table-column type="selection" width="55"></el-table-column>
            <el-table-column prop="code" label="商品编码" width="120"></el-table-column>
            <el-table-column label="商品信息" width="350">
                <template slot-scope="scope">
            <div class="product-info mini">
              <div class="product-image mini">
                <img :src="scope.row.image" alt="商品图片">
              </div>
              <div class="product-details">
                <div class="product-name">{{ scope.row.name }}</div>
                <div class="product-store">{{ scope.row.store }}</div>
              </div>
            </div>
          </template>
            </el-table-column>
            <el-table-column prop="team" label="所属团队" width="120"></el-table-column>
            <el-table-column prop="platform" label="销售平台" width="120"></el-table-column>
            <el-table-column label="操作" width="80">
                <template slot-scope="scope">
                <div class="operation-buttons">
                    <el-button
                      type="text"
                      size="small"
                      @click="selectProduct(scope.row)"
                      :disabled="isProductSelected(scope.row) || reportForm.products.length >= 5">
                      选择
                    </el-button>
                </div>
              </template>
            </el-table-column>
        </el-table>
        <div slot="footer" class="dialog-footer">
            <div class="selected-info">已选择: {{ selectedProductsCount }}/5</div>
            <el-button @click="productSelectionVisible = false">取 消</el-button>
            <el-button type="primary" @click="confirmProductSelection">确 定</el-button>
        </div>
    </el-dialog>

    <!-- 图片预览 -->
    <el-dialog :visible.sync="imagePreviewVisible" title="商品图片预览" width="500px" center>
        <div class="image-preview-container">
            <img :src="previewImageUrl" alt="商品图片预览" class="preview-image">
        </div>
    </el-dialog>

    <!-- 报备详情对话框 -->
    <el-dialog title="报备详情" :visible.sync="detailDialogVisible" width="800px">
        <div v-if="currentReportDetail" class="report-detail">
            <el-descriptions :column="2" border>
                <el-descriptions-item label="报备编号">{{ currentReportDetail.reportId }}</el-descriptions-item>
                <el-descriptions-item label="报备日期">{{ currentReportDetail.createTime }}</el-descriptions-item>
                <el-descriptions-item label="商品名称">{{ currentReportDetail.product.name }}</el-descriptions-item>
                <el-descriptions-item label="商品编码">{{ currentReportDetail.product.code }}</el-descriptions-item>
                <el-descriptions-item label="店铺">{{ currentReportDetail.product.store }}</el-descriptions-item>
                <el-descriptions-item label="所属团队">{{ currentReportDetail.product.team }}</el-descriptions-item>
                <el-descriptions-item label="平台">{{ currentReportDetail.product.platform }}</el-descriptions-item>
                <el-descriptions-item label="更新时间">{{ currentReportDetail.updateTime }}</el-descriptions-item>
            </el-descriptions>

            <h4 class="detail-section-title">活动信息</h4>
            <el-table :data="currentReportDetail.activities" border style="width: 100%">
                <el-table-column prop="name" label="活动类型" width="150"></el-table-column>
                <el-table-column label="活动日期" width="220">
                    <template slot-scope="scope">
              {{ scope.row.startDate }} 至 {{ scope.row.endDate }}
            </template>
                </el-table-column>
                <el-table-column label="状态" width="120">
                    <template slot-scope="scope">
              <el-tag :type="getStatusTagType(scope.row.status)">
                {{ getStatusText(scope.row.status) }}
              </el-tag>
            </template>
                </el-table-column>
                <el-table-column label="操作" width="150">
                    <template slot-scope="scope">
              <div class="operation-buttons">
                <el-button 
                  v-if="scope.row.status === 'pending' && userPermissions.canApprove" 
                  type="text" 
                  @click="reviewReportActivity(currentReportDetail.reportId, scope.row.type, 'approve')">
                  审核通过
                </el-button>
                <el-button 
                  v-if="scope.row.status === 'pending' && userPermissions.canApprove" 
                  type="text"
                  class="danger-button" 
                  @click="showRejectDialog(currentReportDetail.reportId, scope.row.type)">
                  拒绝
                </el-button>
              </div>
            </template>
                </el-table-column>
            </el-table>

            <h4 class="detail-section-title">备注信息</h4>
            <div class="detail-remark">
                {{ currentReportDetail.remark || '无' }}
            </div>

            <h4 class="detail-section-title">操作记录</h4>
            <el-timeline>
                <el-timeline-item v-for="(history, index) in currentReportDetail.history" :key="index" :timestamp="history.time" :type="getHistoryTypeIcon(history.type)">
                    {{ history.content }}
                </el-timeline-item>
            </el-timeline>
        </div>
    </el-dialog>

    <!-- 操作历史对话框 -->
    <el-dialog title="操作历史记录" :visible.sync="historyDialogVisible" width="600px">
        <div v-if="currentReportDetail && currentReportDetail.history">
            <el-timeline>
                <el-timeline-item v-for="(history, index) in currentReportDetail.history" :key="index" :timestamp="history.time" :type="getHistoryTypeIcon(history.type)">
                    {{ history.content }}
                </el-timeline-item>
            </el-timeline>
        </div>
        <div v-else class="no-data">暂无操作记录</div>
    </el-dialog>

    <!-- 拒绝原因对话框 -->
    <el-dialog title="填写拒绝原因" :visible.sync="rejectDialogVisible" width="500px">
        <el-form :model="rejectForm" :rules="rejectFormRules">
            <el-form-item label="拒绝原因" prop="reason" required>
                <el-input type="textarea" v-model="rejectForm.reason" placeholder="请输入拒绝原因" rows="3">
                </el-input>
            </el-form-item>
        </el-form>
        <div slot="footer" class="dialog-footer">
            <el-button @click="rejectDialogVisible = false">取 消</el-button>
            <el-button type="primary" @click="confirmRejectActivity">确 定</el-button>
        </div>
    </el-dialog>

    <!-- 批量导入对话框 -->
    <el-dialog title="批量导入商品" :visible.sync="importDialogVisible" width="500px">
        <div class="import-container">
            <el-upload class="upload-demo" drag action="#" :http-request="handleFileUpload" :show-file-list="false" :multiple="false" accept=".xlsx,.csv">
                <i class="el-icon-upload"></i>
                <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
                <div class="el-upload__tip">只能上传xlsx/csv文件，且不超过10MB</div>
            </el-upload>
            <div class="import-actions">
                <el-button type="text">
                    <i class="el-icon-download"></i> 下载导入模板
                </el-button>
            </div>
        </div>
    </el-dialog>
    </div>

    <!-- 引入Vue和Element UI的JS -->
    <script src="https://cdn.jsdelivr.net/npm/vue@2.6.14/dist/vue.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/element-ui@2.15.13/lib/index.js"></script>
    <script>
        // 初始化Vue应用
        new Vue({
            el: '#app',
            data() {
                return {
                    // 表格数据
                    tableData: [],
                    // 表格加载状态
                    loading: false,
                    // 提交状态
                    submitting: false,
                    // 页面权限
                    userPermissions: {
                        canCreate: true,
                        canEdit: true,
                        canDelete: true,
                        canApprove: true
                    },
                    // 搜索表单
                    searchForm: {
                        reportId: '',
                        productName: '',
                        storeName: '',
                        activityType: '',
                        status: '',
                        activityDateRange: []
                    },
                    // 批量选择
                    multipleSelection: [],
                    // 分页信息
                    pagination: {
                        currentPage: 1,
                        pageSize: 10,
                        total: 0
                    },
                    // 对话框可见性控制
                    addReportDialogVisible: false,
                    productSelectionVisible: false,
                    imagePreviewVisible: false,
                    detailDialogVisible: false,
                    historyDialogVisible: false,
                    rejectDialogVisible: false,
                    importDialogVisible: false,

                    // 预览图片URL
                    previewImageUrl: '',

                    // 报备表单
                    reportForm: {
                        products: [],
                        activities: [],
                        remark: ''
                    },

                    // 拒绝表单
                    rejectForm: {
                        reportId: '',
                        activityType: '',
                        reason: ''
                    },

                    // 商品搜索
                    productSearchKeyword: '',
                    selectedProducts: [],

                    // 活动类型
                    activityTypes: [{
                        type: 'promo',
                        name: '新品主推让利'
                    }, {
                        type: 'race',
                        name: '新品赛马'
                    }, {
                        type: 'support',
                        name: '新品亏损扶持'
                    }],

                    // 新增报备时的活动选项
                    activityOptions: [{
                        type: 'promo',
                        name: '新品主推让利',
                        selected: false,
                        dateRange: []
                    }, {
                        type: 'race',
                        name: '新品赛马',
                        selected: false,
                        dateRange: []
                    }, {
                        type: 'support',
                        name: '新品亏损扶持',
                        selected: false,
                        dateRange: []
                    }],

                    // 当前查看的报备详情
                    currentReportDetail: null,

                    // 模拟产品数据
                    allProducts: [{
                        id: 1,
                        code: 'SKU20086',
                        name: 'Samsung Galaxy S24',
                        image: 'https://via.placeholder.com/100',
                        store: '专卖店',
                        team: '销售二部',
                        platform: '京东'
                    }, {
                        id: 2,
                        code: 'SKU20087',
                        name: 'iPhone 15 Pro',
                        image: 'https://via.placeholder.com/100',
                        store: 'Apple官方旗舰店',
                        team: '销售一部',
                        platform: '天猫'
                    }, {
                        id: 3,
                        code: 'SKU20088',
                        name: 'Google Pixel 8',
                        image: 'https://via.placeholder.com/100',
                        store: 'Google官方店',
                        team: '销售三部',
                        platform: '小红书'
                    }, {
                        id: 4,
                        code: 'SKU20089',
                        name: 'Xiaomi 14 Pro',
                        image: 'https://via.placeholder.com/100',
                        store: '小米官方旗舰店',
                        team: '销售二部',
                        platform: '京东'
                    }, {
                        id: 5,
                        code: 'SKU20090',
                        name: 'OPPO Find X7',
                        image: 'https://via.placeholder.com/100',
                        store: 'OPPO官方旗舰店',
                        team: '销售四部',
                        platform: '拼多多'
                    }],

                    // 表单验证规则
                    formRules: {
                        products: [{
                            required: true,
                            message: '请选择至少一个商品',
                            trigger: 'change'
                        }]
                    },

                    // 拒绝表单验证规则
                    rejectFormRules: {
                        reason: [{
                            required: true,
                            message: '请填写拒绝原因',
                            trigger: 'blur'
                        }, {
                            min: 5,
                            message: '拒绝原因不能少于5个字符',
                            trigger: 'blur'
                        }]
                    }
                };
            },

            computed: {
                // 过滤后的商品列表
                filteredProducts() {
                    if (!this.productSearchKeyword) {
                        return this.allProducts;
                    }

                    const keyword = this.productSearchKeyword.toLowerCase();
                    return this.allProducts.filter(product =>
                        product.name.toLowerCase().includes(keyword) ||
                        product.code.toLowerCase().includes(keyword)
                    );
                },

                // 已选择的商品数量
                selectedProductsCount() {
                    return this.selectedProducts.length;
                }
            },

            created() {
                // 获取初始数据
                this.fetchReportData();
            },

            methods: {
                // 获取报备数据
                fetchReportData() {
                    this.loading = true;

                    // 模拟API请求
                    setTimeout(() => {
                        // 生成模拟数据
                        this.tableData = this.generateMockData();
                        this.pagination.total = 123; // 模拟总数据量
                        this.loading = false;
                    }, 500);
                },

                // 生成模拟数据
                generateMockData() {
                    const data = [];

                    for (let i = 0; i < this.pagination.pageSize; i++) {
                        const reportId = `RPT${Math.floor(Math.random() * 10000000000).toString().padStart(11, '0')}`;
                        const productIndex = i % this.allProducts.length;

                        // 随机生成活动
                        const activities = [];
                        const activityStatuses = ['pending', 'approved', 'rejected'];

                        // 至少添加一种活动类型
                        activities.push({
                            type: 'promo',
                            name: '新品主推让利',
                            status: activityStatuses[Math.floor(Math.random() * 3)],
                            startDate: '2025-05-10',
                            endDate: '2025-06-10',
                            checked: true
                        });

                        // 随机添加其它活动类型
                        if (Math.random() > 0.5) {
                            activities.push({
                                type: 'race',
                                name: '新品赛马',
                                status: activityStatuses[Math.floor(Math.random() * 3)],
                                startDate: '2025-05-15',
                                endDate: '2025-06-15',
                                checked: true
                            });

                            // 如果有赛马活动，有概率添加扶持活动
                            if (Math.random() > 0.6) {
                                activities.push({
                                    type: 'support',
                                    name: '新品亏损扶持',
                                    status: activityStatuses[Math.floor(Math.random() * 3)],
                                    startDate: '2025-05-15',
                                    endDate: '2025-06-15',
                                    checked: true
                                });
                            }
                        }

                        data.push({
                            reportId: reportId,
                            product: this.allProducts[productIndex],
                            activities: activities,
                            updateTime: '2025-05-06 14:30:22',
                            updateUser: '张经理',
                            createTime: '2025-05-05 10:15:32',
                            remark: '新品上市促销活动',
                            history: [{
                                time: '2025-05-05 10:15:32',
                                content: '创建报备',
                                type: 'create'
                            }, {
                                time: '2025-05-06 14:30:22',
                                content: '更新活动信息',
                                type: 'update'
                            }]
                        });
                    }

                    return data;
                },

                // 处理搜索
                handleSearch() {
                    this.pagination.currentPage = 1;
                    this.fetchReportData();
                },

                // 重置搜索条件
                resetSearch() {
                    this.searchForm = {
                        reportId: '',
                        productName: '',
                        storeName: '',
                        activityType: '',
                        status: '',
                        activityDateRange: []
                    };
                    this.handleSearch();
                },

                // 处理分页大小变化
                handleSizeChange(val) {
                    this.pagination.pageSize = val;
                    this.fetchReportData();
                },

                // 处理当前页变化
                handleCurrentChange(val) {
                    this.pagination.currentPage = val;
                    this.fetchReportData();
                },

                // 处理表格选择变化
                handleSelectionChange(val) {
                    this.multipleSelection = val;
                },

                // 获取表格行样式
                getTableRowStyle({
                    row,
                    rowIndex
                }) {
                    // 对已更新的行高亮显示
                    if (row.isUpdated) {
                        return {
                            backgroundColor: '#f0f9eb'
                        };
                    }
                    return null;
                },

                // 预览商品图片
                previewProductImage(imageUrl) {
                    this.previewImageUrl = imageUrl;
                    this.imagePreviewVisible = true;
                },

                // 查看报备详情
                viewReportDetail(row) {
                    this.currentReportDetail = JSON.parse(JSON.stringify(row));
                    this.detailDialogVisible = true;
                },

                // 编辑报备
                editReport(row) {
                    // 实际项目中应跳转到编辑页面或弹出编辑对话框
                    this.$message({
                        message: '编辑功能待实现',
                        type: 'info'
                    });
                },

                // 删除报备
                deleteReport(row) {
                    this.$confirm('确定要删除该报备记录吗？', '提示', {
                        confirmButtonText: '确定',
                        cancelButtonText: '取消',
                        type: 'warning'
                    }).then(() => {
                        this.loading = true;

                        // 模拟删除请求
                        setTimeout(() => {
                            const index = this.tableData.findIndex(item => item.reportId === row.reportId);
                            if (index > -1) {
                                this.tableData.splice(index, 1);
                                this.$message({
                                    type: 'success',
                                    message: '删除成功!'
                                });
                            }
                            this.loading = false;
                        }, 400);
                    }).catch(() => {
                        // 用户取消删除
                    });
                },

                // 查看操作历史
                viewOperationHistory(reportId) {
                    const report = this.tableData.find(item => item.reportId === reportId);
                    if (report) {
                        this.currentReportDetail = JSON.parse(JSON.stringify(report));
                        this.historyDialogVisible = true;
                    }
                },

                // 发送通知
                sendNotification(type, reportId, status) {
                    let message = '';
                    if (type === 'sms') {
                        message = '短信通知已发送';
                    } else if (type === 'email') {
                        message = '邮件通知已发送';
                    }

                    this.$message({
                        message,
                        type: 'success'
                    });
                },

                // 批量操作报备
                batchOperateReports(operation, selection) {
                    if (selection.length === 0) {
                        this.$message({
                            message: '请选择需要操作的记录',
                            type: 'warning'
                        });
                        return;
                    }

                    let confirmMessage = '';
                    let successMessage = '';

                    if (operation === 'approve') {
                        confirmMessage = '确定要批量通过选中的报备吗？';
                        successMessage = '批量审核通过成功';
                    } else if (operation === 'reject') {
                        confirmMessage = '确定要批量拒绝选中的报备吗？';
                        successMessage = '批量拒绝成功';
                    } else {
                        return;
                    }

                    this.$confirm(confirmMessage, '提示', {
                        confirmButtonText: '确定',
                        cancelButtonText: '取消',
                        type: 'warning'
                    }).then(() => {
                        this.loading = true;

                        // 模拟批量操作请求
                        setTimeout(() => {
                            selection.forEach(row => {
                                if (row.activities) {
                                    row.activities.forEach(activity => {
                                        if (activity.status === 'pending') {
                                            activity.status = operation === 'approve' ? 'approved' : 'rejected';
                                        }
                                    });
                                }
                            });

                            this.$message({
                                type: 'success',
                                message: successMessage
                            });

                            this.loading = false;
                        }, 600);
                    }).catch(() => {
                        // 用户取消操作
                    });
                },

                // 处理添加报备对话框关闭
                handleAddReportDialogClose(done) {
                    if (this.reportForm.products.length > 0 || this.isAnyActivitySelected()) {
                        this.$confirm('确定要取消新增报备吗？已填写的内容将丢失', '提示', {
                            confirmButtonText: '确定',
                            cancelButtonText: '取消',
                            type: 'warning'
                        }).then(() => {
                            this.resetReportForm();
                            done();
                        }).catch(() => {});
                    } else {
                        this.resetReportForm();
                        done();
                    }
                },

                // 重置报备表单
                resetReportForm() {
                    this.reportForm = {
                        products: [],
                        activities: [],
                        remark: ''
                    };

                    this.activityOptions.forEach(activity => {
                        activity.selected = false;
                        activity.dateRange = [];
                    });

                    this.selectedProducts = [];
                },

                // 检查是否有选中的活动
                isAnyActivitySelected() {
                    return this.activityOptions.some(activity => activity.selected);
                },

                // 处理活动选项变化
                handleActivityOptionChange(changedActivity) {
                    // 如果选中赛马活动并且扶持活动依赖于它
                    if (changedActivity.type === 'race' && !changedActivity.selected) {
                        const supportActivity = this.activityOptions.find(a => a.type === 'support');
                        if (supportActivity && supportActivity.selected) {
                            supportActivity.selected = false;
                            this.$message({
                                type: 'warning',
                                message: '新品亏损扶持依赖于新品赛马活动，已自动取消选择'
                            });
                        }
                    }
                },

                // 移除选中的商品
                removeProduct(product) {
                    const index = this.reportForm.products.findIndex(p => p.id === product.id);
                    if (index > -1) {
                        this.reportForm.products.splice(index, 1);
                    }

                    const selectedIndex = this.selectedProducts.findIndex(p => p.id === product.id);
                    if (selectedIndex > -1) {
                        this.selectedProducts.splice(selectedIndex, 1);
                    }
                },

                // 判断商品是否已选中
                isProductSelected(product) {
                    return this.reportForm.products.some(p => p.id === product.id);
                },

                // 搜索商品
                searchProducts() {
                    // 已在计算属性中实现
                },

                // 处理商品选择变化
                handleProductSelectionChange(val) {
                    this.selectedProducts = val;
                },

                // 选择单个商品
                selectProduct(product) {
                    if (this.reportForm.products.length >= 5) {
                        this.$message({
                            type: 'warning',
                            message: '最多只能选择5个商品'
                        });
                        return;
                    }

                    if (!this.isProductSelected(product)) {
                        this.reportForm.products.push(product);
                    }
                },

                // 确认商品选择
                confirmProductSelection() {
                    this.selectedProducts.forEach(product => {
                        if (!this.isProductSelected(product)) {
                            if (this.reportForm.products.length < 5) {
                                this.reportForm.products.push(product);
                            }
                        }
                    });

                    this.productSelectionVisible = false;
                    this.selectedProducts = [];
                },

                // 提交报备表单
                submitReportForm() {
                    // 检查是否选择了商品
                    if (this.reportForm.products.length === 0) {
                        this.$message({
                            message: '请至少选择一个商品',
                            type: 'warning'
                        });
                        return;
                    }

                    // 检查是否选择了活动类型
                    const selectedActivities = this.activityOptions.filter(a => a.selected);
                    if (selectedActivities.length === 0) {
                        this.$message({
                            message: '请至少选择一种活动类型',
                            type: 'warning'
                        });
                        return;
                    }

                    // 检查活动日期是否完整
                    for (const activity of selectedActivities) {
                        if (!activity.dateRange || activity.dateRange.length < 2) {
                            this.$message({
                                message: `请为 ${activity.name} 设置完整的日期范围`,
                                type: 'warning'
                            });
                            return;
                        }
                    }

                    this.submitting = true;

                    // 模拟提交请求
                    setTimeout(() => {
                        // 构造提交的报备数据
                        const newReports = [];

                        this.reportForm.products.forEach(product => {
                            const activities = selectedActivities.map(a => ({
                                type: a.type,
                                name: a.name,
                                status: 'pending',
                                startDate: this.formatDate(a.dateRange[0]),
                                endDate: this.formatDate(a.dateRange[1]),
                                checked: true
                            }));

                            const reportId = `RPT${Math.floor(Math.random() * 10000000000).toString().padStart(11, '0')}`;

                            newReports.push({
                                reportId: reportId,
                                product: product,
                                activities: activities,
                                updateTime: this.formatDateTime(new Date()),
                                updateUser: '当前用户',
                                createTime: this.formatDateTime(new Date()),
                                remark: this.reportForm.remark,
                                history: [{
                                    time: this.formatDateTime(new Date()),
                                    content: '创建报备',
                                    type: 'create'
                                }],
                                isUpdated: true
                            });
                        });

                        // 添加到表格数据
                        this.tableData = [...newReports, ...this.tableData];

                        // 更新分页信息
                        this.pagination.total += newReports.length;

                        this.$message({
                            message: '报备提交成功',
                            type: 'success'
                        });

                        // 重置表单和关闭对话框
                        this.resetReportForm();
                        this.addReportDialogVisible = false;
                        this.submitting = false;
                    }, 800);
                },

                // 审核报备活动
                reviewReportActivity(reportId, activityType, action) {
                    if (action === 'approve') {
                        this.loading = true;

                        // 模拟审核请求
                        setTimeout(() => {
                            const report = this.tableData.find(r => r.reportId === reportId);
                            if (report && report.activities) {
                                const activity = report.activities.find(a => a.type === activityType);
                                if (activity) {
                                    activity.status = 'approved';
                                    report.updateTime = this.formatDateTime(new Date());
                                    report.history.push({
                                        time: this.formatDateTime(new Date()),
                                        content: `活动 "${activity.name}" 审核通过`,
                                        type: 'approve'
                                    });

                                    this.$message({
                                        message: '审核通过成功',
                                        type: 'success'
                                    });

                                    // 更新当前查看的报备详情
                                    if (this.currentReportDetail && this.currentReportDetail.reportId === reportId) {
                                        const detailActivity = this.currentReportDetail.activities.find(a => a.type === activityType);
                                        if (detailActivity) {
                                            detailActivity.status = 'approved';
                                        }
                                        this.currentReportDetail.history = [...report.history];
                                        this.currentReportDetail.updateTime = report.updateTime;
                                    }
                                }
                            }

                            this.loading = false;
                        }, 500);
                    } else {
                        // 显示拒绝对话框
                        this.showRejectDialog(reportId, activityType);
                    }
                },

                // 显示拒绝对话框
                showRejectDialog(reportId, activityType) {
                    this.rejectForm.reportId = reportId;
                    this.rejectForm.activityType = activityType;
                    this.rejectForm.reason = '';
                    this.rejectDialogVisible = true;
                },

                // 确认拒绝活动
                confirmRejectActivity() {
                    if (!this.rejectForm.reason) {
                        this.$message({
                            message: '请输入拒绝原因',
                            type: 'warning'
                        });
                        return;
                    }

                    this.loading = true;
                    this.rejectDialogVisible = false;

                    // 模拟拒绝请求
                    setTimeout(() => {
                        const report = this.tableData.find(r => r.reportId === this.rejectForm.reportId);
                        if (report && report.activities) {
                            const activity = report.activities.find(a => a.type === this.rejectForm.activityType);
                            if (activity) {
                                activity.status = 'rejected';
                                report.updateTime = this.formatDateTime(new Date());
                                report.history.push({
                                    time: this.formatDateTime(new Date()),
                                    content: `活动 "${activity.name}" 被拒绝: ${this.rejectForm.reason}`,
                                    type: 'reject'
                                });

                                this.$message({
                                    message: '已拒绝该活动',
                                    type: 'success'
                                });

                                // 更新当前查看的报备详情
                                if (this.currentReportDetail && this.currentReportDetail.reportId === this.rejectForm.reportId) {
                                    const detailActivity = this.currentReportDetail.activities.find(a => a.type === this.rejectForm.activityType);
                                    if (detailActivity) {
                                        detailActivity.status = 'rejected';
                                    }
                                    this.currentReportDetail.history = [...report.history];
                                    this.currentReportDetail.updateTime = report.updateTime;
                                }
                            }
                        }

                        this.loading = false;
                    }, 500);
                },

                // 处理文件上传
                handleFileUpload(options) {
                    this.$message({
                        message: '文件上传功能待实现',
                        type: 'info'
                    });
                },

                // 导出报表数据
                exportReportData(fileType) {
                    this.$message({
                        message: `正在导出 ${fileType.toUpperCase()} 文件，请稍候...`,
                        type: 'info'
                    });

                    setTimeout(() => {
                        this.$message({
                            message: '导出成功，文件已下载',
                            type: 'success'
                        });
                    }, 1500);
                },

                // 格式化日期为 YYYY-MM-DD
                formatDate(dateString) {
                    if (!dateString) return '';
                    const date = new Date(dateString);
                    const year = date.getFullYear();
                    const month = String(date.getMonth() + 1).padStart(2, '0');
                    const day = String(date.getDate()).padStart(2, '0');
                    return `${year}-${month}-${day}`;
                },

                // 格式化日期时间为 YYYY-MM-DD HH:MM:SS
                formatDateTime(date) {
                    if (!date) return '';
                    const year = date.getFullYear();
                    const month = String(date.getMonth() + 1).padStart(2, '0');
                    const day = String(date.getDate()).padStart(2, '0');
                    const hours = String(date.getHours()).padStart(2, '0');
                    const minutes = String(date.getMinutes()).padStart(2, '0');
                    const seconds = String(date.getSeconds()).padStart(2, '0');
                    return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
                },

                // 获取状态标签类型
                getStatusTagType(status) {
                    switch (status) {
                        case 'pending':
                            return 'warning';
                        case 'approved':
                            return 'success';
                        case 'rejected':
                            return 'danger';
                        default:
                            return 'info';
                    }
                },

                // 获取状态文本
                getStatusText(status) {
                    switch (status) {
                        case 'pending':
                            return '待审核';
                        case 'approved':
                            return '已通过';
                        case 'rejected':
                            return '已拒绝';
                        default:
                            return '未知状态';
                    }
                },

                // 获取历史记录图标类型
                getHistoryTypeIcon(type) {
                    switch (type) {
                        case 'create':
                            return 'primary';
                        case 'update':
                            return 'info';
                        case 'approve':
                            return 'success';
                        case 'reject':
                            return 'danger';
                        default:
                            return 'info';
                    }
                },

                /**
                 * 检查活动是否被选中
                 * @param {Object} row 行数据
                 * @param {String} activityType 活动类型
                 * @returns {Boolean} 是否被选中
                 */
                isActivitySelected(row, activityType) {
                    if (!row.activities) return false;
                    return row.activities.some(a => a.type === activityType);
                },

                /**
                 * 获取活动状态
                 * @param {Object} row 行数据
                 * @param {String} activityType 活动类型
                 * @returns {String} 状态
                 */
                getActivityStatus(row, activityType) {
                    if (!row.activities) return '';
                    const activity = row.activities.find(a => a.type === activityType);
                    return activity ? activity.status : '';
                },

                /**
                 * 获取活动开始日期
                 * @param {Object} row 行数据
                 * @param {String} activityType 活动类型
                 * @returns {String} 开始日期
                 */
                getActivityStartDate(row, activityType) {
                    if (!row.activities) return '';
                    const activity = row.activities.find(a => a.type === activityType);
                    return activity ? activity.startDate : '';
                },

                /**
                 * 获取活动结束日期
                 * @param {Object} row 行数据
                 * @param {String} activityType 活动类型
                 * @returns {String} 结束日期
                 */
                getActivityEndDate(row, activityType) {
                    if (!row.activities) return '';
                    const activity = row.activities.find(a => a.type === activityType);
                    return activity ? activity.endDate : '';
                },

                /**
                 * 处理活动勾选
                 * @param {Boolean} checked 是否勾选
                 * @param {Object} row 行数据
                 * @param {Object} activity 活动对象
                 */
                handleActivityCheck(checked, row, activity) {
                    if (checked) {
                        // 显示二次确认
                        this.$confirm(`确认报备"${activity.name}"活动？`, '确认报备', {
                            confirmButtonText: '确认',
                            cancelButtonText: '取消',
                            type: 'info'
                        }).then(() => {
                            // 用户确认，模拟报备流程
                            this.submitActivityReport(row, activity);
                        }).catch(() => {
                            // 用户取消，不做处理
                            this.$message.info('已取消报备');
                        });
                    } else {
                        // 如果是取消勾选，需要询问是否取消报备
                        this.$confirm(`确认取消"${activity.name}"活动报备？`, '确认取消', {
                            confirmButtonText: '确认',
                            cancelButtonText: '返回',
                            type: 'warning'
                        }).then(() => {
                            // 用户确认，模拟取消报备流程
                            this.cancelActivityReport(row, activity);
                        }).catch(() => {
                            // 用户取消，不做处理
                        });
                    }
                },

                /**
                 * 提交活动报备
                 * @param {Object} row 行数据
                 * @param {Object} activity 活动对象
                 */
                submitActivityReport(row, activity) {
                    // 显示日期选择对话框
                    this.$prompt('请设置活动日期范围', '活动日期', {
                        confirmButtonText: '确定',
                        cancelButtonText: '取消',
                        inputType: 'daterange',
                        inputPlaceholder: '选择日期范围'
                    }).then(({
                        value
                    }) => {
                        if (!value || value.length !== 2) {
                            this.$message.error('请选择完整的日期范围');
                            return;
                        }

                        // 模拟提交请求
                        this.loading = true;
                        setTimeout(() => {
                            // 更新表格数据
                            if (!row.activities) {
                                row.activities = [];
                            }

                            row.activities.push({
                                type: activity.type,
                                name: activity.name,
                                startDate: this.formatDate(value[0]),
                                endDate: this.formatDate(value[1]),
                                status: 'pending',
                                checked: true
                            });

                            // 更新历史记录
                            if (!row.history) {
                                row.history = [];
                            }

                            row.history.push({
                                time: this.formatDateTime(new Date()),
                                content: `报备活动 "${activity.name}"`,
                                type: 'create'
                            });

                            row.updateTime = this.formatDateTime(new Date());

                            this.$message.success(`已成功报备"${activity.name}"活动`);
                            this.loading = false;
                        }, 500);
                    }).catch(() => {
                        this.$message.info('已取消设置活动日期');
                    });
                },

                /**
                 * 取消活动报备
                 * @param {Object} row 行数据
                 * @param {Object} activity 活动对象
                 */
                cancelActivityReport(row, activity) {
                    // 模拟取消请求
                    this.loading = true;
                    setTimeout(() => {
                        // 更新表格数据
                        if (row.activities) {
                            const index = row.activities.findIndex(a => a.type === activity.type);
                            if (index > -1) {
                                row.activities.splice(index, 1);

                                // 更新历史记录
                                if (!row.history) {
                                    row.history = [];
                                }

                                row.history.push({
                                    time: this.formatDateTime(new Date()),
                                    content: `取消活动 "${activity.name}" 报备`,
                                    type: 'update'
                                });

                                row.updateTime = this.formatDateTime(new Date());
                            }
                        }

                        this.$message.success(`已取消"${activity.name}"活动报备`);
                        this.loading = false;
                    }, 500);
                },

                /**
                 * 检查活动勾选是否禁用
                 * @param {Object} row 行数据
                 * @param {String} activityType 活动类型
                 * @returns {Boolean} 是否禁用
                 */
                isActivityCheckDisabled(row, activityType) {
                    // 亏损扶持依赖于赛马活动
                    if (activityType === 'support') {
                        return !this.isActivitySelected(row, 'race');
                    }
                    return false;
                }
            }
        });
    </script>
</body>

</html>