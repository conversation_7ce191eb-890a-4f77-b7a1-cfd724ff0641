<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>客服经营日报管理系统</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', 'Helvetica Neue', Helvetica, Arial, sans-serif;
            background-color: #f5f7fa;
            color: #303133;
            line-height: 1.6;
        }
        /* 容器样式 */
        
        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
        }
        /* 页面头部 */
        
        .page-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 40px 0;
            margin: -20px -20px 30px -20px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
        }
        
        .page-header h1 {
            font-size: 32px;
            font-weight: 600;
            text-align: center;
            margin-bottom: 10px;
        }
        
        .page-header p {
            text-align: center;
            opacity: 0.9;
            font-size: 16px;
        }
        /* 操作栏样式 */
        
        .action-bar {
            background: white;
            padding: 20px;
            border-radius: 12px;
            box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
            margin-bottom: 30px;
        }
        
        .action-bar-top {
            display: flex;
            justify-content: space-between;
            align-items: center;
            flex-wrap: wrap;
            gap: 20px;
            margin-bottom: 20px;
        }
        
        .filter-group {
            display: flex;
            align-items: center;
            gap: 15px;
            flex-wrap: wrap;
        }
        
        .filter-group label {
            font-weight: 500;
            color: #606266;
            white-space: nowrap;
        }
        
        .filter-group input[type="date"] {
            padding: 8px 12px;
            border: 1px solid #dcdfe6;
            border-radius: 6px;
            font-size: 14px;
            transition: all 0.3s;
        }
        
        .filter-group input[type="date"]:focus {
            outline: none;
            border-color: #667eea;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }
        /* 按钮样式 */
        
        .btn {
            padding: 10px 20px;
            border: none;
            border-radius: 6px;
            font-size: 14px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s;
            display: inline-flex;
            align-items: center;
            gap: 8px;
            white-space: nowrap;
        }
        
        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }
        
        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);
        }
        
        .btn-secondary {
            background: #f0f2f5;
            color: #606266;
        }
        
        .btn-secondary:hover {
            background: #e6e8eb;
        }
        
        .btn-success {
            background: #67c23a;
            color: white;
        }
        
        .btn-success:hover {
            background: #5daf34;
        }
        
        .btn-danger {
            background: #f56c6c;
            color: white;
        }
        
        .btn-danger:hover {
            background: #f45454;
        }
        
        .btn-small {
            padding: 6px 12px;
            font-size: 13px;
        }
        
        .btn-group {
            display: flex;
            gap: 10px;
            flex-wrap: wrap;
        }
        /* 统计卡片 */
        
        .stats-container {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .stat-card {
            background: white;
            padding: 25px;
            border-radius: 12px;
            box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
            transition: all 0.3s;
        }
        
        .stat-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 24px rgba(0, 0, 0, 0.12);
        }
        
        .stat-card h3 {
            font-size: 14px;
            color: #909399;
            margin-bottom: 10px;
            font-weight: 400;
        }
        
        .stat-card .value {
            font-size: 32px;
            font-weight: 600;
            color: #303133;
            margin-bottom: 5px;
        }
        
        .stat-card .trend {
            font-size: 13px;
            color: #67c23a;
        }
        
        .stat-card .trend.down {
            color: #f56c6c;
        }
        /* 表格样式 */
        
        .table-container {
            background: #fff;
            border-radius: 8px;
            border: 1px solid #e8e8e8;
            overflow: hidden;
            margin-top: 15px;
            box-shadow: 0 1px 4px rgba(0, 0, 0, 0.05);
        }
        
        .table-header {
            padding: 20px;
            border-bottom: 1px solid #ebeef5;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .table-title {
            font-size: 18px;
            font-weight: 600;
            color: #303133;
        }
        
        .table-wrapper {
            overflow-x: auto;
            width: 100%;
            border-radius: 8px;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
            position: relative;
            margin-bottom: 20px;
            padding-bottom: 5px;
        }
        
        .data-table {
            width: max-content;
            min-width: 100%;
            border-collapse: collapse;
            table-layout: fixed;
        }
        
        .data-table th,
        .data-table td {
            padding: 15px 8px;
            text-align: center;
            white-space: normal;
            word-break: break-word;
            min-width: 100px;
        }
        
        .data-table th:first-child,
        .data-table td:first-child {
            min-width: 120px;
        }
        
        .data-table th:nth-child(14),
        .data-table td:nth-child(14) {
            min-width: 150px;
        }
        
        .data-table th {
            background: #f5f7fa;
            color: #333;
            font-weight: 500;
            padding: 15px 8px;
            user-select: none;
            border-bottom: 2px solid #e8edf3;
            position: relative;
        }
        
        .data-table td {
            padding: 15px;
            border-bottom: 1px solid #ebeef5;
            color: #606266;
            font-size: 14px;
        }
        
        .data-table tr:nth-child(even) {
            background-color: #fafbfc;
        }
        
        .data-table tr:hover {
            background-color: #f0f7ff;
        }
        /* 操作按钮组 */
        
        .action-group {
            display: flex;
            gap: 8px;
        }
        /* 空状态 */
        
        .empty-state {
            text-align: center;
            padding: 80px 20px;
            color: #909399;
        }
        
        .empty-state img {
            width: 120px;
            opacity: 0.5;
            margin-bottom: 20px;
        }
        
        .empty-state p {
            font-size: 16px;
            margin-bottom: 20px;
        }
        /* 模态框样式 */
        
        .modal {
            display: none;
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.5);
            animation: fadeIn 0.3s;
        }
        
        @keyframes fadeIn {
            from {
                opacity: 0;
            }
            to {
                opacity: 1;
            }
        }
        
        .modal-dialog {
            background-color: white;
            margin: 50px auto;
            padding: 0;
            border-radius: 12px;
            width: 90%;
            max-width: 800px;
            max-height: 90vh;
            overflow-y: auto;
            box-shadow: 0 10px 40px rgba(0, 0, 0, 0.2);
            animation: slideIn 0.3s;
            position: relative;
        }
        
        @keyframes slideIn {
            from {
                transform: translateY(-50px);
                opacity: 0;
            }
            to {
                transform: translateY(0);
                opacity: 1;
            }
        }
        
        .modal-content .modal-header {
            background: #fff;
            border-bottom: 1px solid #f0f0f0;
            padding: 24px 32px;
            position: relative;
        }
        
        .modal-content .modal-title {
            color: #262626;
            font-size: 18px;
            font-weight: 500;
            margin: 0;
        }
        
        .modal-content .close-btn {
            position: absolute;
            right: 24px;
            top: 24px;
            font-size: 18px;
            color: #8c8c8c;
            background: none;
            border: none;
            cursor: pointer;
            width: 24px;
            height: 24px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 50%;
            transition: all 0.2s ease;
        }
        
        .modal-content .close-btn:hover {
            background-color: #f5f5f5;
            color: #262626;
        }
        
        .modal-subtitle {
            font-size: 14px;
            opacity: 0.9;
        }
        
        .modal-body {
            padding: 30px;
        }
        
        .modal-footer {
            padding: 20px 30px;
            border-top: 1px solid #ebeef5;
            display: flex;
            justify-content: flex-end;
            gap: 10px;
        }
        
        .close-btn {
            position: absolute;
            right: 20px;
            top: 20px;
            font-size: 28px;
            font-weight: 300;
            color: white;
            cursor: pointer;
            background: none;
            border: none;
            width: 32px;
            height: 32px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 50%;
            transition: all 0.3s;
            z-index: 10;
        }
        
        .close-btn:hover {
            background: rgba(255, 255, 255, 0.2);
        }
        /* 表单样式 */
        
        .form-group {
            margin-bottom: 20px;
        }
        
        .form-label {
            display: block;
            margin-bottom: 8px;
            font-weight: 500;
            color: #606266;
            font-size: 14px;
        }
        
        .form-input {
            width: 100%;
            padding: 10px 12px;
            border: 1px solid #dcdfe6;
            border-radius: 6px;
            font-size: 14px;
            transition: all 0.3s;
        }
        
        .form-input:focus {
            outline: none;
            border-color: #667eea;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }
        
        .form-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
        }
        
        .form-help {
            font-size: 12px;
            color: #909399;
            margin-top: 5px;
        }
        /* 设置项样式 */
        
        .setting-item {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 15px;
        }
        
        .setting-item-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 10px;
        }
        
        .setting-item-title {
            font-weight: 500;
            color: #303133;
        }
        
        .income-item,
        .expense-item {
            display: flex;
            gap: 10px;
            margin-bottom: 10px;
            align-items: center;
        }
        
        .full-width {
            grid-column: 1 / -1;
        }
        /* 响应式设计 */
        
        @media (max-width: 768px) {
            .container {
                padding: 10px;
            }
            .page-header {
                padding: 20px 0;
            }
            .page-header h1 {
                font-size: 24px;
            }
            .action-bar-top {
                flex-direction: column;
                align-items: stretch;
            }
            .filter-group {
                flex-direction: column;
                align-items: stretch;
                width: 100%;
            }
            .filter-group input[type="date"] {
                width: 100%;
            }
            .btn-group {
                justify-content: stretch;
            }
            .btn {
                flex: 1;
                justify-content: center;
            }
            .modal-dialog {
                width: 95%;
                margin: 20px auto;
            }
            .data-table {
                font-size: 12px;
            }
            .data-table th,
            .data-table td {
                padding: 10px 8px;
            }
        }
        
        .modal-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.5);
            display: none;
            justify-content: center;
            align-items: center;
            z-index: 1000;
            backdrop-filter: blur(8px);
        }
        
        .modal-content {
            background: #fff;
            border-radius: 16px;
            width: 90%;
            max-width: 1000px;
            max-height: 90vh;
            overflow: hidden;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
            position: relative;
        }
        
        .tab-container {
            display: flex;
            border-bottom: 1px solid #f0f0f0;
            background: #fff;
            padding: 0 24px;
        }
        
        .tab-btn {
            padding: 16px 24px;
            font-size: 14px;
            border: none;
            background: transparent;
            color: #595959;
            position: relative;
            transition: all 0.3s;
            cursor: pointer;
            border-bottom: 2px solid transparent;
        }
        
        .tab-btn.active {
            color: #1890ff;
            border-bottom: 2px solid #1890ff;
        }
        
        .tab-btn:hover:not(.active) {
            color: #40a9ff;
        }
        
        .tab-btn.active::after {
            display: none;
        }
        
        .tab-content {
            display: none;
            padding: 32px;
            max-height: 60vh;
            overflow-y: auto;
        }
        
        .tab-content.active {
            display: block;
        }
        
        .number-cell {
            color: #40a9ff;
            white-space: nowrap;
        }
        
        .percentage-cell {
            color: #73d13d;
            white-space: nowrap;
        }
        
        .money-cell {
            color: #ff7875;
            font-weight: 500;
            white-space: nowrap;
        }
        /* 调整滚动条样式，使其更加美观 */
        
        .table-wrapper::-webkit-scrollbar {
            height: 8px;
            /* 横向滚动条高度 */
        }
        
        .table-wrapper::-webkit-scrollbar-thumb {
            background: #cfd8e3;
            border-radius: 4px;
        }
        
        .table-wrapper::-webkit-scrollbar-thumb:hover {
            background: #b0bac5;
        }
        
        .table-wrapper::-webkit-scrollbar-track {
            background: #f0f2f5;
            border-radius: 4px;
        }
        /* 添加基础设置表头样式 */
        
        .tab-content table thead tr {
            background: #f0f2f5;
            /* 浅灰色背景，符合企业级产品的克制风格 */
            border-bottom: 1px solid #e8e8e8;
        }
        
        .tab-content table th {
            color: #5c6b8a;
            /* 偏灰蓝色的文字，增强专业感 */
            font-weight: 500;
            font-size: 14px;
            padding: 12px 16px;
            text-align: center;
        }
        /* 调整表格行间距和内容显示 */
        
        .tab-content table td {
            text-align: center;
            padding: 16px;
            font-size: 14px;
            color: #262626;
        }
        /* 状态标签样式 */
        
        .status-active,
        .status-disabled {
            display: inline-block;
            padding: 0 8px;
            height: 22px;
            line-height: 22px;
            border-radius: 2px;
            font-size: 12px;
            text-align: center;
            vertical-align: middle;
        }
        
        .status-active {
            background: #f6ffed;
            border: 1px solid #b7eb8f;
            color: #52c41a;
        }
        
        .status-disabled {
            background: #fff1f0;
            border: 1px solid #ffccc7;
            color: #ff4d4f;
        }
        /* 表单模态框样式 */
        
        .form-modal {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.5);
            display: none;
            justify-content: center;
            align-items: center;
            z-index: 1001;
            backdrop-filter: blur(8px);
        }
        
        .form-content {
            background: #fff;
            border-radius: 12px;
            width: 90%;
            max-width: 800px;
            max-height: 90vh;
            overflow-y: auto;
            padding: 32px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
        }
        /* 表单按钮 */
        
        .form-buttons {
            display: flex;
            gap: 12px;
            justify-content: flex-end;
            margin-top: 24px;
            padding-top: 24px;
            border-top: 1px solid #e8e8e8;
        }
        
        .btn-cancel,
        .btn-save {
            padding: 10px 20px;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 500;
            transition: all 0.3s ease;
        }
        
        .btn-cancel {
            background: #f0f0f0;
            color: #666;
        }
        
        .btn-cancel:hover {
            background: #e0e0e0;
        }
        
        .btn-save {
            background: #667eea;
            color: white;
        }
        
        .btn-save:hover {
            background: #5a6fd8;
        }
        /* 添加内容标题样式 */
        
        .content-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 24px;
        }
        
        .content-title {
            font-size: 18px;
            font-weight: 500;
            color: #262626;
            margin: 0;
        }
        
        .add-btn {
            background: #1890ff;
            color: white;
            border: none;
            border-radius: 4px;
            padding: 8px 16px;
            font-size: 14px;
            cursor: pointer;
            display: flex;
            align-items: center;
            gap: 6px;
            transition: all 0.3s;
        }
        
        .add-btn:hover {
            background: #40a9ff;
        }
        /* 表头拖拽功能样式 */
        
        .resizer {
            position: absolute;
            right: 0;
            top: 0;
            height: 100%;
            width: 5px;
            cursor: col-resize;
            user-select: none;
            z-index: 1;
        }
        
        th {
            position: relative;
        }
        
        th.resizing {
            cursor: col-resize;
        }
        /* 修改操作按钮样式 */
        
        .action-buttons .btn-edit {
            color: #1890ff;
            background: #e6f7ff;
            border: none;
            height: 24px;
            padding: 0 8px;
            border-radius: 4px;
            font-size: 12px;
            margin-right: 8px;
            transition: all 0.2s;
        }
        
        .action-buttons .btn-edit:hover {
            background: #bae7ff;
        }
        
        .action-buttons .btn-delete {
            color: #ff4d4f;
            background: #fff1f0;
            border: none;
            height: 24px;
            padding: 0 8px;
            border-radius: 4px;
            font-size: 12px;
            transition: all 0.2s;
        }
        
        .action-buttons .btn-delete:hover {
            background: #ffccc7;
        }
    </style>
</head>

<body>
    <div class="container">
        <!-- 页面头部 -->
        <div class="page-header">
            <h1>客服经营日报管理系统</h1>
            <p>高效管理客服团队绩效，提升服务质量</p>
        </div>

        <!-- 统计卡片 -->
        <div class="stats-container">
            <div class="stat-card">
                <h3>今日接待总数</h3>
                <div class="value">450</div>
                <div class="trend">↑ 12.5% 较昨日</div>
            </div>
            <div class="stat-card">
                <h3>平均响应时长</h3>
                <div class="value">22.3s</div>
                <div class="trend">↓ 8.2% 较昨日</div>
            </div>
            <div class="stat-card">
                <h3>询单转化率</h3>
                <div class="value">29.8%</div>
                <div class="trend">↑ 3.1% 较昨日</div>
            </div>
            <div class="stat-card">
                <h3>客服销售总额</h3>
                <div class="value">¥37,500</div>
                <div class="trend">↑ 15.6% 较昨日</div>
            </div>
        </div>

        <!-- 操作栏 -->
        <div class="action-bar">
            <div class="action-bar-top">
                <div class="filter-group">
                    <label>日期范围：</label>
                    <input type="date" id="startDate">
                    <span>至</span>
                    <input type="date" id="endDate">
                    <button class="btn btn-primary" onclick="filterByDate()">
                        <span>🔍</span>
                        查询
                    </button>
                </div>
                <div class="btn-group">
                    <button class="btn btn-success" onclick="showReportForm()">
                        <span>➕</span>
                        新增日报
                    </button>
                    <button class="btn btn-secondary" onclick="showImportModal()">
                        <span>📤</span>
                        批量导入
                    </button>
                    <button class="btn btn-secondary" onclick="exportData()">
                        <span>📥</span>
                        导出数据
                    </button>
                    <button class="btn btn-secondary" onclick="openSettingsModal()">
                        <span>⚙️</span>
                        基础设置
                    </button>
                </div>
            </div>
        </div>

        <!-- 数据表格 -->
        <div class="table-container">
            <div class="table-header">
                <h2 class="table-title">日报数据列表</h2>
                <span style="color: #909399; font-size: 14px;">共 <span id="totalRecords">0</span> 条记录</span>
            </div>
            <div class="table-wrapper">
                <table class="data-table">
                    <thead>
                        <tr>
                            <th>填报日期</th>
                            <th>团队名称</th>
                            <th>店铺名称</th>
                            <th>客服班次</th>
                            <th>客服人员</th>
                            <th>接待人数</th>
                            <th>首次响应时长(s)</th>
                            <th>平均响应时长(s)</th>
                            <th>3分钟人工回复率</th>
                            <th>支付人数</th>
                            <th>询单转化率</th>
                            <th>客服销售额</th>
                            <th>客服满意率</th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody id="reportTableBody">
                        <!-- 数据行将通过JavaScript动态生成 -->
                    </tbody>
                </table>
            </div>
            <div id="emptyState" class="empty-state" style="display: none;">
                <p>暂无数据，请添加日报记录</p>
                <button class="btn btn-primary" onclick="showReportForm()">立即添加</button>
            </div>
        </div>
    </div>

    <!-- 新增/编辑日报模态框 -->
    <div id="reportFormModal" class="modal">
        <div class="modal-dialog">
            <button class="close-btn" onclick="closeReportForm()">&times;</button>
            <div class="modal-header">
                <h3 class="modal-title" id="formModalTitle">新增日报</h3>
                <p class="modal-subtitle">请填写客服经营日报数据</p>
            </div>
            <div class="modal-body">
                <form id="reportForm">
                    <div class="form-grid">
                        <div class="form-group">
                            <label class="form-label">填报日期 <span style="color: red;">*</span></label>
                            <input type="date" class="form-input" id="reportDate" required>
                        </div>
                        <div class="form-group">
                            <label class="form-label">团队名称 <span style="color: red;">*</span></label>
                            <select class="form-input" id="teamName" required>
                                <option value="">请选择团队</option>
                                <option value="A组">A组</option>
                                <option value="B组">B组</option>
                                <option value="C组">C组</option>
                                <option value="D组">D组</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label class="form-label">店铺名称 <span style="color: red;">*</span></label>
                            <select class="form-input" id="shopName" required>
                                <option value="">请选择店铺</option>
                                <option value="旗舰店">旗舰店</option>
                                <option value="专营店">专营店</option>
                                <option value="直营店">直营店</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label class="form-label">客服班次 <span style="color: red;">*</span></label>
                            <select class="form-input" id="serviceShift" required>
                                <option value="">请选择班次</option>
                                <option value="早班">早班</option>
                                <option value="中班">中班</option>
                                <option value="晚班">晚班</option>
                                <option value="夜班">夜班</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label class="form-label">客服人员 <span style="color: red;">*</span></label>
                            <input type="text" class="form-input" id="serviceStaff" placeholder="请输入客服人员姓名" required>
                        </div>
                        <div class="form-group">
                            <label class="form-label">接待人数 <span style="color: red;">*</span></label>
                            <input type="number" class="form-input" id="receptionCount" placeholder="请输入接待人数" min="0" required>
                        </div>
                        <div class="form-group">
                            <label class="form-label">首次响应时长(秒)</label>
                            <input type="number" class="form-input" id="firstResponseTime" placeholder="请输入首次响应时长" min="0" step="0.1">
                        </div>
                        <div class="form-group">
                            <label class="form-label">平均响应时长(秒)</label>
                            <input type="number" class="form-input" id="avgResponseTime" placeholder="请输入平均响应时长" min="0" step="0.1">
                        </div>
                        <div class="form-group">
                            <label class="form-label">3分钟人工回复率(%)</label>
                            <input type="number" class="form-input" id="threeMinReplyRate" placeholder="请输入回复率" min="0" max="100" step="0.1">
                        </div>
                        <div class="form-group">
                            <label class="form-label">支付人数</label>
                            <input type="number" class="form-input" id="paymentCount" placeholder="请输入支付人数" min="0">
                        </div>
                        <div class="form-group">
                            <label class="form-label">询单转化率(%)</label>
                            <input type="number" class="form-input" id="inquiryConversionRate" placeholder="请输入转化率" min="0" max="100" step="0.1">
                        </div>
                        <div class="form-group">
                            <label class="form-label">客服销售额(元)</label>
                            <input type="number" class="form-input" id="salesAmount" placeholder="请输入销售额" min="0" step="0.01">
                        </div>
                        <div class="form-group">
                            <label class="form-label">客服满意率(%)</label>
                            <input type="number" class="form-input" id="satisfactionRate" placeholder="请输入满意率" min="0" max="100" step="0.1">
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" onclick="closeReportForm()">取消</button>
                <button type="submit" form="reportForm" class="btn btn-primary">保存</button>
            </div>
        </div>
    </div>

    <!-- 基础设置模态框 -->
    <div class="modal-overlay" id="settingsModal">
        <div class="modal-content">
            <div class="modal-header">
                <h2 class="modal-title">基础设置</h2>
                <button class="close-btn" onclick="closeSettingsModal()">&times;</button>
            </div>

            <div class="tab-container">
                <button class="tab-btn active" onclick="showTab('income')">客服业务收入基础维护</button>
                <button class="tab-btn" onclick="showTab('expense')">客服业务支出基础维护</button>
                <button class="tab-btn" onclick="showTab('shift')">客服班次设置</button>
            </div>

            <!-- Income Tab -->
            <div class="tab-content active" id="incomeTab">
                <div class="content-header">
                    <h3 class="content-title">客服业务收入设置</h3>
                    <button class="add-btn" onclick="showIncomeForm()">
                        <span>+</span> 新增收入项
                    </button>
                </div>
                <div class="table-container">
                    <table>
                        <thead>
                            <tr>
                                <th style="width: 60px;">ID</th>
                                <th style="width: 30%;">客服班次费用（元/天）</th>
                                <th style="width: 30%;">店铺基础费用（元/天/店）</th>
                                <th style="width: 15%;">状态</th>
                                <th style="width: 20%;">操作</th>
                            </tr>
                        </thead>
                        <tbody id="incomeTableBody">
                            <!-- 数据会通过JavaScript动态生成 -->
                        </tbody>
                    </table>
                </div>
            </div>

            <!-- Expense Tab -->
            <div class="tab-content" id="expenseTab">
                <div class="content-header">
                    <h3 class="content-title">客服业务支出设置</h3>
                    <button class="add-btn" onclick="showExpenseForm()">
                        <span>+</span> 新增支出项
                    </button>
                </div>
                <div class="table-container">
                    <table>
                        <thead>
                            <tr>
                                <th style="width: 60px;">ID</th>
                                <th style="width: 20%;">客服人员</th>
                                <th style="width: 20%;">预估工资（元/天）</th>
                                <th style="width: 20%;">年限工资（元/天）</th>
                                <th style="width: 15%;">状态</th>
                                <th style="width: 20%;">操作</th>
                            </tr>
                        </thead>
                        <tbody id="expenseTableBody">
                            <!-- 数据会通过JavaScript动态生成 -->
                        </tbody>
                    </table>
                </div>
            </div>

            <!-- Shift Tab -->
            <div class="tab-content" id="shiftTab">
                <div class="content-header">
                    <h3 class="content-title">客服班次设置</h3>
                    <button class="add-btn" onclick="showShiftForm()">
                        <span>+</span> 新增班次
                    </button>
                </div>
                <div class="table-container">
                    <table>
                        <thead>
                            <tr>
                                <th style="width: 60px;">ID</th>
                                <th style="width: 30%;">班次名称</th>
                                <th style="width: 30%;">时间段</th>
                                <th style="width: 15%;">状态</th>
                                <th style="width: 20%;">操作</th>
                            </tr>
                        </thead>
                        <tbody id="shiftTableBody">
                            <!-- 数据会通过JavaScript动态生成 -->
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>

    <!-- Income Form Modal -->
    <div class="form-modal" id="incomeFormModal">
        <div class="form-content">
            <h3 style="margin-bottom: 24px; font-size: 18px;">编辑收入设置</h3>
            <form id="incomeForm">
                <div class="form-group">
                    <label class="form-label">客服班次费用（元/天）</label>
                    <input type="number" class="form-input" id="shiftCost" placeholder="请输入客服班次费用" step="0.01" required>
                </div>
                <div class="form-group">
                    <label class="form-label">店铺基础费用（元/天/店）</label>
                    <input type="number" class="form-input" id="shopCost" placeholder="请输入店铺基础费用" step="0.01" required>
                </div>
                <div class="form-buttons">
                    <button type="button" class="btn-cancel" onclick="closeIncomeForm()">取消</button>
                    <button type="submit" class="btn-save">保存</button>
                </div>
            </form>
        </div>
    </div>

    <!-- Expense Form Modal -->
    <div class="form-modal" id="expenseFormModal">
        <div class="form-content">
            <h3 style="margin-bottom: 24px; font-size: 18px;">编辑支出设置</h3>
            <form id="expenseForm">
                <div class="form-group">
                    <label class="form-label">客服人员</label>
                    <input type="text" class="form-input" id="staffName" placeholder="请输入客服人员姓名" required>
                </div>
                <div class="form-group">
                    <label class="form-label">预估工资（元/天）</label>
                    <input type="number" class="form-input" id="estimatedSalary" placeholder="请输入预估工资" step="0.01" required>
                </div>
                <div class="form-group">
                    <label class="form-label">年限工资（元/天）</label>
                    <input type="number" class="form-input" id="yearsSalary" placeholder="请输入年限工资" step="0.01" required>
                </div>
                <div class="form-buttons">
                    <button type="button" class="btn-cancel" onclick="closeExpenseForm()">取消</button>
                    <button type="submit" class="btn-save">保存</button>
                </div>
            </form>
        </div>
    </div>

    <!-- Shift Form Modal -->
    <div class="form-modal" id="shiftFormModal">
        <div class="form-content">
            <h3 style="margin-bottom: 24px; font-size: 18px;">编辑班次设置</h3>
            <form id="shiftForm">
                <div class="form-group">
                    <label class="form-label">班次名称</label>
                    <input type="text" class="form-input" id="shiftName" placeholder="请输入班次名称" required>
                </div>
                <div class="form-group">
                    <label class="form-label">开始时间</label>
                    <input type="time" class="form-input" id="startTime" required>
                </div>
                <div class="form-group">
                    <label class="form-label">结束时间</label>
                    <input type="time" class="form-input" id="endTime" required>
                </div>
                <div class="form-buttons">
                    <button type="button" class="btn-cancel" onclick="closeShiftForm()">取消</button>
                    <button type="submit" class="btn-save">保存</button>
                </div>
            </form>
        </div>
    </div>

    <!-- 数据导入模态框 -->
    <div id="importModal" class="modal">
        <div class="modal-dialog">
            <button class="close-btn" onclick="closeImportModal()">&times;</button>
            <div class="modal-header">
                <h3 class="modal-title">数据导入</h3>
                <p class="modal-subtitle">支持Excel文件导入，批量添加日报数据</p>
            </div>
            <div class="modal-body">
                <div class="form-group">
                    <label class="form-label">选择文件</label>
                    <input type="file" id="importFile" class="form-input" accept=".xlsx,.xls,.csv" onchange="handleFileSelect(this)">
                    <div class="form-help">支持 .xlsx, .xls, .csv 格式文件</div>
                </div>
                <div class="setting-item">
                    <div class="setting-item-header">
                        <span class="setting-item-title">导入说明</span>
                    </div>
                    <ul style="padding-left: 20px; color: #606266; font-size: 14px; line-height: 1.8;">
                        <li>请确保Excel文件包含所有必填字段</li>
                        <li>日期格式：YYYY-MM-DD</li>
                        <li>百分比数据请输入数字，如：95.5（代表95.5%）</li>
                        <li>金额数据请输入数字，不要包含货币符号</li>
                    </ul>
                </div>
                <div class="form-group">
                    <button type="button" class="btn btn-secondary" onclick="downloadTemplate()">
                        <span>📥</span>
                        下载导入模板
                    </button>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" onclick="closeImportModal()">取消</button>
                <button type="button" class="btn btn-primary" onclick="importData()">开始导入</button>
            </div>
        </div>
    </div>

    <script>
        // 示例数据
        let reportData = [{
            id: 1,
            reportDate: '2024-01-15',
            teamName: 'A组',
            shopName: '旗舰店',
            serviceShift: '早班',
            serviceStaff: '张小明',
            receptionCount: 125,
            firstResponseTime: 15.5,
            avgResponseTime: 22.3,
            threeMinReplyRate: 98.5,
            paymentCount: 35,
            inquiryConversionRate: 28.0,
            salesAmount: 12500.00,
            satisfactionRate: 96.5
        }, {
            id: 2,
            reportDate: '2024-01-15',
            teamName: 'B组',
            shopName: '专营店',
            serviceShift: '中班',
            serviceStaff: '李小红',
            receptionCount: 98,
            firstResponseTime: 18.2,
            avgResponseTime: 25.6,
            threeMinReplyRate: 95.2,
            paymentCount: 28,
            inquiryConversionRate: 28.6,
            salesAmount: 9800.50,
            satisfactionRate: 94.8
        }];

        let editingId = null;

        // 初始化
        document.addEventListener('DOMContentLoaded', function() {
            // 设置默认日期为今天
            const today = new Date().toISOString().split('T')[0];
            document.getElementById('endDate').value = today;

            // 设置开始日期为7天前
            const sevenDaysAgo = new Date();
            sevenDaysAgo.setDate(sevenDaysAgo.getDate() - 7);
            document.getElementById('startDate').value = sevenDaysAgo.toISOString().split('T')[0];

            // 渲染表格
            renderTable();

            // 为表头添加拖拽功能
            setupTableResizing();

            // 绑定表单提交事件
            document.getElementById('reportForm').addEventListener('submit', handleFormSubmit);
        });

        // 渲染表格
        function renderTable() {
            const tbody = document.getElementById('reportTableBody');
            const emptyState = document.getElementById('emptyState');

            if (reportData.length === 0) {
                tbody.innerHTML = '';
                emptyState.style.display = 'block';
                document.getElementById('totalRecords').textContent = '0';
                return;
            }

            emptyState.style.display = 'none';
            document.getElementById('totalRecords').textContent = reportData.length;

            // 清空表格内容
            tbody.innerHTML = '';

            // 逐行添加数据
            reportData.forEach(item => {
                const row = document.createElement('tr');
                row.innerHTML = `
                    <td>${item.reportDate}</td>
                    <td>${item.teamName}</td>
                    <td>${item.shopName}</td>
                    <td>${item.serviceShift}</td>
                    <td>${item.serviceStaff}</td>
                    <td class="number-cell">${item.receptionCount}</td>
                    <td class="number-cell">${item.firstResponseTime}</td>
                    <td class="number-cell">${item.avgResponseTime}</td>
                    <td class="percentage-cell">${item.threeMinReplyRate}%</td>
                    <td class="number-cell">${item.paymentCount}</td>
                    <td class="percentage-cell">${item.inquiryConversionRate}%</td>
                    <td class="money-cell">¥${item.salesAmount.toFixed(2)}</td>
                    <td class="percentage-cell">${item.satisfactionRate}%</td>
                    <td>
                        <div class="action-group">
                            <button class="btn btn-primary btn-small" onclick="editReport(${item.id})">编辑</button>
                            <button class="btn btn-danger btn-small" onclick="deleteReport(${item.id})">删除</button>
                        </div>
                    </td>
                `;
                tbody.appendChild(row);
            });

            // 添加表头拖拽功能
            setupTableResizing();
        }

        // 显示新增日报表单
        function showReportForm() {
            editingId = null;
            document.getElementById('formModalTitle').textContent = '新增日报';
            document.getElementById('reportForm').reset();
            document.getElementById('reportFormModal').style.display = 'block';
        }

        // 关闭日报表单
        function closeReportForm() {
            document.getElementById('reportFormModal').style.display = 'none';
            document.getElementById('reportForm').reset();
            editingId = null;
        }

        // 处理表单提交
        function handleFormSubmit(e) {
            e.preventDefault();

            const formData = {
                reportDate: document.getElementById('reportDate').value,
                teamName: document.getElementById('teamName').value,
                shopName: document.getElementById('shopName').value,
                serviceShift: document.getElementById('serviceShift').value,
                serviceStaff: document.getElementById('serviceStaff').value,
                receptionCount: parseInt(document.getElementById('receptionCount').value) || 0,
                firstResponseTime: parseFloat(document.getElementById('firstResponseTime').value) || 0,
                avgResponseTime: parseFloat(document.getElementById('avgResponseTime').value) || 0,
                threeMinReplyRate: parseFloat(document.getElementById('threeMinReplyRate').value) || 0,
                paymentCount: parseInt(document.getElementById('paymentCount').value) || 0,
                inquiryConversionRate: parseFloat(document.getElementById('inquiryConversionRate').value) || 0,
                salesAmount: parseFloat(document.getElementById('salesAmount').value) || 0,
                satisfactionRate: parseFloat(document.getElementById('satisfactionRate').value) || 0
            };

            if (editingId) {
                // 更新数据
                const index = reportData.findIndex(item => item.id === editingId);
                if (index !== -1) {
                    reportData[index] = {...formData,
                        id: editingId
                    };
                }
            } else {
                // 新增数据
                const newId = reportData.length > 0 ? Math.max(...reportData.map(item => item.id)) + 1 : 1;
                reportData.push({...formData,
                    id: newId
                });
            }

            renderTable();
            closeReportForm();

            // 显示成功提示
            alert(editingId ? '更新成功！' : '添加成功！');
        }

        // 编辑日报
        function editReport(id) {
            const report = reportData.find(item => item.id === id);
            if (!report) return;

            editingId = id;
            document.getElementById('formModalTitle').textContent = '编辑日报';

            // 填充表单数据
            document.getElementById('reportDate').value = report.reportDate;
            document.getElementById('teamName').value = report.teamName;
            document.getElementById('shopName').value = report.shopName;
            document.getElementById('serviceShift').value = report.serviceShift;
            document.getElementById('serviceStaff').value = report.serviceStaff;
            document.getElementById('receptionCount').value = report.receptionCount;
            document.getElementById('firstResponseTime').value = report.firstResponseTime;
            document.getElementById('avgResponseTime').value = report.avgResponseTime;
            document.getElementById('threeMinReplyRate').value = report.threeMinReplyRate;
            document.getElementById('paymentCount').value = report.paymentCount;
            document.getElementById('inquiryConversionRate').value = report.inquiryConversionRate;
            document.getElementById('salesAmount').value = report.salesAmount;
            document.getElementById('satisfactionRate').value = report.satisfactionRate;

            document.getElementById('reportFormModal').style.display = 'block';
        }

        // 删除日报
        function deleteReport(id) {
            if (confirm('确定要删除这条记录吗？')) {
                reportData = reportData.filter(item => item.id !== id);
                renderTable();
                alert('删除成功！');
            }
        }

        // 按日期筛选
        function filterByDate() {
            const startDate = document.getElementById('startDate').value;
            const endDate = document.getElementById('endDate').value;

            if (!startDate || !endDate) {
                alert('请选择日期范围');
                return;
            }

            // 这里应该实现日期筛选逻辑
            alert(`筛选日期范围：${startDate} 至 ${endDate}`);
        }

        // 导出数据
        function exportData() {
            if (reportData.length === 0) {
                alert('暂无数据可导出');
                return;
            }

            // 这里应该实现导出功能
            alert('数据导出功能开发中...');
        }

        // 打开基础设置
        function openSettingsModal() {
            document.getElementById('settingsModal').style.display = 'flex';
            document.body.style.overflow = 'hidden';
            renderIncomeTable();
            renderExpenseTable();
            renderShiftTable();

            // 为基础设置中的表格添加拖拽功能
            setTimeout(() => {
                setupTableResizing('#incomeTab table, #expenseTab table, #shiftTab table');
            }, 100);
        }

        // 关闭基础设置
        function closeSettingsModal() {
            document.getElementById('settingsModal').style.display = 'none';
            document.body.style.overflow = 'auto';
        }

        // 添加新函数
        // Tab切换功能
        function showTab(tabName) {
            // 移除所有active类
            document.querySelectorAll('.tab-btn').forEach(btn => btn.classList.remove('active'));
            document.querySelectorAll('.tab-content').forEach(content => content.classList.remove('active'));

            // 添加active类到对应的tab
            const clickedBtn = document.querySelector(`.tab-btn[onclick*="showTab('${tabName}')"]`);
            if (clickedBtn) clickedBtn.classList.add('active');

            // 激活对应的内容面板
            const contentTab = document.getElementById(tabName + 'Tab');
            if (contentTab) contentTab.classList.add('active');
        }

        // 全局变量
        let currentEditId = null;
        let currentEditType = null;

        // 模拟基础数据
        let incomeData = [{
            id: 1,
            shiftCost: 300.00,
            shopCost: 150.00,
            status: 'active'
        }];

        let expenseData = [{
            id: 1,
            staffName: '张小明',
            estimatedSalary: 200.00,
            yearsSalary: 220.00,
            status: 'active'
        }];

        // 客服班次数据
        let shiftData = [{
            id: 1,
            shiftName: '早班',
            startTime: '08:00',
            endTime: '12:00',
            status: 'active'
        }, {
            id: 2,
            shiftName: '中班',
            startTime: '12:00',
            endTime: '18:00',
            status: 'active'
        }, {
            id: 3,
            shiftName: '晚班',
            startTime: '18:00',
            endTime: '22:00',
            status: 'active'
        }];

        // 渲染收入设置表格
        function renderIncomeTable() {
            const tbody = document.getElementById('incomeTableBody');
            tbody.innerHTML = '';

            incomeData.forEach(item => {
                        const row = document.createElement('tr');
                        row.innerHTML = `
                    <td style="width: 60px; text-align: center;">${item.id}</td>
                    <td style="width: 30%; text-align: center;">¥${item.shiftCost.toFixed(2)}</td>
                    <td style="width: 30%; text-align: center;">¥${item.shopCost.toFixed(2)}</td>
                    <td style="width: 15%; text-align: center;"><span class="status-${item.status}">${item.status === 'active' ? '启用' : '停用'}</span></td>
                    <td style="width: 20%; text-align: center;">
                        <div class="action-buttons">
                            <button class="btn-edit" onclick="editIncomeItem(${item.id})">编辑</button>
                            ${item.status === 'active' ? 
                                `<button class="btn-delete" onclick="disableItem('income', ${item.id})">停用</button>` :
                                `<button class="btn-edit" onclick="enableItem('income', ${item.id})">启用</button>`
                            }
                        </div>
                    </td>
                `;
                tbody.appendChild(row);
            });
        }

        // 渲染支出设置表格
        function renderExpenseTable() {
            const tbody = document.getElementById('expenseTableBody');
            tbody.innerHTML = '';
            
            expenseData.forEach(item => {
                const row = document.createElement('tr');
                row.innerHTML = `
                    <td style="width: 60px; text-align: center;">${item.id}</td>
                    <td style="width: 20%;">${item.staffName}</td>
                    <td style="width: 20%;">¥${item.estimatedSalary.toFixed(2)}</td>
                    <td style="width: 20%;">¥${item.yearsSalary.toFixed(2)}</td>
                    <td style="width: 15%; text-align: center;"><span class="status-${item.status}">${item.status === 'active' ? '启用' : '停用'}</span></td>
                    <td style="width: 20%; text-align: center;">
                        <div class="action-buttons">
                            <button class="btn-edit" onclick="editExpenseItem(${item.id})">编辑</button>
                            ${item.status === 'active' ? 
                                `<button class="btn-delete" onclick="disableItem('expense', ${item.id})">停用</button>` :
                                `<button class="btn-edit" onclick="enableItem('expense', ${item.id})">启用</button>`
                            }
                        </div>
                    </td>
                `;
                tbody.appendChild(row);
            });
        }

        // 显示收入表单
        function showIncomeForm(editData = null) {
            currentEditId = editData ? editData.id : null;
            currentEditType = 'income';
            
            const modal = document.getElementById('incomeFormModal');
            
            if (editData) {
                document.getElementById('shiftCost').value = editData.shiftCost;
                document.getElementById('shopCost').value = editData.shopCost;
            } else {
                document.getElementById('incomeForm').reset();
            }
            
            modal.style.display = 'flex';
            document.body.style.overflow = 'hidden';
        }

        // 关闭收入表单
        function closeIncomeForm() {
            document.getElementById('incomeFormModal').style.display = 'none';
            // 当基础设置模态框还在显示时，保持body overflow为hidden
            if (document.getElementById('settingsModal').style.display === 'flex') {
                document.body.style.overflow = 'hidden';
            }
            currentEditId = null;
        }

        // 显示支出表单
        function showExpenseForm(editData = null) {
            currentEditId = editData ? editData.id : null;
            currentEditType = 'expense';
            
            const modal = document.getElementById('expenseFormModal');
            
            if (editData) {
                document.getElementById('staffName').value = editData.staffName;
                document.getElementById('estimatedSalary').value = editData.estimatedSalary;
                document.getElementById('yearsSalary').value = editData.yearsSalary;
            } else {
                document.getElementById('expenseForm').reset();
            }
            
            modal.style.display = 'flex';
            document.body.style.overflow = 'hidden';
        }

        // 关闭支出表单
        function closeExpenseForm() {
            document.getElementById('expenseFormModal').style.display = 'none';
            // 当基础设置模态框还在显示时，保持body overflow为hidden
            if (document.getElementById('settingsModal').style.display === 'flex') {
                document.body.style.overflow = 'hidden';
            }
            currentEditId = null;
        }

        // 编辑收入项目
        function editIncomeItem(id) {
            const item = incomeData.find(i => i.id === id);
            if (item) {
                showIncomeForm(item);
            }
        }

        // 编辑支出项目
        function editExpenseItem(id) {
            const item = expenseData.find(i => i.id === id);
            if (item) {
                showExpenseForm(item);
            }
        }
        
        // 渲染班次设置表格
        function renderShiftTable() {
            const tbody = document.getElementById('shiftTableBody');
            tbody.innerHTML = '';
            
            shiftData.forEach(item => {
                const row = document.createElement('tr');
                row.innerHTML = `
                    <td style="width: 60px; text-align: center;">${item.id}</td>
                    <td style="width: 30%;">${item.shiftName}</td>
                    <td style="width: 30%; text-align: center;">${item.startTime} - ${item.endTime}</td>
                    <td style="width: 15%; text-align: center;"><span class="status-${item.status}">${item.status === 'active' ? '启用' : '停用'}</span></td>
                    <td style="width: 20%; text-align: center;">
                        <div class="action-buttons">
                            <button class="btn-edit" onclick="editShiftItem(${item.id})">编辑</button>
                            ${item.status === 'active' ? 
                                `<button class="btn-delete" onclick="disableItem('shift', ${item.id})">停用</button>` :
                                `<button class="btn-edit" onclick="enableItem('shift', ${item.id})">启用</button>`
                            }
                        </div>
                    </td>
                `;
                tbody.appendChild(row);
            });
        }
        
        // 显示班次表单
        function showShiftForm(editData = null) {
            currentEditId = editData ? editData.id : null;
            currentEditType = 'shift';
            
            const modal = document.getElementById('shiftFormModal');
            
            if (editData) {
                document.getElementById('shiftName').value = editData.shiftName;
                document.getElementById('startTime').value = editData.startTime;
                document.getElementById('endTime').value = editData.endTime;
            } else {
                document.getElementById('shiftForm').reset();
            }
            
            modal.style.display = 'flex';
            document.body.style.overflow = 'hidden';
        }
        
        // 关闭班次表单
        function closeShiftForm() {
            document.getElementById('shiftFormModal').style.display = 'none';
            // 当基础设置模态框还在显示时，保持body overflow为hidden
            if (document.getElementById('settingsModal').style.display === 'flex') {
                document.body.style.overflow = 'hidden';
            }
            currentEditId = null;
        }
        
        // 编辑班次项目
        function editShiftItem(id) {
            const item = shiftData.find(i => i.id === id);
            if (item) {
                showShiftForm(item);
            }
        }

        // 停用项目
        function disableItem(type, id) {
            if (confirm('确认要停用此项目吗？')) {
                if (type === 'income') {
                    const item = incomeData.find(i => i.id === id);
                    if (item) {
                        item.status = 'disabled';
                        renderIncomeTable();
                    }
                } else if (type === 'expense') {
                    const item = expenseData.find(i => i.id === id);
                    if (item) {
                        item.status = 'disabled';
                        renderExpenseTable();
                    }
                } else if (type === 'shift') {
                    const item = shiftData.find(i => i.id === id);
                    if (item) {
                        item.status = 'disabled';
                        renderShiftTable();
                    }
                }
            }
        }
        
        // 启用项目
        function enableItem(type, id) {
            if (confirm('确认要启用此项目吗？')) {
                if (type === 'income') {
                    const item = incomeData.find(i => i.id === id);
                    if (item) {
                        item.status = 'active';
                        renderIncomeTable();
                    }
                } else if (type === 'expense') {
                    const item = expenseData.find(i => i.id === id);
                    if (item) {
                        item.status = 'active';
                        renderExpenseTable();
                    }
                } else if (type === 'shift') {
                    const item = shiftData.find(i => i.id === id);
                    if (item) {
                        item.status = 'active';
                        renderShiftTable();
                    }
                }
            }
        }

        // 处理收入表单提交
        document.addEventListener('DOMContentLoaded', function() {
            document.getElementById('incomeForm').addEventListener('submit', function(e) {
                e.preventDefault();
                
                const shiftCost = parseFloat(document.getElementById('shiftCost').value);
                const shopCost = parseFloat(document.getElementById('shopCost').value);
                
                if (isNaN(shiftCost) || isNaN(shopCost)) {
                    alert('请输入有效的数值');
                    return;
                }
                
                if (shiftCost < 0 || shopCost < 0) {
                    alert('费用不能为负数');
                    return;
                }
                
                if (currentEditId) {
                    // 编辑模式
                    const item = incomeData.find(i => i.id === currentEditId);
                    if (item) {
                        item.shiftCost = shiftCost;
                        item.shopCost = shopCost;
                    }
                } else {
                    // 新增模式
                    const newId = Math.max(...incomeData.map(i => i.id)) + 1;
                    incomeData.push({
                        id: newId,
                        shiftCost: shiftCost,
                        shopCost: shopCost,
                        status: 'active'
                    });
                }
                
                renderIncomeTable();
                closeIncomeForm();
                alert('保存成功！');
            });

            // 处理支出表单提交
            document.getElementById('expenseForm').addEventListener('submit', function(e) {
                e.preventDefault();
                
                const staffName = document.getElementById('staffName').value.trim();
                const estimatedSalary = parseFloat(document.getElementById('estimatedSalary').value);
                const yearsSalary = parseFloat(document.getElementById('yearsSalary').value);
                
                if (!staffName) {
                    alert('请输入客服人员姓名');
                    return;
                }
                
                if (isNaN(estimatedSalary) || isNaN(yearsSalary)) {
                    alert('请输入有效的工资数值');
                    return;
                }
                
                if (estimatedSalary < 0 || yearsSalary < 0) {
                    alert('工资不能为负数');
                    return;
                }
                
                // 检查是否重名（编辑时排除自己）
                const existingItem = expenseData.find(item => 
                    item.staffName === staffName && item.id !== currentEditId
                );
                if (existingItem) {
                    alert('客服人员姓名已存在，请使用其他姓名');
                    return;
                }
                
                if (currentEditId) {
                    // 编辑模式
                    const item = expenseData.find(i => i.id === currentEditId);
                    if (item) {
                        item.staffName = staffName;
                        item.estimatedSalary = estimatedSalary;
                        item.yearsSalary = yearsSalary;
                    }
                } else {
                    // 新增模式
                    const newId = Math.max(...expenseData.map(i => i.id)) + 1;
                    expenseData.push({
                        id: newId,
                        staffName: staffName,
                        estimatedSalary: estimatedSalary,
                        yearsSalary: yearsSalary,
                        status: 'active'
                    });
                }
                
                renderExpenseTable();
                closeExpenseForm();
                alert('保存成功！');
            });
            
            // 处理班次表单提交
            document.getElementById('shiftForm').addEventListener('submit', function(e) {
                e.preventDefault();
                
                const shiftName = document.getElementById('shiftName').value.trim();
                const startTime = document.getElementById('startTime').value;
                const endTime = document.getElementById('endTime').value;
                
                if (!shiftName) {
                    alert('请输入班次名称');
                    return;
                }
                
                if (!startTime) {
                    alert('请选择开始时间');
                    return;
                }
                
                if (!endTime) {
                    alert('请选择结束时间');
                    return;
                }
                
                // 检查时间是否合理
                if (startTime >= endTime) {
                    alert('开始时间必须早于结束时间');
                    return;
                }
                
                // 检查是否重名（编辑时排除自己）
                const existingItem = shiftData.find(item => 
                    item.shiftName === shiftName && item.id !== currentEditId
                );
                if (existingItem) {
                    alert('班次名称已存在，请使用其他名称');
                    return;
                }
                
                if (currentEditId) {
                    // 编辑模式
                    const item = shiftData.find(i => i.id === currentEditId);
                    if (item) {
                        item.shiftName = shiftName;
                        item.startTime = startTime;
                        item.endTime = endTime;
                    }
                } else {
                    // 新增模式
                    const newId = Math.max(...shiftData.map(i => i.id)) + 1;
                    shiftData.push({
                        id: newId,
                        shiftName: shiftName,
                        startTime: startTime,
                        endTime: endTime,
                        status: 'active'
                    });
                }
                
                renderShiftTable();
                closeShiftForm();
                alert('保存成功！');
            });
        });

        // 点击模态框外部关闭功能
        document.addEventListener('DOMContentLoaded', function() {
            document.querySelectorAll('.modal-overlay, .form-modal').forEach(modal => {
                modal.addEventListener('click', function(e) {
                    if (e.target === this) {
                        if (modal.id === 'settingsModal') {
                            closeSettingsModal();
                        } else if (modal.id === 'reportFormModal') {
                            closeReportForm();
                        } else if (modal.id === 'importModal') {
                            closeImportModal();
                        } else if (modal.id === 'incomeFormModal') {
                            closeIncomeForm();
                        } else if (modal.id === 'expenseFormModal') {
                            closeExpenseForm();
                        } else if (modal.id === 'shiftFormModal') {
                            closeShiftForm();
                        }
                    }
                });
            });

            // 键盘事件处理
            document.addEventListener('keydown', function(e) {
                if (e.key === 'Escape') {
                    // 按ESC键关闭弹窗（优先级：子弹窗 > 主弹窗）
                    if (document.getElementById('incomeFormModal').style.display === 'flex') {
                        closeIncomeForm();
                    } else if (document.getElementById('expenseFormModal').style.display === 'flex') {
                        closeExpenseForm();
                    } else if (document.getElementById('reportFormModal').style.display === 'flex') {
                        closeReportForm();
                    } else if (document.getElementById('importModal').style.display === 'flex') {
                        closeImportModal();
                    } else if (document.getElementById('settingsModal').style.display === 'flex') {
                        closeSettingsModal();
                    }
                }
            });
        });

        // 添加表头拖拽功能的JavaScript代码
        function setupTableResizing(tableSelector = '.data-table') {
            const tables = document.querySelectorAll(tableSelector);
            
            tables.forEach(table => {
                const headers = table.querySelectorAll('th');
                
                // 为每个表头添加拖拽元素
                headers.forEach(header => {
                    // 如果已经有resizer，不再添加
                    if (!header.querySelector('.resizer')) {
                        const resizer = document.createElement('div');
                        resizer.className = 'resizer';
                        header.appendChild(resizer);
                        
                        // 创建拖拽处理器
                        createResizableColumn(header, resizer);
                    }
                });
            });
        }

        function createResizableColumn(header, resizer) {
            let startX, startWidth;
            
            function startResizing(e) {
                startX = e.clientX;
                startWidth = parseInt(window.getComputedStyle(header).width, 10);
                header.classList.add('resizing');
                
                // 添加鼠标移动和鼠标抬起事件监听器
                document.addEventListener('mousemove', resizing);
                document.addEventListener('mouseup', stopResizing);
                
                // 阻止其他事件
                e.preventDefault();
            }
            
            function resizing(e) {
                const width = startWidth + (e.clientX - startX);
                if (width > 50) {
                    header.style.width = `${width}px`;
                    header.style.minWidth = `${width}px`;
                    
                    // 获取当前列的索引
                    const headRow = header.parentElement;
                    const headerIndex = Array.from(headRow.children).indexOf(header);
                    
                    // 设置对应列所有单元格的宽度
                    const table = header.closest('table');
                    const rows = table.querySelectorAll('tbody tr');
                    rows.forEach(row => {
                        const cell = row.children[headerIndex];
                        if (cell) {
                            cell.style.width = `${width}px`;
                            cell.style.minWidth = `${width}px`;
                        }
                    });
                }
            }
            
            function stopResizing() {
                header.classList.remove('resizing');
                document.removeEventListener('mousemove', resizing);
                document.removeEventListener('mouseup', stopResizing);
            }
            
            // 鼠标按下事件监听
            resizer.addEventListener('mousedown', startResizing);
        }

        // 显示导入模态框
        function showImportModal() {
            document.getElementById('importModal').style.display = 'block';
        }

        // 关闭导入模态框
        function closeImportModal() {
            document.getElementById('importModal').style.display = 'none';
        }

        // 处理文件选择
        function handleFileSelect(input) {
            if (input.files && input.files[0]) {
                const fileName = input.files[0].name;
                console.log(`选择了文件: ${fileName}`);
            }
        }

        // 下载导入模板
        function downloadTemplate() {
            alert('导入模板下载功能开发中...');
        }

        // 导入数据
        function importData() {
            const fileInput = document.getElementById('importFile');
            if (!fileInput.files || fileInput.files.length === 0) {
                alert('请先选择文件');
                return;
            }
            
            alert(`正在导入文件: ${fileInput.files[0].name}`);
            // 这里应该是实际的导入逻辑
            
            closeImportModal();
        }
    </script>
</body>

</html>

</html>