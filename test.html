<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>续重UI组件 - 专业设计</title>
    <!-- 引入Font Awesome图标库 -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- 引入Google Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600&display=swap" rel="stylesheet">
    <style>
         :root {
            /* 主色调 - 采用流行的蓝紫色调 */
            --primary-color: #5B5FEF;
            --primary-hover: #4549E0;
            --primary-light: #EEF0FF;
            --primary-lighter: #F7F8FF;
            /* 中性色 - 低饱和度灰色系 */
            --gray-50: #F9FAFB;
            --gray-100: #F3F4F6;
            --gray-200: #E5E7EB;
            --gray-300: #D1D5DB;
            --gray-400: #9CA3AF;
            --gray-500: #6B7280;
            --gray-600: #4B5563;
            --gray-700: #374151;
            --gray-800: #1F2937;
            --gray-900: #111827;
            /* 功能色 */
            --success: #10B981;
            --success-light: #ECFDF5;
            --warning: #F59E0B;
            --warning-light: #FFFBEB;
            --danger: #EF4444;
            --danger-light: #FEF2F2;
            --info: #3B82F6;
            --info-light: #EFF6FF;
            /* 阴影 */
            --shadow-xs: 0px 1px 2px rgba(16, 24, 40, 0.05);
            --shadow-sm: 0px 1px 3px rgba(16, 24, 40, 0.1), 0px 1px 2px rgba(16, 24, 40, 0.06);
            --shadow-md: 0px 4px 8px -2px rgba(16, 24, 40, 0.1), 0px 2px 4px -1px rgba(16, 24, 40, 0.06);
            --shadow-lg: 0px 12px 16px -4px rgba(16, 24, 40, 0.08), 0px 4px 6px -2px rgba(16, 24, 40, 0.03);
            /* 圆角 */
            --radius-sm: 4px;
            --radius-md: 6px;
            --radius-lg: 8px;
            --radius-xl: 12px;
            --radius-full: 9999px;
            /* 间距 */
            --space-2: 2px;
            --space-4: 4px;
            --space-8: 8px;
            --space-12: 12px;
            --space-16: 16px;
            --space-20: 20px;
            --space-24: 24px;
            --space-32: 32px;
            /* 交互时间 */
            --duration-fast: 100ms;
            --duration-normal: 200ms;
            --duration-slow: 300ms;
        }
        /* 重置样式 */
        
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, "Segoe UI", "PingFang SC", "Hiragino Sans GB", "Microsoft YaHei", sans-serif;
            background-color: #F9FAFB;
            color: var(--gray-800);
            font-size: 14px;
            line-height: 1.5;
            -webkit-font-smoothing: antialiased;
            -moz-osx-font-smoothing: grayscale;
            padding: var(--space-32);
        }
        /* 布局容器 */
        
        .app-container {
            max-width: 1200px;
            margin: 0 auto;
            background-color: #FFFFFF;
            border-radius: var(--radius-lg);
            box-shadow: var(--shadow-sm);
            padding: var(--space-32);
        }
        
        .section-title {
            font-size: 16px;
            font-weight: 600;
            color: var(--gray-900);
            margin-bottom: var(--space-24);
            position: relative;
            display: flex;
            align-items: center;
        }
        
        .section-title::before {
            content: '';
            width: 4px;
            height: 16px;
            background-color: var(--primary-color);
            border-radius: var(--radius-full);
            margin-right: var(--space-8);
            display: inline-block;
        }
        /* 卡片样式 */
        
        .card {
            background-color: #FFFFFF;
            border-radius: var(--radius-lg);
            box-shadow: var(--shadow-sm);
            border: 1px solid var(--gray-100);
            margin-bottom: var(--space-24);
            overflow: hidden;
            transition: box-shadow var(--duration-normal) ease;
        }
        
        .card:hover {
            box-shadow: var(--shadow-md);
        }
        
        .card-header {
            padding: var(--space-16) var(--space-20);
            border-bottom: 1px solid var(--gray-100);
            background-color: var(--gray-50);
        }
        
        .card-title {
            font-size: 15px;
            font-weight: 600;
            color: var(--gray-800);
            display: flex;
            align-items: center;
        }
        
        .card-title i {
            color: var(--primary-color);
            margin-right: var(--space-8);
            font-size: 16px;
        }
        
        .card-body {
            padding: var(--space-20);
        }
        /* 输入框组样式 */
        
        .form-row {
            display: flex;
            flex-wrap: wrap;
            gap: var(--space-20);
            margin-bottom: var(--space-20);
            align-items: flex-start;
        }
        
        .form-group {
            flex: 1;
            min-width: 240px;
            position: relative;
        }
        
        .form-label {
            display: block;
            font-size: 13px;
            font-weight: 500;
            color: var(--gray-700);
            margin-bottom: var(--space-8);
        }
        
        .input-container {
            position: relative;
            display: flex;
        }
        
        .form-control {
            width: 100%;
            height: 40px;
            padding: 0 var(--space-12);
            font-size: 14px;
            font-weight: 400;
            color: var(--gray-800);
            background-color: #FFFFFF;
            border: 1px solid var(--gray-300);
            border-radius: var(--radius-md);
            transition: all var(--duration-normal) ease;
        }
        
        .form-control:hover {
            border-color: var(--gray-400);
        }
        
        .form-control:focus {
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px rgba(91, 95, 239, 0.15);
            outline: none;
        }
        
        .form-control::placeholder {
            color: var(--gray-400);
        }
        
        .input-suffix {
            position: absolute;
            right: var(--space-12);
            top: 50%;
            transform: translateY(-50%);
            color: var(--gray-500);
            font-weight: 500;
            font-size: 13px;
            pointer-events: none;
        }
        /* 复选框样式 */
        
        .checkbox-group {
            display: flex;
            align-items: flex-start;
            margin-top: 4px;
        }
        
        .custom-checkbox {
            position: relative;
            display: flex;
            align-items: center;
            cursor: pointer;
            user-select: none;
            padding: var(--space-12) var(--space-16);
            border-radius: var(--radius-md);
            transition: background-color var(--duration-normal) ease;
            margin-top: 19px;
            /* 对齐其他输入框 */
        }
        
        .custom-checkbox:hover {
            background-color: var(--primary-lighter);
        }
        
        .custom-checkbox input {
            position: absolute;
            opacity: 0;
            height: 0;
            width: 0;
        }
        
        .checkmark {
            position: relative;
            height: 18px;
            width: 18px;
            background-color: #FFFFFF;
            border: 1px solid var(--gray-300);
            border-radius: var(--radius-sm);
            transition: all var(--duration-normal) ease;
            margin-right: var(--space-8);
        }
        
        .custom-checkbox:hover .checkmark {
            border-color: var(--primary-color);
        }
        
        .custom-checkbox input:checked~.checkmark {
            background-color: var(--primary-color);
            border-color: var(--primary-color);
        }
        
        .checkmark:after {
            content: "";
            position: absolute;
            display: none;
            left: 6px;
            top: 3px;
            width: 5px;
            height: 9px;
            border: solid white;
            border-width: 0 2px 2px 0;
            transform: rotate(45deg);
        }
        
        .custom-checkbox input:checked~.checkmark:after {
            display: block;
        }
        
        .checkbox-label {
            font-size: 14px;
            font-weight: 500;
            color: var(--gray-700);
        }
        /* 续重输入项 */
        
        .continue-weight-fields {
            display: flex;
            flex-wrap: wrap;
            gap: var(--space-20);
            width: 100%;
            max-height: 0;
            opacity: 0;
            overflow: hidden;
            transition: all var(--duration-normal) ease;
            padding: 0 var(--space-16);
            margin: 0;
        }
        
        .continue-weight-fields.active {
            max-height: 500px;
            opacity: 1;
            margin: var(--space-16) 0;
            padding-top: var(--space-16);
            border-top: 1px dashed var(--gray-200);
        }
        
        .sub-label {
            display: flex;
            align-items: center;
            font-size: 13px;
            color: var(--gray-600);
            margin-bottom: var(--space-4);
        }
        
        .sub-label i {
            font-size: 12px;
            margin-right: var(--space-4);
            color: var(--primary-color);
        }
        /* 响应式调整 */
        
        @media (max-width: 768px) {
            body {
                padding: var(--space-16);
            }
            .app-container {
                padding: var(--space-16);
            }
            .form-group {
                min-width: 100%;
            }
            .form-row {
                gap: var(--space-16);
            }
        }
    </style>
</head>

<body>
    <div class="app-container">
        <h1 class="section-title">物流计费设置</h1>

        <div class="card">
            <div class="card-header">
                <h2 class="card-title">
                    <i class="fas fa-balance-scale"></i>重量与计费规则
                </h2>
            </div>
            <div class="card-body">
                <div class="form-row">
                    <div class="form-group">
                        <label class="form-label">起始重量</label>
                        <div class="input-container">
                            <input type="number" class="form-control" placeholder="输入最小重量" min="0" step="0.01">
                            <span class="input-suffix">KG</span>
                        </div>
                    </div>

                    <div class="form-group">
                        <label class="form-label">最大重量</label>
                        <div class="input-container">
                            <input type="number" class="form-control" placeholder="输入最大重量" min="0" step="0.01">
                            <span class="input-suffix">KG</span>
                        </div>
                    </div>

                    <div class="checkbox-group">
                        <label class="custom-checkbox">
                            <input type="checkbox" id="continueWeightCheck" onchange="toggleContinueWeight()">
                            <span class="checkmark"></span>
                            <span class="checkbox-label">启用续重计费</span>
                        </label>
                    </div>
                </div>

                <div class="continue-weight-fields" id="continueWeightFields">
                    <div class="form-group">
                        <label class="form-label">
                            <span class="sub-label">
                                <i class="fas fa-plus-circle"></i>续重单位
                            </span>
                        </label>
                        <div class="input-container">
                            <input type="number" class="form-control" placeholder="每增加重量" min="0" step="0.01">
                            <span class="input-suffix">KG</span>
                        </div>
                    </div>

                    <div class="form-group">
                        <label class="form-label">
                            <span class="sub-label">
                                <i class="fas fa-plus-circle"></i>续重费用
                            </span>
                        </label>
                        <div class="input-container">
                            <input type="number" class="form-control" placeholder="增加金额" min="0" step="0.01">
                            <span class="input-suffix">¥</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        function toggleContinueWeight() {
            const checkbox = document.getElementById('continueWeightCheck');
            const fields = document.getElementById('continueWeightFields');

            if (checkbox.checked) {
                fields.classList.add('active');
            } else {
                fields.classList.remove('active');
            }
        }
    </script>
</body>

</html>