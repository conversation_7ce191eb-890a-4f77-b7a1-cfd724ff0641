<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8">
    <title>区间冲突提示</title>
    <!-- 引入字体图标 -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        /* 重置与基础样式 */
        
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
        }
        /* 遮罩层 - 添加模糊效果 */
        
        .overlay {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-color: rgba(0, 0, 0, 0.45);
            backdrop-filter: blur(4px);
            display: flex;
            justify-content: center;
            align-items: center;
            z-index: 1000;
        }
        /* 提示框容器 - 优化阴影和动画 */
        
        .alert-container {
            background: white;
            border-radius: 12px;
            width: 520px;
            box-shadow: 0 12px 32px rgba(0, 0, 0, 0.1), 0 2px 24px rgba(0, 0, 0, 0.08);
            animation: modalShow 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            transform-origin: center center;
        }
        /* 提示框头部 - 现代化设计 */
        
        .alert-header {
            padding: 24px;
            border-bottom: 1px solid rgba(0, 0, 0, 0.06);
            display: flex;
            align-items: center;
            gap: 12px;
        }
        
        .alert-icon {
            width: 32px;
            height: 32px;
            background: linear-gradient(135deg, #fff2f0 0%, #ffedeb 100%);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            box-shadow: 0 2px 8px rgba(255, 77, 79, 0.15);
        }
        
        .alert-icon i {
            color: #ff4d4f;
            font-size: 16px;
        }
        
        .alert-header h3 {
            font-size: 18px;
            font-weight: 600;
            color: #1f1f1f;
            letter-spacing: -0.01em;
        }
        /* 内容区域 - 优化间距和视觉层级 */
        
        .alert-content {
            padding: 24px;
        }
        
        .conflict-section {
            margin-bottom: 24px;
        }
        
        .section-title {
            font-size: 15px;
            color: #454545;
            margin-bottom: 16px;
            font-weight: 500;
        }
        /* 区间展示框 - 增加立体感 */
        
        .range-box {
            background: #fafafa;
            border: 1px solid rgba(0, 0, 0, 0.06);
            border-radius: 8px;
            padding: 20px;
            box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.02);
        }
        /* 区间项 - 添加悬停效果 */
        
        .range-item {
            display: flex;
            align-items: center;
            padding: 16px;
            background: white;
            border-radius: 6px;
            margin-bottom: 12px;
            transition: all 0.3s ease;
            border: 1px solid rgba(0, 0, 0, 0.04);
            box-shadow: 0 1px 2px rgba(0, 0, 0, 0.02);
        }
        
        .range-item:hover {
            box-shadow: 0 3px 8px rgba(0, 0, 0, 0.08);
            transform: translateY(-1px);
        }
        
        .range-item:last-child {
            margin-bottom: 0;
        }
        
        .range-label {
            flex: 0 0 130px;
            font-weight: 500;
            color: #262626;
            font-size: 14px;
        }
        
        .range-value {
            flex: 1;
            color: #595959;
            padding: 0 16px;
            font-size: 14px;
        }
        /* 状态标签 - 优化视觉效果 */
        
        .range-status {
            flex: 0 0 auto;
            font-size: 13px;
            font-weight: 500;
            padding: 4px 12px;
            border-radius: 6px;
            transition: all 0.3s ease;
        }
        
        .status-existing {
            color: #52c41a;
            background: linear-gradient(135deg, #f6ffed 0%, #edf9e8 100%);
            border: 1px solid #b7eb8f;
        }
        
        .status-new {
            color: #ff4d4f;
            background: linear-gradient(135deg, #fff2f0 0%, #fff1f0 100%);
            border: 1px solid #ffccc7;
        }
        /* 警告区域 - 优化视觉效果 */
        
        .conflict-warning {
            background: linear-gradient(135deg, #fffbe6 0%, #fff9db 100%);
            border: 1px solid #ffe58f;
            border-radius: 8px;
            padding: 20px;
            margin-top: 20px;
            box-shadow: 0 2px 8px rgba(250, 173, 20, 0.08);
        }
        
        .warning-title {
            display: flex;
            align-items: center;
            gap: 8px;
            margin-bottom: 12px;
            color: #faad14;
            font-weight: 600;
            font-size: 15px;
        }
        
        .warning-content {
            color: #434343;
            font-size: 14px;
            line-height: 1.6;
        }
        
        .suggestion {
            margin-top: 12px;
            color: #666;
            font-size: 13px;
            padding-left: 24px;
            position: relative;
        }
        
        .suggestion::before {
            content: '💡';
            position: absolute;
            left: 0;
            top: 0;
        }
        /* 底部按钮区 - 优化按钮样式 */
        
        .alert-footer {
            padding: 20px 24px;
            border-top: 1px solid rgba(0, 0, 0, 0.06);
            display: flex;
            justify-content: flex-end;
            gap: 12px;
        }
        
        .btn {
            height: 36px;
            padding: 0 20px;
            border-radius: 6px;
            font-size: 14px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 6px;
        }
        
        .btn-default {
            background: white;
            color: #595959;
            border: 1px solid #d9d9d9;
        }
        
        .btn-default:hover {
            color: #1890ff;
            border-color: #1890ff;
            background: #f0f7ff;
        }
        
        .btn-primary {
            background: #1890ff;
            color: white;
            border: 1px solid #1890ff;
            box-shadow: 0 2px 0 rgba(0, 0, 0, 0.045);
        }
        
        .btn-primary:hover {
            background: #40a9ff;
            border-color: #40a9ff;
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(24, 144, 255, 0.25);
        }
        /* 动画效果 */
        
        @keyframes modalShow {
            from {
                opacity: 0;
                transform: scale(0.95);
            }
            to {
                opacity: 1;
                transform: scale(1);
            }
        }
        /* 添加响应式设计 */
        
        @media screen and (max-width: 576px) {
            .alert-container {
                width: 90%;
                margin: 20px;
            }
        }
    </style>
</head>

<body>
    <div class="overlay">
        <div class="alert-container">
            <div class="alert-header">
                <div class="alert-icon">
                    <i class="fas fa-exclamation"></i>
                </div>
                <h3>利润区间设置冲突</h3>
            </div>
            <div class="alert-content">
                <div class="conflict-section">
                    <div class="section-title">利润起始区间信息</div>
                    <div class="range-box">
                        <div class="range-item">
                            <span class="range-label">已有利润起始区间</span>
                            <span class="range-value">￥0 - ￥3,000</span>
                            <span class="range-status status-existing">已存在</span>
                        </div>
                        <div class="range-item">
                            <span class="range-label">新增利润起始区间</span>
                            <span class="range-value">￥1,000 - ￥1,500</span>
                            <span class="range-status status-new">待添加</span>
                        </div>
                    </div>

                    <div class="conflict-warning">
                        <div class="warning-title">
                            <i class="fas fa-exclamation-circle"></i>
                            <span>区间冲突说明</span>
                        </div>
                        <div class="warning-content">
                            新增区间（￥1,000 - ￥1,500）与已有区间（￥0 - ￥3,000）存在重叠，无法直接添加。
                            <div class="suggestion">
                                建议：调整新增利润起始区间的范围，确保与现有利润区间不重叠。
                            </div>
                        </div>
                    </div>
                </div>
            </div>

        </div>
    </div>

    <script>
        function closeAlert() {
            const overlay = document.querySelector('.overlay');
            overlay.style.opacity = '0';
            overlay.style.transform = 'scale(0.95)';
            overlay.style.transition = 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)';
            setTimeout(() => {
                overlay.style.display = 'none';
            }, 300);
        }

        function viewAllRanges() {
            // 查看所有区间的逻辑
            console.log('查看所有区间');
        }
    </script>
</body>

</html>