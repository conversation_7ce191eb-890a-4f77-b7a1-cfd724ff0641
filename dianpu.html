<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>店铺归属管理系统</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/daterangepicker/daterangepicker.css" rel="stylesheet">
    <style>
        .timeline {
            position: relative;
            margin: 20px 0;
            padding: 20px 0;
        }
        
        .timeline-item {
            padding: 10px;
            border-radius: 4px;
            margin-bottom: 10px;
            background: #f8f9fa;
            border-left: 4px solid #0d6efd;
            position: relative;
        }
        
        .timeline-item.active {
            border-left-color: #198754;
        }
        
        .timeline-item.expired {
            border-left-color: #dc3545;
        }
        
        .delete-confirmation-modal {
            max-width: 500px;
        }
        
        .impact-warning {
            color: #dc3545;
            margin: 15px 0;
            padding: 10px;
            border: 1px solid #dc3545;
            border-radius: 4px;
        }
        
        .financial-impact {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 4px;
            margin-top: 10px;
        }
        
        .validation-error {
            color: #dc3545;
            font-size: 0.875rem;
            margin-top: 5px;
        }
        
        .ownership-card {
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 15px;
            margin-bottom: 15px;
        }
        
        .ownership-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
        }
        
        .status-badge {
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
        }
        
        .status-active {
            background-color: #d1e7dd;
            color: #0f5132;
        }
        
        .status-expired {
            background-color: #f8d7da;
            color: #842029;
        }
        
        .status-future {
            background-color: #cff4fc;
            color: #055160;
        }
    </style>
</head>

<body>
    <div class="container mt-4">
        <h2 class="mb-4">店铺归属管理系统</h2>

        <!-- 店铺选择 -->
        <div class="row mb-4">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-body">
                        <h5 class="card-title">当前店铺</h5>
                        <select class="form-select" id="shopSelect">
                            <option value="1">测试店铺一</option>
                            <option value="2">测试店铺二</option>
                        </select>
                    </div>
                </div>
            </div>
        </div>

        <!-- 操作按钮 -->
        <div class="row mb-4">
            <div class="col">
                <button class="btn btn-primary" onclick="showAddModal()">
                    新增归属记录
                </button>
            </div>
        </div>

        <!-- 归属记录列表 -->
        <div class="row">
            <div class="col">
                <div class="card">
                    <div class="card-header">
                        归属记录列表
                    </div>
                    <div class="card-body">
                        <div id="ownershipList"></div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 时间线视图 -->
        <div class="row mt-4">
            <div class="col">
                <div class="card">
                    <div class="card-header">
                        归属时间线
                    </div>
                    <div class="card-body">
                        <div id="timeline" class="timeline"></div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 新增/修改模态框 -->
    <div class="modal fade" id="ownershipModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="modalTitle">新增归属记录</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="ownershipForm">
                        <div class="mb-3">
                            <label class="form-label">归属类型</label>
                            <select class="form-select" id="ownershipType">
                                <option value="team">团队</option>
                                <option value="company">公司</option>
                            </select>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">归属对象</label>
                            <select class="form-select" id="ownershipTarget"></select>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">时间范围</label>
                            <input type="text" class="form-control" id="dateRange">
                        </div>
                        <div class="mb-3">
                            <label class="form-label">分成比例 (%)</label>
                            <input type="number" class="form-control" id="profitShare" min="0" max="100">
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-primary" onclick="saveOwnership()">保存</button>
                </div>
            </div>
        </div>
    </div>

    <!-- 删除确认模态框 -->
    <div class="modal fade" id="deleteModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">删除确认</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <div class="delete-confirmation-modal">
                        <div class="time-range-info" id="deleteTimeRange"></div>
                        <div class="impact-warning">
                            警告：此操作将影响该时间段内的所有财务计算结果
                        </div>
                        <div class="financial-impact" id="deleteImpact"></div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-danger" onclick="confirmDelete()">确认删除</button>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/jquery/dist/jquery.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/moment/moment.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/daterangepicker/daterangepicker.min.js"></script>
    <script>
        // 模拟数据
        let mockData = {
            teams: [{
                id: 1,
                name: '销售一组'
            }, {
                id: 2,
                name: '销售二组'
            }, {
                id: 3,
                name: '销售三组'
            }],
            companies: [{
                id: 1,
                name: '总公司'
            }, {
                id: 2,
                name: '分公司A'
            }, {
                id: 3,
                name: '分公司B'
            }],
            ownershipRecords: [{
                id: 1,
                shopId: 1,
                type: 'team',
                targetId: 1,
                startDate: '2025-01-01',
                endDate: '2025-06-30',
                profitShare: 80
            }, {
                id: 2,
                shopId: 1,
                type: 'company',
                targetId: 2,
                startDate: '2025-07-01',
                endDate: '2025-12-31',
                profitShare: 70
            }],
            // 模拟财务数据
            financials: {
                'shop1_202501': {
                    revenue: 100000,
                    profit: 30000
                },
                'shop1_202502': {
                    revenue: 120000,
                    profit: 35000
                },
                'shop1_202503': {
                    revenue: 150000,
                    profit: 45000
                }
            }
        };

        // 全局变量
        let currentShopId = 1;
        let selectedRecordId = null;
        let ownershipModal = null;
        let deleteModal = null;

        // 初始化
        document.addEventListener('DOMContentLoaded', function() {
            // 初始化模态框
            ownershipModal = new bootstrap.Modal(document.getElementById('ownershipModal'));
            deleteModal = new bootstrap.Modal(document.getElementById('deleteModal'));

            // 初始化日期选择器
            initializeDateRangePicker();

            // 初始化归属类型切换事件
            document.getElementById('ownershipType').addEventListener('change', updateOwnershipTargets);

            // 初始化店铺选择事件
            document.getElementById('shopSelect').addEventListener('change', function(e) {
                currentShopId = parseInt(e.target.value);
                refreshData();
            });

            // 首次加载数据
            refreshData();
        });

        // 初始化日期范围选择器
        function initializeDateRangePicker() {
            $('#dateRange').daterangepicker({
                startDate: moment(),
                endDate: moment().add(1, 'month'),
                locale: {
                    format: 'YYYY-MM-DD',
                    applyLabel: '确定',
                    cancelLabel: '取消',
                    customRangeLabel: '自定义范围'
                }
            });
        }

        // 刷新数据显示
        function refreshData() {
            renderOwnershipList();
            renderTimeline();
        }

        // 渲染归属记录列表
        function renderOwnershipList() {
            const records = mockData.ownershipRecords.filter(r => r.shopId === currentShopId);
            const container = document.getElementById('ownershipList');
            container.innerHTML = '';

            records.forEach(record => {
                const status = getRecordStatus(record);
                const targetName = getTargetName(record.type, record.targetId);

                const recordElement = document.createElement('div');
                recordElement.className = 'ownership-card';
                recordElement.innerHTML = `
            <div class="ownership-header">
                <div>
                    <h5>${record.type === 'team' ? '团队' : '公司'}归属: ${targetName}</h5>
                    <div class="text-muted">
                        ${record.startDate} 至 ${record.endDate}
                    </div>
                </div>
                <span class="status-badge status-${status.toLowerCase()}">${status}</span>
            </div>
            <div>
                <p>分成比例: ${record.profitShare}%</p>
                <div class="btn-group">
                    <button class="btn btn-sm btn-outline-primary" onclick="editRecord(${record.id})">
                        编辑
                    </button>
                    <button class="btn btn-sm btn-outline-danger" onclick="showDeleteConfirmation(${record.id})">
                        删除
                    </button>
                </div>
            </div>
        `;
                container.appendChild(recordElement);
            });
        }

        // 渲染时间线
        function renderTimeline() {
            const records = mockData.ownershipRecords.filter(r => r.shopId === currentShopId);
            const container = document.getElementById('timeline');
            container.innerHTML = '';

            records.sort((a, b) => new Date(a.startDate) - new Date(b.startDate));

            records.forEach(record => {
                const status = getRecordStatus(record);
                const targetName = getTargetName(record.type, record.targetId);

                const timelineItem = document.createElement('div');
                timelineItem.className = `timeline-item ${status.toLowerCase()}`;
                timelineItem.innerHTML = `
            <div class="d-flex justify-content-between">
                <strong>${targetName}</strong>
                <span>${record.startDate} → ${record.endDate}</span>
            </div>
            <div>分成比例: ${record.profitShare}%</div>
        `;
                container.appendChild(timelineItem);
            });
        }

        // 获取记录状态
        function getRecordStatus(record) {
            const now = new Date();
            const startDate = new Date(record.startDate);
            const endDate = new Date(record.endDate);

            if (now < startDate) return 'FUTURE';
            if (now > endDate) return 'EXPIRED';
            return 'ACTIVE';
        }

        // 获取目标名称
        function getTargetName(type, targetId) {
            const list = type === 'team' ? mockData.teams : mockData.companies;
            const target = list.find(t => t.id === targetId);
            return target ? target.name : '未知';
        }

        // 更新归属目标下拉列表
        function updateOwnershipTargets() {
            const type = document.getElementById('ownershipType').value;
            const targetSelect = document.getElementById('ownershipTarget');
            const targets = type === 'team' ? mockData.teams : mockData.companies;

            targetSelect.innerHTML = targets.map(t =>
                `<option value="${t.id}">${t.name}</option>`
            ).join('');
        }

        // 显示新增模态框
        function showAddModal() {
            selectedRecordId = null;
            document.getElementById('modalTitle').textContent = '新增归属记录';
            document.getElementById('ownershipForm').reset();
            updateOwnershipTargets();
            ownershipModal.show();
        }

        // 编辑记录
        function editRecord(recordId) {
            selectedRecordId = recordId;
            const record = mockData.ownershipRecords.find(r => r.id === recordId);

            document.getElementById('modalTitle').textContent = '编辑归属记录';
            document.getElementById('ownershipType').value = record.type;
            updateOwnershipTargets();
            document.getElementById('ownershipTarget').value = record.targetId;
            document.getElementById('profitShare').value = record.profitShare;

            $('#dateRange').data('daterangepicker').setStartDate(record.startDate);
            $('#dateRange').data('daterangepicker').setEndDate(record.endDate);

            ownershipModal.show();
        }

        // 保存归属记录
        function saveOwnership() {
            const form = document.getElementById('ownershipForm');
            const dateRange = $('#dateRange').data('daterangepicker');

            const newRecord = {
                shopId: currentShopId,
                type: document.getElementById('ownershipType').value,
                targetId: parseInt(document.getElementById('ownershipTarget').value),
                startDate: dateRange.startDate.format('YYYY-MM-DD'),
                endDate: dateRange.endDate.format('YYYY-MM-DD'),
                profitShare: parseInt(document.getElementById('profitShare').value)
            };

            // 验证时间段是否有重叠
            if (!validateTimelineIntegrity(newRecord)) {
                alert('该时间段与现有记录重叠，请调整时间范围！');
                return;
            }

            if (selectedRecordId) {
                // 更新现有记录
                const index = mockData.ownershipRecords.findIndex(r => r.id === selectedRecordId);
                newRecord.id = selectedRecordId;
                mockData.ownershipRecords[index] = newRecord;
            } else {
                // 添加新记录
                newRecord.id = Math.max(...mockData.ownershipRecords.map(r => r.id)) + 1;
                mockData.ownershipRecords.push(newRecord);
            }

            refreshData();
            ownershipModal.hide();
        }

        // 显示删除确认
        function showDeleteConfirmation(recordId) {
            const record = mockData.ownershipRecords.find(r => r.id === recordId);
            selectedRecordId = recordId;

            // 显示时间范围
            document.getElementById('deleteTimeRange').innerHTML = `
        <h6>删除时间范围</h6>
        <p>${record.startDate} 至 ${record.endDate}</p>
    `;

            // 显示财务影响
            const impact = calculateFinancialImpact(record);
            document.getElementById('deleteImpact').innerHTML = `
        <h6>财务影响预估</h6>
        <p>该时间段内总收入：¥${impact.revenue.toLocaleString()}</p>
        <p>该时间段内总利润：¥${impact.profit.toLocaleString()}</p>
        <p>将影响分成金额：¥${impact.shareAmount.toLocaleString()}</p>
    `;

            deleteModal.show();
        }

        // 确认删除
        function confirmDelete() {
            if (!selectedRecordId) return;

            const record = mockData.ownershipRecords.find(r => r.id === selectedRecordId);

            // 检查是否为历史记录
            if (new Date(record.startDate) < new Date()) {
                alert('无法删除历史归属记录！');
                return;
            }

            // 执行删除
            mockData.ownershipRecords = mockData.ownershipRecords.filter(r => r.id !== selectedRecordId);

            refreshData();
            deleteModal.hide();
        }

        // 验证时间段完整性
        function validateTimelineIntegrity(newRecord) {
            const existingRecords = mockData.ownershipRecords.filter(r =>
                r.shopId === currentShopId &&
                (selectedRecordId === null || r.id !== selectedRecordId)
            );

            for (const record of existingRecords) {
                const newStart = new Date(newRecord.startDate);
                const newEnd = new Date(newRecord.endDate);
                const existingStart = new Date(record.startDate);
                const existingEnd = new Date(record.endDate);

                if (
                    (newStart >= existingStart && newStart <= existingEnd) ||
                    (newEnd >= existingStart && newEnd <= existingEnd) ||
                    (newStart <= existingStart && newEnd >= existingEnd)
                ) {
                    return false;
                }
            }

            return true;
        }

        // 计算财务影响
        function calculateFinancialImpact(record) {
            // 简化的财务影响计算
            return {
                revenue: 370000,
                profit: 110000,
                shareAmount: 110000 * (record.profitShare / 100)
            };
        }
    </script>
</body>

</html>