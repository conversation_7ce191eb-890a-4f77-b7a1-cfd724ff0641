<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>广告账户商品费用</title>
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="icon" href="https://panhan.xin/assets/favicon-DfI5CYqk.ico">
    <!-- Litepicker CSS for DateRangePicker -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/litepicker/dist/css/litepicker.css" />
    <!-- 字体图标 -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css">
    <!-- Select2 CSS -->
    <link href="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css" rel="stylesheet" />
    <link href="https://cdn.jsdelivr.net/npm/select2-bootstrap-5-theme@1.3.0/dist/select2-bootstrap-5-theme.min.css" rel="stylesheet" />
    <!-- 自定义样式 -->
    <style>
         :root {
            /* 主要颜色 */
            --primary-color: #2468f2;
            --primary-light: #edf3fe;
            --secondary-color: #6c757d;
            /* 文本颜色 */
            --text-dark: #333333;
            --text-light: #666666;
            --text-muted: #999999;
            /* 状态颜色 */
            --success: #52c41a;
            --success-bg: #f6ffed;
            --warning: #faad14;
            --warning-bg: #fffbe6;
            --danger: #ff4d4f;
            --danger-bg: #fff2f0;
            --info: #2468f2;
            --info-bg: #edf3fe;
            /* 背景色 */
            --bg-light: #f5f7fa;
            --bg-lighter: #fafafa;
            --bg-white: #ffffff;
            /* 边框 */
            --border-color: #e8e8e8;
            --border-light: #f0f0f0;
            /* 阴影 */
            --shadow-sm: 0 1px 2px rgba(0, 0, 0, 0.05);
            --shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
            --shadow-lg: 0 4px 12px rgba(0, 0, 0, 0.12);
            /* 圆角 */
            --border-radius: 2px;
            --border-radius-lg: 4px;
            /* 过渡 */
            --transition: all 0.2s ease-in-out;
        }
        /* 状态筛选样式 */
        
        .status-tabs {
            height: 32px;
        }
        
        .status-tab {
            flex: 1;
            min-width: 80px;
            border-right: 1px solid var(--border-color);
        }
        
        .status-tab:last-child {
            border-right: none;
        }
        
        .status-tab-btn {
            width: 100%;
            height: 100%;
            border: none;
            background: var(--bg-white);
            color: var(--text-light);
            padding: 0 12px;
            font-size: 14px;
            transition: var(--transition);
            outline: none;
            cursor: pointer;
        }
        
        .status-tab-btn:hover {
            background-color: var(--bg-lighter);
        }
        
        .status-tab-btn.active {
            background-color: var(--primary-color);
            color: white;
            font-weight: 500;
        }
        /* 整体页面样式 */
        
        body {
            background-color: var(--bg-light);
            color: var(--text-dark);
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
            min-height: 100vh;
            padding-bottom: 1rem;
            font-size: 14px;
            line-height: 1.5;
        }
        /* 页面容器 */
        
        .main-container {
            max-width: 1280px;
            margin: 0 auto;
            padding: 1rem;
        }
        /* 页面标题 */
        
        .page-title {
            color: var(--text-dark);
            font-weight: 500;
            margin-bottom: 1rem;
            font-size: 16px;
            display: flex;
            align-items: center;
        }
        
        .page-title i {
            margin-right: 0.5rem;
            color: var(--primary-color);
        }
        /* 卡片样式优化 */
        
        .card {
            border: none;
            border-radius: var(--border-radius);
            box-shadow: var(--shadow-sm);
            background-color: var(--bg-white);
            margin-bottom: 1rem;
        }
        
        .card-body {
            padding: 16px;
        }
        /* 表格相关样式 */
        
        .table {
            font-size: 14px;
            width: 100%;
            border-collapse: separate;
            border-spacing: 0;
            border: none;
            box-shadow: none;
            margin-bottom: 0;
        }
        
        .table th,
        .table td {
            white-space: nowrap;
            vertical-align: middle;
            padding: 12px 16px;
            border-bottom: 1px solid var(--border-light);
            border-top: none;
            border-right: 1px solid var(--border-light);
        }
        
        .table th:last-child,
        .table td:last-child {
            border-right: none;
        }
        /* 表头样式 */
        
        .table thead th {
            background-color: var(--bg-lighter);
            color: var(--text-light);
            font-weight: 500;
            border-bottom: 1px solid var(--border-color);
            font-size: 13px;
            padding-top: 14px;
            padding-bottom: 14px;
        }
        /* 表格行样式 */
        
        .table tbody tr:last-child td {
            border-bottom: none;
        }
        /* 隔行变色 */
        
        .table-responsive .table tbody tr.row-even,
        .table tbody tr:nth-child(even) {
            background-color: rgba(0, 0, 0, 0.02);
        }
        /* 悬停效果 */
        
        .table-responsive .table tbody tr:hover,
        .table tbody tr:hover {
            background-color: var(--primary-light) !important;
            transition: background-color 0.15s ease-in-out;
        }
        /* 行选中状态样式 */
        
        .table tbody tr.selected {
            background-color: rgba(36, 104, 242, 0.08) !important;
            transition: background-color 0.15s ease-in-out;
        }
        /* 数字列右对齐 */
        
        .table td.text-right {
            text-align: right;
        }
        /* 数值格式化 */
        
        .amount {
            font-family: DINPro, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            color: var(--text-dark);
            font-weight: 500;
        }
        /* 表格容器 */
        
        .table-responsive {
            overflow-x: auto !important;
            background-color: var(--bg-white);
            padding: 0;
            border-top: 1px solid var(--border-light);
        }
        /* 表格标题 */
        
        .table-caption {
            font-weight: 500;
            color: var(--text-dark);
            font-size: 14px;
        }
        /* 分页容器 */
        
        .pagination-container {
            background-color: var(--bg-white);
            padding: 12px 16px;
            border-radius: var(--border-radius);
            box-shadow: var(--shadow-sm);
            font-size: 14px;
        }
        /* 分页控件 */
        
        .pagination {
            font-size: 14px;
            margin-bottom: 0;
            margin-right: 12px;
        }
        
        .page-item .page-link {
            border-radius: var(--border-radius);
            margin: 0 2px;
            color: var(--text-light);
            border: 1px solid var(--border-color);
            min-width: 32px;
            height: 32px;
            line-height: 1.5;
            padding: 4px 8px;
            text-align: center;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .page-item.active .page-link {
            background-color: var(--primary-color);
            border-color: var(--primary-color);
            color: white;
        }
        
        .page-item.disabled .page-link {
            color: var(--text-muted);
            background-color: var(--bg-lighter);
        }
        /* 页数选择器样式 */
        
        .page-size-wrapper {
            margin-left: 0;
        }
        
        .page-size-selector {
            height: 32px;
            border: 1px solid var(--border-color);
            border-radius: var(--border-radius);
            background-color: var(--bg-white);
            display: flex;
            align-items: center;
            position: relative;
            min-width: 100px;
            padding: 0 8px;
            cursor: pointer;
        }
        
        .page-size-selector::after {
            content: '';
            display: inline-block;
            margin-left: auto;
            width: 0;
            height: 0;
            border-left: 4px solid transparent;
            border-right: 4px solid transparent;
            border-top: 4px solid var(--text-light);
        }
        
        .page-size-number {
            font-weight: 500;
            color: var(--text-dark);
            margin-right: 4px;
            font-size: 14px;
        }
        
        .page-size-label {
            color: var(--text-light);
            font-size: 14px;
        }
        
        .page-size-select {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            opacity: 0;
            cursor: pointer;
            z-index: 1;
        }
        /* 按钮样式优化 */
        
        .btn {
            border-radius: var(--border-radius);
            font-weight: 400;
            padding: 4px 16px;
            transition: var(--transition);
            height: 32px;
            font-size: 14px;
            display: inline-flex;
            align-items: center;
            justify-content: center;
        }
        
        .btn i {
            margin-right: 6px;
            font-size: 14px;
        }
        
        .btn-sm {
            padding: 0 12px;
            height: 28px;
            font-size: 14px;
        }
        
        .btn-primary {
            background-color: var(--primary-color);
            border-color: var(--primary-color);
        }
        
        .btn-primary:hover {
            background-color: #1a5ad7;
            border-color: #1a5ad7;
        }
        
        .btn-secondary {
            background-color: #f2f2f2;
            border-color: #e8e8e8;
            color: var(--text-dark);
        }
        
        .btn-secondary:hover {
            background-color: #e8e8e8;
            border-color: #d9d9d9;
            color: var(--text-dark);
        }
        
        .btn-outline-danger {
            color: var(--danger);
            border-color: var(--danger);
            background-color: white;
        }
        
        .btn-outline-danger:hover {
            color: white;
            background-color: var(--danger);
        }
        /* 搜索框样式 - 修复图标与文字重叠问题 */
        
        .search-container {
            position: relative;
        }
        
        .search-icon {
            position: absolute;
            top: 50%;
            left: 10px;
            transform: translateY(-50%);
            color: var(--text-light);
            font-size: 14px;
            z-index: 2;
        }
        
        .search-input {
            padding-left: 32px !important;
            padding-right: 30px;
        }
        
        .btn-clear-search {
            position: absolute;
            top: 50%;
            right: 10px;
            transform: translateY(-50%);
            border: none;
            background: transparent;
            color: var(--text-light);
            padding: 0;
            cursor: pointer;
            font-size: 14px;
            z-index: 2;
        }
        
        .btn-clear-search:hover {
            color: var(--text-dark);
        }
        /* 日期范围选择器样式 - 修复图标与文字重叠问题 */
        
        .date-range-container {
            position: relative;
        }
        
        .date-range-icon {
            position: absolute;
            top: 50%;
            left: 10px;
            transform: translateY(-50%);
            color: var(--text-light);
            font-size: 14px;
            z-index: 2;
        }
        
        .date-range-input {
            padding-left: 32px !important;
            cursor: pointer;
            background-color: var(--bg-white);
        }
        /* 搜索结果高亮 */
        
        .highlight-text {
            background-color: rgba(250, 173, 20, 0.2);
            border-radius: 2px;
            padding: 0 2px;
        }
        /* 表单元素样式统一 */
        
        .form-select-sm,
        .form-control-sm {
            font-size: 14px;
            border-radius: var(--border-radius);
            padding: 4px 12px;
            height: 32px;
            border-color: var(--border-color);
        }
        
        .form-select-sm:focus,
        .form-control-sm:focus {
            border-color: var(--primary-color);
            box-shadow: 0 0 0 2px rgba(36, 104, 242, 0.1);
        }
        /* 操作按钮列样式 */
        
        .action-column {
            display: flex;
            gap: 8px;
        }
        
        .btn-icon {
            padding: 0 8px;
            height: 28px;
            border-radius: var(--border-radius);
            display: inline-flex;
            align-items: center;
            justify-content: center;
            color: var(--primary-color);
            background-color: var(--bg-white);
            border: 1px solid var(--primary-color);
            transition: var(--transition);
            cursor: pointer;
            font-size: 13px;
        }
        
        .btn-icon:hover {
            background-color: var(--primary-color);
            color: var(--bg-white);
        }
        
        .btn-icon:active {
            transform: translateY(1px);
        }
        
        .btn-icon i {
            margin-right: 4px;
            font-size: 14px;
        }
        /* 给编辑按钮添加特定样式 */
        
        .btn-icon[title="编辑"] {
            color: var(--primary-color);
            border-color: var(--primary-color);
        }
        
        .btn-icon[title="编辑"]:hover {
            background-color: var(--primary-color);
            color: white;
        }
        
        .btn-icon[title="删除"]:hover {
            color: var(--danger);
            border-color: var(--danger);
            background-color: var(--danger-bg);
        }
        /* Select2定制样式 */
        
        .select2-container--bootstrap-5 .select2-selection {
            border-radius: 2px;
            border-color: #d9d9d9;
        }
        /* 下拉菜单样式 */
        
        .select2-container--bootstrap-5 .select2-dropdown {
            border-radius: 2px !important;
            border-color: #d9d9d9 !important;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15) !important;
        }
        /* 基本的选中项行样式 (如果需要，例如去除默认背景) */
        
        .select2-results__option--selected {
            background-color: transparent !important;
        }
        /* 应用于勾选框本身的选中样式 */
        
        .select2-results__option--selected .select2-option-checkbox {
            background-color: var(--primary-color);
            border-color: var(--primary-color);
        }
        /* 确保选中项中的文本颜色可见 */
        
        .select2-results__option--selected .select2-option-text {
            color: var(--text-dark) !important;
        }
        
        .select2-option-checkbox {
            margin-left: 0;
            cursor: pointer;
            width: 18px;
            height: 18px;
            position: relative;
            border-radius: 2px;
            -webkit-appearance: none;
            -moz-appearance: none;
            appearance: none;
            border: 1px solid #d9d9d9;
            outline: none;
            transition: all 0.2s;
            background-color: #fff;
            box-sizing: border-box;
            flex-shrink: 0;
        }
        
        .select2-option-checkbox:checked {
            background-color: #1890ff;
            border-color: #1890ff;
        }
        /* 确保勾选标记清晰可见 */
        
        .select2-option-checkbox:checked::after {
            content: '';
            position: absolute;
            left: 6px;
            top: 2px;
            width: 5px;
            height: 10px;
            border: solid white;
            border-width: 0 2px 2px 0;
            transform: rotate(45deg);
            box-sizing: content-box;
        }
        /* 选中项标签样式 */
        
        .select2-container--bootstrap-5 .select2-selection--multiple .select2-selection__choice {
            display: inline-flex;
            align-items: center;
            height: 22px;
            padding: 0 4px 0 8px;
            /* 左侧多留空间 */
            margin: 4px 4px 4px 0;
            background-color: #f0f7ff;
            border: 1px solid #d9e7fd;
            color: #333;
            border-radius: 4px;
            font-size: 13px;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            max-width: 150px;
            /* 增加选中项最大宽度 */
        }
        /* 删除按钮样式 */
        
        .select2-container--bootstrap-5 .select2-selection--multiple .select2-selection__choice__remove {
            margin-left: 3px;
            margin-right: 0;
            color: rgba(0, 0, 0, 0.45);
            font-size: 12px;
            font-weight: normal;
            background: none;
            border: none;
            cursor: pointer;
        }
        
        .select2-container--bootstrap-5 .select2-selection--multiple .select2-selection__choice__remove:hover {
            color: rgba(0, 0, 0, 0.85);
        }
        /* 计数器样式 */
        
        .select2-selection__choice__display-limit {
            display: inline-flex;
            align-items: center;
            height: 22px;
            padding: 0 8px;
            margin: 4px 4px 4px 0;
            background-color: #f5f5f5;
            border: 1px solid #e0e0e0;
            color: #666;
            border-radius: 4px;
            font-size: 13px;
            font-weight: 500;
            /* 加粗显示计数器 */
        }
        /* 优化选项容器样式 */
        
        .select2-option-container {
            display: flex;
            align-items: center;
            width: 100%;
            padding: 4px 0;
        }
        /* 确保复选框正确显示 */
        
        .select2-option-check {
            margin-right: 10px;
            padding: 0;
            display: flex;
            align-items: center;
            justify-content: center;
            width: 24px;
            height: 24px;
            flex-shrink: 0;
        }
        /* 确保文本正确显示 */
        
        .select2-option-text {
            flex: 1;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }
        /* 修复选择框高度问题 */
        
        .select2-container--bootstrap-5 .select2-selection--multiple {
            min-height: 32px;
            height: auto;
            /* 允许自适应高度 */
            max-height: none;
            /* 移除最大高度限制 */
            overflow: visible;
            /* 允许内容溢出 */
            display: flex;
            align-items: center;
            flex-wrap: wrap;
            /* 允许换行 */
            padding: 0 4px 4px 4px;
            /* 调整内边距，底部留空间防止标签被裁切 */
            border-radius: 4px;
            border-color: #d9d9d9;
        }
        /* 优化选中项容器 */
        
        .select2-container--bootstrap-5 .select2-selection--multiple .select2-selection__rendered {
            display: flex;
            align-items: center;
            flex-wrap: nowrap !important;
            /* 不允许换行，保持单行 */
            overflow: hidden !important;
            /* 隐藏溢出内容 */
            white-space: nowrap !important;
            /* 强制不换行 */
            padding: 0;
            margin: 0;
            width: calc(100% - 20px);
            /* 为清除按钮留出空间 */
        }
        /* 优化下拉菜单样式 */
        
        .select2-container--bootstrap-5 .select2-dropdown {
            border-radius: 4px !important;
            border-color: #d9d9d9 !important;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15) !important;
            overflow: hidden;
        }
        /* 下拉选项样式 */
        
        .select2-container--bootstrap-5 .select2-results__option {
            padding: 5px 12px;
            font-size: 14px;
            line-height: 22px;
            color: rgba(0, 0, 0, 0.85);
        }
        
        .select2-container--bootstrap-5 .select2-results__option:hover {
            background-color: #f5f5f5;
        }
        /* 搜索框样式 */
        
        .select2-container--bootstrap-5 .select2-search--dropdown .select2-search__field {
            border-radius: 4px;
            border-color: #d9d9d9;
            padding: 4px 8px;
        }
        /* 选中项高亮样式 */
        
        .select2-container--bootstrap-5 .select2-results__option--selected {
            background-color: #e6f4ff !important;
        }
        /* 清除按钮样式 */
        
        .select2-container--bootstrap-5 .select2-selection--multiple .select2-selection__clear {
            margin-right: 6px;
            font-size: 18px;
            color: #999;
            cursor: pointer;
        }
        
        .select2-container--bootstrap-5 .select2-selection--multiple .select2-selection__clear:hover {
            color: #666;
        }
        /* 优化placeholder样式 */
        
        .select2-container--bootstrap-5 .select2-selection--multiple .select2-search--inline .select2-search__field::placeholder {
            color: #bfbfbf;
        }
        /* 设置输入框样式以适应placeholder AND selected items */
        
        .select2-container--bootstrap-5 .select2-selection--multiple .select2-search--inline {
            flex-grow: 0;
            /* Don't allow search input to grow excessively when there are many items */
            flex-shrink: 0;
            /* Prevent shrinking too much */
            /* width: auto; /* Let it size by its content or min-width */
            margin-left: 2px;
            /* Small gap after last item/counter */
        }
        
        .select2-container--bootstrap-5 .select2-selection--multiple .select2-search--inline .select2-search__field {
            min-width: 1em;
            /* Reduce min-width, placeholder will manage empty state text */
            text-align: left;
            margin: 0;
            padding: 0;
            width: 100%;
            /* Fill its flex item container */
        }
        /* 优化选中项样式 */
        
        .select2-container--bootstrap-5 .select2-selection--multiple .select2-selection__choice {
            display: inline-flex;
            align-items: center;
            height: 22px;
            padding: 0 6px;
            margin: 4px 4px 4px 0;
            background-color: #e6f7ff;
            border: 1px solid #91d5ff;
            color: #1890ff;
            border-radius: 2px;
            font-size: 12px;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            max-width: 120px;
            /* 增加最大宽度，确保更多文本可见 */
        }
        /* 优化选中项删除按钮 */
        
        .select2-container--bootstrap-5 .select2-selection--multiple .select2-selection__choice__remove {
            margin-right: 4px;
            color: #1890ff;
            opacity: 0.7;
        }
        
        .select2-container--bootstrap-5 .select2-selection--multiple .select2-selection__choice__remove:hover {
            opacity: 1;
            background: none;
            color: #1890ff;
        }
        /* 修复选中项计数器样式 */
        
        .select2-selection__choice__display-limit {
            display: inline-flex;
            align-items: center;
            height: 22px;
            padding: 0 6px;
            margin: 4px 4px 4px 0;
            background-color: #f0f0f0;
            border: 1px solid #d9d9d9;
            color: #666;
            border-radius: 2px;
            font-size: 12px;
            font-weight: 500;
            /* 加粗显示计数器 */
        }
        /* 表头全选框样式 */
        
        #select-all {
            width: 18px;
            height: 18px;
        }
        
        .select2-option-check {
            margin-right: 10px;
            padding-left: 0;
            display: flex;
            align-items: center;
            min-width: 24px;
        }
        
        .select2-option-checkbox {
            margin-left: 0;
            cursor: pointer;
            width: 16px;
            height: 16px;
            position: relative;
            /* Ensure ::after pseudo-element is positioned relative to this */
            top: 0;
            border-color: #d9d9d9;
            border-radius: 2px;
            -webkit-appearance: none;
            -moz-appearance: none;
            appearance: none;
            border: 1px solid #d9d9d9;
            outline: none;
            transition: all 0.2s;
            background-color: #fff;
            box-sizing: border-box;
            flex-shrink: 0;
        }
        
        .select2-option-checkbox:checked {
            background-color: #1890ff;
            border-color: #1890ff;
        }
        /* Ensure the checkmark is visible and correctly positioned */
        
        .select2-option-checkbox:checked::after {
            content: '';
            position: absolute;
            left: 4.5px;
            top: 1.5px;
            width: 5px;
            height: 9px;
            border: 2px solid #fff;
            border-top: 0;
            border-left: 0;
            transform: rotate(45deg);
        }
        /* ... existing code ... */
        /* Ensure text color of selected option item remains visible */
        
        .select2-results__option--selected .select2-option-text {
            color: var(--text-dark) !important;
            /* Added !important to ensure override */
        }
        /* ... existing code ... */
        /* Select2多选容器样式 - 参考Ant Design Cascader */
        
        .select2-container--bootstrap-5 .select2-selection--multiple {
            min-height: 32px !important;
            height: 32px !important;
            /* 强制固定高度 */
            overflow: hidden !important;
            /* 强制隐藏溢出内容 */
            display: flex;
            align-items: center;
            padding: 0 30px 0 8px !important;
            /* 右侧为清除按钮预留空间 */
            border-radius: 6px;
            border-color: #d9d9d9;
            box-sizing: border-box;
            position: relative;
        }
        /* 优化选中项容器 */
        
        .select2-container--bootstrap-5 .select2-selection--multiple .select2-selection__rendered {
            display: flex;
            align-items: center;
            flex-wrap: wrap !important;
            /* 修改为允许标签换行 */
            overflow: visible !important;
            /* 修改为允许内容溢出显示 */
            white-space: normal;
            /* 允许文本换行 */
            padding: 0;
            margin: 0;
            width: 100%;
        }
        /* 选中项标签样式 - Ant Design风格 */
        
        .select2-container--bootstrap-5 .select2-selection--multiple .select2-selection__choice {
            display: inline-flex;
            align-items: center;
            height: 24px;
            padding: 0 8px;
            margin: 4px 4px 4px 0;
            background-color: rgba(0, 0, 0, 0.06);
            /* Ant Design背景色 */
            border: 1px solid transparent;
            color: rgba(0, 0, 0, 0.85);
            border-radius: 4px;
            font-size: 14px;
            line-height: 22px;
            max-width: 100px;
            overflow: hidden;
            text-overflow: ellipsis;
        }
        /* 计数器样式 (+N) */
        
        .select2-selection__choice__display-limit {
            display: inline-flex;
            align-items: center;
            height: 24px;
            padding: 0 7px;
            margin: 4px 4px 4px 0;
            background-color: rgba(0, 0, 0, 0.06);
            border: 1px solid transparent;
            color: rgba(0, 0, 0, 0.65);
            border-radius: 4px;
            font-size: 14px;
            flex-shrink: 0;
        }
        /* 搜索框容器 */
        
        .select2-container--bootstrap-5 .select2-selection--multiple .select2-search--inline {
            flex-grow: 0;
            flex-shrink: 0;
            margin-left: 2px;
        }
        /* 搜索输入框 */
        
        .select2-container--bootstrap-5 .select2-selection--multiple .select2-search--inline .select2-search__field {
            min-width: 20px;
            width: auto !important;
            margin: 0;
            padding: 0;
        }
        /* 确保选中项标签完全可见 */
        
        .select2-container--bootstrap-5 .select2-selection--multiple .select2-selection__choice {
            display: inline-flex !important;
            visibility: visible !important;
            z-index: 10;
            max-width: 150px;
            text-overflow: ellipsis;
            overflow: hidden;
            white-space: nowrap;
        }
        /* ... existing code ... */
    </style>
</head>

<body>
    <div class="main-container">
        <!-- 页面标题和按钮区域 -->
        <div class="page-header d-flex justify-content-between align-items-center mb-3">
            <h4 class="page-title"><i class="bi bi-bag-check"></i>广告账户商品费用</h4>
            <div class="d-flex gap-2">
                <button class="btn btn-success btn-sm" id="add-record-btn">
                    <i class="bi bi-plus-circle"></i>新增
                </button>
                <button class="btn btn-primary btn-sm" id="select-file-btn">
                    <i class="bi bi-upload"></i>导入
                </button>
                <input type="file" id="csv-file-input" accept=".csv,.xls,.xlsx" style="display: none;">
            </div>
        </div>

        <!-- 筛选区域 - 调整布局和间距 -->
        <div class="card mb-3">
            <div class="card-body">
                <div class="row g-3 align-items-end">
                    <!-- 日期范围筛选 -->
                    <div class="col-md-4">
                        <label class="form-label small text-muted mb-2">数据日期范围</label>
                        <div class="date-range-container">
                            <i class="bi bi-calendar3 date-range-icon position-absolute"></i>
                            <input type="text" id="date-range-input" class="form-control form-control-sm date-range-input" placeholder="选择日期范围" readonly>
                        </div>
                    </div>

                    <!-- 店铺名称筛选 -->
                    <div class="col-md-4">
                        <label class="form-label small text-muted mb-2">店铺名称</label>
                        <select id="shop-filter" class="form-select form-select-sm" multiple="multiple">
                            <!-- 店铺选项将由JavaScript动态添加 -->
                        </select>
                    </div>

                    <!-- 平台商品ID筛选 -->
                    <div class="col-md-3">
                        <label class="form-label small text-muted mb-2">平台商品ID</label>
                        <div class="search-container position-relative">
                            <i class="bi bi-search search-icon position-absolute"></i>
                            <input type="text" id="product-id-filter" class="form-control form-control-sm search-input" placeholder="输入平台商品ID">
                            <button type="button" class="btn-clear-search position-absolute" style="display: none;">
                                <i class="bi bi-x-circle"></i>
                            </button>
                        </div>
                    </div>

                    <!-- 清除数据按钮 -->
                    <div class="col-md-1">
                        <label class="form-label small text-muted mb-2">&nbsp;</label>
                        <button class="btn btn-secondary btn-sm w-100" id="clear-data-btn">
                            <i class="bi bi-trash"></i>清除
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- 数据表格 -->
        <div class="card mb-3">
            <div class="table-caption p-3 border-bottom">
                <div class="d-flex justify-content-between align-items-center">
                    <span class="fw-medium">商品广告费用列表</span>
                    <div class="d-flex align-items-center">
                        <div id="batch-actions" class="d-none d-flex align-items-center">
                            <span class="me-3 text-muted">已选择 <span id="selected-count">0</span> 项</span>
                            <button id="batch-delete-btn" class="btn btn-danger btn-sm">
                                <i class="bi bi-trash"></i>删除
                            </button>
                        </div>
                        <span class="text-muted small ms-3">显示所有符合条件的记录</span>
                    </div>
                </div>
            </div>
            <div class="table-responsive">
                <table class="table align-middle">
                    <thead>
                        <tr>
                            <th style="width: 40px;">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="select-all">
                                </div>
                            </th>
                            <th style="width: 60px;">#</th>
                            <th>数据日期</th>
                            <th>广告户ID</th>
                            <th>平台商品ID</th>
                            <th>店铺名称</th>
                            <th>管易店铺ID</th>
                            <th class="text-right">广告户实际花费</th>
                            <th>操作时间</th>
                            <th>操作人员</th>
                            <th style="width: 80px;">操作</th>
                        </tr>
                    </thead>
                    <tbody id="data-table-body">
                        <!-- 数据行将通过JavaScript动态插入 -->
                    </tbody>
                </table>
            </div>

            <!-- 分页和总记录数容器 -->
            <div class="d-flex justify-content-between align-items-center p-3 border-top">
                <div class="d-flex align-items-center">
                    <span class="text-muted">共 <span id="total-records" class="fw-medium text-dark">0</span> 条记录</span>
                </div>
                <div class="d-flex align-items-center">
                    <nav aria-label="Page navigation">
                        <ul class="pagination justify-content-end mb-0" id="pagination">
                            <!-- 分页项将通过JavaScript动态插入 -->
                        </ul>
                    </nav>
                    <div class="page-size-wrapper">
                        <div class="page-size-selector">
                            <span class="page-size-number" id="page-size-display">20</span>
                            <span class="page-size-label">条/页</span>
                            <select id="page-size-select" class="page-size-select">
                               <option value="10">10</option>
                               <option value="20" selected>20</option>
                               <option value="50">50</option>
                               <option value="100">100</option>
                            </select>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 回到顶部按钮 -->
    <button class="btn btn-icon btn-primary position-fixed" id="back-to-top" style="bottom: 24px; right: 24px; display: none;">
        <i class="bi bi-arrow-up"></i>
    </button>

    <!-- 编辑记录模态框 -->
    <div class="modal fade" id="editRecordModal" tabindex="-1" aria-labelledby="editRecordModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="editRecordModalLabel">编辑广告费用</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <form id="editRecordForm">
                        <input type="hidden" id="edit-record-id">
                        <div class="mb-3">
                            <label for="edit-date" class="form-label">数据日期</label>
                            <input type="text" class="form-control" id="edit-date" disabled>
                        </div>
                        <div class="mb-3">
                            <label for="edit-ad-id" class="form-label">广告户ID</label>
                            <input type="text" class="form-control" id="edit-ad-id" disabled>
                        </div>
                        <div class="mb-3">
                            <label for="edit-product-id" class="form-label">平台商品ID</label>
                            <input type="text" class="form-control" id="edit-product-id" disabled>
                        </div>
                        <div class="mb-3">
                            <label for="edit-shop-name" class="form-label">店铺名称</label>
                            <input type="text" class="form-control" id="edit-shop-name" disabled>
                        </div>
                        <div class="mb-3">
                            <label for="edit-guanyi-id" class="form-label">管易店铺ID</label>
                            <input type="text" class="form-control" id="edit-guanyi-id" disabled>
                        </div>
                        <div class="mb-3">
                            <label for="edit-ad-cost" class="form-label">广告户实际花费 <span class="text-danger">*</span></label>
                            <input type="number" class="form-control" id="edit-ad-cost" step="0.01" min="0" required>
                            <div class="form-text text-muted">仅可修改广告户实际花费</div>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-primary" id="saveEditBtn">保存</button>
                </div>
            </div>
        </div>
    </div>

    <!-- 新增记录模态框 -->
    <div class="modal fade" id="addRecordModal" tabindex="-1" aria-labelledby="addRecordModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="addRecordModalLabel">新增广告费用记录</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <form id="addRecordForm">
                        <div class="mb-3">
                            <label for="add-date" class="form-label">数据日期 <span class="text-danger">*</span></label>
                            <input type="date" class="form-control" id="add-date" required>
                        </div>
                        <div class="mb-3">
                            <label for="add-ad-id" class="form-label">广告户ID <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" id="add-ad-id" required>
                        </div>
                        <div class="mb-3">
                            <label for="add-product-id" class="form-label">平台商品ID <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" id="add-product-id" required>
                        </div>
                        <div class="mb-3">
                            <label for="add-shop-name" class="form-label">店铺名称 <span class="text-danger">*</span></label>
                            <select class="form-select" id="add-shop-name" required>
                                <!-- 将通过JavaScript动态填充 -->
                            </select>
                        </div>
                        <div class="mb-3">
                            <label for="add-guanyi-id" class="form-label">管易店铺ID <span class="text-danger">*</span></label>
                            <select class="form-select" id="add-guanyi-id" required>
                                <!-- 将通过JavaScript动态填充 -->
                            </select>
                        </div>
                        <div class="mb-3">
                            <label for="add-ad-cost" class="form-label">广告户实际花费 <span class="text-danger">*</span></label>
                            <input type="number" class="form-control" id="add-ad-cost" step="0.01" min="0" required>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-success" id="saveAddBtn">保存</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS Bundle -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <!-- jQuery -->
    <script src="https://cdn.jsdelivr.net/npm/jquery@3.6.0/dist/jquery.min.js"></script>
    <!-- Select2 JS -->
    <script src="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.min.js"></script>
    <!-- Day.js -->
    <script src="https://cdn.jsdelivr.net/npm/dayjs@1/dayjs.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/dayjs@1/plugin/customParseFormat.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/dayjs@1/plugin/isoWeek.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/dayjs@1/plugin/isBetween.js"></script>
    <!-- Litepicker JS for DateRangePicker -->
    <script src="https://cdn.jsdelivr.net/npm/litepicker/dist/litepicker.js"></script>
    <!-- Excel处理库 -->
    <script src="https://cdn.jsdelivr.net/npm/xlsx@0.18.5/dist/xlsx.full.min.js"></script>
    <!-- 自定义脚本 -->
    <script>
        // 基础变量定义
        const LOCAL_STORAGE_KEY = 'adProductCostData';
        let allData = [];
        let filteredData = [];
        let currentPage = 1;
        let recordsPerPage = 20;
        const selectedItems = new Set(); // 存储选中项的ID

        // DOM元素引用
        const dateRangeInput = document.getElementById('date-range-input');
        const shopFilter = document.getElementById('shop-filter');
        const productIdFilter = document.getElementById('product-id-filter');
        const productIdClearBtn = document.querySelector('.btn-clear-search');
        const clearDataBtn = document.getElementById('clear-data-btn');
        const selectFileBtn = document.getElementById('select-file-btn');
        const csvFileInput = document.getElementById('csv-file-input');
        const dataTableBody = document.getElementById('data-table-body');
        const pagination = document.getElementById('pagination');
        const pageSizeSelect = document.getElementById('page-size-select');
        const pageSizeDisplay = document.getElementById('page-size-display');
        const totalRecordsSpan = document.getElementById('total-records');
        const backToTopBtn = document.getElementById('back-to-top');
        const batchActionsContainer = document.getElementById('batch-actions');
        const selectedCountSpan = document.getElementById('selected-count');
        const batchDeleteBtn = document.getElementById('batch-delete-btn');
        const selectAllCheckbox = document.getElementById('select-all');

        // 筛选条件
        const filterCriteria = {
            dateRange: {
                startDate: null,
                endDate: null
            },
            shopName: [],
            productId: '',
        };

        // 定义全局函数，以便在HTML中调用
        window.editRecord = function(recordId) {
            // 根据ID查找记录
            const record = allData.find(item => item.id === recordId);
            if (!record) {
                showToast('错误', '未找到记录', 'danger');
                return;
            }

            // 填充表单
            document.getElementById('edit-record-id').value = record.id;
            document.getElementById('edit-date').value = record.date;
            document.getElementById('edit-ad-id').value = record.adId;
            document.getElementById('edit-product-id').value = record.productId;
            document.getElementById('edit-shop-name').value = record.shopName;
            document.getElementById('edit-guanyi-id').value = record.guanyiShopId;
            document.getElementById('edit-ad-cost').value = record.adCost;

            // 显示模态框
            const editModal = new bootstrap.Modal(document.getElementById('editRecordModal'));
            editModal.show();
        };

        // 初始化函数
        document.addEventListener('DOMContentLoaded', function() {
            // 初始化日期选择器
            const picker = new Litepicker({
                element: dateRangeInput,
                singleMode: false,
                numberOfMonths: 2,
                numberOfColumns: 2,
                format: 'YYYY-MM-DD',
                lang: 'zh-CN',
                delimiter: ' 至 ',
                resetButton: true,
                setup: (picker) => {
                    picker.on('selected', (startDate, endDate) => {
                        if (startDate && endDate) {
                            filterCriteria.dateRange.startDate = startDate.format('YYYY-MM-DD');
                            filterCriteria.dateRange.endDate = endDate.format('YYYY-MM-DD');
                            applyFilters();
                        }
                    });
                    picker.on('clear', () => {
                        filterCriteria.dateRange.startDate = null;
                        filterCriteria.dateRange.endDate = null;
                        dateRangeInput.value = '';
                        applyFilters();
                    });
                }
            });

            // 初始化选择框和批量操作功能
            selectAllCheckbox.addEventListener('change', handleSelectAll);
            batchDeleteBtn.addEventListener('click', batchDelete);

            // 处理表格内的复选框委托事件
            dataTableBody.addEventListener('change', function(e) {
                if (e.target.classList.contains('item-checkbox')) {
                    const id = parseInt(e.target.dataset.id);
                    const row = e.target.closest('tr');

                    if (e.target.checked) {
                        selectedItems.add(id);
                        row.classList.add('selected');
                    } else {
                        selectedItems.delete(id);
                        row.classList.remove('selected');
                    }
                    updateSelectionState();
                }
            });

            // 初始化Select2多选下拉
            $(shopFilter).select2({
                theme: 'bootstrap-5',
                width: '100%',
                placeholder: '选择店铺',
                allowClear: true,
                closeOnSelect: false,
                tags: false,
                escapeMarkup: function(markup) {
                    return markup;
                }, // 允许HTML内容
                maximumSelectionLength: 0, // 不限制选择数量
                language: {
                    noResults: function() {
                        return "没有找到匹配的店铺";
                    },
                    inputTooShort: function() {
                        return "请输入店铺名称关键词";
                    }
                },
                templateResult: formatShopOption,
                templateSelection: formatShopSelection,
                dropdownCssClass: 'select2-dropdown-with-checkboxes'
            });

            // 处理Select2下拉选项的点击事件
            $(document).on('click', '.select2-results__option', function(e) {
                if ($(e.target).hasClass('select2-option-checkbox')) {
                    e.stopPropagation();

                    // 获取选项值
                    const $option = $(this).closest('.select2-results__option');
                    const value = $option.data('value');
                    const $checkbox = $(e.target);

                    // 获取当前选择的值
                    const currentValues = $(shopFilter).val() || [];

                    // 更新选择值
                    if ($checkbox.prop('checked')) {
                        if (!currentValues.includes(value)) {
                            currentValues.push(value);
                        }
                    } else {
                        const index = currentValues.indexOf(value);
                        if (index > -1) {
                            currentValues.splice(index, 1);
                        }
                    }

                    // 设置新值并触发change事件
                    $(shopFilter).val(currentValues).trigger('change');
                    return false;
                }
            });

            // 处理下拉框打开事件，确保复选框状态正确
            $(shopFilter).on('select2:open', function() {
                setTimeout(function() {
                    const selectedValues = $(shopFilter).val() || [];

                    $('.select2-results__option').each(function() {
                        const value = $(this).data('value');
                        if (value) {
                            const $checkbox = $(this).find('.select2-option-checkbox');
                            $checkbox.prop('checked', selectedValues.includes(value));
                        }
                    });
                }, 0);
            });

            // 添加全选/取消全选按钮 - 改进版UI
            $(shopFilter).on('select2:open', function() {
                if (!$('#select-all-shops-container').length) {
                    let $dropdown = $('.select2-dropdown');

                    // 创建更美观的头部容器
                    let $headerContainer = $('<div id="select-all-shops-container" class="select2-custom-header"></div>');

                    // 使用更专业的样式创建操作按钮
                    let $actionContainer = $('<div class="d-flex justify-content-between align-items-center px-3 py-2"></div>');

                    // 左侧：标题
                    let $titleDiv = $('<div class="select2-header-title">选择店铺</div>');

                    // 右侧：操作按钮
                    let $actionsDiv = $('<div class="select2-header-actions"></div>');
                    let $allBtn = $('<a href="#" id="select-all-shops" class="select2-header-action me-3">全选</a>');
                    let $clearBtn = $('<a href="#" id="clear-all-shops" class="select2-header-action">清除</a>');

                    $actionsDiv.append($allBtn).append($clearBtn);
                    $actionContainer.append($titleDiv).append($actionsDiv);
                    $headerContainer.append($actionContainer);

                    // 添加搜索提示
                    let $searchHint = $('<div class="px-3 py-2 text-muted small">输入关键字可搜索店铺名称</div>');
                    $headerContainer.append($searchHint);

                    // 插入到下拉框顶部
                    $dropdown.prepend($headerContainer);

                    // 全选事件
                    $allBtn.on('click', function(e) {
                        e.preventDefault();
                        e.stopPropagation();
                        let allOptions = [];
                        $('#shop-filter option').each(function() {
                            if ($(this).val()) allOptions.push($(this).val());
                        });
                        $(shopFilter).val(allOptions).trigger('change');

                        // 更新复选框状态
                        $('.select2-results__option').each(function() {
                            $(this).find('.select2-option-checkbox').prop('checked', true);
                        });

                        $(shopFilter).select2('close');
                    });

                    // 取消全选事件
                    $clearBtn.on('click', function(e) {
                        e.preventDefault();
                        e.stopPropagation();
                        $(shopFilter).val(null).trigger('change');

                        // 更新复选框状态
                        $('.select2-results__option').each(function() {
                            $(this).find('.select2-option-checkbox').prop('checked', false);
                        });

                        $(shopFilter).select2('close');
                    });
                }
            });

            // 选项格式化函数 - Ant Design风格
            function formatShopOption(shop) {
                if (!shop.id) return shop.text; // 全部选项
                const selected = $(shop.element).prop('selected');
                return $('<div class="select2-option-container">' +
                    '<div class="select2-option-check">' +
                    '<input type="checkbox" class="select2-option-checkbox"' +
                    (selected ? ' checked' : '') + '/>' +
                    '</div>' +
                    '<div class="select2-option-text">' + shop.text + '</div>' +
                    '</div>');
            }

            // 选中项格式化函数
            function formatShopSelection(shop) {
                console.log('formatShopSelection called:', shop);
                // 确保返回完整的文本
                return shop.text || '';
            }

            // Select2值变更事件
            $(shopFilter).on('change', function() {
                const selectedValues = $(this).val() || [];
                console.log("店铺选择变更:", selectedValues); // 添加调试输出

                filterCriteria.shopName = selectedValues;
                applyFilters();

                // 调用专门处理标签渲染的函数
                renderSelectionTags(selectedValues);
            });

            // 新增函数：专门处理标签渲染
            function renderSelectionTags(selectedItems) {
                console.log("开始渲染标签，选中项:", selectedItems);

                if (selectedItems.length === 0) return;

                // 确保Select2容器已经初始化
                setTimeout(function() {
                    const maxVisibleTags = 1; // 最多显示1个标签

                    try {
                        // 强制刷新Select2
                        $(shopFilter).select2('destroy');
                        $(shopFilter).select2({
                            theme: 'bootstrap-5',
                            width: '100%',
                            placeholder: '选择店铺',
                            allowClear: true,
                            closeOnSelect: false,
                            tags: false,
                            escapeMarkup: function(markup) {
                                return markup;
                            },
                            language: {
                                noResults: function() {
                                    return "没有找到匹配的店铺";
                                },
                                inputTooShort: function() {
                                    return "请输入店铺名称关键词";
                                }
                            },
                            templateResult: formatShopOption,
                            templateSelection: formatShopSelection,
                            dropdownCssClass: 'select2-dropdown-with-checkboxes'
                        });

                        // 重新设置选中值
                        $(shopFilter).val(selectedItems).trigger('change.select2');

                        // 找到并处理选中项容器
                        const $container = $(shopFilter).next('.select2-container').find('.select2-selection__rendered');
                        const $choices = $container.find('.select2-selection__choice');
                        const $searchBox = $container.find('.select2-search--inline');

                        console.log("选中项容器:", $container.length, "选中标签:", $choices.length);

                        // 先显示所有标签
                        $choices.show();

                        // 移除之前可能存在的计数器
                        $container.find('.select2-selection__choice__display-limit').remove();

                        // 如果选中项超过最大显示数量，添加+N计数器
                        if (selectedItems.length > maxVisibleTags) {
                            // 隐藏多余标签
                            $choices.each(function(index) {
                                if (index >= maxVisibleTags) {
                                    $(this).css('display', 'none');
                                }
                            });

                            // 创建计数器
                            const hiddenCount = selectedItems.length - maxVisibleTags;
                            const $counter = $('<span class="select2-selection__choice select2-selection__choice__display-limit">+' + hiddenCount + '</span>');

                            // 插入计数器
                            if ($searchBox.length > 0) {
                                $counter.insertBefore($searchBox);
                            } else {
                                $container.append($counter);
                            }
                        }

                        // 确保标签可见
                        $container.css('visibility', 'visible');
                        $container.css('display', 'flex');
                    } catch (e) {
                        console.error("标签渲染错误:", e);
                    }
                }, 100);
            }

            // 优化标签显示 - 参考Ant Design Cascader
            setTimeout(function() {
                const maxVisibleTags = 1; // 最多显示1个标签
                const selectedItems = $(shopFilter).val() || [];

                // 如果有选中项，确保标签正确显示
                if (selectedItems.length > 0) {
                    // 强制刷新Select2，确保标签显示正确
                    $(shopFilter).select2('close').select2('open').select2('close');

                    // 找到选中项容器
                    const $container = $(shopFilter).next('.select2-container').find('.select2-selection__rendered');
                    const $choices = $container.find('.select2-selection__choice');
                    const $searchBox = $container.find('.select2-search--inline');

                    // 先显示所有标签，以便正确计算
                    $choices.show();

                    // 移除之前可能存在的计数器
                    $container.find('.select2-selection__choice__display-limit').remove();

                    // 如果选中项超过最大显示数量，添加+N计数器
                    if (selectedItems.length > maxVisibleTags) {
                        // 隐藏多余标签
                        $choices.each(function(index) {
                            if (index >= maxVisibleTags) {
                                $(this).css('display', 'none');
                            }
                        });

                        // 创建计数器
                        const hiddenCount = selectedItems.length - maxVisibleTags;
                        const $counter = $('<span class="select2-selection__choice select2-selection__choice__display-limit">+' + hiddenCount + '</span>');

                        // 插入计数器
                        if ($searchBox.length > 0) {
                            $counter.insertBefore($searchBox);
                        } else {
                            $container.append($counter);
                        }
                    }
                }
            }, 0);

            // 平台商品ID筛选器
            productIdFilter.addEventListener('input', function() {
                const value = this.value.trim();
                if (value) {
                    productIdClearBtn.style.display = 'block';
                } else {
                    productIdClearBtn.style.display = 'none';
                }
                filterCriteria.productId = value;
                applyFilters();
            });

            // 清除平台商品ID筛选
            productIdClearBtn.addEventListener('click', function() {
                productIdFilter.value = '';
                filterCriteria.productId = '';
                this.style.display = 'none';
                applyFilters();
            });

            // 每页显示记录数
            pageSizeSelect.addEventListener('change', function() {
                recordsPerPage = parseInt(this.value);
                pageSizeDisplay.textContent = this.value;
                currentPage = 1;
                renderTable(currentPage);
            });

            // 清除所有数据
            clearDataBtn.addEventListener('click', clearAllData);

            // 文件导入
            selectFileBtn.addEventListener('click', function() {
                csvFileInput.click();
            });

            csvFileInput.addEventListener('change', function(e) {
                if (this.files.length > 0) {
                    importFile(this.files[0]);
                }
            });

            // 回到顶部按钮
            window.addEventListener('scroll', function() {
                if (window.pageYOffset > 300) {
                    backToTopBtn.style.display = 'flex';
                } else {
                    backToTopBtn.style.display = 'none';
                }
            });

            backToTopBtn.addEventListener('click', function() {
                window.scrollTo({
                    top: 0,
                    behavior: 'smooth'
                });
            });

            // 新增按钮事件
            const addRecordBtn = document.getElementById('add-record-btn');
            const addRecordModal = new bootstrap.Modal(document.getElementById('addRecordModal'));
            const saveAddBtn = document.getElementById('saveAddBtn');

            addRecordBtn.addEventListener('click', function() {
                // 设置日期默认值为今天
                document.getElementById('add-date').value = new Date().toISOString().split('T')[0];

                // 清空表单其他字段
                document.getElementById('add-ad-id').value = '';
                document.getElementById('add-product-id').value = '';
                document.getElementById('add-ad-cost').value = '';

                // 填充店铺下拉框
                const shopSelect = document.getElementById('add-shop-name');
                shopSelect.innerHTML = '';

                const shops = [...new Set(allData.map(item => item.shopName).filter(Boolean))];
                shops.forEach(shop => {
                    const option = document.createElement('option');
                    option.value = shop;
                    option.textContent = shop;
                    shopSelect.appendChild(option);
                });

                // 填充管易ID下拉框
                const guanyiSelect = document.getElementById('add-guanyi-id');
                guanyiSelect.innerHTML = '';

                const guanyiIds = [...new Set(allData.map(item => item.guanyiShopId).filter(Boolean))];
                guanyiIds.forEach(id => {
                    const option = document.createElement('option');
                    option.value = id;
                    option.textContent = id;
                    guanyiSelect.appendChild(option);
                });

                // 显示模态框
                addRecordModal.show();
            });

            // 保存新增记录
            saveAddBtn.addEventListener('click', function() {
                // 验证表单
                const addForm = document.getElementById('addRecordForm');
                if (!addForm.checkValidity()) {
                    addForm.reportValidity();
                    return;
                }

                // 获取表单数据
                const date = document.getElementById('add-date').value;
                const adId = document.getElementById('add-ad-id').value;
                const productId = document.getElementById('add-product-id').value;
                const shopName = document.getElementById('add-shop-name').value;
                const guanyiShopId = document.getElementById('add-guanyi-id').value;
                const adCost = parseFloat(document.getElementById('add-ad-cost').value);

                // 创建新记录
                const newRecord = {
                    id: Date.now(), // 使用时间戳作为唯一ID
                    date: date,
                    adId: adId,
                    productId: productId,
                    shopName: shopName,
                    guanyiShopId: guanyiShopId,
                    adCost: adCost,
                    operateTime: new Date().toISOString().replace('T', ' ').substring(0, 16),
                    operator: '当前用户' // 可以根据实际情况修改
                };

                // 添加到数据数组
                allData.unshift(newRecord);

                // 保存到本地存储
                saveDataToLocal(allData);

                // 更新表格
                updateFilterOptions();
                filterData();
                renderTable(1);

                // 隐藏模态框
                addRecordModal.hide();

                // 显示成功消息
                showToast('成功', '新增记录成功', 'success');
            });

            // 编辑记录函数
            function editRecord(recordId) {
                // 根据ID查找记录
                const record = allData.find(item => item.id === recordId);
                if (!record) {
                    showToast('错误', '未找到记录', 'danger');
                    return;
                }

                // 填充表单
                document.getElementById('edit-record-id').value = record.id;
                document.getElementById('edit-date').value = record.date;
                document.getElementById('edit-ad-id').value = record.adId;
                document.getElementById('edit-product-id').value = record.productId;
                document.getElementById('edit-shop-name').value = record.shopName;
                document.getElementById('edit-guanyi-id').value = record.guanyiShopId;
                document.getElementById('edit-ad-cost').value = record.adCost;

                // 显示模态框
                const editModal = new bootstrap.Modal(document.getElementById('editRecordModal'));
                editModal.show();
            }

            // 保存编辑
            document.getElementById('saveEditBtn').addEventListener('click', function() {
                // 验证表单
                const editForm = document.getElementById('editRecordForm');
                if (!editForm.checkValidity()) {
                    editForm.reportValidity();
                    return;
                }

                // 获取记录ID和更新后的花费值
                const recordId = parseInt(document.getElementById('edit-record-id').value);
                const newAdCost = parseFloat(document.getElementById('edit-ad-cost').value);

                // 更新数据
                const recordIndex = allData.findIndex(item => item.id === recordId);
                if (recordIndex !== -1) {
                    // 只更新广告花费字段
                    allData[recordIndex].adCost = newAdCost;
                    // 更新操作时间和操作人员
                    allData[recordIndex].operateTime = new Date().toISOString().replace('T', ' ').substring(0, 16);
                    allData[recordIndex].operator = '当前用户'; // 可以根据实际情况修改

                    // 保存到本地存储
                    saveDataToLocal(allData);

                    // 更新表格
                    filterData();
                    renderTable(currentPage);

                    // 隐藏模态框
                    bootstrap.Modal.getInstance(document.getElementById('editRecordModal')).hide();

                    // 显示成功消息
                    showToast('成功', '修改成功', 'success');
                } else {
                    showToast('错误', '未找到记录', 'danger');
                }
            });

            // 加载数据
            loadDataFromLocal();
            // 如果没有数据，添加一些模拟数据
            if (allData.length === 0) {
                addSampleData();
            }
            updateFilterOptions();
            filterData();
            renderTable(1);

            // 初始化店铺筛选标签显示
            setTimeout(function() {
                const initialValues = $(shopFilter).val() || [];
                if (initialValues.length > 0) {
                    renderSelectionTags(initialValues);
                } else {
                    console.log("初始化时没有选中店铺");
                }
                console.log("店铺筛选标签修复已实施 - " + new Date().toISOString());
            }, 200);
        });

        // 从localStorage加载数据
        function loadDataFromLocal() {
            const storedData = localStorage.getItem(LOCAL_STORAGE_KEY);
            if (storedData) {
                try {
                    allData = JSON.parse(storedData);
                } catch (e) {
                    console.error('解析本地存储数据失败', e);
                    allData = [];
                }
            }
        }

        // 将数据保存到localStorage
        function saveDataToLocal(data) {
            try {
                localStorage.setItem(LOCAL_STORAGE_KEY, JSON.stringify(data));
            } catch (e) {
                console.error('保存数据到本地存储失败', e);
                showToast('警告', '数据保存失败，可能是存储空间不足', 'warning');
            }
        }

        // 更新筛选选项
        function updateFilterOptions() {
            // 清空现有选项
            shopFilter.innerHTML = '';

            // 提取唯一的店铺名称
            const shops = [...new Set(allData.map(item => item.shopName).filter(Boolean))];

            // 添加店铺选项
            shops.forEach(shop => {
                const option = document.createElement('option');
                option.value = shop;
                option.textContent = shop;
                shopFilter.appendChild(option);
            });

            // 重新初始化Select2
            $(shopFilter).trigger('change');
        }

        // 生成模拟数据
        function addSampleData() {
            // 扩充数据集，增加多样性
            const shopNames = [
                '天猫旗舰店', '京东自营店', '拼多多专卖店', '淘宝专卖店', '抖音小店', '小红书店铺', '微信小商店',
                '唯品会旗舰店', '苏宁易购店', '阿里巴巴商城', '亚马逊海外专营店', '蘑菇街店铺', '网易严选旗舰店',
                '当当网官方店', '云集全球精选', '洋码头旗舰店', '环球捕手店'
            ];

            const productCategories = [
                '手机数码', '家用电器', '电脑办公', '服装鞋包', '美妆护肤', '食品生鲜', '家居家装',
                '运动户外', '母婴玩具', '图书音像', '钟表珠宝', '医药保健', '汽车用品', '宠物用品'
            ];

            const operators = [
                '张三', '李四', '王五', '赵六', '周七', '吴八', '郑九', '杨十',
                '陈一', '林二', '黄三', '刘四', '赵五', '孙六', '朱七', '胡八'
            ];

            const guanyiShopIds = [
                'GY001', 'GY002', 'GY003', 'GY004', 'GY005', 'GY006', 'GY007', 'GY008',
                'GY009', 'GY010', 'GY011', 'GY012', 'GY013', 'GY014', 'GY015', 'GY016'
            ];

            const sampleData = [];
            const currentDate = new Date();

            // 生成200条模拟数据
            for (let i = 0; i < 200; i++) {
                // 随机日期（过去90天内）
                const randomDays = Math.floor(Math.random() * 90);
                const dataDate = new Date(currentDate);
                dataDate.setDate(dataDate.getDate() - randomDays);
                const formattedDataDate = dataDate.toISOString().split('T')[0];

                // 随机操作时间（当天的随机时间）
                const operateDate = new Date(dataDate);
                operateDate.setHours(Math.floor(Math.random() * 24));
                operateDate.setMinutes(Math.floor(Math.random() * 60));
                const formattedOperateTime = operateDate.toISOString().replace('T', ' ').substring(0, 16);

                // 随机广告户ID
                const adId = 'AD' + String(10000 + Math.floor(Math.random() * 90000));

                // 随机平台商品ID和名称
                const category = productCategories[Math.floor(Math.random() * productCategories.length)];
                const productId = 'P' + String(100000 + Math.floor(Math.random() * 900000));

                // 随机店铺名称
                const shopName = shopNames[Math.floor(Math.random() * shopNames.length)];

                // 随机管易店铺ID
                const guanyiShopId = guanyiShopIds[Math.floor(Math.random() * guanyiShopIds.length)];

                // 更加真实的广告花费（根据品类不同，花费范围也不同）
                let adCost;
                if (category === '手机数码' || category === '家用电器' || category === '电脑办公') {
                    // 高价值产品，广告花费较高
                    adCost = Math.round((100 + Math.random() * 2000) * 100) / 100;
                } else if (category === '服装鞋包' || category === '美妆护肤' || category === '钟表珠宝') {
                    // 中等价值产品
                    adCost = Math.round((50 + Math.random() * 800) * 100) / 100;
                } else {
                    // 低价值产品
                    adCost = Math.round((10 + Math.random() * 300) * 100) / 100;
                }

                // 随机操作人员
                const operator = operators[Math.floor(Math.random() * operators.length)];

                // 创建数据项
                sampleData.push({
                    id: Date.now() + i,
                    date: formattedDataDate,
                    adId: adId,
                    productId: productId,
                    shopName: shopName,
                    guanyiShopId: guanyiShopId,
                    adCost: adCost,
                    operateTime: formattedOperateTime,
                    operator: operator
                });
            }

            allData = sampleData;
            saveDataToLocal(allData);
        }

        // 文件导入处理
        function importFile(file) {
            selectFileBtn.disabled = true;
            selectFileBtn.innerHTML = '<i class="bi bi-hourglass-split"></i>处理中';

            const reader = new FileReader();

            reader.onload = function(e) {
                try {
                    const data = e.target.result;
                    // 判断文件类型（CSV或Excel）
                    if (file.name.endsWith('.csv')) {
                        processCSV(data);
                    } else if (file.name.endsWith('.xls') || file.name.endsWith('.xlsx')) {
                        processExcel(data);
                    } else {
                        showToast('错误', '不支持的文件格式', 'danger');
                    }
                } catch (err) {
                    console.error("导入文件处理错误:", err);
                    showToast('错误', '文件处理失败: ' + err.message, 'danger');
                    resetImportState();
                }
            };

            reader.onerror = function() {
                console.error("文件读取错误");
                showToast('错误', '文件读取失败', 'danger');
                resetImportState();
            };

            // 根据文件类型读取
            if (file.name.endsWith('.csv')) {
                reader.readAsText(file);
            } else {
                reader.readAsArrayBuffer(file);
            }
        }

        // 处理CSV文件
        function processCSV(data) {
            try {
                const lines = data.trim().split(/\r?\n/);
                if (lines.length < 2) {
                    throw new Error("CSV文件没有足够的数据行");
                }

                // 尝试检测分隔符
                const firstLine = lines[0];
                let separator = ','; // 默认分隔符
                if (firstLine.includes('\t')) {
                    separator = '\t';
                } else if (firstLine.includes(';')) {
                    separator = ';';
                }

                // 解析表头
                const headers = lines[0].split(separator);

                // 尝试识别关键列的位置
                const colIndexes = {
                    date: headers.findIndex(h => h.includes('日期')),
                    adId: headers.findIndex(h => h.includes('广告') && h.includes('ID')),
                    productId: headers.findIndex(h => h.includes('商品') && h.includes('ID')),
                    shopName: headers.findIndex(h => h.includes('店铺名称')),
                    guanyiShopId: headers.findIndex(h => h.includes('管易') && h.includes('ID')),
                    adCost: headers.findIndex(h => h.includes('花费')),
                    operateTime: headers.findIndex(h => h.includes('操作时间')),
                    operator: headers.findIndex(h => h.includes('操作人员'))
                };

                // 验证是否找到所有必要的列
                const missingColumns = Object.entries(colIndexes)
                    .filter(([_, index]) => index === -1)
                    .map(([key, _]) => key);

                if (missingColumns.length > 0) {
                    throw new Error(`找不到必要的列: ${missingColumns.join(', ')}`);
                }

                // 解析数据行
                const parsedData = [];
                for (let i = 1; i < lines.length; i++) {
                    const line = lines[i].trim();
                    if (!line) continue; // 跳过空行

                    const values = line.split(separator);
                    if (values.length < Object.keys(colIndexes).length) {
                        console.warn(`行 ${i+1} 数据不完整, 跳过`);
                        continue;
                    }

                    try {
                        const dataItem = {
                            date: values[colIndexes.date].trim(),
                            adId: values[colIndexes.adId].trim(),
                            productId: values[colIndexes.productId].trim(),
                            shopName: values[colIndexes.shopName].trim(),
                            guanyiShopId: values[colIndexes.guanyiShopId].trim(),
                            adCost: parseFloat(values[colIndexes.adCost].trim().replace(/[^\d.-]/g, '')) || 0,
                            operateTime: values[colIndexes.operateTime].trim(),
                            operator: values[colIndexes.operator].trim(),
                            id: Date.now() + i // 生成唯一ID
                        };

                        parsedData.push(dataItem);
                    } catch (e) {
                        console.error(`解析第 ${i+1} 行时出错:`, e);
                        // 继续处理其他行
                    }
                }

                if (parsedData.length === 0) {
                    throw new Error("没有可导入的有效数据");
                }

                // 保存并显示数据
                allData = parsedData;
                saveDataToLocal(allData);
                updateFilterOptions();
                currentPage = 1;
                renderTable(1);

                showToast('成功', `成功导入 ${parsedData.length} 条记录`, 'success');
            } catch (err) {
                console.error("CSV处理错误:", err);
                showToast('错误', '文件处理失败: ' + err.message, 'danger');
            } finally {
                resetImportState();
            }
        }

        // 处理Excel文件
        function processExcel(data) {
            try {
                // 将ArrayBuffer转换为二进制字符串
                const arr = new Uint8Array(data);
                let binary = '';
                for (let i = 0; i < arr.length; i++) {
                    binary += String.fromCharCode(arr[i]);
                }

                // 使用XLSX库解析Excel
                const workbook = XLSX.read(binary, {
                    type: 'binary'
                });
                const firstSheetName = workbook.SheetNames[0];
                const worksheet = workbook.Sheets[firstSheetName];

                // 转换为JSON
                const jsonData = XLSX.utils.sheet_to_json(worksheet, {
                    header: 1
                });

                if (jsonData.length < 2) {
                    throw new Error("Excel文件没有足够的数据行");
                }

                // 解析表头
                const headers = jsonData[0];

                // 尝试识别关键列的位置
                const colIndexes = {
                    date: headers.findIndex(h => h && h.toString().includes('日期')),
                    adId: headers.findIndex(h => h && h.toString().includes('广告') && h.toString().includes('ID')),
                    productId: headers.findIndex(h => h && h.toString().includes('商品') && h.toString().includes('ID')),
                    shopName: headers.findIndex(h => h && h.toString().includes('店铺名称')),
                    guanyiShopId: headers.findIndex(h => h && h.toString().includes('管易') && h.toString().includes('ID')),
                    adCost: headers.findIndex(h => h && h.toString().includes('花费')),
                    operateTime: headers.findIndex(h => h && h.toString().includes('操作时间')),
                    operator: headers.findIndex(h => h && h.toString().includes('操作人员'))
                };

                // 验证是否找到所有必要的列
                const missingColumns = Object.entries(colIndexes)
                    .filter(([_, index]) => index === -1)
                    .map(([key, _]) => key);

                if (missingColumns.length > 0) {
                    throw new Error(`找不到必要的列: ${missingColumns.join(', ')}`);
                }

                // 解析数据行
                const parsedData = [];
                for (let i = 1; i < jsonData.length; i++) {
                    const row = jsonData[i];
                    if (!row || row.length < Object.keys(colIndexes).length) {
                        continue; // 跳过空行或不完整的行
                    }

                    try {
                        const dataItem = {
                            date: row[colIndexes.date] ? row[colIndexes.date].toString() : '',
                            adId: row[colIndexes.adId] ? row[colIndexes.adId].toString() : '',
                            productId: row[colIndexes.productId] ? row[colIndexes.productId].toString() : '',
                            shopName: row[colIndexes.shopName] ? row[colIndexes.shopName].toString() : '',
                            guanyiShopId: row[colIndexes.guanyiShopId] ? row[colIndexes.guanyiShopId].toString() : '',
                            adCost: parseFloat(row[colIndexes.adCost] ? row[colIndexes.adCost].toString().replace(/[^\d.-]/g, '') : '0') || 0,
                            operateTime: row[colIndexes.operateTime] ? row[colIndexes.operateTime].toString() : '',
                            operator: row[colIndexes.operator] ? row[colIndexes.operator].toString() : '',
                            id: Date.now() + i // 生成唯一ID
                        };

                        parsedData.push(dataItem);
                    } catch (e) {
                        console.error(`解析第 ${i+1} 行时出错:`, e);
                        // 继续处理其他行
                    }
                }

                if (parsedData.length === 0) {
                    throw new Error("没有可导入的有效数据");
                }

                // 保存并显示数据
                allData = parsedData;
                saveDataToLocal(allData);
                updateFilterOptions();
                currentPage = 1;
                renderTable(1);

                showToast('成功', `成功导入 ${parsedData.length} 条记录`, 'success');
            } catch (err) {
                console.error("Excel处理错误:", err);
                showToast('错误', '文件处理失败: ' + err.message, 'danger');
            } finally {
                resetImportState();
            }
        }

        // 重置导入状态
        function resetImportState() {
            if (csvFileInput) csvFileInput.value = null;
            if (selectFileBtn) {
                selectFileBtn.disabled = false;
                selectFileBtn.innerHTML = '<i class="bi bi-upload"></i>导入';
            }
        }

        // 应用筛选条件
        function applyFilters() {
            currentPage = 1; // 重置到第一页
            filterData();
            renderTable(currentPage);
        }

        // 更新选择状态
        function updateSelectionState() {
            const count = selectedItems.size;
            if (count > 0) {
                batchActionsContainer.classList.remove('d-none');
                selectedCountSpan.textContent = count;
            } else {
                batchActionsContainer.classList.add('d-none');
            }

            // 更新全选框状态
            const checkboxes = document.querySelectorAll('.item-checkbox');
            selectAllCheckbox.checked = checkboxes.length > 0 && selectedItems.size === checkboxes.length;
            selectAllCheckbox.indeterminate = selectedItems.size > 0 && selectedItems.size < checkboxes.length;
        }

        // 处理全选事件
        function handleSelectAll(e) {
            const checkboxes = document.querySelectorAll('.item-checkbox');
            const tableRows = document.querySelectorAll('#data-table-body tr');

            if (e.target.checked) {
                // 选中所有行
                checkboxes.forEach((cb, index) => {
                    cb.checked = true;
                    selectedItems.add(parseInt(cb.dataset.id));
                    if (tableRows[index]) {
                        tableRows[index].classList.add('selected');
                    }
                });
            } else {
                // 取消选中所有行
                checkboxes.forEach((cb, index) => {
                    cb.checked = false;
                    if (tableRows[index]) {
                        tableRows[index].classList.remove('selected');
                    }
                });
                selectedItems.clear();
            }
            updateSelectionState();
        }

        // 批量删除
        function batchDelete() {
            if (selectedItems.size === 0) return;

            if (confirm(`确定要删除选中的 ${selectedItems.size} 条记录吗？`)) {
                allData = allData.filter(item => !selectedItems.has(item.id));
                saveDataToLocal(allData);
                updateFilterOptions();
                filterData();
                renderTable(currentPage);
                selectedItems.clear();
                updateSelectionState();
                showToast('成功', '已删除选中记录', 'success');
            }
        }

        // 筛选数据
        function filterData() {
            // 如果没有筛选条件，显示所有数据
            if (!hasActiveFilters()) {
                filteredData = [...allData];
                return;
            }

            // 应用筛选条件
            filteredData = allData.filter(item => {
                // 日期范围筛选
                if (filterCriteria.dateRange.startDate && filterCriteria.dateRange.endDate) {
                    const itemDate = item.date;
                    if (!itemDate) return false;

                    const startDate = filterCriteria.dateRange.startDate;
                    const endDate = filterCriteria.dateRange.endDate;

                    if (itemDate < startDate || itemDate > endDate) {
                        return false;
                    }
                }

                // 店铺名称筛选 - 支持多选
                if (filterCriteria.shopName && filterCriteria.shopName.length > 0 && filterCriteria.shopName[0] !== '') {
                    if (!filterCriteria.shopName.includes(item.shopName)) {
                        return false;
                    }
                }

                // 平台商品ID筛选
                if (filterCriteria.productId && !item.productId.includes(filterCriteria.productId)) {
                    return false;
                }

                return true;
            });
        }

        // 检查是否有激活的筛选条件
        function hasActiveFilters() {
            return (
                (filterCriteria.dateRange.startDate && filterCriteria.dateRange.endDate) ||
                (filterCriteria.shopName && filterCriteria.shopName.length > 0 && filterCriteria.shopName[0] !== '') ||
                filterCriteria.productId
            );
        }

        // 渲染表格数据
        function renderTable(page) {
            currentPage = page;

            // 确保数据已筛选
            if (!filteredData || filteredData !== allData) {
                filterData();
            }

            // 更新总记录数
            totalRecordsSpan.textContent = filteredData.length;

            // 清空表格
            dataTableBody.innerHTML = '';

            // 清空选择项
            selectedItems.clear();
            updateSelectionState();

            // 如果没有数据，显示空状态
            if (filteredData.length === 0) {
                let emptyMessage = '暂无数据';
                if (hasActiveFilters()) {
                    emptyMessage = '没有找到匹配的筛选结果';
                }

                dataTableBody.innerHTML = `
                    <tr>
                        <td colspan="11" class="text-center py-5">
                            <div class="d-flex flex-column align-items-center">
                                <i class="bi bi-inbox text-muted mb-3" style="font-size: 2rem;"></i>
                                <p class="text-muted mb-0">${emptyMessage}</p>
                            </div>
                        </td>
                    </tr>`;

                // 清空分页
                pagination.innerHTML = '';
                return;
            }

            // 分页
            const startIndex = (page - 1) * recordsPerPage;
            const endIndex = Math.min(startIndex + recordsPerPage, filteredData.length);
            const paginatedData = filteredData.slice(startIndex, endIndex);

            // 渲染数据行
            paginatedData.forEach((item, index) => {
                const row = document.createElement('tr');
                const rowIndex = startIndex + index + 1;
                if (index % 2 === 1) {
                    row.classList.add('row-even');
                }

                // 格式化日期
                const formatDate = (dateStr) => {
                    if (!dateStr) return '-';
                    return dateStr;
                };

                // 格式化金额
                const formatAmount = (amount) => {
                    return new Intl.NumberFormat('zh-CN', {
                        minimumFractionDigits: 2,
                        maximumFractionDigits: 2
                    }).format(amount);
                };

                // 高亮搜索结果
                const highlightIfNeeded = (text, field) => {
                    if (!text) return '-';
                    if (field === 'productId' && filterCriteria.productId) {
                        return highlightText(text, filterCriteria.productId);
                    }
                    if (field === 'shopName' && filterCriteria.shopName && filterCriteria.shopName.length > 0 && filterCriteria.shopName[0] !== '') {
                        return `<span class="highlight-text">${text}</span>`;
                    }
                    return text;
                };

                // 构建行内容
                row.innerHTML = `
                    <td>
                        <div class="form-check">
                            <input class="form-check-input item-checkbox" type="checkbox" data-id="${item.id}">
                        </div>
                    </td>
                    <td class="text-muted">${rowIndex}</td>
                    <td>${formatDate(item.date)}</td>
                    <td>${item.adId || '-'}</td>
                    <td>${highlightIfNeeded(item.productId, 'productId')}</td>
                    <td>${highlightIfNeeded(item.shopName, 'shopName')}</td>
                    <td>${item.guanyiShopId || '-'}</td>
                    <td class="text-right amount">${formatAmount(item.adCost)}</td>
                    <td>${formatDate(item.operateTime)}</td>
                    <td>${item.operator || '-'}</td>
                    <td>
                        <div class="action-column">
                            <button class="btn-icon" onclick="editRecord(${item.id})" title="编辑">
                                <i class="bi bi-pencil"></i>编辑
                            </button>
                        </div>
                    </td>
                `;

                dataTableBody.appendChild(row);
            });

            // 渲染分页
            renderPagination();
        }

        // 渲染分页
        function renderPagination() {
            pagination.innerHTML = '';

            const totalPages = Math.ceil(filteredData.length / recordsPerPage);
            if (totalPages <= 1) return; // 不需要分页

            // 创建分页项
            const createPageItem = (text, pageNum, isDisabled = false, isActive = false, isEllipsis = false) => {
                const li = document.createElement('li');
                li.className = `page-item ${isDisabled ? 'disabled' : ''} ${isActive ? 'active' : ''}`;

                const a = document.createElement('a');
                a.className = 'page-link';
                a.href = '#';

                // 添加图标到上一页/下一页按钮
                if (text === '上一页') {
                    a.innerHTML = '<i class="bi bi-chevron-left"></i>';
                    a.setAttribute('aria-label', '上一页');
                } else if (text === '下一页') {
                    a.innerHTML = '<i class="bi bi-chevron-right"></i>';
                    a.setAttribute('aria-label', '下一页');
                } else {
                    a.textContent = text;
                }

                if (!isDisabled && !isEllipsis) {
                    a.onclick = (e) => {
                        e.preventDefault();
                        renderTable(pageNum);
                    };
                }

                li.appendChild(a);
                return li;
            };

            // 上一页按钮
            pagination.appendChild(createPageItem('上一页', currentPage - 1, currentPage === 1));

            // 页码按钮
            const maxPagesToShow = 5;
            let startPage = Math.max(1, currentPage - Math.floor(maxPagesToShow / 2));
            let endPage = Math.min(totalPages, startPage + maxPagesToShow - 1);

            // 调整起始页码
            if (endPage - startPage + 1 < maxPagesToShow) {
                startPage = Math.max(1, endPage - maxPagesToShow + 1);
            }

            // 显示第一页和省略号
            if (startPage > 1) {
                pagination.appendChild(createPageItem('1', 1));
                if (startPage > 2) {
                    pagination.appendChild(createPageItem('...', 0, true, false, true));
                }
            }

            // 显示页码
            for (let i = startPage; i <= endPage; i++) {
                pagination.appendChild(createPageItem(i, i, false, i === currentPage));
            }

            // 显示省略号和最后一页
            if (endPage < totalPages) {
                if (endPage < totalPages - 1) {
                    pagination.appendChild(createPageItem('...', 0, true, false, true));
                }
                pagination.appendChild(createPageItem(totalPages, totalPages));
            }

            // 下一页按钮
            pagination.appendChild(createPageItem('下一页', currentPage + 1, currentPage === totalPages));
        }

        // 清除所有数据
        function clearAllData() {
            if (confirm('确定要清空所有数据吗？此操作不可撤销！')) {
                // 清除数据
                allData = [];
                filteredData = [];
                // 清除localStorage
                localStorage.removeItem(LOCAL_STORAGE_KEY);
                // 重置筛选条件
                resetFilters();
                // 更新筛选器选项
                updateFilterOptions();
                // 清空选择项
                selectedItems.clear();
                updateSelectionState();
                // 重新渲染表格
                renderTable(1);

                showToast('成功', '所有数据已清除', 'success');
            }
        }

        // 重置筛选条件
        function resetFilters() {
            // 重置日期范围
            filterCriteria.dateRange.startDate = null;
            filterCriteria.dateRange.endDate = null;
            if (dateRangeInput) dateRangeInput.value = '';

            // 重置店铺名称
            filterCriteria.shopName = [];
            // 对于Select2控件，需要使用特定方法重置
            $(shopFilter).val(null).trigger('change');

            // 重置平台商品ID
            filterCriteria.productId = '';
            if (productIdFilter) productIdFilter.value = '';
            if (productIdClearBtn) productIdClearBtn.style.display = 'none';
        }

        // 高亮搜索关键词
        function highlightText(text, keyword) {
            if (!keyword) return text;
            const regex = new RegExp(keyword, 'gi');
            return text.replace(regex, match => `<span class="highlight-text">${match}</span>`);
        }

        // 显示Toast消息
        function showToast(title, message, type = 'info') {
            // 创建Toast容器
            let toastContainer = document.getElementById('toast-container');
            if (!toastContainer) {
                toastContainer = document.createElement('div');
                toastContainer.id = 'toast-container';
                toastContainer.className = 'position-fixed bottom-0 end-0 p-3';
                toastContainer.style.zIndex = '1080';
                document.body.appendChild(toastContainer);
            }

            // 创建Toast
            const toastId = 'toast-' + Date.now();
            const iconMap = {
                'success': 'bi-check-circle text-success',
                'danger': 'bi-exclamation-circle text-danger',
                'warning': 'bi-exclamation-triangle text-warning',
                'info': 'bi-info-circle text-info'
            };

            const toastHtml = `
                <div id="${toastId}" class="toast" role="alert" aria-live="assertive" aria-atomic="true">
                    <div class="toast-header">
                        <i class="bi ${iconMap[type]} me-2"></i>
                        <strong class="me-auto">${title}</strong>
                        <button type="button" class="btn-close" data-bs-dismiss="toast" aria-label="Close"></button>
                    </div>
                    <div class="toast-body">${message}</div>
                </div>
            `;

            toastContainer.insertAdjacentHTML('beforeend', toastHtml);

            // 显示Toast
            const toastElement = document.getElementById(toastId);
            const toast = new bootstrap.Toast(toastElement, {
                animation: true,
                autohide: true,
                delay: 3000
            });

            toast.show();

            // 删除已关闭的Toast
            toastElement.addEventListener('hidden.bs.toast', () => {
                toastElement.remove();
            });
        }
    </script>
</body>

</html>

</html>