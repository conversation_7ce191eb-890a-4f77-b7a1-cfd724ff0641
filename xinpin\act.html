<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>商品活动报备管理 - 电商管理系统</title>
    <link rel="stylesheet" href="https://unpkg.com/element-ui/lib/theme-chalk/index.css">
    <link rel="icon" href="https://panhan.xin/assets/favicon-DfI5CYqk.ico">
    <style>
        body {
            font-family: "Helvetica Neue", Helvetica, "PingFang SC", "Hiragino Sans GB", "Microsoft YaHei", sans-serif;
            margin: 0;
            padding: 0;
            background-color: #f5f7fa;
            color: #333;
        }
        
        .app-container {
            padding: 20px;
        }
        
        .page-header {
            background-color: #fff;
            padding: 16px 20px;
            box-shadow: 0 1px 4px rgba(0, 0, 0, 0.1);
            margin-bottom: 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .page-title {
            margin: 0;
            font-size: 20px;
            font-weight: 500;
        }
        
        .action-buttons {
            display: flex;
            gap: 15px;
        }
        
        .card {
            background-color: #fff;
            border-radius: 4px;
            box-shadow: 0 1px 4px rgba(0, 0, 0, 0.1);
            margin-bottom: 20px;
            padding: 20px;
        }
        
        .card-title {
            font-size: 16px;
            font-weight: 500;
            margin: 0 0 20px;
            padding-bottom: 10px;
            border-bottom: 1px solid #ebeef5;
            color: #303133;
        }
        
        .search-area {
            margin-bottom: 20px;
            background: #fff;
            padding: 20px;
            border-radius: 4px;
        }
        
        .form-group {
            margin-bottom: 20px;
        }
        
        .product-card {
            border: 1px solid #ebeef5;
            border-radius: 4px;
            padding: 15px;
            margin-bottom: 15px;
            position: relative;
        }
        
        .product-card .remove-btn {
            position: absolute;
            top: 10px;
            right: 10px;
            font-size: 18px;
            color: #909399;
            cursor: pointer;
        }
        
        .product-info {
            display: flex;
            align-items: flex-start;
        }
        
        .product-image {
            width: 80px;
            height: 80px;
            object-fit: cover;
            margin-right: 15px;
            border-radius: 4px;
        }
        
        .product-details {
            flex: 1;
        }
        
        .product-details h4 {
            margin: 0 0 5px;
            font-size: 16px;
        }
        
        .product-meta {
            display: flex;
            flex-wrap: wrap;
            margin-top: 10px;
        }
        
        .product-meta-item {
            margin-right: 20px;
            font-size: 13px;
            color: #606266;
            margin-bottom: 5px;
        }
        
        .meta-label {
            color: #909399;
            margin-right: 5px;
        }
        
        .activity-params {
            padding-left: 20px;
            margin-top: 10px;
            border-left: 2px solid #ebeef5;
        }
        
        .info-text {
            font-size: 13px;
            color: #909399;
        }
        
        .status-tag {
            padding: 2px 8px;
            border-radius: 2px;
            font-size: 12px;
        }
        
        .status-pending {
            background-color: #e6a23c;
            color: #fff;
        }
        
        .status-approved {
            background-color: #67c23a;
            color: #fff;
        }
        
        .status-rejected {
            background-color: #f56c6c;
            color: #fff;
        }
        
        .footer-actions {
            text-align: center;
            margin-top: 30px;
        }
        
        .form-tip {
            font-size: 13px;
            color: #909399;
            margin-top: 5px;
        }
        
        .error-tip {
            color: #f56c6c;
        }
        /* 新增样式 */
        
        .table-operations {
            margin-right: 10px;
        }
        
        .table-operations button {
            padding: 4px 8px;
            font-size: 12px;
        }
        
        .status-tag {
            display: inline-block;
            padding: 0 10px;
            height: 24px;
            line-height: 24px;
            font-size: 12px;
            border-radius: 12px;
        }
        
        .dialog-form .el-form-item {
            margin-bottom: 22px;
        }
        
        .activity-list {
            margin-top: 8px;
            padding-left: 0;
        }
        
        .activity-list-item {
            margin-bottom: 5px;
            list-style-type: none;
            display: flex;
            align-items: center;
        }
        
        .activity-icon {
            margin-right: 5px;
            font-size: 14px;
        }
        
        .small-tag {
            height: 20px;
            line-height: 18px;
            font-size: 10px;
            padding: 0 5px;
            margin-left: 5px;
        }
        /* 响应式样式调整 */
        
        @media (max-width: 768px) {
            .el-form--inline .el-form-item {
                display: block;
                margin-right: 0;
                margin-bottom: 20px;
                width: 100%;
            }
            .product-meta {
                flex-direction: column;
            }
            .product-meta-item {
                margin-right: 0;
            }
        }
    </style>
</head>

<body>
    <div id="app" class="app-container">
        <!-- 页面标题区域 -->
        <div class="page-header">
            <h1 class="page-title">商品活动报备管理</h1>
            <div class="action-buttons">
                <el-button type="primary" icon="el-icon-plus" size="small" @click="showAddReportDialog">新品产品报备</el-button>
                <el-button icon="el-icon-download" size="small">导出数据</el-button>
            </div>
        </div>

        <!-- 搜索区域 -->
        <div class="search-area">
            <el-form :inline="true" :model="searchForm" class="demo-form-inline">
                <el-form-item label="商品ID">
                    <el-input v-model="searchForm.productId" placeholder="请输入商品ID" clearable></el-input>
                </el-form-item>
                <el-form-item label="报备日期">
                    <el-date-picker v-model="searchForm.dateRange" type="daterange" range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期">
                    </el-date-picker>
                </el-form-item>
                <el-form-item label="活动类型">
                    <el-select v-model="searchForm.activityType" placeholder="请选择活动类型" clearable>
                        <el-option label="新品主推让利" value="promo"></el-option>
                        <el-option label="新品赛马锁品" value="race"></el-option>
                        <el-option label="新品亏损扶持" value="support"></el-option>
                    </el-select>
                </el-form-item>
                <el-form-item label="审核状态">
                    <el-select v-model="searchForm.reviewStatus" placeholder="请选择审核状态" clearable>
                        <el-option label="全部" value=""></el-option>
                        <el-option label="待审核" value="pending"></el-option>
                        <el-option label="已通过" value="approved"></el-option>
                        <el-option label="已拒绝" value="rejected"></el-option>
                    </el-select>
                </el-form-item>
                <el-form-item>
                    <el-button type="primary" @click="handleSearch">查询</el-button>
                    <el-button @click="resetSearch">重置</el-button>
                </el-form-item>
            </el-form>
        </div>

        <!-- 数据表格 -->
        <div class="card">
            <el-table :data="reportRecords" style="width: 100%" border v-loading="tableLoading">
                <el-table-column type="index" width="50" label="序号">
                </el-table-column>
                <el-table-column prop="reportId" label="报备ID" width="120">
                </el-table-column>
                <el-table-column label="商品信息" min-width="250">
                    <template slot-scope="scope">
                        <div style="display: flex; align-items: center;">
                            <img :src="scope.row.product.image" style="width: 40px; height: 40px; margin-right: 10px; border-radius: 4px;">
                            <div>
                                <div>{{ scope.row.product.name }}</div>
                                <div style="font-size: 12px; color: #909399;">
                                    ID: {{ scope.row.product.id }} | 店铺: {{ scope.row.product.shop }}
                                </div>
                            </div>
                        </div>
                    </template>
                </el-table-column>
                <el-table-column label="活动信息" min-width="180">
                    <template slot-scope="scope">
                        <ul class="activity-list">
                            <li v-for="activity in scope.row.activities" :key="activity.type" class="activity-list-item">
                                <i class="el-icon-promotion activity-icon"></i>
                                {{ activity.name }}
                                <el-tag v-if="activity.requireReview" size="mini" class="small-tag" :type="getStatusType(activity.reviewStatus)">
                                    {{ getStatusText(activity.reviewStatus) }}
                                </el-tag>
                            </li>
                        </ul>
                    </template>
                </el-table-column>
                <el-table-column label="活动日期" min-width="180">
                    <template slot-scope="scope">
                        <div v-for="activity in scope.row.activities" :key="activity.type">
                            {{ formatDate(activity.startDate) }} 至 {{ formatDate(activity.endDate) }}
                        </div>
                    </template>
                </el-table-column>
                <el-table-column label="录入信息" min-width="180">
                    <template slot-scope="scope">
                        <div>录入人：{{ scope.row.creator }}</div>
                        <div>录入时间：{{ formatDateTime(scope.row.createTime) }}</div>
                    </template>
                </el-table-column>
                <el-table-column label="操作" width="180">
                    <template slot-scope="scope">
                        <div class="table-operations">
                            <el-button
                                size="mini"
                                @click="viewReportDetail(scope.row)">查看</el-button>
                            <el-button
                                size="mini"
                                type="danger"
                                @click="cancelReport(scope.row)">撤销</el-button>
                        </div>
                    </template>
                </el-table-column>
            </el-table>

            <!-- 分页 -->
            <div style="margin-top: 20px; text-align: right;">
                <el-pagination @size-change="handleSizeChange" @current-change="handleCurrentChange" :current-page="pagination.currentPage" :page-sizes="[10, 20, 50, 100]" :page-size="pagination.pageSize" layout="total, sizes, prev, pager, next, jumper" :total="pagination.total">
                </el-pagination>
            </div>
        </div>

        <!-- 报备新增弹窗 -->
        <el-dialog title="新品产品报备" :visible.sync="dialogVisible" width="70%" :before-close="handleDialogClose">
            <div class="dialog-form">
                <!-- 选择商品区域 -->
                <div class="card">
                    <h2 class="card-title">选择商品</h2>
                    <div class="form-group">
                        <el-autocomplete v-model="productIdInput" :fetch-suggestions="queryProductIds" placeholder="请输入商品ID或名称" @select="handleProductSelect" :disabled="selectedProducts.length >= 3" style="width: 100%;">
                            <template slot="append">
                                <el-button @click="addProduct" :disabled="!productIdInput || selectedProducts.length >= 3">添加</el-button>
                            </template>
                            <template slot="suffix">
                                <i class="el-icon-search"></i>
                            </template>
                        </el-autocomplete>
                        <div class="form-tip" v-if="selectedProducts.length >= 3">
                            <i class="el-icon-warning-outline"></i> 最多选择3个商品
                        </div>
                    </div>

                    <div v-if="selectedProducts.length > 0">
                        <div class="product-card" v-for="(product, index) in selectedProducts" :key="product.id">
                            <i class="el-icon-close remove-btn" @click="removeProduct(index)"></i>
                            <div class="product-info">
                                <img :src="product.image" :alt="product.name" class="product-image">
                                <div class="product-details">
                                    <h4>{{ product.name }}</h4>
                                    <div class="product-meta">
                                        <div class="product-meta-item">
                                            <span class="meta-label">商品ID:</span>
                                            <span>{{ product.id }}</span>
                                        </div>
                                        <div class="product-meta-item">
                                            <span class="meta-label">商品代码:</span>
                                            <span>{{ product.code }}</span>
                                        </div>
                                        <div class="product-meta-item">
                                            <span class="meta-label">店铺名:</span>
                                            <span>{{ product.shop }}</span>
                                        </div>
                                        <div class="product-meta-item">
                                            <span class="meta-label">平台:</span>
                                            <span>{{ product.platform }}</span>
                                        </div>
                                        <div class="product-meta-item">
                                            <span class="meta-label">团队:</span>
                                            <span>{{ product.team }}</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div v-else>
                        <el-empty description="暂未选择商品"></el-empty>
                    </div>
                </div>

                <!-- 活动报备区域 -->
                <div class="card">
                    <h2 class="card-title">活动报备</h2>
                    <div class="form-group">
                        <el-checkbox-group v-model="selectedActivities">
                            <div style="margin-bottom: 15px;" v-for="activity in activities" :key="activity.type">
                                <el-checkbox :label="activity.type">{{ activity.name }}</el-checkbox>
                                <div class="activity-params" v-if="selectedActivities.includes(activity.type)">
                                    <el-row :gutter="20" style="margin-bottom: 15px;">
                                        <el-col :span="8">
                                            <el-form-item label="生效日期">
                                                <el-date-picker v-model="activity.startDate" type="date" placeholder="选择生效日期" :disabled="activity.type === 'promo' && isAutomaticDateCalculation" style="width: 100%;">
                                                </el-date-picker>
                                                <div class="form-tip" v-if="activity.type === 'promo' && isAutomaticDateCalculation">
                                                    <i class="el-icon-info"></i> 系统根据规则自动计算
                                                </div>
                                            </el-form-item>
                                        </el-col>
                                        <el-col :span="8">
                                            <el-form-item label="结束日期">
                                                <el-date-picker v-model="activity.endDate" type="date" placeholder="选择结束日期" :disabled="activity.type === 'promo' && isAutomaticDateCalculation" style="width: 100%;">
                                                </el-date-picker>
                                                <div class="form-tip" v-if="activity.type === 'promo' && isAutomaticDateCalculation">
                                                    <i class="el-icon-info"></i> 系统根据规则自动计算
                                                </div>
                                            </el-form-item>
                                        </el-col>
                                        <el-col :span="8" v-if="activity.type === 'promo'">
                                            <el-form-item label="手动设置日期">
                                                <el-switch v-model="manualDateSetting" @change="toggleDateCalculation"></el-switch>
                                            </el-form-item>
                                        </el-col>
                                    </el-row>

                                    <!-- 录入信息 -->
                                    <el-row :gutter="20">
                                        <el-col :span="8">
                                            <el-form-item label="录入人员">
                                                <el-input v-model="formData.creator" placeholder="请输入录入人员" disabled></el-input>
                                            </el-form-item>
                                        </el-col>
                                        <el-col :span="8">
                                            <el-form-item label="录入时间">
                                                <el-input v-model="formData.createTimeDisplay" placeholder="系统自动生成" disabled></el-input>
                                            </el-form-item>
                                        </el-col>
                                    </el-row>

                                    <!-- 活动达标条件 -->
                                    <div style="margin-top: 15px;">
                                        <div class="form-title">达标条件</div>
                                        <div v-if="activity.type === 'promo'">
                                            <div class="info-text">
                                                <i class="el-icon-info"></i> 第一个月广告支出需>300元才能获得活动奖励
                                            </div>
                                        </div>
                                    </div>

                                    <!-- 审核状态 -->
                                    <div v-if="activity.requireReview" style="margin-top: 15px;">
                                        <div class="form-title">审核状态</div>
                                        <span class="status-tag status-pending">待审核</span>
                                        <div class="info-text" style="margin-top: 5px;">
                                            <i class="el-icon-info"></i> 该活动需要管理员审核后才能生效
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </el-checkbox-group>
                        <div class="error-tip" v-if="!selectedActivities.length && showValidationErrors">
                            <i class="el-icon-warning"></i> 请至少选择一种活动类型
                        </div>
                    </div>
                </div>
            </div>
            <span slot="footer" class="dialog-footer">
                <el-button @click="resetForm">重置</el-button>
                <el-button type="primary" @click="submitForm">提交报备</el-button>
            </span>
        </el-dialog>

        <!-- 查看详情弹窗 -->
        <el-dialog title="报备详情" :visible.sync="detailDialogVisible" width="60%">
            <div v-if="currentReport">
                <el-descriptions title="商品信息" :column="3" border>
                    <el-descriptions-item label="商品名称">{{ currentReport.product.name }}</el-descriptions-item>
                    <el-descriptions-item label="商品ID">{{ currentReport.product.id }}</el-descriptions-item>
                    <el-descriptions-item label="商品代码">{{ currentReport.product.code }}</el-descriptions-item>
                    <el-descriptions-item label="店铺名称">{{ currentReport.product.shop }}</el-descriptions-item>
                    <el-descriptions-item label="平台">{{ currentReport.product.platform }}</el-descriptions-item>
                    <el-descriptions-item label="所属团队">{{ currentReport.product.team }}</el-descriptions-item>
                </el-descriptions>

                <el-divider></el-divider>

                <el-descriptions title="报备信息" :column="3" border>
                    <el-descriptions-item label="报备ID">{{ currentReport.reportId }}</el-descriptions-item>
                    <el-descriptions-item label="录入人员">{{ currentReport.creator }}</el-descriptions-item>
                    <el-descriptions-item label="录入时间">{{ formatDateTime(currentReport.createTime) }}</el-descriptions-item>
                </el-descriptions>

                <el-divider></el-divider>

                <el-descriptions title="活动信息" :column="1" border>
                    <el-descriptions-item v-for="activity in currentReport.activities" :key="activity.type" :label="activity.name">
                        <div style="margin-bottom: 5px;">生效时间: {{ formatDate(activity.startDate) }} 至 {{ formatDate(activity.endDate) }}</div>
                        <div v-if="activity.requireReview" style="margin-bottom: 5px;">
                            审核状态:
                            <el-tag size="mini" :type="getStatusType(activity.reviewStatus)">
                                {{ getStatusText(activity.reviewStatus) }}
                            </el-tag>
                        </div>
                    </el-descriptions-item>
                </el-descriptions>
            </div>
            <span slot="footer" class="dialog-footer">
                <el-button @click="detailDialogVisible = false">关闭</el-button>
                <el-button type="primary" v-if="canCancel(currentReport)" @click="handleCancel">撤销报备</el-button>
            </span>
        </el-dialog>
    </div>

    <script src="https://unpkg.com/vue@2.6.14/dist/vue.js"></script>
    <script src="https://unpkg.com/element-ui/lib/index.js"></script>
    <!-- 引入我们的独立JS文件 -->
    <script>
        new Vue({
            el: '#app',
            data() {
                return {
                    // 主页面数据
                    searchForm: {
                        productId: '',
                        dateRange: [],
                        activityType: '',
                        reviewStatus: ''
                    },
                    tableLoading: false,
                    reportRecords: [],
                    pagination: {
                        currentPage: 1,
                        pageSize: 10,
                        total: 0
                    },

                    // 弹窗数据
                    dialogVisible: false,
                    detailDialogVisible: false,
                    currentReport: null,

                    // 报备表单数据
                    productIdInput: '',
                    selectedProducts: [],
                    selectedActivities: [],
                    isAutomaticDateCalculation: true,
                    manualDateSetting: false,
                    showValidationErrors: false,
                    formData: {
                        creator: '张小明', // 默认为当前登录用户
                        createTime: null,
                        createTimeDisplay: '系统自动生成'
                    },
                    activities: [{
                        type: 'promo',
                        name: '新品主推让利',
                        requireReview: false,
                        startDate: null,
                        endDate: null
                    }, {
                        type: 'race',
                        name: '新品赛马锁品',
                        requireReview: true,
                        startDate: null,
                        endDate: null,
                        reviewStatus: 'pending'
                    }, {
                        type: 'support',
                        name: '新品亏损扶持',
                        requireReview: true,
                        startDate: null,
                        endDate: null,
                        reviewStatus: 'pending'
                    }],
                    productDatabase: [{
                        id: 'P001',
                        code: 'SKU-10001',
                        name: '高级真丝连衣裙',
                        image: 'https://via.placeholder.com/120/FF5733/FFFFFF?text=连衣裙',
                        shop: '优雅时尚旗舰店',
                        platform: '天猫',
                        team: '女装事业部'
                    }, {
                        id: 'P002',
                        code: 'SKU-10002',
                        name: '男士商务休闲鞋',
                        image: 'https://via.placeholder.com/120/337DFF/FFFFFF?text=休闲鞋',
                        shop: '潮流男鞋专营店',
                        platform: '淘宝',
                        team: '男鞋事业部'
                    }, {
                        id: 'P003',
                        code: 'SKU-10003',
                        name: '智能蓝牙音箱',
                        image: 'https://via.placeholder.com/120/33FF57/333333?text=音箱',
                        shop: '科技数码专营店',
                        platform: '京东',
                        team: '数码事业部'
                    }, {
                        id: 'P004',
                        code: 'SKU-10004',
                        name: '婴儿纯棉连体衣',
                        image: 'https://via.placeholder.com/120/F3FF33/333333?text=婴儿服',
                        shop: '母婴生活馆',
                        platform: '拼多多',
                        team: '母婴事业部'
                    }, {
                        id: 'P005',
                        code: 'SKU-10005',
                        name: '多功能料理机',
                        image: 'https://via.placeholder.com/120/FF33F3/FFFFFF?text=料理机',
                        shop: '厨房电器旗舰店',
                        platform: '天猫',
                        team: '家电事业部'
                    }]
                }
            },
            mounted() {
                // 初始加载数据
                this.loadReportData();
                this.calculateDefaultDates();
            },
            methods: {
                // 主页面功能
                showAddReportDialog() {
                    this.dialogVisible = true;
                    // 设置当前时间
                    this.formData.createTime = new Date();
                    this.formData.createTimeDisplay = this.formatDateTime(this.formData.createTime);
                },
                handleSearch() {
                    this.loadReportData();
                },
                resetSearch() {
                    this.searchForm = {
                        productId: '',
                        dateRange: [],
                        activityType: '',
                        reviewStatus: ''
                    };
                    this.loadReportData();
                },
                handleSizeChange(val) {
                    this.pagination.pageSize = val;
                    this.loadReportData();
                },
                handleCurrentChange(val) {
                    this.pagination.currentPage = val;
                    this.loadReportData();
                },
                loadReportData() {
                    this.tableLoading = true;

                    // 模拟API请求
                    setTimeout(() => {
                        // 这里应该                // 模拟后端返回的数据
                        const mockData = [{
                            reportId: 'RPT20250501001',
                            product: this.productDatabase[0],
                            activities: [{
                                type: 'promo',
                                name: '新品主推让利',
                                startDate: new Date('2025-05-10'),
                                endDate: new Date('2025-05-31'),
                                requireReview: false
                            }, {
                                type: 'race',
                                name: '新品赛马锁品',
                                startDate: new Date('2025-05-10'),
                                endDate: new Date('2025-06-30'),
                                requireReview: true,
                                reviewStatus: 'approved'
                            }],
                            creator: '张小明',
                            createTime: new Date('2025-05-01 09:30:25')
                        }, {
                            reportId: 'RPT20250501002',
                            product: this.productDatabase[1],
                            activities: [{
                                type: 'support',
                                name: '新品亏损扶持',
                                startDate: new Date('2025-05-15'),
                                endDate: new Date('2025-06-15'),
                                requireReview: true,
                                reviewStatus: 'pending'
                            }],
                            creator: '李四',
                            createTime: new Date('2025-05-01 10:15:32')
                        }, {
                            reportId: 'RPT20250502001',
                            product: this.productDatabase[2],
                            activities: [{
                                type: 'promo',
                                name: '新品主推让利',
                                startDate: new Date('2025-05-03'),
                                endDate: new Date('2025-05-30'),
                                requireReview: false
                            }],
                            creator: '王五',
                            createTime: new Date('2025-05-02 14:22:45')
                        }, {
                            reportId: 'RPT20250503001',
                            product: this.productDatabase[3],
                            activities: [{
                                type: 'race',
                                name: '新品赛马锁品',
                                startDate: new Date('2025-05-05'),
                                endDate: new Date('2025-06-30'),
                                requireReview: true,
                                reviewStatus: 'rejected'
                            }, {
                                type: 'support',
                                name: '新品亏损扶持',
                                startDate: new Date('2025-05-05'),
                                endDate: new Date('2025-06-30'),
                                requireReview: true,
                                reviewStatus: 'pending'
                            }],
                            creator: '赵六',
                            createTime: new Date('2025-05-03 08:55:12')
                        }, {
                            reportId: 'RPT20250504001',
                            product: this.productDatabase[4],
                            activities: [{
                                type: 'promo',
                                name: '新品主推让利',
                                startDate: new Date('2025-05-05'),
                                endDate: new Date('2025-05-31'),
                                requireReview: false
                            }, {
                                type: 'race',
                                name: '新品赛马锁品',
                                startDate: new Date('2025-05-05'),
                                endDate: new Date('2025-06-30'),
                                requireReview: true,
                                reviewStatus: 'approved'
                            }],
                            creator: '张小明',
                            createTime: new Date('2025-05-04 16:30:40')
                        }];

                        // 过滤处理
                        let filteredData = mockData;

                        // 根据搜索条件过滤
                        if (this.searchForm.productId) {
                            filteredData = filteredData.filter(item =>
                                item.product.id.toLowerCase().includes(this.searchForm.productId.toLowerCase())
                            );
                        }

                        if (this.searchForm.dateRange && this.searchForm.dateRange.length === 2) {
                            const startDate = new Date(this.searchForm.dateRange[0]);
                            startDate.setHours(0, 0, 0, 0);

                            const endDate = new Date(this.searchForm.dateRange[1]);
                            endDate.setHours(23, 59, 59, 999);

                            filteredData = filteredData.filter(item => {
                                const createDate = new Date(item.createTime);
                                return createDate >= startDate && createDate <= endDate;
                            });
                        }

                        if (this.searchForm.activityType) {
                            filteredData = filteredData.filter(item =>
                                item.activities.some(activity => activity.type === this.searchForm.activityType)
                            );
                        }

                        if (this.searchForm.reviewStatus) {
                            filteredData = filteredData.filter(item =>
                                item.activities.some(activity =>
                                    activity.requireReview && activity.reviewStatus === this.searchForm.reviewStatus
                                )
                            );
                        }

                        // 计算分页
                        this.pagination.total = filteredData.length;
                        const start = (this.pagination.currentPage - 1) * this.pagination.pageSize;
                        const end = start + this.pagination.pageSize;
                        this.reportRecords = filteredData.slice(start, end);

                        this.tableLoading = false;
                    }, 500);
                },
                viewReportDetail(row) {
                    this.currentReport = JSON.parse(JSON.stringify(row)); // 深拷贝
                    this.detailDialogVisible = true;
                },
                cancelReport(row) {
                    this.$confirm('确认撤销该条报备信息吗?', '提示', {
                        confirmButtonText: '确定',
                        cancelButtonText: '取消',
                        type: 'warning'
                    }).then(() => {
                        // 模拟API请求
                        setTimeout(() => {
                            // 从列表中移除
                            const index = this.reportRecords.findIndex(item => item.reportId === row.reportId);
                            if (index !== -1) {
                                this.reportRecords.splice(index, 1);
                            }
                            this.$message.success('报备已成功撤销');
                        }, 500);
                    }).catch(() => {
                        // 取消操作
                    });
                },
                handleDialogClose(done) {
                    if (this.selectedProducts.length > 0 || this.selectedActivities.length > 0) {
                        this.$confirm('确定要关闭吗？未保存的数据将丢失', '提示', {
                            confirmButtonText: '确定',
                            cancelButtonText: '取消',
                            type: 'warning'
                        }).then(() => {
                            this.resetForm();
                            done();
                        }).catch(() => {
                            // 取消关闭
                        });
                    } else {
                        done();
                    }
                },
                canCancel(report) {
                    if (!report) return false;

                    // 判断是否有已审核通过的活动
                    const hasApproved = report.activities.some(activity =>
                        activity.requireReview && activity.reviewStatus === 'approved'
                    );

                    // 如果有已审核通过的活动，则不能撤销
                    return !hasApproved;
                },
                handleCancel() {
                    this.cancelReport(this.currentReport);
                    this.detailDialogVisible = false;
                },

                // 报备表单相关功能
                queryProductIds(queryString, cb) {
                    const results = queryString ?
                        this.productDatabase.filter(product => {
                            return product.id.toLowerCase().includes(queryString.toLowerCase()) ||
                                product.name.toLowerCase().includes(queryString.toLowerCase());
                        }) :
                        this.productDatabase;

                    // 过滤掉已选择的商品
                    const filteredResults = results.filter(product =>
                        !this.selectedProducts.some(selected => selected.id === product.id)
                    );

                    cb(filteredResults.map(product => ({
                        value: product.id,
                        label: `${product.id} - ${product.name}`
                    })));
                },
                handleProductSelect(item) {
                    this.productIdInput = item.value;
                    this.addProduct();
                },
                addProduct() {
                    if (!this.productIdInput || this.selectedProducts.length >= 3) return;

                    const product = this.productDatabase.find(p => p.id === this.productIdInput);
                    if (product && !this.selectedProducts.some(p => p.id === product.id)) {
                        this.selectedProducts.push(JSON.parse(JSON.stringify(product)));
                        this.productIdInput = '';

                        // 重新计算活动日期
                        this.calculateDefaultDates();
                    }
                },
                removeProduct(index) {
                    this.selectedProducts.splice(index, 1);

                    // 重新计算活动日期
                    this.calculateDefaultDates();
                },
                calculateDefaultDates() {
                    // 计算当前日期
                    const today = new Date();
                    const currentDay = today.getDate();
                    const currentMonth = today.getMonth();
                    const currentYear = today.getFullYear();

                    // 当月最后一天
                    const lastDayOfMonth = new Date(currentYear, currentMonth + 1, 0);

                    // 下月第一天
                    const firstDayOfNextMonth = new Date(currentYear, currentMonth + 1, 1);

                    // 下月最后一天
                    const lastDayOfNextMonth = new Date(currentYear, currentMonth + 2, 0);

                    // 当月剩余天数
                    const daysRemainingInMonth = lastDayOfMonth.getDate() - currentDay;

                    // 找到新品主推让利活动
                    const promoActivity = this.activities.find(a => a.type === 'promo');
                    if (promoActivity) {
                        // 判断当月剩余天数是否小于等于5天
                        if (daysRemainingInMonth <= 5) {
                            // 生效日期设为次月1号
                            promoActivity.startDate = firstDayOfNextMonth;
                        } else {
                            // 当天生效
                            promoActivity.startDate = new Date();
                        }

                        // 判断是否同时报备了"新品赛马锁品"
                        if (this.selectedActivities.includes('race')) {
                            // 结束日期为次月月底
                            promoActivity.endDate = lastDayOfNextMonth;
                        } else {
                            // 结束日期为当月月底
                            promoActivity.endDate = lastDayOfMonth;
                        }
                    }
                },
                toggleDateCalculation(value) {
                    this.isAutomaticDateCalculation = !value;
                    if (this.isAutomaticDateCalculation) {
                        this.calculateDefaultDates();
                    }
                },
                resetForm() {
                    this.selectedProducts = [];
                    this.selectedActivities = [];
                    this.productIdInput = '';
                    this.isAutomaticDateCalculation = true;
                    this.manualDateSetting = false;
                    this.showValidationErrors = false;

                    // 重置活动日期
                    this.activities.forEach(activity => {
                        activity.startDate = null;
                        activity.endDate = null;
                    });
                },
                validateForm() {
                    let isValid = true;

                    // 检查是否选择了商品
                    if (this.selectedProducts.length === 0) {
                        this.$message.error('请至少选择一个商品');
                        isValid = false;
                    }

                    // 检查是否选择了活动类型
                    if (this.selectedActivities.length === 0) {
                        this.showValidationErrors = true;
                        isValid = false;
                    }

                    // 检查是否设置了活动日期
                    for (const activityType of this.selectedActivities) {
                        const activity = this.activities.find(a => a.type === activityType);
                        if (!activity.startDate || !activity.endDate) {
                            this.$message.error(`请为${activity.name}设置完整的活动日期`);
                            isValid = false;
                        }
                    }

                    return isValid;
                },
                submitForm() {
                    if (!this.validateForm()) return;

                    // 构建提交数据
                    const formData = {
                        reportId: 'RPT' + this.formatDate(new Date(), 'YYYYMMDD') + Math.floor(Math.random() * 900 + 100),
                        products: this.selectedProducts.map(p => p.id),
                        activities: this.selectedActivities.map(type => {
                            const activity = this.activities.find(a => a.type === type);
                            return {
                                type: activity.type,
                                name: activity.name,
                                startDate: activity.startDate,
                                endDate: activity.endDate,
                                requireReview: activity.requireReview,
                                reviewStatus: activity.requireReview ? 'pending' : null
                            };
                        }),
                        creator: this.formData.creator,
                        createTime: this.formData.createTime
                    };

                    // 在实际项目中，这里应该发送API请求
                    console.log('提交的数据:', formData);

                    this.$message.success('活动报备提交成功！');

                    // 如果有需要审核的活动，显示提示
                    const hasReviewActivities = this.selectedActivities.some(type =>
                        this.activities.find(a => a.type === type).requireReview
                    );

                    if (hasReviewActivities) {
                        this.$notify({
                            title: '提交成功',
                            message: '您的部分活动需要管理员审核后才能生效，请关注审核状态',
                            type: 'warning',
                            duration: 5000
                        });
                    }

                    // 提交后关闭弹窗并重置表单
                    this.dialogVisible = false;
                    this.resetForm();

                    // 重新加载列表数据
                    this.loadReportData();
                },

                // 工具函数
                formatDate(date, format) {
                    if (!date) return '—';

                    if (format === 'YYYYMMDD') {
                        const year = date.getFullYear();
                        const month = String(date.getMonth() + 1).padStart(2, '0');
                        const day = String(date.getDate()).padStart(2, '0');
                        return `${year}${month}${day}`;
                    }

                    const year = date.getFullYear();
                    const month = String(date.getMonth() + 1).padStart(2, '0');
                    const day = String(date.getDate()).padStart(2, '0');
                    return `${year}-${month}-${day}`;
                },
                formatDateTime(date) {
                    if (!date) return '—';

                    const year = date.getFullYear();
                    const month = String(date.getMonth() + 1).padStart(2, '0');
                    const day = String(date.getDate()).padStart(2, '0');
                    const hours = String(date.getHours()).padStart(2, '0');
                    const minutes = String(date.getMinutes()).padStart(2, '0');
                    const seconds = String(date.getSeconds()).padStart(2, '0');

                    return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
                },
                getStatusType(status) {
                    switch (status) {
                        case 'pending':
                            return 'warning';
                        case 'approved':
                            return 'success';
                        case 'rejected':
                            return 'danger';
                        default:
                            return 'info';
                    }
                },
                getStatusText(status) {
                    switch (status) {
                        case 'pending':
                            return '待审核';
                        case 'approved':
                            return '已通过';
                        case 'rejected':
                            return '已拒绝';
                        default:
                            return '未知状态';
                    }
                }
            },
            watch: {
                selectedActivities: {
                    handler() {
                        this.calculateDefaultDates();
                        this.showValidationErrors = false;
                    },
                    deep: true
                }
            }
        });
    </script>
</body>

</html>