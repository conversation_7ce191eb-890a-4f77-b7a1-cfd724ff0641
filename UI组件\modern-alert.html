<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>现代风格区间冲突提示</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        :root {
            --primary: #2563eb;
            --primary-light: #eff6ff;
            --danger: #dc2626;
            --danger-light: #fef2f2;
            --success: #16a34a;
            --success-light: #f0fdf4;
            --warning: #ca8a04;
            --warning-light: #fefce8;
            --text-primary: #1e293b;
            --text-secondary: #64748b;
            --border-light: #e2e8f0;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', system-ui, sans-serif;
        }

        .overlay {
            position: fixed;
            inset: 0;
            background-color: rgba(15, 23, 42, 0.3);
            backdrop-filter: blur(8px);
            display: grid;
            place-items: center;
            padding: 1rem;
            z-index: 1000;
            opacity: 0;
            animation: fadeIn 0.3s ease forwards;
        }

        .alert-container {
            width: min(560px, 100%);
            background: white;
            border-radius: 16px;
            box-shadow: 
                0 4px 6px -1px rgba(0, 0, 0, 0.1),
                0 2px 4px -1px rgba(0, 0, 0, 0.06),
                0 20px 25px -5px rgba(0, 0, 0, 0.1),
                0 10px 10px -5px rgba(0, 0, 0, 0.04);
            transform: scale(0.95);
            opacity: 0;
            animation: slideIn 0.4s cubic-bezier(0.16, 1, 0.3, 1) forwards;
        }

        .alert-header {
            padding: 1.5rem;
            display: flex;
            align-items: center;
            gap: 1rem;
            border-bottom: 1px solid var(--border-light);
        }

        .alert-icon {
            width: 40px;
            height: 40px;
            background: var(--danger-light);
            border-radius: 12px;
            display: grid;
            place-items: center;
        }

        .alert-icon i {
            color: var(--danger);
            font-size: 1.25rem;
        }

        .alert-header h3 {
            color: var(--text-primary);
            font-size: 1.25rem;
            font-weight: 600;
            letter-spacing: -0.025em;
        }

        .alert-content {
            padding: 1.5rem;
        }

        .section-title {
            color: var(--text-primary);
            font-size: 0.975rem;
            font-weight: 500;
            margin-bottom: 1rem;
        }

        .range-box {
            background: #f8fafc;
            border-radius: 12px;
            padding: 1.25rem;
            border: 1px solid var(--border-light);
        }

        .range-item {
            display: flex;
            align-items: center;
            padding: 1rem;
            background: white;
            border-radius: 10px;
            margin-bottom: 0.75rem;
            border: 1px solid var(--border-light);
            transition: all 0.2s ease;
        }

        .range-item:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
        }

        .range-label {
            flex: 0 0 140px;
            font-weight: 500;
            color: var(--text-primary);
            font-size: 0.925rem;
        }

        .range-value {
            flex: 1;
            color: var(--text-secondary);
            padding: 0 1rem;
            font-size: 0.925rem;
        }

        .range-status {
            padding: 0.375rem 0.75rem;
            border-radius: 8px;
            font-size: 0.875rem;
            font-weight: 500;
        }

        .status-existing {
            background: var(--success-light);
            color: var(--success);
        }

        .status-new {
            background: var(--danger-light);
            color: var(--danger);
        }

        .conflict-warning {
            margin-top: 1.5rem;
            background: var(--warning-light);
            border-radius: 12px;
            padding: 1.25rem;
        }

        .warning-title {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            color: var(--warning);
            font-weight: 600;
            font-size: 0.975rem;
            margin-bottom: 0.75rem;
        }

        .warning-content {
            color: var(--text-secondary);
            font-size: 0.925rem;
            line-height: 1.6;
        }

        .suggestion {
            margin-top: 1rem;
            padding-left: 1.5rem;
            position: relative;
            color: var(--text-secondary);
            font-size: 0.875rem;
        }

        .suggestion::before {
            content: '💡';
            position: absolute;
            left: 0;
        }

        @keyframes fadeIn {
            to {
                opacity: 1;
            }
        }

        @keyframes slideIn {
            to {
                transform: scale(1);
                opacity: 1;
            }
        }

        @media (max-width: 640px) {
            .alert-container {
                border-radius: 12px;
            }

            .range-item {
                flex-direction: column;
                align-items: flex-start;
                gap: 0.5rem;
            }

            .range-label {
                flex: none;
            }

            .range-value {
                padding: 0;
            }

            .range-status {
                align-self: flex-start;
            }
        }
    </style>
</head>

<body>
    <div class="overlay">
        <div class="alert-container">
            <div class="alert-header">
                <div class="alert-icon">
                    <i class="fas fa-exclamation"></i>
                </div>
                <h3>利润区间设置冲突</h3>
            </div>
            <div class="alert-content">
                <div class="section-title">利润起始区间信息</div>
                <div class="range-box">
                    <div class="range-item">
                        <span class="range-label">已有利润起始区间</span>
                        <span class="range-value">￥0 - ￥3,000</span>
                        <span class="range-status status-existing">已存在</span>
                    </div>
                    <div class="range-item">
                        <span class="range-label">新增利润起始区间</span>
                        <span class="range-value">￥1,000 - ￥1,500</span>
                        <span class="range-status status-new">待添加</span>
                    </div>
                </div>

                <div class="conflict-warning">
                    <div class="warning-title">
                        <i class="fas fa-exclamation-circle"></i>
                        <span>区间冲突说明</span>
                    </div>
                    <div class="warning-content">
                        新增区间（￥1,000 - ￥1,500）与已有区间（￥0 - ￥3,000）存在重叠，无法直接添加。
                        <div class="suggestion">
                            建议：调整新增利润起始区间的范围，确保与现有利润区间不重叠。
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</body>

</html>
