<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>商品活动报备管理 - 电商管理系统</title>
    <link rel="stylesheet" href="https://unpkg.com/element-ui/lib/theme-chalk/index.css">
    <link rel="icon" href="https://panhan.xin/assets/favicon-DfI5CYqk.ico">
    <style>
        body {
            font-family: "Helvetica Neue", Helvetica, "PingFang SC", "Hiragino Sans GB", "Microsoft YaHei", sans-serif;
            margin: 0;
            padding: 0;
            background-color: #f5f7fa;
            color: #333;
        }
        
        .app-container {
            padding: 20px;
        }
        
        .page-header {
            background-color: #fff;
            padding: 16px 20px;
            box-shadow: 0 1px 4px rgba(0, 0, 0, 0.1);
            margin-bottom: 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .page-title {
            margin: 0;
            font-size: 20px;
            font-weight: 500;
        }
        
        .action-buttons {
            display: flex;
            gap: 15px;
        }
        
        .card {
            background-color: #fff;
            border-radius: 4px;
            box-shadow: 0 1px 4px rgba(0, 0, 0, 0.1);
            margin-bottom: 20px;
            padding: 20px;
        }
        
        .card-title {
            font-size: 16px;
            font-weight: 500;
            margin: 0 0 20px;
            padding-bottom: 10px;
            border-bottom: 1px solid #ebeef5;
            color: #303133;
        }
        
        .search-area {
            margin-bottom: 20px;
            background: #fff;
            padding: 20px;
            border-radius: 4px;
        }
        
        .form-group {
            margin-bottom: 20px;
        }
        
        .product-card {
            /* 用于弹窗内显示已选商品或基础商品 */
            border: 1px solid #ebeef5;
            border-radius: 4px;
            padding: 15px;
            margin-bottom: 15px;
            position: relative;
            display: flex;
            align-items: flex-start;
        }
        
        .product-card .remove-btn {
            /* 用于移除“待添加ID”场景 或 清空基础商品 */
            position: absolute;
            top: 10px;
            right: 10px;
            font-size: 18px;
            color: #f56c6c;
            cursor: pointer;
        }
        
        .product-image-display-container {
            /* 弹窗内基础商品图片 */
            width: 100px;
            height: 100px;
            margin-right: 20px;
            border-radius: 4px;
            overflow: hidden;
            background-color: #f0f2f5;
            flex-shrink: 0;
        }
        
        .product-image-display-container .el-image {
            width: 100%;
            height: 100%;
        }
        
        .image-slot-display {
            /* el-image error slot style */
            display: flex;
            justify-content: center;
            align-items: center;
            width: 100%;
            height: 100%;
            background: #f5f7fa;
            color: #909399;
            font-size: 12px;
        }
        
        .table-product-image-container {
            /* 表格内商品图片 */
            width: 60px;
            height: 60px;
            margin-right: 12px;
            border-radius: 3px;
            overflow: hidden;
            background-color: #f0f2f5;
            flex-shrink: 0;
        }
        
        .table-product-image-container .el-image {
            width: 100%;
            height: 100%;
        }
        
        .product-details-shared {
            /* 弹窗内基础商品信息 */
            flex: 1;
        }
        
        .product-details-shared h4 {
            margin: 0 0 8px;
            font-size: 16px;
            line-height: 1.4;
        }
        
        .product-details-shared p {
            margin: 4px 0;
            font-size: 13px;
            color: #606266;
            line-height: 1.5;
        }
        
        .product-details-shared .meta-label {
            color: #909399;
        }
        
        .activity-params {
            padding-left: 20px;
            margin-top: 10px;
            border-left: 2px solid #ebeef5;
        }
        
        .info-text {
            font-size: 13px;
            color: #909399;
        }
        
        .status-tag {
            padding: 2px 8px;
            border-radius: 2px;
            font-size: 12px;
        }
        
        .status-pending {
            background-color: #e6a23c;
            color: #fff;
        }
        
        .status-approved {
            background-color: #67c23a;
            color: #fff;
        }
        
        .status-rejected {
            background-color: #f56c6c;
            color: #fff;
        }
        
        .form-tip {
            font-size: 13px;
            color: #909399;
            margin-top: 5px;
        }
        
        .error-tip {
            color: #f56c6c;
        }
        
        .table-operations {
            display: flex;
            /* 确保按钮在flex容器中 */
            align-items: center;
            gap: 10px;
            /* 按钮之间的间距 */
        }
        
        .table-operations button {
            padding: 4px 8px;
            font-size: 12px;
        }
        
        .dialog-form .el-form-item {
            margin-bottom: 22px;
        }
        
        .activity-list {
            margin-top: 8px;
            padding-left: 0;
        }
        
        .activity-list-item {
            margin-bottom: 5px;
            list-style-type: none;
            display: flex;
            align-items: center;
        }
        
        .activity-icon {
            margin-right: 5px;
            font-size: 14px;
        }
        
        .small-tag {
            height: 20px;
            line-height: 18px;
            font-size: 10px;
            padding: 0 5px;
            margin-left: 5px;
        }
        
        .table-product-info-layout {
            display: flex;
            align-items: flex-start;
        }
        
        .table-product-text-details {
            flex: 1;
        }
        
        .table-product-name {
            font-weight: 500;
            color: #303133;
            margin-bottom: 4px;
            font-size: 14px;
        }
        
        .table-product-sub-info {
            font-size: 12px;
            color: #606266;
            line-height: 1.5;
        }
        
        .table-product-sub-info .meta-label {
            color: #909399;
        }
        
        .product-id-list {
            margin-top: 8px;
            padding-left: 0;
            list-style: none;
        }
        
        .product-id-list li {
            background-color: #f0f2f5;
            padding: 3px 8px;
            border-radius: 3px;
            margin-bottom: 4px;
            font-size: 12px;
            color: #303133;
            display: inline-block;
            margin-right: 5px;
        }
        
        .edit-dialog-section-title {
            font-size: 15px;
            font-weight: bold;
            color: #303133;
            margin-bottom: 10px;
            padding-bottom: 5px;
            border-bottom: 1px solid #eee;
        }
        
        .associated-ids-section .el-tag {
            margin-right: 8px;
            margin-bottom: 8px;
        }
        
        .associated-ids-section .button-new-tag {
            margin-left: 10px;
            height: 32px;
            line-height: 30px;
            padding-top: 0;
            padding-bottom: 0;
        }
        
        .associated-ids-section .input-new-tag {
            width: 120px;
            margin-left: 10px;
            vertical-align: bottom;
        }
    </style>
</head>

<body>
    <div id="app" class="app-container">
        <div class="page-header">
            <h1 class="page-title">商品活动报备管理</h1>
            <div class="action-buttons">
                <el-button type="primary" icon="el-icon-plus" size="small" @click="showAddReportDialog">新品产品报备</el-button>
                <el-button icon="el-icon-download" size="small">导出数据</el-button>
            </div>
        </div>

        <div class="search-area">
            <el-form :inline="true" :model="searchForm" class="demo-form-inline">
                <el-form-item label="商品名称/ID/代码">
                    <el-input v-model="searchForm.productKeyword" placeholder="商品名称/ID/代码" clearable></el-input>
                </el-form-item>
                <el-form-item label="报备日期">
                    <el-date-picker v-model="searchForm.dateRange" type="daterange" range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期"></el-date-picker>
                </el-form-item>
                <el-form-item label="活动类型">
                    <el-select v-model="searchForm.activityType" placeholder="请选择活动类型" clearable>
                        <el-option label="新品主推让利" value="promo"></el-option>
                        <el-option label="新品赛马锁品" value="race"></el-option>
                        <el-option label="新品亏损扶持" value="support"></el-option>
                    </el-select>
                </el-form-item>
                <el-form-item label="审核状态">
                    <el-select v-model="searchForm.reviewStatus" placeholder="请选择审核状态" clearable>
                        <el-option label="全部" value=""></el-option>
                        <el-option label="待审核" value="pending"></el-option>
                        <el-option label="已通过" value="approved"></el-option>
                        <el-option label="已拒绝" value="rejected"></el-option>
                    </el-select>
                </el-form-item>
                <el-form-item>
                    <el-button type="primary" @click="handleSearch">查询</el-button>
                    <el-button @click="resetSearch">重置</el-button>
                </el-form-item>
            </el-form>
        </div>

        <div class="card">
            <el-table :data="reportRecords" style="width: 100%" border v-loading="tableLoading">
                <el-table-column prop="reportId" label="报备ID" width="120" fixed="left"></el-table-column>
                <el-table-column label="商品信息" min-width="350"> <template slot-scope="scope">
                        <div class="table-product-info-layout">
                            <div class="table-product-image-container">
                                <el-image :src="scope.row.productImage" fit="cover" :preview-src-list="[scope.row.productImage]">
                                    <div slot="error" class="image-slot-display">无图</div>
                                </el-image>
                            </div>
                            <div class="table-product-text-details">
                                <div class="table-product-name">{{ scope.row.productName }}</div>
                                <div class="table-product-sub-info">
                                    <span class="meta-label">代码:</span> {{ scope.row.productCode }} <br>
                                    <span class="meta-label">店铺:</span> {{ scope.row.shop }} | 
                                    <span class="meta-label">平台:</span> {{ scope.row.platform }} | 
                                    <span class="meta-label">团队:</span> {{ scope.row.team }}
                                </div>
                                <div v-if="scope.row.productIDs && scope.row.productIDs.length" class="product-id-list">
                                    <span class="meta-label" style="font-weight:500; font-size:12px;">关联商品ID:</span>
                                    <li v-for="id in scope.row.productIDs" :key="id">{{ id }}</li>
                                </div>
                            </div>
                        </div>
                    </template>
                </el-table-column>
                <el-table-column label="活动信息" min-width="200">
                    <template slot-scope="scope">
                        <ul class="activity-list">
                            <li v-for="activity in scope.row.activities" :key="activity.type" class="activity-list-item">
                                <i class="el-icon-promotion activity-icon"></i>
                                {{ activity.name }}
                                <el-tag v-if="activity.requireReview" size="mini" class="small-tag" :type="getStatusType(activity.reviewStatus)">
                                    {{ getStatusText(activity.reviewStatus) }}
                                </el-tag>
                            </li>
                        </ul>
                    </template>
                </el-table-column>
                <el-table-column label="活动日期" min-width="180">
                    <template slot-scope="scope">
                        <div v-for="activity in scope.row.activities" :key="activity.type" style="margin-bottom:3px; font-size: 13px;">
                            {{ formatDate(activity.startDate) }} 至 {{ formatDate(activity.endDate) }}
                        </div>
                    </template>
                </el-table-column>
                <el-table-column label="录入信息" min-width="180">
                    <template slot-scope="scope">
                        <div style="font-size:13px;">录入人：{{ scope.row.creator }}</div>
                        <div style="font-size:13px;">录入时间：{{ formatDateTime(scope.row.createTime) }}</div>
                    </template>
                </el-table-column>
                <el-table-column label="操作" width="190" fixed="right"> <template slot-scope="scope">
                        <div class="table-operations">
                            <el-button size="mini" @click="viewReportDetail(scope.row)">查看</el-button>
                            <el-button size="mini" type="primary" @click="handleEditReport(scope.row)">编辑/追加ID</el-button>
                        </div>
                    </template>
                </el-table-column>
            </el-table>
            <div style="margin-top: 20px; text-align: right;">
                <el-pagination @size-change="handleSizeChange" @current-change="handleCurrentChange" :current-page="pagination.currentPage" :page-sizes="[10, 20, 50, 100]" :page-size="pagination.pageSize" layout="total, sizes, prev, pager, next, jumper" :total="pagination.total"></el-pagination>
            </div>
        </div>

        <el-dialog title="新品产品报备" :visible.sync="dialogVisible" width="75%" :before-close="handleDialogClose" top="5vh">
            <div class="dialog-form">
                <div class="card">
                    <h2 class="card-title">选择基础商品信息</h2>
                    <div class="form-group">
                        <el-autocomplete v-model="productIdInput" :fetch-suggestions="queryBaseProduct" placeholder="搜索选择基础商品 (ID/名称/代码)" @select="handleBaseProductSelect" style="width: 100%;" :disabled="!!formData.productName">
                            <template slot="suffix"> <i class="el-icon-search"></i> </template>
                        </el-autocomplete>
                        <div class="form-tip" v-if="formData.productName">基础商品信息已选定。如需更改，请先
                            <el-link type="primary" @click="clearBaseProduct" :underline="false" style="font-size:13px;">清空</el-link>。</div>
                    </div>

                    <div v-if="formData.productName" class="product-card">
                        <i class="el-icon-refresh-left remove-btn" @click="clearBaseProduct" title="清空并重新选择基础商品"></i>
                        <div class="product-image-display-container">
                            <el-image :src="formData.productImage" fit="cover" :preview-src-list="[formData.productImage]">
                                <div slot="error" class="image-slot-display">无图</div>
                            </el-image>
                        </div>
                        <div class="product-details-shared">
                            <h4>{{ formData.productName }}</h4>
                            <p><span class="meta-label">代码:</span> {{ formData.productCode }}</p>
                            <p><span class="meta-label">店铺:</span> {{ formData.shop }}</p>
                            <p><span class="meta-label">平台:</span> {{ formData.platform }}</p>
                            <p><span class="meta-label">团队:</span> {{ formData.team }}</p>
                        </div>
                    </div>
                    <el-empty v-else description="请先选择一个基础商品"></el-empty>
                </div>

                <div class="card" v-if="formData.productName">
                    <h2 class="card-title">关联商品ID (最多3个，包含基础商品ID)</h2>
                    <div class="associated-ids-section">
                        <el-tag v-for="(tag, index) in formData.productIDs" :key="tag + index" :closable="index > 0" :disable-transitions="false" @close="handleRemoveProductID(index, 'add')">
                            {{tag}}
                        </el-tag>
                        <el-input class="input-new-tag" v-if="inputProductIDVisible.add && formData.productIDs.length < 3" v-model="newProductID.add" ref="saveTagInputAdd" size="small" placeholder="输入ID后回车" @keyup.enter.native="handleProductIDInputConfirm('add')" @blur="handleProductIDInputConfirm('add')">
                        </el-input>
                        <el-button v-else-if="formData.productIDs.length < 3" class="button-new-tag" size="small" @click="showProductIDInput('add')">+ 添加ID</el-button>
                        <div class="form-tip" v-if="formData.productIDs.length >= 3 && !inputProductIDVisible.add">已达到3个ID上限。</div>
                    </div>
                </div>

                <div class="card">
                    <h2 class="card-title">活动报备</h2>
                    <div class="form-group">
                        <el-checkbox-group v-model="selectedActivities">
                            <div style="margin-bottom: 15px;" v-for="activity_template in activities" :key="activity_template.type">
                                <el-checkbox :label="activity_template.type">{{ activity_template.name }}</el-checkbox>
                                <div class="activity-params" v-if="selectedActivities.includes(activity_template.type)">
                                    <el-row :gutter="20" style="margin-bottom: 15px;">
                                        <el-col :span="8">
                                            <el-form-item label="生效日期">
                                                <el-date-picker v-model="activities.find(a=>a.type===activity_template.type).startDate" type="date" placeholder="选择生效日期" :disabled="activity_template.type === 'promo' && isAutomaticDateCalculation" style="width: 100%;"></el-date-picker>
                                                <div class="form-tip" v-if="activity_template.type === 'promo' && isAutomaticDateCalculation"><i class="el-icon-info"></i> 系统自动计算</div>
                                            </el-form-item>
                                        </el-col>
                                        <el-col :span="8">
                                            <el-form-item label="结束日期">
                                                <el-date-picker v-model="activities.find(a=>a.type===activity_template.type).endDate" type="date" placeholder="选择结束日期" :disabled="activity_template.type === 'promo' && isAutomaticDateCalculation" style="width: 100%;"></el-date-picker>
                                                <div class="form-tip" v-if="activity_template.type === 'promo' && isAutomaticDateCalculation"><i class="el-icon-info"></i> 系统自动计算</div>
                                            </el-form-item>
                                        </el-col>
                                        <el-col :span="8" v-if="activity_template.type === 'promo'">
                                            <el-form-item label="手动设置日期">
                                                <el-switch v-model="manualDateSetting" @change="toggleDateCalculation"></el-switch>
                                            </el-form-item>
                                        </el-col>
                                    </el-row>
                                    <el-row :gutter="20">
                                        <el-col :span="8">
                                            <el-form-item label="录入人员">
                                                <el-input v-model="formData.creator" disabled></el-input>
                                            </el-form-item>
                                        </el-col>
                                        <el-col :span="8">
                                            <el-form-item label="录入时间">
                                                <el-input v-model="formData.createTimeDisplay" disabled></el-input>
                                            </el-form-item>
                                        </el-col>
                                    </el-row>
                                    <div style="margin-top: 15px;" v-if="activity_template.type === 'promo'">
                                        <div class="form-title">达标条件</div>
                                        <div class="info-text"><i class="el-icon-info"></i> 第一个月广告支出需>300元才能获得活动奖励</div>
                                    </div>
                                    <div v-if="activity_template.requireReview" style="margin-top: 15px;">
                                        <div class="form-title">审核状态</div><span class="status-tag status-pending">待审核</span>
                                        <div class="info-text" style="margin-top: 5px;"><i class="el-icon-info"></i> 该活动需管理员审核后生效</div>
                                    </div>
                                </div>
                            </div>
                        </el-checkbox-group>
                        <div class="error-tip" v-if="!selectedActivities.length && showValidationErrors"><i class="el-icon-warning"></i> 请至少选择一种活动类型</div>
                    </div>
                </div>
            </div>
            <span slot="footer" class="dialog-footer">
                <el-button @click="resetForm">重置</el-button>
                <el-button type="primary" @click="submitForm">提交报备</el-button>
            </span>
        </el-dialog>

        <el-dialog :title="'编辑报备 (ID: ' + editForm.reportId + ') - 追加关联ID'" :visible.sync="editDialogVisible" width="75%" :before-close="handleEditDialogClose" top="5vh">
            <div class="dialog-form">
                <div class="card">
                    <h2 class="card-title">基础商品信息 (不可更改)</h2>
                    <div class="product-card" style="background-color: #f9f9f9;">
                        <div class="product-image-display-container">
                            <el-image :src="editForm.productImage" fit="cover" :preview-src-list="[editForm.productImage]">
                                <div slot="error" class="image-slot-display">无图</div>
                            </el-image>
                        </div>
                        <div class="product-details-shared">
                            <h4>{{ editForm.productName }}</h4>
                            <p><span class="meta-label">代码:</span> {{ editForm.productCode }}</p>
                            <p><span class="meta-label">店铺:</span> {{ editForm.shop }}</p>
                            <p><span class="meta-label">平台:</span> {{ editForm.platform }}</p>
                            <p><span class="meta-label">团队:</span> {{ editForm.team }}</p>
                        </div>
                    </div>
                </div>

                <div class="card">
                    <h2 class="card-title">关联商品ID (当前 {{ editForm.productIDs.length }}/3 个)</h2>
                    <div class="associated-ids-section">
                        <el-tag v-for="(tag, index) in editForm.productIDs" :key="tag + '-' + index" :closable="index >= editForm.existingProductIDsCount" :disable-transitions="false" @close="handleRemoveProductID(index, 'edit')">
                            {{tag}}
                        </el-tag>
                        <el-input class="input-new-tag" v-if="inputProductIDVisible.edit && editForm.productIDs.length < 3" v-model="newProductID.edit" ref="saveTagInputEdit" size="small" placeholder="输入ID后回车" @keyup.enter.native="handleProductIDInputConfirm('edit')" @blur="handleProductIDInputConfirm('edit')">
                        </el-input>
                        <el-button v-else-if="editForm.productIDs.length < 3" class="button-new-tag" size="small" @click="showProductIDInput('edit')">+ 追加ID</el-button>
                        <div class="form-tip error-tip" v-if="editForm.productIDs.length >= 3 && !inputProductIDVisible.edit">已达到3个ID上限，无法继续追加。</div>
                        <div class="form-tip" v-if="editForm.existingProductIDsCount > 0 && editForm.productIDs.length <3">提示：原有关联ID不可移除。</div>
                    </div>
                </div>

                <div class="card">
                    <h2 class="card-title">编辑活动报备信息</h2>
                    <div class="form-group">
                        <el-checkbox-group v-model="editForm.selectedActivities" @change="handleEditActivitiesChange">
                            <div style="margin-bottom: 15px;" v-for="activity_template in activities" :key="activity_template.type">
                                <el-checkbox :label="activity_template.type">{{ activity_template.name }}</el-checkbox>
                                <div class="activity-params" v-if="editForm.selectedActivities.includes(activity_template.type)">
                                    <el-row :gutter="20" style="margin-bottom: 15px;">
                                        <el-col :span="8">
                                            <el-form-item label="生效日期">
                                                <el-date-picker v-model="editForm.activitiesData.find(a=>a.type===activity_template.type).startDate" type="date" placeholder="选择生效日期" :disabled="activity_template.type === 'promo' && editIsAutomaticDateCalculation" style="width: 100%;"></el-date-picker>
                                                <div class="form-tip" v-if="activity_template.type === 'promo' && editIsAutomaticDateCalculation"><i class="el-icon-info"></i> 系统自动计算</div>
                                            </el-form-item>
                                        </el-col>
                                        <el-col :span="8">
                                            <el-form-item label="结束日期">
                                                <el-date-picker v-model="editForm.activitiesData.find(a=>a.type===activity_template.type).endDate" type="date" placeholder="选择结束日期" :disabled="activity_template.type === 'promo' && editIsAutomaticDateCalculation" style="width: 100%;"></el-date-picker>
                                                <div class="form-tip" v-if="activity_template.type === 'promo' && editIsAutomaticDateCalculation"><i class="el-icon-info"></i> 系统自动计算</div>
                                            </el-form-item>
                                        </el-col>
                                        <el-col :span="8" v-if="activity_template.type === 'promo'">
                                            <el-form-item label="手动设置日期">
                                                <el-switch v-model="editManualDateSetting" @change="toggleEditDateCalculation"></el-switch>
                                            </el-form-item>
                                        </el-col>
                                    </el-row>
                                    <el-row :gutter="20">
                                        <el-col :span="8">
                                            <el-form-item label="录入人员">
                                                <el-input v-model="editForm.creator" disabled></el-input>
                                            </el-form-item>
                                        </el-col>
                                        <el-col :span="8">
                                            <el-form-item label="录入时间">
                                                <el-input v-model="editForm.createTimeDisplay" disabled></el-input>
                                            </el-form-item>
                                        </el-col>
                                    </el-row>
                                    <div style="margin-top: 15px;" v-if="activity_template.type === 'promo'">
                                        <div class="form-title">达标条件</div>
                                        <div class="info-text"><i class="el-icon-info"></i> 第一个月广告支出需>300元才能获得活动奖励</div>
                                    </div>
                                    <div v-if="activity_template.requireReview" style="margin-top: 15px;">
                                        <div class="form-title">审核状态</div>
                                        <el-tag size="medium" :type="getStatusType(editForm.activitiesData.find(a=>a.type===activity_template.type).reviewStatus)">{{ getStatusText(editForm.activitiesData.find(a=>a.type===activity_template.type).reviewStatus) }}</el-tag>
                                        <div class="info-text" style="margin-top: 5px;" v-if="editForm.activitiesData.find(a=>a.type===activity_template.type).reviewStatus === 'pending'"><i class="el-icon-info"></i> 编辑后若活动日期等关键信息变更，可能需重新审核。</div>
                                    </div>
                                </div>
                            </div>
                        </el-checkbox-group>
                        <div class="error-tip" v-if="editForm.selectedActivities && !editForm.selectedActivities.length && editShowValidationErrors"><i class="el-icon-warning"></i> 请至少选择一种活动类型</div>
                    </div>
                </div>
            </div>
            <span slot="footer" class="dialog-footer">
                <el-button @click="editDialogVisible = false">取消</el-button>
                <el-button type="primary" @click="submitEditForm" :disabled="!isEditFormChanged()">保存更改</el-button> 
            </span>
        </el-dialog>

        <el-dialog title="报备详情" :visible.sync="detailDialogVisible" width="65%" top="5vh">
            <div v-if="currentReport" class="report-detail-view">
                <el-descriptions :column="2" border>
                    <el-descriptions-item label="报备ID" :span="2">{{ currentReport.reportId }}</el-descriptions-item>
                    <el-descriptions-item label="商品名称">{{ currentReport.productName }}</el-descriptions-item>
                    <el-descriptions-item label="商品代码">{{ currentReport.productCode }}</el-descriptions-item>
                    <el-descriptions-item label="商品图片" :span="2">
                        <div class="product-image-display-container" style="width:120px; height:120px;">
                            <el-image :src="currentReport.productImage" fit="cover" :preview-src-list="[currentReport.productImage]">
                                <div slot="error" class="image-slot-display">无图</div>
                            </el-image>
                        </div>
                    </el-descriptions-item>
                    <el-descriptions-item label="店铺名称">{{ currentReport.shop }}</el-descriptions-item>
                    <el-descriptions-item label="平台">{{ currentReport.platform }}</el-descriptions-item>
                    <el-descriptions-item label="所属团队">{{ currentReport.team }}</el-descriptions-item>
                    <el-descriptions-item label="关联商品ID" :span="2">
                        <span v-if="currentReport.productIDs && currentReport.productIDs.length">
                            <el-tag v-for="id in currentReport.productIDs" :key="id" type="info" style="margin-right:5px; margin-bottom:5px;">{{id}}</el-tag>
                        </span>
                        <span v-else>--</span>
                    </el-descriptions-item>
                    <el-descriptions-item label="录入人员">{{ currentReport.creator }}</el-descriptions-item>
                    <el-descriptions-item label="录入时间">{{ formatDateTime(currentReport.createTime) }}</el-descriptions-item>
                </el-descriptions>

                <el-divider content-position="left">活动信息</el-divider>
                <el-descriptions :column="1" border>
                    <el-descriptions-item v-for="activity in currentReport.activities" :key="activity.type" :label="activity.name">
                        <div style="margin-bottom: 5px;">生效时间: {{ formatDate(activity.startDate) }} 至 {{ formatDate(activity.endDate) }}</div>
                        <div v-if="activity.requireReview" style="margin-bottom: 5px;">
                            审核状态:
                            <el-tag size="mini" :type="getStatusType(activity.reviewStatus)">
                                {{ getStatusText(activity.reviewStatus) }}
                            </el-tag>
                        </div>
                    </el-descriptions-item>
                </el-descriptions>
            </div>
            <span slot="footer" class="dialog-footer"> <el-button @click="detailDialogVisible = false">关闭</el-button> </span>
        </el-dialog>
    </div>

    <script src="https://unpkg.com/vue@2.6.14/dist/vue.js"></script>
    <script src="https://unpkg.com/element-ui/lib/index.js"></script>
    <script>
        new Vue({
            el: '#app',
            data() {
                return {
                    searchForm: {
                        productKeyword: '',
                        dateRange: [],
                        activityType: '',
                        reviewStatus: ''
                    },
                    tableLoading: false,
                    reportRecords: [],
                    pagination: {
                        currentPage: 1,
                        pageSize: 10,
                        total: 0
                    },
                    dialogVisible: false,
                    detailDialogVisible: false,
                    currentReport: null,
                    productIdInput: '',
                    formData: {
                        creator: '张小明',
                        createTime: null,
                        createTimeDisplay: '系统自动生成',
                        productName: '',
                        productCode: '',
                        productImage: '',
                        shop: '',
                        platform: '',
                        team: '',
                        productIDs: [],
                    },
                    selectedActivities: [],
                    isAutomaticDateCalculation: true,
                    manualDateSetting: false,
                    showValidationErrors: false,
                    activities: [{
                        type: 'promo',
                        name: '新品主推让利',
                        requireReview: false,
                        startDate: null,
                        endDate: null
                    }, {
                        type: 'race',
                        name: '新品赛马锁品',
                        requireReview: true,
                        startDate: null,
                        endDate: null,
                        reviewStatus: 'pending'
                    }, {
                        type: 'support',
                        name: '新品亏损扶持',
                        requireReview: true,
                        startDate: null,
                        endDate: null,
                        reviewStatus: 'pending'
                    }],
                    productDatabase: [{
                        id: 'P001',
                        code: 'SKU-10001',
                        name: '优雅印花真丝连衣裙 (春夏新款)',
                        image: 'https://images.unsplash.com/photo-1585250012399-358954688313?ixlib=rb-1.2.1&auto=format&fit=crop&w=300&q=80',
                        shop: '优雅时尚旗舰店',
                        platform: '天猫',
                        team: '女装事业部'
                    }, {
                        id: 'P002',
                        code: 'SKU-10002',
                        name: '透气网面运动休闲跑鞋 (男款)',
                        image: 'https://images.unsplash.com/photo-1595950653106-069305c5a326?ixlib=rb-1.2.1&auto=format&fit=crop&w=300&q=80',
                        shop: '潮流前线运动馆',
                        platform: '淘宝',
                        team: '鞋履运动部'
                    }, {
                        id: 'P003',
                        code: 'SKU-10003',
                        name: '便携式蓝牙音箱Pro (重低音增强)',
                        image: 'https://images.unsplash.com/photo-1618384887929-16ec33fab9ef?ixlib=rb-1.2.1&auto=format&fit=crop&w=300&q=80',
                        shop: '极客数码专营',
                        platform: '京东',
                        team: '影音娱乐部'
                    }, {
                        id: 'P004',
                        code: 'SKU-10004',
                        name: '纯棉透气婴儿连体衣套装 (0-6月)',
                        image: 'https://images.unsplash.com/photo-1522771048208-3d515886623e?ixlib=rb-1.2.1&auto=format&fit=crop&w=300&q=80',
                        shop: '宝宝优选母婴馆',
                        platform: '拼多多',
                        team: '母婴用品部'
                    }, {
                        id: 'P005',
                        code: 'SKU-10005',
                        name: '多功能智能破壁料理机 (家用静音)',
                        image: 'https://images.unsplash.com/photo-1568207739882-652497a05e72?ixlib=rb-1.2.1&auto=format&fit=crop&w=300&q=80',
                        shop: '智能厨电旗舰店',
                        platform: '天猫',
                        team: '生活电器部'
                    }],
                    editDialogVisible: false,
                    editingReportOriginal: null,
                    editForm: {
                        reportId: '',
                        productName: '',
                        productCode: '',
                        productImage: '',
                        shop: '',
                        platform: '',
                        team: '',
                        productIDs: [],
                        existingProductIDsCount: 0,
                        selectedActivities: [],
                        activitiesData: [],
                        creator: '',
                        createTimeDisplay: '',
                    },
                    inputProductIDVisible: {
                        add: false,
                        edit: false
                    },
                    newProductID: {
                        add: '',
                        edit: ''
                    },
                    editIsAutomaticDateCalculation: true,
                    editManualDateSetting: false,
                    editShowValidationErrors: false
                }
            },
            mounted() {
                this.loadReportData();
                this.activities.forEach(a => {
                    this.$set(a, 'startDate', null);
                    this.$set(a, 'endDate', null);
                });
                this.calculateDefaultDates();
            },
            methods: {
                showAddReportDialog() {
                    this.resetForm();
                    this.dialogVisible = true;
                    this.formData.createTime = new Date();
                    this.formData.createTimeDisplay = this.formatDateTime(this.formData.createTime);
                    this.calculateDefaultDates();
                },
                handleSearch() {
                    this.pagination.currentPage = 1;
                    this.loadReportData();
                },
                resetSearch() {
                    this.searchForm = {
                        productKeyword: '',
                        dateRange: [],
                        activityType: '',
                        reviewStatus: ''
                    };
                    this.loadReportData();
                },
                handleSizeChange(val) {
                    this.pagination.pageSize = val;
                    this.loadReportData();
                },
                handleCurrentChange(val) {
                    this.pagination.currentPage = val;
                    this.loadReportData();
                },
                loadReportData() {
                    this.tableLoading = true;
                    setTimeout(() => {
                        const mockData = [{
                            reportId: 'RPT20250501001',
                            productName: '优雅印花真丝连衣裙 (春夏新款)',
                            productCode: 'SKU-10001',
                            productImage: 'https://images.unsplash.com/photo-1585250012399-358954688313?ixlib=rb-1.2.1&auto=format&fit=crop&w=300&q=80',
                            shop: '优雅时尚旗舰店',
                            platform: '天猫',
                            team: '女装事业部',
                            productIDs: ['P001', 'P001-ChannelA'],
                            activities: [{
                                type: 'promo',
                                name: '新品主推让利',
                                startDate: new Date('2025-05-10'),
                                endDate: new Date('2025-05-31'),
                                requireReview: false
                            }, {
                                type: 'race',
                                name: '新品赛马锁品',
                                startDate: new Date('2025-05-10'),
                                endDate: new Date('2025-06-30'),
                                requireReview: true,
                                reviewStatus: 'approved'
                            }],
                            creator: '张小明',
                            createTime: new Date('2025-05-01 09:30:25')
                        }, {
                            reportId: 'RPT20250501002',
                            productName: '便携式蓝牙音箱Pro (重低音增强)',
                            productCode: 'SKU-10003',
                            productImage: 'https://images.unsplash.com/photo-1618384887929-16ec33fab9ef?ixlib=rb-1.2.1&auto=format&fit=crop&w=300&q=80',
                            shop: '极客数码专营',
                            platform: '京东',
                            team: '影音娱乐部',
                            productIDs: ['P003'],
                            activities: [{
                                type: 'support',
                                name: '新品亏损扶持',
                                startDate: new Date('2025-05-15'),
                                endDate: new Date('2025-06-15'),
                                requireReview: true,
                                reviewStatus: 'pending'
                            }],
                            creator: '李四',
                            createTime: new Date('2025-05-01 10:15:32')
                        }, {
                            reportId: 'RPT20250502003',
                            productName: '纯棉透气婴儿连体衣套装 (0-6月)',
                            productCode: 'SKU-10004',
                            productImage: 'https://images.unsplash.com/photo-1522771048208-3d515886623e?ixlib=rb-1.2.1&auto=format&fit=crop&w=300&q=80',
                            shop: '宝宝优选母婴馆',
                            platform: '拼多多',
                            team: '母婴用品部',
                            productIDs: ['P004', 'P004-StoreX', 'P004-Online'],
                            activities: [{
                                type: 'promo',
                                name: '新品主推让利',
                                startDate: new Date('2025-05-03'),
                                endDate: new Date('2025-05-30'),
                                requireReview: false
                            }],
                            creator: '王五',
                            createTime: new Date('2025-05-02 14:22:45')
                        }];

                        let filteredData = mockData.map(item => ({...item,
                            activities: item.activities.map(act => ({...act,
                                startDate: act.startDate ? new Date(act.startDate) : null,
                                endDate: act.endDate ? new Date(act.endDate) : null
                            })),
                            createTime: new Date(item.createTime)
                        }));

                        if (this.searchForm.productKeyword) {
                            const keyword = this.searchForm.productKeyword.toLowerCase();
                            filteredData = filteredData.filter(item =>
                                item.productName.toLowerCase().includes(keyword) ||
                                item.productCode.toLowerCase().includes(keyword) ||
                                (item.productIDs && item.productIDs.some(id => id.toLowerCase().includes(keyword)))
                            );
                        }
                        if (this.searchForm.dateRange && this.searchForm.dateRange.length === 2) {
                            const startDateSearch = new Date(this.searchForm.dateRange[0]);
                            startDateSearch.setHours(0, 0, 0, 0);
                            const endDateSearch = new Date(this.searchForm.dateRange[1]);
                            endDateSearch.setHours(23, 59, 59, 999);
                            filteredData = filteredData.filter(item => new Date(item.createTime) >= startDateSearch && new Date(item.createTime) <= endDateSearch);
                        }
                        if (this.searchForm.activityType) filteredData = filteredData.filter(item => item.activities.some(a => a.type === this.searchForm.activityType));
                        if (this.searchForm.reviewStatus) filteredData = filteredData.filter(item => item.activities.some(a => a.requireReview && a.reviewStatus === this.searchForm.reviewStatus));

                        this.pagination.total = filteredData.length;
                        this.reportRecords = filteredData.slice((this.pagination.currentPage - 1) * this.pagination.pageSize, this.pagination.currentPage * this.pagination.pageSize);
                        this.tableLoading = false;
                    }, 500);
                },
                viewReportDetail(row) {
                    this.currentReport = JSON.parse(JSON.stringify(row));
                    this.detailDialogVisible = true;
                },
                handleDialogClose(done) {
                    if (this.formData.productName || this.selectedActivities.length > 0) {
                        this.$confirm('确定要关闭吗？未保存的数据将丢失', '提示', {
                                confirmButtonText: '确定',
                                cancelButtonText: '取消',
                                type: 'warning'
                            })
                            .then(() => {
                                this.resetForm();
                                done();
                            }).catch(() => {});
                    } else {
                        this.resetForm();
                        done();
                    }
                },
                queryBaseProduct(queryString, cb) {
                    const results = queryString ?
                        this.productDatabase.filter(product =>
                            (product.id.toLowerCase().includes(queryString.toLowerCase()) ||
                                product.name.toLowerCase().includes(queryString.toLowerCase()) ||
                                product.code.toLowerCase().includes(queryString.toLowerCase()))
                        ) : this.productDatabase;
                    cb(results.map(product => ({
                        value: `${product.name} (ID: ${product.id})`,
                        productFull: product
                    })));
                },
                handleBaseProductSelect(item) {
                    if (item && item.productFull) {
                        const p = item.productFull;
                        this.formData.productName = p.name;
                        this.formData.productCode = p.code;
                        this.formData.productImage = p.image;
                        this.formData.shop = p.shop;
                        this.formData.platform = p.platform;
                        this.formData.team = p.team;
                        this.formData.productIDs = [p.id];
                        this.productIdInput = '';
                    }
                },
                clearBaseProduct() {
                    this.formData.productName = '';
                    this.formData.productCode = '';
                    this.formData.productImage = '';
                    this.formData.shop = '';
                    this.formData.platform = '';
                    this.formData.team = '';
                    this.formData.productIDs = [];
                    this.productIdInput = '';
                    this.inputProductIDVisible.add = false;
                    this.newProductID.add = '';
                },
                showProductIDInput(mode) {
                    this.inputProductIDVisible[mode] = true;
                    this.$nextTick(_ => {
                        if (mode === 'add' && this.$refs.saveTagInputAdd) this.$refs.saveTagInputAdd.focus();
                        if (mode === 'edit' && this.$refs.saveTagInputEdit) this.$refs.saveTagInputEdit.focus();
                    });
                },
                handleProductIDInputConfirm(mode) {
                    let newID = this.newProductID[mode].trim();
                    let currentIDs = (mode === 'add') ? this.formData.productIDs : this.editForm.productIDs;

                    if (newID) {
                        if (currentIDs.length < 3) {
                            if (!currentIDs.includes(newID)) {
                                currentIDs.push(newID);
                            } else {
                                this.$message.warning('此ID已存在');
                            }
                        } else {
                            this.$message.warning('已达到ID数量上限');
                        }
                    }
                    this.inputProductIDVisible[mode] = false;
                    this.newProductID[mode] = '';
                },
                handleRemoveProductID(index, mode) {
                    if (mode === 'add') {
                        if (index > 0) {
                            this.formData.productIDs.splice(index, 1);
                        } else {
                            this.$message.warning('基础商品ID不可移除，请通过“清空”操作重选基础商品。');
                        }
                    } else {
                        if (index >= this.editForm.existingProductIDsCount) {
                            this.editForm.productIDs.splice(index, 1);
                        }
                    }
                },
                resetForm() {
                    this.clearBaseProduct();
                    this.selectedActivities = [];
                    this.isAutomaticDateCalculation = true;
                    this.manualDateSetting = false;
                    this.showValidationErrors = false;
                    this.activities.forEach(activity => {
                        this.$set(activity, 'startDate', null);
                        this.$set(activity, 'endDate', null);
                    });
                    this.calculateDefaultDates();
                },
                validateForm() {
                    this.showValidationErrors = true;
                    if (!this.formData.productName) {
                        this.$message.error('请选择一个基础商品');
                        return false;
                    }
                    if (!this.formData.productIDs || this.formData.productIDs.length === 0) {
                        this.$message.error('请至少关联一个商品ID');
                        return false;
                    }
                    if (this.formData.productIDs.length > 3) {
                        this.$message.error('最多关联3个商品ID');
                        return false;
                    }
                    if (this.selectedActivities.length === 0) {
                        this.showValidationErrors = true;
                        return false;
                    }
                    for (const activityType of this.selectedActivities) {
                        const activity = this.activities.find(a => a.type === activityType);
                        if (!activity.startDate || !activity.endDate) {
                            this.$message.error(`请为 ${activity.name} 设置活动日期`);
                            return false;
                        }
                        if (new Date(activity.startDate) > new Date(activity.endDate)) {
                            this.$message.error(`${activity.name} 的开始日期不能晚于结束日期`);
                            return false;
                        }
                    }
                    return true;
                },
                submitForm() {
                    if (!this.validateForm()) return;
                    const newReport = {
                        reportId: 'RPT' + this.formatDate(new Date(), 'YYYYMMDD') + Math.floor(Math.random() * 900 + 100),
                        productName: this.formData.productName,
                        productCode: this.formData.productCode,
                        productImage: this.formData.productImage,
                        shop: this.formData.shop,
                        platform: this.formData.platform,
                        team: this.formData.team,
                        productIDs: [...this.formData.productIDs],
                        activities: this.selectedActivities.map(type => {
                            const ad = this.activities.find(a => a.type === type);
                            return {
                                name: ad.name,
                                type: ad.type,
                                requireReview: ad.requireReview,
                                startDate: new Date(ad.startDate),
                                endDate: new Date(ad.endDate),
                                reviewStatus: ad.requireReview ? 'pending' : null
                            }
                        }),
                        creator: this.formData.creator,
                        createTime: new Date(this.formData.createTime)
                    };
                    this.reportRecords.unshift(newReport);
                    this.pagination.total++;
                    this.$message.success('活动报备提交成功！');
                    if (newReport.activities.some(a => a.requireReview)) {
                        this.$notify({
                            title: '提交成功',
                            message: '您的部分活动需管理员审核后生效',
                            type: 'warning',
                            duration: 5000
                        });
                    }
                    this.dialogVisible = false;
                    this.resetForm();
                },
                handleEditReport(row) {
                    if (row.productIDs && row.productIDs.length >= 3) {
                        this.$message.warning('当前报备已有3个关联ID，无法继续追加！');
                        return;
                    }
                    this.editingReportOriginal = JSON.parse(JSON.stringify(row)); // Store original

                    // Populate editForm
                    this.editForm.reportId = row.reportId;
                    this.editForm.productName = row.productName;
                    this.editForm.productCode = row.productCode;
                    this.editForm.productImage = row.productImage;
                    this.editForm.shop = row.shop;
                    this.editForm.platform = row.platform;
                    this.editForm.team = row.team;
                    this.editForm.productIDs = row.productIDs ? [...row.productIDs] : [];
                    this.editForm.existingProductIDsCount = row.productIDs ? row.productIDs.length : 0;
                    this.editForm.creator = row.creator;
                    this.editForm.createTimeDisplay = this.formatDateTime(row.createTime);

                    // Deep copy activities template for edit form's activitiesData
                    this.editForm.activitiesData = JSON.parse(JSON.stringify(this.activities));
                    this.editForm.selectedActivities = row.activities.map(a => a.type);

                    // Populate dates and reviewStatus for selected activities in editForm.activitiesData
                    this.editForm.activitiesData.forEach(activityInForm => {
                        const existingActivity = row.activities.find(ra => ra.type === activityInForm.type);
                        if (existingActivity) {
                            activityInForm.startDate = existingActivity.startDate ? new Date(existingActivity.startDate) : null;
                            activityInForm.endDate = existingActivity.endDate ? new Date(existingActivity.endDate) : null;
                            if (activityInForm.requireReview) {
                                activityInForm.reviewStatus = existingActivity.reviewStatus || 'pending';
                            }
                        } else { // Ensure non-selected activities have null dates
                            activityInForm.startDate = null;
                            activityInForm.endDate = null;
                        }
                    });

                    // Handle promo date calculation settings for edit mode
                    const promoActivityInEditForm = this.editForm.activitiesData.find(a => a.type === 'promo');
                    if (promoActivityInEditForm && this.editForm.selectedActivities.includes('promo')) {
                        if (promoActivityInEditForm.startDate || promoActivityInEditForm.endDate) { // If dates exist
                            this.editManualDateSetting = true;
                            this.editIsAutomaticDateCalculation = false;
                        } else { // If selected but no dates, enable auto-calc
                            this.editManualDateSetting = false;
                            this.editIsAutomaticDateCalculation = true;
                            // Delay initial calculation to prevent potential loops during setup
                            this.$nextTick(() => {
                                this.calculateEditPromoDates();
                            });
                        }
                    } else { // Default if promo not selected
                        this.editManualDateSetting = false;
                        this.editIsAutomaticDateCalculation = true;
                    }

                    this.newProductID.edit = '';
                    this.inputProductIDVisible.edit = false;
                    this.editShowValidationErrors = false;
                    this.editDialogVisible = true;
                },
                isEditFormChanged() {
                    if (!this.editingReportOriginal) return false;
                    const originalIDs = this.editingReportOriginal.productIDs || [];
                    const currentIDs = this.editForm.productIDs || [];
                    let idsChanged = currentIDs.length !== originalIDs.length || !currentIDs.every((id, index) => id === originalIDs[index]);


                    const originalSelectedTypes = (this.editingReportOriginal.activities || []).map(a => a.type).sort();
                    const currentSelectedTypes = [...this.editForm.selectedActivities].sort();
                    let activitiesStructureChanged = JSON.stringify(originalSelectedTypes) !== JSON.stringify(currentSelectedTypes);

                    let activityDetailsChanged = false;
                    if (!activitiesStructureChanged) { // Only check details if types are the same
                        activityDetailsChanged = this.editForm.selectedActivities.some(type => {
                            const current = this.editForm.activitiesData.find(a => a.type === type);
                            const original = this.editingReportOriginal.activities.find(a => a.type === type);
                            // If an activity was selected in original but not now, or vice-versa, it's a change (covered by activitiesStructureChanged)
                            // If both exist, compare their properties
                            if (original && current) {
                                return this.formatDate(current.startDate) !== this.formatDate(original.startDate) ||
                                    this.formatDate(current.endDate) !== this.formatDate(original.endDate) ||
                                    current.reviewStatus !== original.reviewStatus; // Consider reviewStatus as well
                            }
                            return false; // Should not happen if types are same and both exist
                        });
                    }
                    return idsChanged || activitiesStructureChanged || activityDetailsChanged;
                },
                validateEditForm() {
                    if (!this.editForm.productIDs || this.editForm.productIDs.length === 0) {
                        this.$message.error('请至少关联一个商品ID');
                        return false;
                    }
                    if (this.editForm.productIDs.length > 3) {
                        this.$message.error('最多关联3个商品ID');
                        return false;
                    }

                    if (this.isEditFormChanged() && this.editForm.selectedActivities.length === 0) {
                        this.$message.error('检测到更改，请至少选择一种活动类型');
                        return false;
                    }
                    for (const activityType of this.editForm.selectedActivities) {
                        const activity = this.editForm.activitiesData.find(a => a.type === activityType);
                        if (!activity.startDate || !activity.endDate) {
                            this.$message.error(`请为 ${activity.name} 设置活动日期`);
                            return false;
                        }
                        if (new Date(activity.startDate) > new Date(activity.endDate)) {
                            this.$message.error(`${activity.name} 的开始日期不能晚于结束日期`);
                            return false;
                        }
                    }
                    return true;
                },
                submitEditForm() {
                    if (!this.validateEditForm()) return;
                    if (!this.isEditFormChanged()) {
                        this.$message.info('未检测到关联ID追加或活动信息变更。');
                        this.editDialogVisible = false;
                        return;
                    }
                    const reportIndex = this.reportRecords.findIndex(r => r.reportId === this.editForm.reportId);
                    if (reportIndex === -1) {
                        this.$message.error('未找到记录');
                        return;
                    }

                    const updatedActivities = this.editForm.selectedActivities.map(type => {
                        const ad = this.editForm.activitiesData.find(a => a.type === type);
                        let newReviewStatus = ad.reviewStatus;
                        if (ad.requireReview) {
                            const originalActivity = this.editingReportOriginal.activities.find(oa => oa.type === type);
                            const datesChanged = originalActivity && (this.formatDate(originalActivity.startDate) !== this.formatDate(ad.startDate) || this.formatDate(originalActivity.endDate) !== this.formatDate(ad.endDate));
                            if ((originalActivity && originalActivity.reviewStatus === 'approved' && datesChanged) ||
                                (!originalActivity && this.editForm.selectedActivities.includes(ad.type)) // Activity was newly selected
                            ) {
                                newReviewStatus = 'pending';
                                if (datesChanged) this.$message.warning(`${ad.name} 的日期已更改，状态已重置为待审核。`);
                            }
                        } else {
                            newReviewStatus = null;
                        }
                        return {
                            name: ad.name,
                            type: ad.type,
                            requireReview: ad.requireReview,
                            startDate: new Date(ad.startDate),
                            endDate: new Date(ad.endDate),
                            reviewStatus: newReviewStatus
                        };
                    });

                    const updatedReport = {
                        ...this.editingReportOriginal, // Preserves original creator, createTime, and UNCHANGED shared product info
                        productIDs: [...this.editForm.productIDs],
                        activities: updatedActivities
                    };
                    this.$set(this.reportRecords, reportIndex, updatedReport);
                    this.$message.success('报备信息更新成功！');
                    this.editDialogVisible = false;
                },
                handleEditDialogClose(done) {
                    if (this.isEditFormChanged()) {
                        this.$confirm('内容已修改，确定关闭吗？', '提示', {
                            type: 'warning'
                        }).then(done).catch(() => {});
                    } else {
                        done();
                    }
                },
                formatDate(date, formatStr) {
                    if (!date) return '—';
                    const d = new Date(date);
                    if (isNaN(d.getTime())) return '—';
                    const Y = d.getFullYear(),
                        M = (d.getMonth() + 1 + '').padStart(2, '0'),
                        D = (d.getDate() + '').padStart(2, '0');
                    if (formatStr === 'YYYYMMDD') return `${Y}${M}${D}`;
                    return `${Y}-${M}-${D}`;
                },
                formatDateTime(date) {
                    if (!date) return '—';
                    const d = new Date(date);
                    if (isNaN(d.getTime())) return '—';
                    return `${this.formatDate(d)} ${(d.getHours()+'').padStart(2,'0')}:${(d.getMinutes()+'').padStart(2,'0')}:${(d.getSeconds()+'').padStart(2,'0')}`;
                },
                getStatusType(status) {
                    switch (status) {
                        case 'pending':
                            return 'warning';
                        case 'approved':
                            return 'success';
                        case 'rejected':
                            return 'danger';
                        default:
                            return 'info';
                    }
                },
                getStatusText(status) {
                    switch (status) {
                        case 'pending':
                            return '待审核';
                        case 'approved':
                            return '已通过';
                        case 'rejected':
                            return '已拒绝';
                        default:
                            return '未知';
                    }
                },
                _updatePromoDate(targetActivityContainer, selectedActivitiesList, isAutoCalc) {
                    const today = new Date();
                    const promoActivity = targetActivityContainer.find(a => a.type === 'promo');
                    if (!promoActivity) return;

                    if (isAutoCalc && selectedActivitiesList.includes('promo')) {
                        const currentDay = today.getDate();
                        const currentMonth = today.getMonth();
                        const currentYear = today.getFullYear();
                        const lastDayOfMonth = new Date(currentYear, currentMonth + 1, 0);
                        const firstDayOfNextMonth = new Date(currentYear, currentMonth + 1, 1);
                        const lastDayOfNextMonth = new Date(currentYear, currentMonth + 2, 0);
                        const daysRemainingInMonth = lastDayOfMonth.getDate() - currentDay;

                        let newStartDate, newEndDate;
                        if (daysRemainingInMonth <= 5) {
                            newStartDate = new Date(firstDayOfNextMonth);
                        } else {
                            newStartDate = new Date(today);
                        }
                        if (selectedActivitiesList.includes('race')) {
                            newEndDate = new Date(lastDayOfNextMonth);
                        } else {
                            newEndDate = new Date(lastDayOfMonth);
                        }

                        if (!promoActivity.startDate || promoActivity.startDate.getTime() !== newStartDate.getTime()) {
                            this.$set(promoActivity, 'startDate', newStartDate);
                        }
                        if (!promoActivity.endDate || promoActivity.endDate.getTime() !== newEndDate.getTime()) {
                            this.$set(promoActivity, 'endDate', newEndDate);
                        }
                    } else if (isAutoCalc && !selectedActivitiesList.includes('promo')) { // Promo deselected in auto mode
                        if (promoActivity.startDate !== null) this.$set(promoActivity, 'startDate', null);
                        if (promoActivity.endDate !== null) this.$set(promoActivity, 'endDate', null);
                    }
                },
                calculateDefaultDates() {
                    this._updatePromoDate(this.activities, this.selectedActivities, this.isAutomaticDateCalculation);
                },
                toggleDateCalculation(value) {
                    this.isAutomaticDateCalculation = !value;
                    if (this.isAutomaticDateCalculation) {
                        this.calculateDefaultDates();
                    }
                },
                calculateEditPromoDates() {
                    this._updatePromoDate(this.editForm.activitiesData, this.editForm.selectedActivities, this.editIsAutomaticDateCalculation);
                },
                toggleEditDateCalculation(value) {
                    this.editIsAutomaticDateCalculation = !value;
                    if (this.editIsAutomaticDateCalculation) {
                        this.calculateEditPromoDates();
                    }
                },
                handleEditActivitiesChange() {
                    this.editShowValidationErrors = false;
                    if (this.editIsAutomaticDateCalculation) this.calculateEditPromoDates();
                },
            },
            watch: {
                selectedActivities: {
                    handler() {
                        this.calculateDefaultDates();
                        this.showValidationErrors = false;
                    },
                    deep: true
                },
                'formData.productIDs': {
                    handler(newVal) { /* ... */ },
                    deep: true
                },
                'editForm.productIDs': {
                    handler(newVal) { /* ... */ },
                    deep: true
                }
            }
        });
    </script>
</body>

</html>
</script>
</body>

</html>