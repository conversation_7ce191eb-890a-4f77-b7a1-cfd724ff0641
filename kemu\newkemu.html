<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>电商后台管理系统 - 手工科目补录</title>
    <style>
        /* 全局样式 */
        
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: 'PingFang SC', 'Microsoft YaHei', sans-serif;
        }
        
         :root {
            --primary-color: #1890ff;
            --primary-hover: #40a9ff;
            --success-color: #52c41a;
            --warning-color: #faad14;
            --danger-color: #ff4d4f;
            --text-primary: #262626;
            --text-secondary: #595959;
            --text-tertiary: #8c8c8c;
            --border-color: #e8e8e8;
            --bg-color: #f5f5f5;
            --header-height: 64px;
            --sidebar-width: 240px;
            --container-bg: #fff;
            --shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
            --radius: 4px;
        }
        
        body {
            background-color: var(--bg-color);
            color: var(--text-primary);
            line-height: 1.5;
            font-size: 14px;
        }
        
        .layout {
            display: flex;
            min-height: 100vh;
        }
        /* 侧边栏样式 */
        
        .sidebar {
            width: var(--sidebar-width);
            background-color: var(--container-bg);
            box-shadow: var(--shadow);
            position: fixed;
            height: 100vh;
            z-index: 10;
            transition: all 0.3s;
        }
        
        .sidebar-logo {
            height: var(--header-height);
            display: flex;
            align-items: center;
            padding: 0 24px;
            box-shadow: 0 1px 0 0 var(--border-color);
        }
        
        .sidebar-logo img {
            height: 32px;
            margin-right: 12px;
        }
        
        .sidebar-logo h1 {
            color: var(--text-primary);
            font-size: 18px;
            font-weight: 600;
            margin: 0;
        }
        
        .menu {
            padding: 16px 0;
        }
        
        .menu-item {
            padding: 12px 24px;
            color: var(--text-secondary);
            display: flex;
            align-items: center;
            cursor: pointer;
            transition: all 0.3s;
        }
        
        .menu-item:hover {
            color: var(--primary-color);
            background-color: rgba(24, 144, 255, 0.05);
        }
        
        .menu-item.active {
            color: var(--primary-color);
            background-color: rgba(24, 144, 255, 0.1);
            border-right: 3px solid var(--primary-color);
            font-weight: 500;
        }
        
        .menu-item i {
            margin-right: 12px;
            font-size: 16px;
        }
        /* 主内容区样式 */
        
        .main-content {
            flex: 1;
            margin-left: var(--sidebar-width);
            display: flex;
            flex-direction: column;
            min-width: 0;
        }
        
        .header {
            height: var(--header-height);
            background-color: var(--container-bg);
            box-shadow: var(--shadow);
            display: flex;
            align-items: center;
            padding: 0 24px;
            justify-content: space-between;
            position: sticky;
            top: 0;
            z-index: 9;
        }
        
        .header-title {
            font-size: 18px;
            font-weight: 500;
        }
        
        .user-info {
            display: flex;
            align-items: center;
        }
        
        .user-avatar {
            width: 32px;
            height: 32px;
            border-radius: 50%;
            background-color: var(--primary-color);
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 14px;
            margin-right: 8px;
        }
        
        .username {
            margin-right: 24px;
            color: var(--text-secondary);
        }
        /* 页面内容区域 */
        
        .page-content {
            padding: 24px;
            flex: 1;
        }
        
        .page-header {
            margin-bottom: 24px;
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding-bottom: 16px;
            border-bottom: 1px solid var(--border-color);
        }
        
        .page-title {
            font-size: 20px;
            font-weight: 500;
            color: var(--text-primary);
        }
        
        .btn {
            padding: 8px 16px;
            border-radius: var(--radius);
            font-size: 14px;
            border: none;
            cursor: pointer;
            display: inline-flex;
            align-items: center;
            transition: all 0.3s;
            outline: none;
        }
        
        .btn i {
            margin-right: 6px;
            font-size: 16px;
        }
        
        .btn-primary {
            background-color: var(--primary-color);
            color: white;
        }
        
        .btn-primary:hover {
            background-color: var(--primary-hover);
        }
        
        .btn-secondary {
            background-color: white;
            color: var(--text-primary);
            border: 1px solid var(--border-color);
        }
        
        .btn-secondary:hover {
            color: var(--primary-color);
            border-color: var(--primary-color);
        }
        
        .btn-text {
            background: none;
            color: var(--primary-color);
            padding: 4px 8px;
        }
        
        .btn-text:hover {
            background-color: rgba(24, 144, 255, 0.05);
        }
        
        .btn-danger-text {
            color: var(--danger-color);
        }
        
        .btn-danger-text:hover {
            background-color: rgba(255, 77, 79, 0.05);
        }
        
        .card {
            background-color: var(--container-bg);
            border-radius: var(--radius);
            box-shadow: var(--shadow);
            margin-bottom: 24px;
        }
        
        .card-header {
            padding: 16px 24px;
            border-bottom: 1px solid var(--border-color);
            font-weight: 500;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }
        
        .card-body {
            padding: 24px;
        }
        
        .card-footer {
            padding: 16px 24px;
            border-top: 1px solid var(--border-color);
            display: flex;
            justify-content: flex-end;
            gap: 12px;
        }
        /* 表格样式 */
        
        .table-container {
            width: 100%;
            overflow-x: auto;
        }
        
        table {
            width: 100%;
            border-collapse: collapse;
            text-align: left;
        }
        
        thead {
            background-color: #fafafa;
            border-bottom: 1px solid var(--border-color);
        }
        
        th {
            padding: 16px;
            color: var(--text-secondary);
            font-weight: 500;
            white-space: nowrap;
        }
        
        td {
            padding: 16px;
            border-bottom: 1px solid var(--border-color);
            color: var(--text-primary);
        }
        
        .table-action-group {
            display: flex;
            gap: 8px;
        }
        /* 标签样式 */
        
        .tag {
            display: inline-block;
            padding: 2px 8px;
            border-radius: var(--radius);
            font-size: 12px;
        }
        
        .tag-blue {
            background-color: rgba(24, 144, 255, 0.1);
            color: var(--primary-color);
            border: 1px solid rgba(24, 144, 255, 0.2);
        }
        
        .tag-green {
            background-color: rgba(82, 196, 26, 0.1);
            color: var(--success-color);
            border: 1px solid rgba(82, 196, 26, 0.2);
        }
        
        .tag-red {
            background-color: rgba(255, 77, 79, 0.1);
            color: var(--danger-color);
            border: 1px solid rgba(255, 77, 79, 0.2);
        }
        /* 状态标签 */
        
        .status-badge {
            display: inline-flex;
            align-items: center;
            padding: 0 8px;
            height: 22px;
            font-size: 12px;
            border-radius: 11px;
        }
        
        .status-active {
            background-color: rgba(82, 196, 26, 0.1);
            color: var(--success-color);
        }
        
        .status-inactive {
            background-color: rgba(140, 140, 140, 0.1);
            color: var(--text-tertiary);
        }
        /* 通知提示 */
        
        .toast-container {
            position: fixed;
            top: 16px;
            right: 16px;
            z-index: 1000;
        }
        
        .toast {
            padding: 12px 16px;
            border-radius: var(--radius);
            background-color: var(--container-bg);
            box-shadow: 0 3px 6px -4px rgba(0, 0, 0, 0.12), 0 6px 16px 0 rgba(0, 0, 0, 0.08);
            margin-bottom: 16px;
            display: flex;
            align-items: center;
            min-width: 300px;
            max-width: 400px;
            animation: slideIn 0.3s;
            opacity: 0;
            transform: translateX(100%);
        }
        
        .toast.show {
            opacity: 1;
            transform: translateX(0);
        }
        
        .toast-icon {
            margin-right: 12px;
            font-size: 16px;
        }
        
        .toast-content {
            flex: 1;
        }
        
        .toast-title {
            font-weight: 500;
            margin-bottom: 2px;
        }
        
        .toast-success .toast-icon {
            color: var(--success-color);
        }
        
        .toast-error .toast-icon {
            color: var(--danger-color);
        }
        
        @keyframes slideIn {
            from {
                opacity: 0;
                transform: translateX(100%);
            }
            to {
                opacity: 1;
                transform: translateX(0);
            }
        }
        
        @keyframes slideOut {
            from {
                opacity: 1;
                transform: translateX(0);
            }
            to {
                opacity: 0;
                transform: translateX(100%);
            }
        }
        /* 表单样式 */
        
        .form-row {
            display: flex;
            gap: 16px;
            margin-bottom: 24px;
        }
        
        .form-col {
            flex: 1;
        }
        
        .form-group {
            margin-bottom: 24px;
        }
        
        .form-label {
            display: block;
            margin-bottom: 8px;
            color: var(--text-secondary);
        }
        
        .form-control {
            width: 100%;
            padding: 8px 12px;
            border: 1px solid var(--border-color);
            border-radius: var(--radius);
            font-size: 14px;
            transition: all 0.3s;
        }
        
        .form-control:focus {
            border-color: var(--primary-color);
            outline: none;
            box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
        }
        
        .form-control:disabled,
        .form-control[readonly] {
            background-color: #f5f5f5;
            cursor: not-allowed;
        }
        
        .form-select {
            appearance: none;
            background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='12' height='12' viewBox='0 0 12 12'%3E%3Cpath fill='%23595959' d='M6 8.825l-4.475-4.5L2.45 3.4 6 6.975 9.55 3.4l.925.925L6 8.825z'/%3E%3C/svg%3E");
            background-repeat: no-repeat;
            background-position: right 12px center;
            padding-right: 32px;
        }
        
        .form-help {
            margin-top: 4px;
            font-size: 12px;
            color: var(--text-tertiary);
        }
        
        .form-error {
            color: var(--danger-color);
            font-size: 12px;
            margin-top: 4px;
        }
        
        .form-check {
            display: flex;
            align-items: center;
            margin-bottom: 8px;
        }
        
        .form-check input[type="radio"],
        .form-check input[type="checkbox"] {
            margin-right: 8px;
            cursor: pointer;
        }
        
        .form-check label {
            cursor: pointer;
            color: var(--text-secondary);
        }
        /* 模态框样式 */
        
        .modal-backdrop {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-color: rgba(0, 0, 0, 0.45);
            z-index: 100;
            display: flex;
            align-items: center;
            justify-content: center;
            opacity: 0;
            visibility: hidden;
            transition: opacity 0.3s, visibility 0.3s;
        }
        
        .modal-backdrop.show {
            opacity: 1;
            visibility: visible;
        }
        
        .modal {
            background-color: var(--container-bg);
            border-radius: var(--radius);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
            width: 100%;
            max-width: 700px;
            max-height: 90vh;
            overflow-y: auto;
            transform: translateY(20px);
            transition: transform 0.3s;
        }
        
        .modal-backdrop.show .modal {
            transform: translateY(0);
        }
        
        .modal-header {
            padding: 16px 24px;
            border-bottom: 1px solid var(--border-color);
            display: flex;
            align-items: center;
            justify-content: space-between;
        }
        
        .modal-title {
            font-size: 16px;
            font-weight: 500;
            color: var(--text-primary);
            margin: 0;
        }
        
        .modal-close {
            background: none;
            border: none;
            cursor: pointer;
            font-size: 16px;
            color: var(--text-tertiary);
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .modal-close:hover {
            color: var(--text-primary);
        }
        
        .modal-body {
            padding: 24px;
        }
        
        .modal-footer {
            padding: 16px 24px;
            border-top: 1px solid var(--border-color);
            display: flex;
            justify-content: flex-end;
            gap: 12px;
        }
        
        .modal-confirm {
            width: 420px;
        }
        
        .modal-confirm-icon {
            width: 48px;
            height: 48px;
            margin-right: 16px;
            font-size: 32px;
            color: var(--warning-color);
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .modal-confirm-content {
            flex: 1;
        }
        
        .modal-confirm-title {
            font-size: 16px;
            font-weight: 500;
            margin-bottom: 8px;
        }
        
        .modal-confirm-description {
            color: var(--text-secondary);
        }
        
        .modal.modal-confirm .modal-body {
            display: flex;
            padding: 24px 24px 32px;
        }
        /* 记录列表样式 */
        
        .record-list {
            margin-top: 24px;
        }
        
        .record-list-header {
            font-weight: 500;
            margin-bottom: 16px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .record-list-title {
            font-size: 16px;
            color: var(--text-primary);
        }
        
        .record-summary {
            display: flex;
            background-color: #ffffff;
            padding: 20px;
            border-radius: var(--radius);
            margin-bottom: 24px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
            border: 1px solid rgba(0, 0, 0, 0.05);
        }
        
        .summary-item {
            flex: 1;
            text-align: center;
            padding: 0 20px;
            position: relative;
        }
        
        .summary-item:not(:last-child)::after {
            content: '';
            position: absolute;
            right: 0;
            top: 15%;
            height: 70%;
            width: 1px;
            background: linear-gradient(to bottom, rgba(0, 0, 0, 0.02), rgba(0, 0, 0, 0.08), rgba(0, 0, 0, 0.02));
        }
        
        .summary-label {
            color: var(--text-secondary);
            margin-bottom: 8px;
            font-size: 14px;
            font-weight: 500;
        }
        
        .summary-value {
            font-size: 24px;
            font-weight: 600;
            color: var(--text-primary);
            line-height: 1.2;
        }
        
        .summary-value.income {
            color: var(--success-color);
            position: relative;
        }
        
        .summary-value.income::before {
            content: '';
            display: inline-block;
            width: 6px;
            height: 6px;
            border-radius: 50%;
            background-color: var(--success-color);
            margin-right: 6px;
            position: relative;
            top: -4px;
        }
        
        .summary-value.expense {
            color: var(--danger-color);
            position: relative;
        }
        
        .summary-value.expense::before {
            content: '';
            display: inline-block;
            width: 6px;
            height: 6px;
            border-radius: 50%;
            background-color: var(--danger-color);
            margin-right: 6px;
            position: relative;
            top: -4px;
        }
        /* 日历选择器 */
        
        .date-selector {
            display: flex;
            align-items: center;
        }
        
        .date-selector .date-btn {
            padding: 6px 12px;
            border: 1px solid var(--border-color);
            background-color: white;
            cursor: pointer;
            transition: all 0.3s;
            font-size: 14px;
        }
        
        .date-selector .date-btn:first-child {
            border-radius: var(--radius) 0 0 var(--radius);
        }
        
        .date-selector .date-btn:last-child {
            border-radius: 0 var(--radius) var(--radius) 0;
        }
        
        .date-selector .date-btn.active {
            background-color: var(--primary-color);
            color: white;
            border-color: var(--primary-color);
        }
        
        .date-selector .date-input {
            padding: 6px 12px;
            border: 1px solid var(--border-color);
            border-left: none;
            border-right: none;
            text-align: center;
            width: 120px;
            cursor: pointer;
        }
        /* 改进的日期选择器样式 */
        
        .date-picker-container {
            position: relative;
            margin-right: 16px;
        }
        
        .date-picker-wrapper {
            display: flex;
            align-items: center;
            border: 1px solid var(--border-color);
            border-radius: var(--radius);
            background-color: white;
            overflow: hidden;
            box-shadow: var(--shadow);
            transition: all 0.3s ease;
        }
        
        .date-picker-wrapper:hover {
            border-color: var(--primary-color);
            box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.1);
        }
        
        .date-picker-input {
            padding: 10px 14px;
            border: none;
            outline: none;
            font-size: 14px;
            font-weight: 500;
            text-align: left;
            flex: 1;
            min-width: 110px;
            cursor: pointer;
            color: var(--text-primary);
            background-color: transparent;
            transition: all 0.3s;
        }
        
        .date-picker-input::placeholder {
            color: var(--text-tertiary);
            font-weight: normal;
        }
        
        .date-picker-icon {
            padding: 0 12px;
            height: 100%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: var(--primary-color);
            cursor: pointer;
            border-left: 1px solid transparent;
            background-color: rgba(24, 144, 255, 0.03);
            transition: all 0.3s;
        }
        
        .date-picker-icon i {
            font-size: 18px;
            transition: transform 0.3s;
        }
        
        .date-picker-wrapper:hover .date-picker-icon {
            background-color: rgba(24, 144, 255, 0.08);
        }
        
        .date-picker-icon:hover {
            color: var(--primary-hover);
        }
        
        .date-picker-icon:hover i {
            transform: scale(1.1);
        }
        /* 美化表单中日期选择器的样式 */
        
        .form-group .date-picker-wrapper {
            width: 100%;
            box-shadow: none;
        }
        
        .form-group .date-picker-input {
            padding: 8px 12px;
        }
        
        .form-group .date-picker-icon {
            padding: 0 10px;
            border-left: 1px solid var(--border-color);
        }
        
        .form-group .date-picker-wrapper:focus-within {
            border-color: var(--primary-color);
            box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
        }
        /* 改进的页面标题和操作区域样式 */
        
        .page-header {
            margin-bottom: 24px;
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding-bottom: 16px;
            border-bottom: 1px solid var(--border-color);
        }
        
        .action-group {
            display: flex;
            align-items: center;
            gap: 16px;
        }
        
        .record-table .amount-cell {
            font-weight: 500;
        }
        
        .record-table .amount-income {
            color: var(--success-color);
        }
        
        .record-table .amount-expense {
            color: var(--danger-color);
        }
        /* 进度条 */
        
        .progress {
            height: 8px;
            background-color: #f5f5f5;
            border-radius: 4px;
            overflow: hidden;
            margin-top: 4px;
        }
        
        .progress-bar {
            height: 100%;
            background-color: var(--primary-color);
            border-radius: 4px;
            transition: width 0.3s;
        }
        /* 日历弹出框样式 */
        
        .date-calendar {
            position: absolute;
            top: calc(100% + 8px);
            left: 0;
            z-index: 50;
            background-color: white;
            border-radius: var(--radius);
            box-shadow: 0 6px 16px -8px rgba(0, 0, 0, 0.08), 0 9px 28px 0 rgba(0, 0, 0, 0.05), 0 12px 48px 16px rgba(0, 0, 0, 0.03);
            padding: 16px;
            display: none;
            width: 320px;
            animation: fadeInDown 0.2s ease;
        }
        
        @keyframes fadeInDown {
            from {
                opacity: 0;
                transform: translateY(-10px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }
        
        .date-calendar.show {
            display: block;
        }
        
        .calendar-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
        }
        
        .calendar-title {
            font-weight: 600;
            font-size: 16px;
            color: var(--text-primary);
        }
        
        .calendar-nav {
            display: flex;
            gap: 8px;
        }
        
        .calendar-nav-btn {
            width: 28px;
            height: 28px;
            display: flex;
            align-items: center;
            justify-content: center;
            background: none;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            color: var(--text-secondary);
            transition: all 0.2s;
        }
        
        .calendar-nav-btn:hover {
            background-color: rgba(24, 144, 255, 0.1);
            color: var(--primary-color);
        }
        
        .calendar-weekdays {
            display: grid;
            grid-template-columns: repeat(7, 1fr);
            gap: 4px;
            margin-bottom: 8px;
            border-bottom: 1px solid var(--border-color);
            padding-bottom: 8px;
        }
        
        .calendar-weekday {
            text-align: center;
            font-size: 13px;
            color: var(--text-secondary);
            padding: 8px 0;
            font-weight: 500;
        }
        
        .calendar-days {
            display: grid;
            grid-template-columns: repeat(7, 1fr);
            gap: 6px;
        }
        
        .calendar-day {
            text-align: center;
            padding: 10px 0;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            color: var(--text-primary);
            transition: all 0.2s;
        }
        
        .calendar-day:hover {
            background-color: rgba(24, 144, 255, 0.1);
        }
        
        .calendar-day.current {
            background-color: var(--primary-color);
            color: white;
            font-weight: 500;
            box-shadow: 0 2px 6px rgba(24, 144, 255, 0.2);
        }
        
        .calendar-day.other-month {
            color: var(--text-tertiary);
        }
        
        .calendar-footer {
            margin-top: 16px;
            display: flex;
            justify-content: space-between;
            border-top: 1px solid var(--border-color);
            padding-top: 12px;
        }
        
        .calendar-today-btn {
            background: none;
            border: none;
            color: var(--primary-color);
            cursor: pointer;
            font-size: 14px;
            padding: 4px 10px;
            border-radius: 4px;
            transition: all 0.2s;
            font-weight: 500;
        }
        
        .calendar-today-btn:hover {
            background-color: rgba(24, 144, 255, 0.1);
            color: var(--primary-hover);
        }
        /* 日历快捷选择按钮样式 */
        
        .calendar-shortcuts {
            margin-top: 12px;
            border-top: 1px solid var(--border-color);
            padding-top: 12px;
        }
        
        .shortcuts-title {
            font-size: 13px;
            color: var(--text-secondary);
            margin-bottom: 8px;
            font-weight: 500;
        }
        
        .shortcuts-buttons {
            display: flex;
            flex-wrap: wrap;
            gap: 8px;
        }
        
        .shortcut-btn {
            background: none;
            border: 1px solid var(--border-color);
            border-radius: 4px;
            padding: 4px 8px;
            font-size: 12px;
            cursor: pointer;
            transition: all 0.2s;
            color: var(--text-secondary);
        }
        
        .shortcut-btn:hover {
            border-color: var(--primary-color);
            color: var(--primary-color);
            background-color: rgba(24, 144, 255, 0.05);
        }
        /* 添加新的数据仪表盘样式到style标签内，放在原有样式的最后 */
        
        .data-dashboard {
            margin-bottom: 32px;
        }
        /* 核心指标卡片样式 */
        
        .metric-cards {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 20px;
            margin-bottom: 24px;
        }
        
        .metric-card {
            background: white;
            border-radius: 12px;
            padding: 20px;
            display: flex;
            align-items: flex-start;
            box-shadow: 0 2px 12px rgba(0, 0, 0, 0.06);
            transition: transform 0.3s, box-shadow 0.3s;
            border: 1px solid rgba(0, 0, 0, 0.05);
            overflow: hidden;
            position: relative;
        }
        
        .metric-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
        }
        
        .metric-card::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 6px;
            height: 100%;
            background: var(--primary-color);
            opacity: 0;
            transition: opacity 0.3s;
        }
        
        .metric-card:hover::after {
            opacity: 1;
        }
        
        .metric-card.highlight {
            background: linear-gradient(135deg, rgba(24, 144, 255, 0.05) 0%, rgba(24, 144, 255, 0.01) 100%);
            border: 1px solid rgba(24, 144, 255, 0.15);
        }
        
        .metric-icon {
            width: 48px;
            height: 48px;
            border-radius: 12px;
            background: rgba(24, 144, 255, 0.1);
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 16px;
            flex-shrink: 0;
        }
        
        .metric-icon i {
            font-size: 24px;
            color: var(--primary-color);
        }
        
        .metric-icon.income {
            background: rgba(82, 196, 26, 0.1);
        }
        
        .metric-icon.income i {
            color: var(--success-color);
        }
        
        .metric-icon.expense {
            background: rgba(255, 77, 79, 0.1);
        }
        
        .metric-icon.expense i {
            color: var(--danger-color);
        }
        
        .metric-content {
            flex: 1;
        }
        
        .metric-title {
            color: var(--text-secondary);
            font-size: 14px;
            margin-bottom: 6px;
            font-weight: 500;
        }
        
        .metric-value {
            font-size: 28px;
            font-weight: 700;
            color: var(--text-primary);
            margin-bottom: 4px;
            line-height: 1.1;
            letter-spacing: -0.5px;
        }
        
        .metric-value.income {
            color: var(--success-color);
        }
        
        .metric-value.expense {
            color: var(--danger-color);
        }
        
        .metric-trend {
            display: flex;
            align-items: center;
            font-size: 12px;
            color: var(--text-tertiary);
        }
        
        .metric-trend.positive {
            color: var(--success-color);
        }
        
        .metric-trend.negative {
            color: var(--danger-color);
        }
        
        .metric-trend i {
            font-size: 18px;
            margin-right: 2px;
        }
        
        .trend-period {
            margin-left: 6px;
            color: var(--text-tertiary);
        }
        /* 图表区域样式 */
        
        .chart-section {
            background: white;
            border-radius: 12px;
            box-shadow: 0 2px 12px rgba(0, 0, 0, 0.06);
            border: 1px solid rgba(0, 0, 0, 0.05);
            padding: 20px;
            margin-bottom: 24px;
        }
        
        .chart-tabs {
            display: flex;
            margin-bottom: 20px;
            border-bottom: 1px solid var(--border-color);
            padding-bottom: 10px;
        }
        
        .chart-tab {
            background: none;
            border: none;
            padding: 8px 16px;
            font-size: 14px;
            color: var(--text-secondary);
            cursor: pointer;
            position: relative;
            font-weight: 500;
        }
        
        .chart-tab.active {
            color: var(--primary-color);
        }
        
        .chart-tab.active::after {
            content: '';
            position: absolute;
            bottom: -11px;
            left: 0;
            width: 100%;
            height: 2px;
            background-color: var(--primary-color);
        }
        
        .chart-container {
            height: 300px;
            position: relative;
        }
        
        .chart-placeholder {
            width: 100%;
            height: 100%;
            display: flex;
            align-items: flex-end;
            padding-bottom: 30px;
        }
        
        .chart-bars {
            width: 100%;
            display: flex;
            justify-content: space-around;
            height: 100%;
            padding: 0 20px;
        }
        
        .chart-bar-group {
            display: flex;
            flex-direction: column;
            align-items: center;
            width: 60px;
            position: relative;
            opacity: 0.7;
            transition: opacity 0.3s;
        }
        
        .chart-bar-group.active,
        .chart-bar-group:hover {
            opacity: 1;
        }
        
        .chart-bar-income {
            width: 24px;
            background: linear-gradient(to top, var(--success-color), #95de64);
            border-radius: 3px 3px 0 0;
            margin-bottom: 1px;
        }
        
        .chart-bar-expense {
            width: 24px;
            background: linear-gradient(to top, var(--danger-color), #ff7875);
            border-radius: 3px 3px 0 0;
        }
        
        .chart-bar-label {
            position: absolute;
            bottom: -25px;
            font-size: 12px;
            color: var(--text-secondary);
        }
        
        .chart-legend {
            display: flex;
            justify-content: center;
            margin-top: 10px;
        }
        
        .legend-item {
            display: flex;
            align-items: center;
            margin: 0 12px;
        }
        
        .legend-color {
            width: 12px;
            height: 12px;
            border-radius: 3px;
            margin-right: 6px;
        }
        
        .legend-color.income {
            background-color: var(--success-color);
        }
        
        .legend-color.expense {
            background-color: var(--danger-color);
        }
        
        .legend-text {
            font-size: 12px;
            color: var(--text-secondary);
        }
        /* 记录列表样式优化 */
        
        .records-section {
            background: white;
            border-radius: 12px;
            box-shadow: 0 2px 12px rgba(0, 0, 0, 0.06);
            border: 1px solid rgba(0, 0, 0, 0.05);
            overflow: hidden;
        }
        
        .records-header {
            padding: 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            border-bottom: 1px solid var(--border-color);
        }
        
        .records-title {
            font-size: 16px;
            font-weight: 600;
            color: var(--text-primary);
            margin: 0;
        }
        
        .records-filter {
            display: flex;
            gap: 12px;
        }
        
        .filter-item {
            position: relative;
        }
        
        .filter-select {
            padding: 8px 28px 8px 12px;
            border: 1px solid var(--border-color);
            border-radius: 6px;
            font-size: 13px;
            appearance: none;
            background: white url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='12' height='12' viewBox='0 0 12 12'%3E%3Cpath fill='%23595959' d='M6 8.825l-4.475-4.5L2.45 3.4 6 6.975 9.55 3.4l.925.925L6 8.825z'/%3E%3C/svg%3E") no-repeat right 10px center;
            min-width: 120px;
        }
        
        .search-box {
            display: flex;
            align-items: center;
            border: 1px solid var(--border-color);
            border-radius: 6px;
            overflow: hidden;
        }
        
        .search-input {
            border: none;
            padding: 8px 12px;
            font-size: 13px;
            outline: none;
            width: 160px;
        }
        
        .search-btn {
            background: none;
            border: none;
            padding: 0 12px;
            cursor: pointer;
            height: 100%;
            display: flex;
            align-items: center;
            color: var(--text-secondary);
        }
        
        .search-btn:hover {
            color: var(--primary-color);
        }
        
        .records-footer {
            padding: 16px 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            border-top: 1px solid var(--border-color);
        }
        
        .pagination {
            display: flex;
            align-items: center;
            gap: 6px;
        }
        
        .pagination-btn {
            min-width: 32px;
            height: 32px;
            border: 1px solid var(--border-color);
            background: white;
            border-radius: 4px;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: all 0.3s;
            font-size: 13px;
        }
        
        .pagination-btn:hover {
            border-color: var(--primary-color);
            color: var(--primary-color);
        }
        
        .pagination-btn.active {
            background-color: var(--primary-color);
            color: white;
            border-color: var(--primary-color);
        }
        
        .pagination-ellipsis {
            font-size: 14px;
        }
        
        .page-size {
            display: flex;
            align-items: center;
            gap: 8px;
            font-size: 13px;
            color: var(--text-secondary);
        }
        
        .page-size-select {
            padding: 6px 24px 6px 8px;
            border: 1px solid var(--border-color);
            border-radius: 4px;
            appearance: none;
            background: white url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='12' height='12' viewBox='0 0 12 12'%3E%3Cpath fill='%23595959' d='M6 8.825l-4.475-4.5L2.45 3.4 6 6.975 9.55 3.4l.925.925L6 8.825z'/%3E%3C/svg%3E") no-repeat right 8px center;
            font-size: 13px;
        }
    </style>
    <!-- 引入字体图标 -->
    <link href="https://cdn.jsdelivr.net/npm/remixicon@2.5.0/fonts/remixicon.css" rel="stylesheet">
</head>

<body>
    <div class="layout">
        <!-- 侧边栏 -->
        <aside class="sidebar">
            <div class="sidebar-logo">
                <img src="data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='32' height='32' viewBox='0 0 24 24' fill='none' stroke='%231890ff' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpath d='M2 9l10 4.5L22 9'/%3E%3Cpath d='M2 14l10 4.5L22 14'/%3E%3Cpath d='M2 4l10 4.5L22 4'/%3E%3C/svg%3E"
                    alt="Logo">
                <h1>电商管理系统</h1>
            </div>
            <nav class="menu">
                <div class="menu-item">
                    <i class="ri-dashboard-line"></i>
                    <span>仪表盘</span>
                </div>
                <div class="menu-item">
                    <i class="ri-store-2-line"></i>
                    <span>商品管理</span>
                </div>
                <div class="menu-item">
                    <i class="ri-user-line"></i>
                    <span>用户管理</span>
                </div>
                <div class="menu-item">
                    <i class="ri-shopping-cart-line"></i>
                    <span>订单管理</span>
                </div>
                <div class="menu-item active">
                    <i class="ri-book-2-line"></i>
                    <span>财务管理</span>
                </div>
                <div class="menu-item">
                    <i class="ri-settings-line"></i>
                    <span>系统设置</span>
                </div>
            </nav>
        </aside>

        <!-- 主内容区域 -->
        <div class="main-content">
            <header class="header">
                <div class="header-title">财务管理</div>
                <div class="user-info">
                    <div class="user-avatar">管</div>
                    <span class="username">管理员</span>
                    <i class="ri-logout-box-r-line"></i>
                </div>
            </header>

            <main class="page-content">
                <div class="page-header">
                    <h1 class="page-title">手工科目补录</h1>
                    <div class="action-group">
                        <div class="date-picker-container">
                            <div class="date-picker-wrapper">
                                <input type="text" class="date-picker-input" id="currentDate" readonly>
                                <div class="date-picker-icon" id="calendarToggle">
                                    <i class="ri-calendar-line"></i>
                                </div>
                            </div>
                            <div class="date-calendar" id="dateCalendar">
                                <div class="calendar-header">
                                    <div class="calendar-title" id="calendarTitle"></div>
                                    <div class="calendar-nav">
                                        <button class="calendar-nav-btn" id="prevMonth">
                                            <i class="ri-arrow-left-s-line"></i>
                                        </button>
                                        <button class="calendar-nav-btn" id="nextMonth">
                                            <i class="ri-arrow-right-s-line"></i>
                                        </button>
                                    </div>
                                </div>
                                <div class="calendar-weekdays">
                                    <div class="calendar-weekday">日</div>
                                    <div class="calendar-weekday">一</div>
                                    <div class="calendar-weekday">二</div>
                                    <div class="calendar-weekday">三</div>
                                    <div class="calendar-weekday">四</div>
                                    <div class="calendar-weekday">五</div>
                                    <div class="calendar-weekday">六</div>
                                </div>
                                <div class="calendar-days" id="calendarDays">
                                    <!-- 日历日期将通过JS动态生成 -->
                                </div>
                                <div class="calendar-footer">
                                    <button class="calendar-today-btn" id="todayBtn">今天</button>
                                </div>
                                <div class="calendar-shortcuts">
                                    <div class="shortcuts-title">快捷选择</div>
                                    <div class="shortcuts-buttons">
                                        <button class="shortcut-btn" data-days="0">今天</button>
                                        <button class="shortcut-btn" data-days="-1">昨天</button>
                                        <button class="shortcut-btn" data-days="-7">一周前</button>
                                        <button class="shortcut-btn" data-days="-30">一月前</button>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <button class="btn btn-secondary" id="exportDataBtn">
                            <i class="ri-download-line"></i>导出数据
                        </button>
                        <button class="btn btn-secondary" id="importDataBtn">
                            <i class="ri-upload-line"></i>导入数据
                        </button>
                        <button class="btn btn-primary" id="addRecordBtn">
                            <i class="ri-add-line"></i>新增
                        </button>
                    </div>
                </div>

                <!-- 补录记录卡片 -->
                <div class="card">
                    <div class="card-header">
                        <span>补录记录</span>
                        <small id="todayDate"></small>
                    </div>
                    <div class="card-body">
                        <!-- 今日概览统计 - 新UI设计 -->
                        <div class="data-dashboard">
                            <!-- 核心指标卡片 -->
                            <div class="metric-cards">
                                <div class="metric-card">
                                    <div class="metric-icon">
                                        <i class="ri-file-list-3-line"></i>
                                    </div>
                                    <div class="metric-content">
                                        <div class="metric-title">补录总笔数</div>
                                        <div class="metric-value" id="totalRecords">8</div>
                                        <div class="metric-trend positive">
                                            <i class="ri-arrow-up-s-line"></i>
                                            <span>4.2%</span>
                                            <span class="trend-period">较上日</span>
                                        </div>
                                    </div>
                                </div>

                                <div class="metric-card highlight">
                                    <div class="metric-icon income">
                                        <i class="ri-money-cny-circle-line"></i>
                                    </div>
                                    <div class="metric-content">
                                        <div class="metric-title">收入补差总额</div>
                                        <div class="metric-value income" id="totalIncome">¥25,890.00</div>
                                        <div class="metric-trend positive">
                                            <i class="ri-arrow-up-s-line"></i>
                                            <span>6.3%</span>
                                            <span class="trend-period">较上日</span>
                                        </div>
                                    </div>
                                </div>

                                <div class="metric-card">
                                    <div class="metric-icon expense">
                                        <i class="ri-money-cny-circle-line"></i>
                                    </div>
                                    <div class="metric-content">
                                        <div class="metric-title">支出补差总额</div>
                                        <div class="metric-value expense" id="totalExpense">¥12,450.75</div>
                                        <div class="metric-trend negative">
                                            <i class="ri-arrow-down-s-line"></i>
                                            <span>2.1%</span>
                                            <span class="trend-period">较上日</span>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- 数据图表区域 -->
                            <div class="chart-section">
                                <div class="chart-tabs">
                                    <button class="chart-tab active">收支概览</button>
                                    <button class="chart-tab">店铺分布</button>
                                    <button class="chart-tab">科目分类</button>
                                </div>

                                <div class="chart-container">
                                    <div class="chart-area" id="overviewChart">
                                        <!-- 这里用Canvas或SVG实际绘制图表 -->
                                        <div class="chart-placeholder">
                                            <div class="chart-bars">
                                                <div class="chart-bar-group">
                                                    <div class="chart-bar-income" style="height: 80%;"></div>
                                                    <div class="chart-bar-expense" style="height: 40%;"></div>
                                                    <div class="chart-bar-label">6-3</div>
                                                </div>
                                                <div class="chart-bar-group">
                                                    <div class="chart-bar-income" style="height: 65%;"></div>
                                                    <div class="chart-bar-expense" style="height: 35%;"></div>
                                                    <div class="chart-bar-label">6-4</div>
                                                </div>
                                                <div class="chart-bar-group">
                                                    <div class="chart-bar-income" style="height: 90%;"></div>
                                                    <div class="chart-bar-expense" style="height: 50%;"></div>
                                                    <div class="chart-bar-label">6-5</div>
                                                </div>
                                                <div class="chart-bar-group">
                                                    <div class="chart-bar-income" style="height: 75%;"></div>
                                                    <div class="chart-bar-expense" style="height: 30%;"></div>
                                                    <div class="chart-bar-label">6-6</div>
                                                </div>
                                                <div class="chart-bar-group">
                                                    <div class="chart-bar-income" style="height: 60%;"></div>
                                                    <div class="chart-bar-expense" style="height: 45%;"></div>
                                                    <div class="chart-bar-label">6-7</div>
                                                </div>
                                                <div class="chart-bar-group">
                                                    <div class="chart-bar-income" style="height: 70%;"></div>
                                                    <div class="chart-bar-expense" style="height: 25%;"></div>
                                                    <div class="chart-bar-label">6-8</div>
                                                </div>
                                                <div class="chart-bar-group active">
                                                    <div class="chart-bar-income" style="height: 85%;"></div>
                                                    <div class="chart-bar-expense" style="height: 45%;"></div>
                                                    <div class="chart-bar-label">今日</div>
                                                </div>
                                            </div>
                                        </div>

                                        <div class="chart-legend">
                                            <div class="legend-item">
                                                <span class="legend-color income"></span>
                                                <span class="legend-text">收入</span>
                                            </div>
                                            <div class="legend-item">
                                                <span class="legend-color expense"></span>
                                                <span class="legend-text">支出</span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 补录记录表格优化 -->
                        <div class="records-section">
                            <div class="records-header">
                                <h3 class="records-title">补录明细</h3>
                                <div class="records-filter">
                                    <div class="filter-item">
                                        <select class="filter-select" id="typeFilter">
                                            <option value="">全部类型</option>
                                            <option value="income">收入</option>
                                            <option value="expense">支出</option>
                                        </select>
                                    </div>
                                    <div class="filter-item">
                                        <select class="filter-select" id="shopFilter">
                                            <option value="">全部店铺</option>
                                            <option value="shop1">旗舰店</option>
                                            <option value="shop4">天猫店</option>
                                            <option value="shop3">京东店</option>
                                            <option value="shop5">拼多多店</option>
                                        </select>
                                    </div>
                                    <div class="search-box">
                                        <input type="text" placeholder="搜索关键词" class="search-input" id="recordSearch">
                                        <button class="search-btn"><i class="ri-search-line"></i></button>
                                    </div>
                                </div>
                            </div>

                            <div class="table-container">
                                <table class="record-table" id="recordTable">
                                    <thead>
                                        <tr>
                                            <th>序号</th>
                                            <th>报表类型</th>
                                            <th>店铺名称</th>
                                            <th>收支类型</th>
                                            <th>科目名称</th>
                                            <th>金额</th>
                                            <th>日期</th>
                                            <th>备注</th>
                                            <th>操作时间</th>
                                            <th>操作人员</th>
                                            <th>操作</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                            <td>1</td>
                                            <td>运营日报填报</td>
                                            <td>旗舰店</td>
                                            <td><span class="tag tag-blue">收入</span></td>
                                            <td>销售收入</td>
                                            <td class="amount-cell amount-income">¥8,650.00</td>
                                            <td>6月9日销售</td>
                                            <td>2025-06-09 09:15:20</td>
                                            <td>管理员</td>
                                            <td class="table-action-group">
                                                <button class="btn btn-text btn-danger-text delete-btn" data-id="1">
                                                        <i class="ri-delete-bin-line"></i>删除
                                                    </button>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td>2</td>
                                            <td>财报填报</td>
                                            <td>天猫店</td>
                                            <td><span class="tag tag-blue">收入</span></td>
                                            <td>销售收入</td>
                                            <td class="amount-cell amount-income">¥12,340.00</td>
                                            <td>6月促销活动</td>
                                            <td>2025-06-09 10:05:45</td>
                                            <td>财务主管</td>
                                            <td class="table-action-group">
                                                <button class="btn btn-text btn-danger-text delete-btn" data-id="2">
                                                        <i class="ri-delete-bin-line"></i>删除
                                                    </button>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td>3</td>
                                            <td>费用填报</td>
                                            <td>旗舰店</td>
                                            <td>物流费用</td>
                                            <td class="amount-cell amount-expense">¥3,250.75</td>
                                            <td>快递费用</td>
                                            <td>2025-06-09 11:30:18</td>
                                            <td>管理员</td>
                                            <td class="table-action-group">
                                                <button class="btn btn-text btn-danger-text delete-btn" data-id="3">
                                                        <i class="ri-delete-bin-line"></i>删除
                                                    </button>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td>4</td>
                                            <td>客服日报填报</td>
                                            <td>京东店</td>
                                            <td><span class="tag tag-blue">收入</span></td>
                                            <td>会员费</td>
                                            <td class="amount-cell amount-income">¥2,400.00</td>
                                            <td>会员续费收入</td>
                                            <td>2025-06-09 13:22:10</td>
                                            <td>客服主管</td>
                                            <td class="table-action-group">
                                                <button class="btn btn-text btn-danger-text delete-btn" data-id="4">
                                                    <i class="ri-delete-bin-line"></i>删除
                                                </button>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td>5</td>
                                            <td>财报填报</td>
                                            <td>拼多多店</td>
                                            <td><span class="tag tag-blue">收入</span></td>
                                            <td>平台佣金</td>
                                            <td class="amount-cell amount-income">¥2,500.00</td>
                                            <td>平台返佣</td>
                                            <td>2025-06-09 14:15:33</td>
                                            <td>财务主管</td>
                                            <td class="table-action-group">
                                                <button class="btn btn-text btn-danger-text delete-btn" data-id="5">
                                                        <i class="ri-delete-bin-line"></i>删除
                                                    </button>
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>

                        <div class="records-footer">
                            <div class="pagination">
                                <button class="pagination-btn"><i class="ri-arrow-left-s-line"></i></button>
                                <button class="pagination-btn active">1</button>
                                <button class="pagination-btn">2</button>
                                <button class="pagination-btn">3</button>
                                <span class="pagination-ellipsis">...</span>
                                <button class="pagination-btn">10</button>
                                <button class="pagination-btn"><i class="ri-arrow-right-s-line"></i></button>
                            </div>

                            <div class="page-size">
                                <span>每页显示:</span>
                                <select class="page-size-select">
                                    <option value="10">10条</option>
                                    <option value="20">20条</option>
                                    <option value="50">50条</option>
                                </select>
                            </div>
                        </div>
                    </div>
                </div>
            </main>
        </div>
    </div>

    <!-- 新增补录模态框 -->
    <div class="modal-backdrop" id="addRecordModal">
        <div class="modal">
            <div class="modal-header">
                <h3 class="modal-title">手工科目新增</h3>
                <button class="modal-close" id="closeAddModal">
                    <i class="ri-close-line"></i>
                </button>
            </div>
            <div class="modal-body">
                <form id="subjectRecordForm">
                    <div class="form-row">
                        <div class="form-col">
                            <div class="form-group">
                                <label class="form-label">日期</label>
                                <div class="date-picker-wrapper">
                                    <input type="text" class="date-picker-input form-control" id="recordDate" readonly placeholder="请选择日期">
                                    <div class="date-picker-icon" id="recordDateToggle">
                                        <i class="ri-calendar-line"></i>
                                    </div>
                                </div>
                                <div class="form-error" id="recordDateError"></div>
                            </div>
                        </div>
                        <div class="form-col">
                            <div class="form-group">
                                <label class="form-label">报表类型</label>
                                <select class="form-control form-select" id="reportType">
                                    <option value="">请选择报表类型</option>
                                    <option value="operation">运营日报填报</option>
                                    <option value="customer">客服日报填报</option>
                                    <option value="finance">财报填报</option>
                                    <option value="expense">费用填报</option>
                                    <option value="sales">销售填报</option>
                                    <option value="purchase">采购填报</option>
                                    <option value="promotion">推广费用填报</option>
                                </select>
                                <div class="form-error" id="reportTypeError"></div>
                            </div>
                        </div>
                    </div>
                    <div class="form-row">
                        <div class="form-col">
                            <div class="form-group">
                                <label class="form-label">店铺名称</label>
                                <select class="form-control form-select" id="shopName">
                                    <option value="">请选择店铺</option>
                                    <option value="shop1">旗舰店</option>
                                    <option value="shop2">专卖店</option>
                                    <option value="shop3">京东店</option>
                                    <option value="shop4">天猫店</option>
                                    <option value="shop5">拼多多店</option>
                                    <option value="shop6">抖音店</option>
                                    <option value="shop7">快手店</option>
                                    <option value="shop8">小红书店</option>
                                    <option value="shop9">国际站</option>
                                    <option value="shop10">线下门店</option>
                                    <option value="shop11">社区团购</option>
                                    <option value="shop12">其他渠道</option>
                                </select>
                                <div class="form-error" id="shopNameError"></div>
                            </div>
                        </div>
                        <div class="form-col">
                            <div class="form-group">
                                <label class="form-label">收支类型</label>
                                <select class="form-control form-select" id="incomeType">
                                    <option value="">请选择收支类型</option>
                                    <option value="income">收入补差</option>
                                    <option value="expense">支出补差</option>
                                </select>
                                <div class="form-error" id="incomeTypeError"></div>
                            </div>
                        </div>
                    </div>
                    <div class="form-row">
                        <div class="form-col">
                            <div class="form-group">
                                <label class="form-label">科目名称</label>
                                <select class="form-control form-select" id="subjectName" disabled>
                                    <option value="">请先选择收支类型</option>
                                </select>
                                <div class="form-error" id="subjectNameError"></div>
                            </div>
                        </div>
                        <div class="form-col">
                            <div class="form-group">
                                <label class="form-label">费用金额</label>
                                <input type="number" class="form-control" id="amount" step="0.01" placeholder="请输入金额">
                                <div class="form-error" id="amountError"></div>
                            </div>
                        </div>
                    </div>
                    <div class="form-row">
                        <div class="form-col">
                            <div class="form-group">
                                <label class="form-label">备注</label>
                                <input type="text" class="form-control" id="remark" placeholder="可选填写备注信息">
                            </div>
                        </div>
                        <div class="form-col">
                            <!-- 为了保持布局的对称性，这里添加一个空列 -->
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button class="btn btn-secondary" id="resetBtn">重置</button>
                <button class="btn btn-primary" id="submitBtn">提交</button>
            </div>
        </div>
    </div>

    <!-- 删除确认模态框 -->
    <div class="modal-backdrop" id="deleteConfirmModal">
        <div class="modal modal-confirm">
            <div class="modal-header">
                <h3 class="modal-title">确认删除</h3>
                <button class="modal-close" id="closeDeleteModal">
                            <i class="ri-close-line"></i>
                        </button>
            </div>
            <div class="modal-body">
                <div class="modal-confirm-icon">
                    <i class="ri-error-warning-line"></i>
                </div>
                <div class="modal-confirm-content">
                    <h4 class="modal-confirm-title">确定要删除此补录记录吗？</h4>
                    <p class="modal-confirm-description">删除后数据将无法恢复，请确认是否继续。</p>
                </div>
            </div>
            <div class="modal-footer">
                <button class="btn btn-secondary" id="cancelDeleteBtn">取消</button>
                <button class="btn btn-primary" id="confirmDeleteBtn">确认删除</button>
            </div>
        </div>
    </div>

    <!-- 导入数据模态框 -->
    <div class="modal-backdrop" id="importDataModal">
        <div class="modal">
            <div class="modal-header">
                <h3 class="modal-title">导入数据</h3>
                <button class="modal-close" id="closeImportModal">
                    <i class="ri-close-line"></i>
                </button>
            </div>
            <div class="modal-body">
                <div class="form-group">
                    <label class="form-label">选择JSON文件</label>
                    <input type="file" class="form-control" id="importFileInput" accept=".json,application/json">
                    <div class="form-help">请选择之前导出的JSON格式数据文件</div>
                    <div class="form-error" id="importFileError"></div>
                </div>
                <div class="form-group">
                    <label class="form-label">导入选项</label>
                    <div class="form-check">
                        <input type="radio" name="importOption" id="importOptionReplace" value="replace" checked>
                        <label for="importOptionReplace">替换现有数据</label>
                    </div>
                    <div class="form-check">
                        <input type="radio" name="importOption" id="importOptionMerge" value="merge">
                        <label for="importOptionMerge">合并到现有数据</label>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button class="btn btn-secondary" id="cancelImportBtn">取消</button>
                <button class="btn btn-primary" id="confirmImportBtn">确认导入</button>
            </div>
        </div>
    </div>

    <!-- 通知提示容器 -->
    <div class="toast-container" id="toastContainer"></div>

    <!-- JavaScript 代码 -->
    <script>
        // 当前登录用户信息（模拟）
        const currentUser = {
            id: 1001,
            name: '管理员',
            role: '系统管理员'
        };

        // 数据持久化相关的工具函数
        const storageUtils = {
            // 存储键名
            STORAGE_KEY: 'financial_record_data',

            // 保存数据到localStorage
            saveData: function(data) {
                try {
                    const serializedData = JSON.stringify(data);
                    localStorage.setItem(this.STORAGE_KEY, serializedData);
                    return true;
                } catch (error) {
                    console.error('保存数据失败:', error);
                    showToast('error', '保存失败', '数据保存到本地存储时出错');
                    return false;
                }
            },

            // 从localStorage加载数据
            loadData: function() {
                try {
                    const serializedData = localStorage.getItem(this.STORAGE_KEY);
                    if (serializedData === null) {
                        return null; // 没有保存的数据
                    }
                    return JSON.parse(serializedData);
                } catch (error) {
                    console.error('加载数据失败:', error);
                    showToast('error', '加载失败', '从本地存储加载数据时出错');
                    return null;
                }
            },

            // 清除保存的数据
            clearData: function() {
                try {
                    localStorage.removeItem(this.STORAGE_KEY);
                    return true;
                } catch (error) {
                    console.error('清除数据失败:', error);
                    return false;
                }
            },

            // 导出数据为JSON文件
            exportData: function(data) {
                try {
                    const serializedData = JSON.stringify(data, null, 2);
                    const blob = new Blob([serializedData], {
                        type: 'application/json'
                    });
                    const url = URL.createObjectURL(blob);

                    // 创建下载链接
                    const downloadLink = document.createElement('a');
                    const now = new Date();
                    const fileName = `财务记录_${now.getFullYear()}${String(now.getMonth() + 1).padStart(2, '0')}${String(now.getDate()).padStart(2, '0')}.json`;

                    downloadLink.href = url;
                    downloadLink.download = fileName;
                    document.body.appendChild(downloadLink);
                    downloadLink.click();
                    document.body.removeChild(downloadLink);

                    // 释放URL对象
                    setTimeout(() => {
                        URL.revokeObjectURL(url);
                    }, 100);

                    return true;
                } catch (error) {
                    console.error('导出数据失败:', error);
                    showToast('error', '导出失败', '导出数据时出错');
                    return false;
                }
            },

            // 导入数据
            importData: function(fileInput, callback) {
                if (!fileInput.files || fileInput.files.length === 0) {
                    showToast('error', '导入失败', '请选择要导入的文件');
                    return;
                }

                const file = fileInput.files[0];
                if (file.type !== 'application/json' && !file.name.endsWith('.json')) {
                    showToast('error', '导入失败', '请选择JSON格式的文件');
                    return;
                }

                const reader = new FileReader();
                reader.onload = function(event) {
                    try {
                        const data = JSON.parse(event.target.result);
                        callback(data);
                    } catch (error) {
                        console.error('解析导入数据失败:', error);
                        showToast('error', '导入失败', '解析导入数据时出错');
                    }
                };

                reader.onerror = function() {
                    showToast('error', '导入失败', '读取文件时出错');
                };

                reader.readAsText(file);
            }
        };

        // 模拟科目数据
        const subjectData = {
            income: [{
                id: 1001,
                name: '销售收入'
            }, {
                id: 1002,
                name: '会员费'
            }, {
                id: 1003,
                name: '广告收入'
            }, {
                id: 1004,
                name: '平台佣金'
            }, {
                id: 1005,
                name: '售后服务费'
            }, {
                id: 1006,
                name: '活动赞助'
            }],
            expense: [{
                id: 2001,
                name: '物流费用'
            }, {
                id: 2002,
                name: '广告投放费'
            }, {
                id: 2003,
                name: '平台服务费'
            }, {
                id: 2004,
                name: '商品采购'
            }, {
                id: 2005,
                name: '人工成本'
            }, {
                id: 2006,
                name: '办公费用'
            }, {
                id: 2007,
                name: '水电费'
            }, {
                id: 2008,
                name: '租赁费'
            }]
        };

        // 模拟补录记录数据
        let originalRecordData = []; // 存储原始完整数据
        let recordData = [{
            id: 1,
            reportType: 'operation',
            reportTypeName: '运营日报填报',
            shopId: 'shop1',
            shopName: '旗舰店',
            incomeType: 'income',
            subjectId: 1001,
            subjectName: '销售收入',
            amount: 8650,
            remark: '6月9日销售',
            recordDate: '2025-06-09', // 添加日期字段
            createTime: '2025-06-09 09:15:20',
            operator: '管理员'
        }, {
            id: 2,
            reportType: 'finance',
            reportTypeName: '财报填报',
            shopId: 'shop4',
            shopName: '天猫店',
            incomeType: 'income',
            subjectId: 1001,
            subjectName: '销售收入',
            amount: 12340,
            remark: '6月促销活动',
            recordDate: '2025-06-09', // 添加日期字段
            createTime: '2025-06-09 10:05:45',
            operator: '财务主管'
        }, {
            id: 3,
            reportType: 'expense',
            reportTypeName: '费用填报',
            shopId: 'shop1',
            shopName: '旗舰店',
            incomeType: 'expense',
            subjectId: 2001,
            subjectName: '物流费用',
            amount: 3250.75,
            remark: '快递费用',
            recordDate: '2025-06-08', // 添加日期字段，设置为前一天
            createTime: '2025-06-09 11:30:18',
            operator: '管理员'
        }, {
            id: 4,
            reportType: 'customer',
            reportTypeName: '客服日报填报',
            shopId: 'shop3',
            shopName: '京东店',
            incomeType: 'income',
            subjectId: 1002,
            subjectName: '会员费',
            amount: 2400,
            remark: '会员续费收入',
            recordDate: '2025-06-08', // 添加日期字段，设置为前一天
            createTime: '2025-06-09 13:22:10',
            operator: '客服主管'
        }, {
            id: 5,
            reportType: 'finance',
            reportTypeName: '财报填报',
            shopId: 'shop5',
            shopName: '拼多多店',
            incomeType: 'income',
            subjectId: 1004,
            subjectName: '平台佣金',
            amount: 2500,
            remark: '平台返佣',
            recordDate: '2025-06-07', // 添加日期字段，设置为前两天
            createTime: '2025-06-09 14:15:33',
            operator: '财务主管'
        }];

        // DOM 元素
        const addRecordModal = document.getElementById('addRecordModal');
        const addRecordBtn = document.getElementById('addRecordBtn');
        const closeAddModal = document.getElementById('closeAddModal');
        const reportTypeSelect = document.getElementById('reportType');
        const shopNameSelect = document.getElementById('shopName');
        const incomeTypeSelect = document.getElementById('incomeType');
        const subjectNameSelect = document.getElementById('subjectName');
        const amountInput = document.getElementById('amount');
        const remarkInput = document.getElementById('remark');

        const reportTypeError = document.getElementById('reportTypeError');
        const shopNameError = document.getElementById('shopNameError');
        const incomeTypeError = document.getElementById('incomeTypeError');
        const subjectNameError = document.getElementById('subjectNameError');
        const amountError = document.getElementById('amountError');
        const recordDateInput = document.getElementById('recordDate');
        const recordDateToggle = document.getElementById('recordDateToggle');
        const recordDateError = document.getElementById('recordDateError');

        const resetBtn = document.getElementById('resetBtn');
        const submitBtn = document.getElementById('submitBtn');

        const recordTable = document.getElementById('recordTable');
        const totalRecordsElement = document.getElementById('totalRecords');
        const totalIncomeElement = document.getElementById('totalIncome');
        const totalExpenseElement = document.getElementById('totalExpense');

        const deleteConfirmModal = document.getElementById('deleteConfirmModal');
        const closeDeleteModal = document.getElementById('closeDeleteModal');
        const cancelDeleteBtn = document.getElementById('cancelDeleteBtn');
        const confirmDeleteBtn = document.getElementById('confirmDeleteBtn');

        const toastContainer = document.getElementById('toastContainer');

        const currentDateInput = document.getElementById('currentDate');
        const calendarToggle = document.getElementById('calendarToggle');
        const dateCalendar = document.getElementById('dateCalendar');
        const calendarTitle = document.getElementById('calendarTitle');
        const calendarDays = document.getElementById('calendarDays');
        const prevMonthBtn = document.getElementById('prevMonth');
        const nextMonthBtn = document.getElementById('nextMonth');
        const todayBtn = document.getElementById('todayBtn');
        const todayDateElement = document.getElementById('todayDate');

        // 当前操作的记录ID
        let currentRecordId = null;
        // 日历当前显示的年月
        let currentCalendarDate = new Date();

        // 页面加载时初始化
        document.addEventListener('DOMContentLoaded', () => {
            // 尝试从localStorage加载数据
            const savedData = storageUtils.loadData();
            if (savedData && Array.isArray(savedData) && savedData.length > 0) {
                originalRecordData = savedData;
                showToast('info', '数据已加载', `已从本地存储加载${savedData.length}条记录`);
            } else {
                // 如果没有保存的数据，使用默认的模拟数据
                originalRecordData = [...recordData];
            }

            // 初始化日期选择器
            initDatePicker();

            // 初始化表格数据
            renderRecordTable();

            // 更新统计数据
            updateSummary();

            // 初始化新UI功能
            initChartTabs();
            initFilters();
            initPagination();

            // 添加收支类型选择事件
            incomeTypeSelect.addEventListener('change', function() {
                const selectedType = this.value;
                populateSubjects(selectedType);
            });

            // 重置按钮事件
            resetBtn.addEventListener('click', function() {
                document.getElementById('subjectRecordForm').reset();
                clearErrors();
                subjectNameSelect.disabled = true;
                subjectNameSelect.innerHTML = '<option value="">请先选择收支类型</option>';

                // 重新设置日期默认值为前一天
                const yesterday = new Date();
                yesterday.setDate(yesterday.getDate() - 1);
                yesterday.setHours(12, 0, 0, 0);

                const year = yesterday.getFullYear();
                const month = String(yesterday.getMonth() + 1).padStart(2, '0');
                const day = String(yesterday.getDate()).padStart(2, '0');
                const formattedDate = `${year}-${month}-${day}`;

                recordDateInput.value = formattedDate;
            });

            // 提交按钮事件
            submitBtn.addEventListener('click', submitRecord);

            // 添加记录日期选择事件
            recordDateInput.addEventListener('click', function() {
                showRecordDatePicker();
            });

            recordDateToggle.addEventListener('click', function() {
                showRecordDatePicker();
            });

            // 删除按钮事件（委托到表格）
            recordTable.addEventListener('click', function(e) {
                const target = e.target.closest('.delete-btn');
                if (target) {
                    const id = parseInt(target.getAttribute('data-id'));
                    currentRecordId = id;
                    openDeleteConfirmModal();
                }
            });

            // 绑定新增按钮事件
            addRecordBtn.addEventListener('click', function() {
                openAddRecordModal();
            });

            // 绑定关闭新增模态框事件
            closeAddModal.addEventListener('click', function() {
                addRecordModal.classList.remove('show');
            });

            // 绑定删除模态框关闭事件
            closeDeleteModal.addEventListener('click', function() {
                deleteConfirmModal.classList.remove('show');
            });

            cancelDeleteBtn.addEventListener('click', function() {
                deleteConfirmModal.classList.remove('show');
            });

            // 绑定确认删除按钮事件
            confirmDeleteBtn.addEventListener('click', deleteRecord);

            // 点击其他地方关闭日历
            document.addEventListener('click', function(e) {
                if (!dateCalendar.contains(e.target) && e.target !== calendarToggle && !calendarToggle.contains(e.target) && e.target !== currentDateInput) {
                    dateCalendar.classList.remove('show');
                }
            });

            // 点击其他地方关闭记录日期选择器
            document.addEventListener('click', function(e) {
                if (recordDatePicker && !recordDatePicker.contains(e.target) &&
                    e.target !== recordDateInput && e.target !== recordDateToggle &&
                    !recordDateToggle.contains(e.target)) {
                    recordDatePicker.classList.remove('show');
                }
            });

            // 导出数据按钮事件
            const exportDataBtn = document.getElementById('exportDataBtn');
            if (exportDataBtn) {
                exportDataBtn.addEventListener('click', function() {
                    if (originalRecordData.length === 0) {
                        showToast('info', '导出提示', '当前没有可导出的数据');
                        return;
                    }

                    storageUtils.exportData(originalRecordData);
                    showToast('success', '导出成功', '数据已成功导出为JSON文件');
                });
            }

            // 导入数据按钮事件
            const importDataBtn = document.getElementById('importDataBtn');
            const importDataModal = document.getElementById('importDataModal');
            const closeImportModal = document.getElementById('closeImportModal');
            const cancelImportBtn = document.getElementById('cancelImportBtn');
            const confirmImportBtn = document.getElementById('confirmImportBtn');
            const importFileInput = document.getElementById('importFileInput');
            const importFileError = document.getElementById('importFileError');

            if (importDataBtn && importDataModal) {
                importDataBtn.addEventListener('click', function() {
                    importFileInput.value = ''; // 清空文件输入
                    importFileError.textContent = ''; // 清空错误提示
                    importDataModal.classList.add('show');
                });

                closeImportModal.addEventListener('click', function() {
                    importDataModal.classList.remove('show');
                });

                cancelImportBtn.addEventListener('click', function() {
                    importDataModal.classList.remove('show');
                });

                confirmImportBtn.addEventListener('click', function() {
                    if (!importFileInput.files || importFileInput.files.length === 0) {
                        importFileError.textContent = '请选择要导入的文件';
                        return;
                    }

                    const importOption = document.querySelector('input[name="importOption"]:checked').value;

                    storageUtils.importData(importFileInput, function(data) {
                        if (!Array.isArray(data)) {
                            importFileError.textContent = '导入的数据格式不正确';
                            return;
                        }

                        if (importOption === 'replace') {
                            // 替换现有数据
                            originalRecordData = data;
                        } else {
                            // 合并数据，需要处理可能的ID冲突
                            const maxId = originalRecordData.length > 0 ?
                                Math.max(...originalRecordData.map(record => record.id)) : 0;

                            // 为导入的数据分配新ID，避免冲突
                            data.forEach((record, index) => {
                                record.id = maxId + index + 1;
                            });

                            // 合并数据
                            originalRecordData = [...originalRecordData, ...data];
                        }

                        // 保存到localStorage
                        storageUtils.saveData(originalRecordData);

                        // 更新UI
                        updateRecordsForDate(parseDate(currentDateInput.value));

                        // 关闭模态框
                        importDataModal.classList.remove('show');

                        // 显示成功通知
                        showToast('success', '导入成功', `已成功导入${data.length}条记录`);
                    });
                });

                // 点击模态框外部关闭
                window.addEventListener('click', function(e) {
                    if (e.target === importDataModal) {
                        importDataModal.classList.remove('show');
                    }
                });
            }
        });

        // 初始化日期选择器
        function initDatePicker() {
            // 获取前一天的日期（而不是今天）
            const yesterday = new Date();
            yesterday.setDate(yesterday.getDate() - 1);
            yesterday.setHours(12, 0, 0, 0); // 设置为中午12点避免时区问题

            const year = yesterday.getFullYear();
            const month = String(yesterday.getMonth() + 1).padStart(2, '0');
            const day = String(yesterday.getDate()).padStart(2, '0');
            const formattedDate = `${year}-${month}-${day}`;

            currentDateInput.value = formattedDate;
            todayDateElement.textContent = `${year}年${month}月${day}日`;

            // 设置初始日历日期为前一天所在的月份
            currentCalendarDate = new Date(year, yesterday.getMonth(), 1);
            currentCalendarDate.setHours(12, 0, 0, 0); // 设置为中午12点避免时区问题

            // 生成日历
            renderCalendar();

            // 初始化时立即过滤并显示默认日期(昨天)的数据
            updateRecordsForDate(yesterday);

            // 绑定日历切换按钮事件
            calendarToggle.addEventListener('click', function() {
                dateCalendar.classList.toggle('show');
                renderCalendar(); // 重新渲染日历确保正确显示
            });

            // 点击日期输入框也显示日历
            currentDateInput.addEventListener('click', function() {
                dateCalendar.classList.add('show');
                renderCalendar();
            });

            // 上个月按钮
            prevMonthBtn.addEventListener('click', function() {
                currentCalendarDate.setMonth(currentCalendarDate.getMonth() - 1);
                renderCalendar();
            });

            // 下个月按钮
            nextMonthBtn.addEventListener('click', function() {
                currentCalendarDate.setMonth(currentCalendarDate.getMonth() + 1);
                renderCalendar();
            });

            // 今天按钮
            todayBtn.addEventListener('click', function() {
                const today = new Date();
                today.setHours(12, 0, 0, 0); // 避免时区问题
                currentCalendarDate = new Date(today.getFullYear(), today.getMonth(), 1);
                currentCalendarDate.setHours(12, 0, 0, 0);
                selectDate(today);
                renderCalendar();
            });

            // 日期快捷选择按钮
            const shortcutBtns = document.querySelectorAll('.shortcut-btn');
            shortcutBtns.forEach(btn => {
                btn.addEventListener('click', function() {
                    const days = parseInt(this.getAttribute('data-days'));
                    const date = new Date();
                    date.setDate(date.getDate() + days);
                    date.setHours(12, 0, 0, 0); // 避免时区问题

                    // 更新日历视图到对应月份
                    currentCalendarDate = new Date(date.getFullYear(), date.getMonth(), 1);
                    currentCalendarDate.setHours(12, 0, 0, 0);

                    // 选择日期并关闭日历
                    selectDate(date);
                    renderCalendar();
                    dateCalendar.classList.remove('show');
                });
            });
        }

        // 渲染日历
        function renderCalendar() {
            // 设置日历标题
            const year = currentCalendarDate.getFullYear();
            const month = currentCalendarDate.getMonth() + 1;
            calendarTitle.textContent = `${year}年${month}月`;

            // 清空日历日期
            calendarDays.innerHTML = '';

            // 计算当月第一天是星期几
            const firstDay = new Date(year, month - 1, 1);
            firstDay.setHours(12, 0, 0, 0); // 避免时区问题
            const firstDayOfMonth = firstDay.getDay();

            // 计算当月有多少天
            const daysInMonth = new Date(year, month, 0).getDate();

            // 计算上个月有多少天
            const daysInPrevMonth = new Date(year, month - 1, 0).getDate();

            // 生成上个月的日期
            for (let i = firstDayOfMonth - 1; i >= 0; i--) {
                const day = daysInPrevMonth - i;
                const dayElement = document.createElement('div');
                dayElement.className = 'calendar-day other-month';
                dayElement.textContent = day;
                dayElement.addEventListener('click', () => {
                    const prevMonth = month === 1 ? 12 : month - 1;
                    const prevYear = month === 1 ? year - 1 : year;
                    const date = new Date(prevYear, prevMonth - 1, day);
                    date.setHours(12, 0, 0, 0);
                    selectDate(date);
                });
                calendarDays.appendChild(dayElement);
            }

            // 生成当月的日期
            const today = new Date();
            today.setHours(12, 0, 0, 0);
            const selectedDate = parseDate(currentDateInput.value);

            for (let day = 1; day <= daysInMonth; day++) {
                const dayElement = document.createElement('div');
                dayElement.className = 'calendar-day';
                dayElement.textContent = day;

                // 判断是否为今天
                const isToday = year === today.getFullYear() && month === today.getMonth() + 1 && day === today.getDate();

                // 判断是否为选中日期
                const isSelected = year === selectedDate.getFullYear() && month === selectedDate.getMonth() + 1 && day === selectedDate.getDate();

                if (isSelected) {
                    dayElement.classList.add('current');
                } else if (isToday) {
                    dayElement.style.fontWeight = 'bold';
                    dayElement.style.color = '#1890ff'; // 使用主色值
                }

                dayElement.addEventListener('click', () => {
                    const date = new Date(year, month - 1, day);
                    date.setHours(12, 0, 0, 0); // 避免时区问题
                    selectDate(date);
                });
                calendarDays.appendChild(dayElement);
            }

            // 计算需要显示多少个下个月的日期
            const totalDaysShown = Math.ceil((firstDayOfMonth + daysInMonth) / 7) * 7;
            const nextMonthDays = totalDaysShown - firstDayOfMonth - daysInMonth;

            // 生成下个月的日期
            for (let day = 1; day <= nextMonthDays; day++) {
                const dayElement = document.createElement('div');
                dayElement.className = 'calendar-day other-month';
                dayElement.textContent = day;
                dayElement.addEventListener('click', () => {
                    const nextMonth = month === 12 ? 1 : month + 1;
                    const nextYear = month === 12 ? year + 1 : year;
                    const date = new Date(nextYear, nextMonth - 1, day);
                    date.setHours(12, 0, 0, 0); // 避免时区问题
                    selectDate(date);
                });
                calendarDays.appendChild(dayElement);
            }
        }

        // 选择日期
        function selectDate(date) {
            // 确保date对象的时间部分是中午12点
            date.setHours(12, 0, 0, 0);

            const year = date.getFullYear();
            const month = String(date.getMonth() + 1).padStart(2, '0');
            const day = String(date.getDate()).padStart(2, '0');
            const formattedDate = `${year}-${month}-${day}`;

            currentDateInput.value = formattedDate;
            todayDateElement.textContent = `${year}年${month}月${day}日`;
            dateCalendar.classList.remove('show');

            // 更新记录数据
            updateRecordsForDate(date);

            // 如果选择的日期不在当前显示的月份，需要更新日历显示
            if (date.getMonth() !== currentCalendarDate.getMonth() || date.getFullYear() !== currentCalendarDate.getFullYear()) {
                currentCalendarDate = new Date(date.getFullYear(), date.getMonth(), 1);
                currentCalendarDate.setHours(12, 0, 0, 0);
                renderCalendar();
            }
        }

        // 将日期字符串解析为Date对象
        function parseDate(dateString) {
            const parts = dateString.split('-');
            // 使用UTC时间来避免时区问题
            return new Date(Date.UTC(parseInt(parts[0]), parseInt(parts[1]) - 1, parseInt(parts[2]), 12, 0, 0));
        }

        // 更新指定日期的记录
        function updateRecordsForDate(date) {
            // 格式化选中的日期为字符串，用于比较
            const year = date.getFullYear();
            const month = String(date.getMonth() + 1).padStart(2, '0');
            const day = String(date.getDate()).padStart(2, '0');
            const formattedDate = `${year}-${month}-${day}`;

            // 显示提示信息
            showToast('info', '日期已切换', `已切换到${formattedDate}的记录`);

            // 从原始数据中过滤出选中日期的记录
            recordData = originalRecordData.filter(record => record.recordDate === formattedDate);

            // 如果该日期没有记录，显示提示信息
            if (recordData.length === 0) {
                showToast('info', '无记录', `${formattedDate}没有补录记录`);
            }

            // 更新UI
            renderRecordTable();
            updateSummary();
        }

        // 打开新增补录模态框
        function openAddRecordModal() {
            // 清空表单
            document.getElementById('subjectRecordForm').reset();
            clearErrors();
            subjectNameSelect.disabled = true;
            subjectNameSelect.innerHTML = '<option value="">请先选择收支类型</option>';

            // 设置日期默认值为前一天
            const yesterday = new Date();
            yesterday.setDate(yesterday.getDate() - 1);
            yesterday.setHours(12, 0, 0, 0); // 设置为中午12点避免时区问题

            const year = yesterday.getFullYear();
            const month = String(yesterday.getMonth() + 1).padStart(2, '0');
            const day = String(yesterday.getDate()).padStart(2, '0');
            const formattedDate = `${year}-${month}-${day}`;

            recordDateInput.value = formattedDate;

            // 显示模态框
            addRecordModal.classList.add('show');
        }

        // 根据收支类型填充科目下拉框
        function populateSubjects(type) {
            subjectNameSelect.innerHTML = '';

            if (!type) {
                subjectNameSelect.disabled = true;
                subjectNameSelect.innerHTML = '<option value="">请先选择收支类型</option>';
                return;
            }

            const subjects = subjectData[type] || [];
            subjectNameSelect.disabled = false;

            // 添加默认选项
            const defaultOption = document.createElement('option');
            defaultOption.value = '';
            defaultOption.textContent = '请选择科目名称';
            subjectNameSelect.appendChild(defaultOption);

            // 添加科目选项
            subjects.forEach(subject => {
                const option = document.createElement('option');
                option.value = subject.id;
                option.textContent = subject.name;
                subjectNameSelect.appendChild(option);
            });
        }

        // 提交记录
        function submitRecord() {
            // 表单验证
            if (!validateForm()) {
                return;
            }

            // 获取当前时间
            const now = new Date();
            const year = now.getFullYear();
            const month = String(now.getMonth() + 1).padStart(2, '0');
            const day = String(now.getDate()).padStart(2, '0');
            const hours = String(now.getHours()).padStart(2, '0');
            const minutes = String(now.getMinutes()).padStart(2, '0');
            const seconds = String(now.getSeconds()).padStart(2, '0');
            const currentTime = `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;

            // 获取报表类型名称
            const reportTypeOption = reportTypeSelect.options[reportTypeSelect.selectedIndex];
            const reportTypeName = reportTypeOption.textContent;

            // 获取店铺名称
            const shopNameOption = shopNameSelect.options[shopNameSelect.selectedIndex];
            const shopNameText = shopNameOption.textContent;

            // 获取科目名称
            const subjectNameOption = subjectNameSelect.options[subjectNameSelect.selectedIndex];
            const subjectNameText = subjectNameOption.textContent;

            // 创建新记录
            const newRecord = {
                id: getNextRecordId(),
                reportType: reportTypeSelect.value,
                reportTypeName: reportTypeName,
                shopId: shopNameSelect.value,
                shopName: shopNameText,
                incomeType: incomeTypeSelect.value,
                subjectId: parseInt(subjectNameSelect.value),
                subjectName: subjectNameText,
                recordDate: recordDateInput.value, // 使用记录日期
                amount: parseFloat(amountInput.value),
                remark: remarkInput.value,
                createTime: currentTime,
                operator: currentUser.name
            };

            // 添加到记录列表和原始数据
            recordData.push(newRecord);
            originalRecordData.push(newRecord);

            // 保存数据到localStorage
            storageUtils.saveData(originalRecordData);

            // 更新UI
            renderRecordTable();
            updateSummary();

            // 关闭模态框
            addRecordModal.classList.remove('show');

            // 显示成功通知
            showToast('success', '补录成功', '费用补录记录已成功添加');
        }

        // 验证表单
        function validateForm() {
            let isValid = true;
            clearErrors();

            if (!reportTypeSelect.value) {
                reportTypeError.textContent = '请选择报表类型';
                isValid = false;
            }

            if (!shopNameSelect.value) {
                shopNameError.textContent = '请选择店铺';
                isValid = false;
            }

            if (!incomeTypeSelect.value) {
                incomeTypeError.textContent = '请选择收支类型';
                isValid = false;
            }

            if (!subjectNameSelect.value) {
                subjectNameError.textContent = '请选择科目名称';
                isValid = false;
            }

            if (!recordDateInput.value) {
                recordDateError.textContent = '请选择日期';
                isValid = false;
            }

            if (!amountInput.value) {
                amountError.textContent = '请输入费用金额';
                isValid = false;
            } else if (parseFloat(amountInput.value) <= 0) {
                amountError.textContent = '金额必须大于0';
                isValid = false;
            }

            return isValid;
        }

        // 清除错误提示
        function clearErrors() {
            reportTypeError.textContent = '';
            shopNameError.textContent = '';
            incomeTypeError.textContent = '';
            subjectNameError.textContent = '';
            recordDateError.textContent = '';
            amountError.textContent = '';
        }

        // 渲染记录表格
        function renderRecordTable() {
            const tbody = recordTable.querySelector('tbody');
            tbody.innerHTML = '';

            recordData.forEach((record, index) => {
                const tr = document.createElement('tr');

                // 设置收支类型标签
                const typeTag = record.incomeType === 'income' ?
                    '<span class="tag tag-blue">收入补差</span>' :
                    '<span class="tag tag-red">支出补差</span>';

                // 设置金额样式
                const amountClass = record.incomeType === 'income' ? 'amount-income' : 'amount-expense';
                const formattedAmount = formatCurrency(record.amount);

                tr.innerHTML = `
                            <td>${index + 1}</td>
                            <td>${record.reportTypeName}</td>
                            <td>${record.shopName}</td>
                            <td>${typeTag}</td>
                            <td>${record.subjectName}</td>
                            <td class="amount-cell ${amountClass}">${formattedAmount}</td>
                    <td>${record.recordDate || '-'}</td>
                            <td>${record.remark || '-'}</td>
                            <td>${record.createTime}</td>
                            <td>${record.operator}</td>
                            <td class="table-action-group">
                                <button class="btn btn-text btn-danger-text delete-btn" data-id="${record.id}">
                                    <i class="ri-delete-bin-line"></i>删除
                                </button>
                            </td>
                        `;

                tbody.appendChild(tr);
            });
        }

        // 更新统计摘要
        function updateSummary() {
            // 计算总记录数
            totalRecordsElement.textContent = recordData.length;

            // 计算收入总额
            const totalIncome = recordData
                .filter(record => record.incomeType === 'income')
                .reduce((sum, record) => sum + record.amount, 0);

            // 计算支出总额
            const totalExpense = recordData
                .filter(record => record.incomeType === 'expense')
                .reduce((sum, record) => sum + record.amount, 0);

            // 更新UI
            totalIncomeElement.textContent = formatCurrency(totalIncome);
            totalExpenseElement.textContent = formatCurrency(totalExpense);

            // 更新趋势指标（此处为模拟数据，实际应用中应该根据历史数据计算）
            updateTrendIndicators();
        }

        // 更新趋势指标
        function updateTrendIndicators() {
            // 在实际应用中，这里应该使用真实的历史数据来计算趋势
            // 此处为演示目的使用固定值
        }

        // 初始化图表选项卡切换
        function initChartTabs() {
            const chartTabs = document.querySelectorAll('.chart-tab');
            if (chartTabs.length > 0) {
                chartTabs.forEach(tab => {
                    tab.addEventListener('click', function() {
                        // 移除所有tab的active类
                        chartTabs.forEach(t => t.classList.remove('active'));
                        // 给当前点击的tab添加active类
                        this.classList.add('active');

                        // 这里可以根据不同的选项卡加载不同的图表数据
                        // showChart(this.textContent);
                    });
                });
            }
        }

        // 初始化筛选功能
        function initFilters() {
            const typeFilter = document.getElementById('typeFilter');
            const shopFilter = document.getElementById('shopFilter');
            const recordSearch = document.getElementById('recordSearch');

            if (typeFilter) {
                typeFilter.addEventListener('change', filterRecords);
            }

            if (shopFilter) {
                shopFilter.addEventListener('change', filterRecords);
            }

            if (recordSearch) {
                recordSearch.addEventListener('input', filterRecords);
            }
        }

        // 筛选记录
        function filterRecords() {
            const typeFilter = document.getElementById('typeFilter');
            const shopFilter = document.getElementById('shopFilter');
            const recordSearch = document.getElementById('recordSearch');

            let filteredData = [...recordData];

            // 按类型筛选
            if (typeFilter && typeFilter.value) {
                filteredData = filteredData.filter(record => record.incomeType === typeFilter.value);
            }

            // 按店铺筛选
            if (shopFilter && shopFilter.value) {
                filteredData = filteredData.filter(record => record.shopId === shopFilter.value);
            }

            // 按关键词搜索
            if (recordSearch && recordSearch.value.trim() !== '') {
                const keyword = recordSearch.value.trim().toLowerCase();
                filteredData = filteredData.filter(record =>
                    record.reportTypeName.toLowerCase().includes(keyword) ||
                    record.shopName.toLowerCase().includes(keyword) ||
                    record.subjectName.toLowerCase().includes(keyword) ||
                    (record.remark && record.remark.toLowerCase().includes(keyword))
                );
            }

            // 更新表格显示
            renderFilteredRecords(filteredData);
        }

        // 渲染筛选后的记录
        function renderFilteredRecords(filteredData) {
            const tbody = recordTable.querySelector('tbody');
            tbody.innerHTML = '';

            filteredData.forEach((record, index) => {
                const tr = document.createElement('tr');

                // 设置收支类型标签
                const typeTag = record.incomeType === 'income' ?
                    '<span class="tag tag-blue">收入补差</span>' :
                    '<span class="tag tag-red">支出补差</span>';

                // 设置金额样式
                const amountClass = record.incomeType === 'income' ? 'amount-income' : 'amount-expense';
                const formattedAmount = formatCurrency(record.amount);

                tr.innerHTML = `
                    <td>${index + 1}</td>
                    <td>${record.reportTypeName}</td>
                    <td>${record.shopName}</td>
                    <td>${typeTag}</td>
                    <td>${record.subjectName}</td>
                    <td class="amount-cell ${amountClass}">${formattedAmount}</td>
                    <td>${record.recordDate || '-'}</td>
                    <td>${record.remark || '-'}</td>
                    <td>${record.createTime}</td>
                    <td>${record.operator}</td>
                    <td class="table-action-group">
                        <button class="btn btn-text btn-danger-text delete-btn" data-id="${record.id}">
                            <i class="ri-delete-bin-line"></i>删除
                        </button>
                    </td>
                `;

                tbody.appendChild(tr);
            });
        }

        // 初始化分页功能
        function initPagination() {
            const paginationBtns = document.querySelectorAll('.pagination-btn');
            if (paginationBtns.length > 0) {
                paginationBtns.forEach(btn => {
                    btn.addEventListener('click', function() {
                        if (!this.classList.contains('active') && !this.querySelector('i')) {
                            // 移除所有按钮的active类
                            paginationBtns.forEach(b => b.classList.remove('active'));
                            // 给当前点击的按钮添加active类
                            this.classList.add('active');

                            // 模拟分页切换
                            // 实际应用中应该根据页码加载对应页的数据
                            // loadPage(parseInt(this.textContent));
                        }
                    });
                });

                // 页码大小选择
                const pageSizeSelect = document.querySelector('.page-size-select');
                if (pageSizeSelect) {
                    pageSizeSelect.addEventListener('change', function() {
                        // 实际应用中应该根据选择的每页条数重新加载数据
                        // const pageSize = parseInt(this.value);
                        // loadWithPageSize(pageSize);
                    });
                }
            }
        }

        // 打开删除确认模态框
        function openDeleteConfirmModal() {
            deleteConfirmModal.classList.add('show');
        }

        // 删除记录
        function deleteRecord() {
            // 从当前显示数据中找到记录索引
            const index = recordData.findIndex(record => record.id === currentRecordId);

            if (index !== -1) {
                // 从当前显示的数据中删除
                recordData.splice(index, 1);

                // 同时从原始数据中删除
                const originalIndex = originalRecordData.findIndex(record => record.id === currentRecordId);
                if (originalIndex !== -1) {
                    originalRecordData.splice(originalIndex, 1);
                }

                // 保存数据到localStorage
                storageUtils.saveData(originalRecordData);

                // 更新UI
                renderRecordTable();
                updateSummary();

                // 显示成功通知
                showToast('success', '删除成功', '补录记录已成功删除');
            }

            // 关闭模态框
            deleteConfirmModal.classList.remove('show');
        }

        // 获取下一个记录ID
        function getNextRecordId() {
            if (recordData.length === 0) return 1;
            return Math.max(...recordData.map(record => record.id)) + 1;
        }

        // 显示通知提示
        function showToast(type, title, message) {
            const toast = document.createElement('div');
            toast.className = `toast toast-${type}`;

            let icon = '';
            switch (type) {
                case 'success':
                    icon = '<i class="ri-check-line"></i>';
                    break;
                case 'error':
                    icon = '<i class="ri-error-warning-line"></i>';
                    break;
                case 'info':
                    icon = '<i class="ri-information-line"></i>';
                    break;
                default:
                    icon = '<i class="ri-notification-line"></i>';
            }

            toast.innerHTML = `
                        <div class="toast-icon">${icon}</div>
                        <div class="toast-content">
                            <div class="toast-title">${title}</div>
                            <div class="toast-message">${message}</div>
                        </div>
                    `;

            toastContainer.appendChild(toast);

            // 显示动画
            setTimeout(() => {
                toast.classList.add('show');
                // 自动消失
                setTimeout(() => {
                    toast.classList.remove('show');
                    setTimeout(() => {
                        toastContainer.removeChild(toast);
                    }, 300);
                }, 3000);
            }, 10);
        }

        // 格式化货币
        function formatCurrency(value) {
            return '¥' + value.toLocaleString('zh-CN', {
                minimumFractionDigits: 2,
                maximumFractionDigits: 2
            });
        }

        // 模态框点击外部关闭
        window.addEventListener('click', function(e) {
            if (e.target === addRecordModal) {
                addRecordModal.classList.remove('show');
            }

            if (e.target === deleteConfirmModal) {
                deleteConfirmModal.classList.remove('show');
            }
        });

        // 点击ESC关闭模态框
        document.addEventListener('keydown', function(e) {
            if (e.key === 'Escape') {
                addRecordModal.classList.remove('show');
                deleteConfirmModal.classList.remove('show');
            }
        });

        // 显示记录日期选择器
        function showRecordDatePicker() {
            // 创建日期选择器容器
            let recordDatePicker = document.getElementById('recordDatePicker');

            // 如果容器不存在，则创建
            if (!recordDatePicker) {
                recordDatePicker = document.createElement('div');
                recordDatePicker.id = 'recordDatePicker';
                recordDatePicker.className = 'date-calendar';
                recordDatePicker.style.position = 'absolute';
                recordDatePicker.style.zIndex = '1000';
                document.body.appendChild(recordDatePicker);

                // 创建日期选择器内容
                recordDatePicker.innerHTML = `
                    <div class="calendar-header">
                        <div class="calendar-title" id="recordCalendarTitle"></div>
                        <div class="calendar-nav">
                            <button class="calendar-nav-btn" id="recordPrevMonth">
                                <i class="ri-arrow-left-s-line"></i>
                            </button>
                            <button class="calendar-nav-btn" id="recordNextMonth">
                                <i class="ri-arrow-right-s-line"></i>
                            </button>
                        </div>
                    </div>
                    <div class="calendar-weekdays">
                        <div class="calendar-weekday">日</div>
                        <div class="calendar-weekday">一</div>
                        <div class="calendar-weekday">二</div>
                        <div class="calendar-weekday">三</div>
                        <div class="calendar-weekday">四</div>
                        <div class="calendar-weekday">五</div>
                        <div class="calendar-weekday">六</div>
                    </div>
                    <div class="calendar-days" id="recordCalendarDays"></div>
                    <div class="calendar-footer">
                        <button class="calendar-today-btn" id="recordTodayBtn">今天</button>
                    </div>
                    <div class="calendar-shortcuts">
                        <div class="shortcuts-title">快捷选择</div>
                        <div class="shortcuts-buttons">
                            <button class="shortcut-btn record-shortcut-btn" data-days="0">今天</button>
                            <button class="shortcut-btn record-shortcut-btn" data-days="-1">昨天</button>
                            <button class="shortcut-btn record-shortcut-btn" data-days="-7">一周前</button>
                            <button class="shortcut-btn record-shortcut-btn" data-days="-30">一月前</button>
                        </div>
                    </div>
                `;

                // 添加事件监听
                document.getElementById('recordPrevMonth').addEventListener('click', function() {
                    recordCalendarDate.setMonth(recordCalendarDate.getMonth() - 1);
                    renderRecordCalendar();
                });

                document.getElementById('recordNextMonth').addEventListener('click', function() {
                    recordCalendarDate.setMonth(recordCalendarDate.getMonth() + 1);
                    renderRecordCalendar();
                });

                document.getElementById('recordTodayBtn').addEventListener('click', function() {
                    const today = new Date();
                    today.setHours(12, 0, 0, 0);
                    selectRecordDate(today);
                    recordCalendarDate = new Date(today.getFullYear(), today.getMonth(), 1);
                    recordCalendarDate.setHours(12, 0, 0, 0);
                    renderRecordCalendar();
                });

                // 添加快捷选择按钮事件
                document.querySelectorAll('.record-shortcut-btn').forEach(btn => {
                    btn.addEventListener('click', function() {
                        const days = parseInt(this.getAttribute('data-days'));
                        const date = new Date();
                        date.setDate(date.getDate() + days);
                        date.setHours(12, 0, 0, 0); // 避免时区问题

                        // 更新日历视图到对应月份
                        recordCalendarDate = new Date(date.getFullYear(), date.getMonth(), 1);
                        recordCalendarDate.setHours(12, 0, 0, 0);

                        // 选择日期并关闭日历
                        selectRecordDate(date);
                        renderRecordCalendar();
                        recordDatePicker.classList.remove('show');
                    });
                });

                // 点击其他地方关闭日历
                document.addEventListener('click', function(e) {
                    if (recordDatePicker && !recordDatePicker.contains(e.target) &&
                        e.target !== recordDateInput && e.target !== recordDateToggle &&
                        !recordDateToggle.contains(e.target)) {
                        recordDatePicker.classList.remove('show');
                    }
                });
            }

            // 设置日历位置
            const rect = recordDateInput.getBoundingClientRect();
            recordDatePicker.style.top = (rect.bottom + window.scrollY) + 'px';
            recordDatePicker.style.left = (rect.left + window.scrollX) + 'px';

            // 初始化日历日期
            recordCalendarDate = recordDateInput.value ? parseDate(recordDateInput.value) : new Date();
            recordCalendarDate = new Date(recordCalendarDate.getFullYear(), recordCalendarDate.getMonth(), 1);
            recordCalendarDate.setHours(12, 0, 0, 0);

            // 渲染并显示日历
            renderRecordCalendar();
            recordDatePicker.classList.add('show');
        }

        // 全局变量用于记录日期选择器当前月份
        let recordCalendarDate;

        // 渲染记录日期日历
        function renderRecordCalendar() {
            const recordDatePicker = document.getElementById('recordDatePicker');
            const recordCalendarDays = document.getElementById('recordCalendarDays');
            const recordCalendarTitle = document.getElementById('recordCalendarTitle');

            if (!recordDatePicker || !recordCalendarDays || !recordCalendarTitle) return;

            // 设置日历标题
            const year = recordCalendarDate.getFullYear();
            const month = recordCalendarDate.getMonth() + 1;
            recordCalendarTitle.textContent = `${year}年${month}月`;

            // 清空日历日期
            recordCalendarDays.innerHTML = '';

            // 计算当月第一天是星期几
            const firstDay = new Date(year, month - 1, 1);
            firstDay.setHours(12, 0, 0, 0);
            const firstDayOfMonth = firstDay.getDay();

            // 计算当月有多少天
            const daysInMonth = new Date(year, month, 0).getDate();

            // 计算上个月有多少天
            const daysInPrevMonth = new Date(year, month - 1, 0).getDate();

            // 生成上个月的日期
            for (let i = firstDayOfMonth - 1; i >= 0; i--) {
                const day = daysInPrevMonth - i;
                const dayElement = document.createElement('div');
                dayElement.className = 'calendar-day other-month';
                dayElement.textContent = day;
                dayElement.addEventListener('click', () => {
                    const prevMonth = month === 1 ? 12 : month - 1;
                    const prevYear = month === 1 ? year - 1 : year;
                    const date = new Date(prevYear, prevMonth - 1, day);
                    date.setHours(12, 0, 0, 0);
                    selectRecordDate(date);
                });
                recordCalendarDays.appendChild(dayElement);
            }

            // 生成当月的日期
            const today = new Date();
            today.setHours(12, 0, 0, 0);
            const selectedDate = recordDateInput.value ? parseDate(recordDateInput.value) : null;

            for (let day = 1; day <= daysInMonth; day++) {
                const dayElement = document.createElement('div');
                dayElement.className = 'calendar-day';
                dayElement.textContent = day;

                // 判断是否为今天
                const isToday = year === today.getFullYear() && month === today.getMonth() + 1 && day === today.getDate();

                // 判断是否为选中日期
                const isSelected = selectedDate &&
                    year === selectedDate.getFullYear() &&
                    month === selectedDate.getMonth() + 1 &&
                    day === selectedDate.getDate();

                if (isSelected) {
                    dayElement.classList.add('current');
                } else if (isToday) {
                    dayElement.style.fontWeight = 'bold';
                    dayElement.style.color = '#1890ff';
                }

                dayElement.addEventListener('click', () => {
                    const date = new Date(year, month - 1, day);
                    date.setHours(12, 0, 0, 0);
                    selectRecordDate(date);
                });
                recordCalendarDays.appendChild(dayElement);
            }

            // 计算需要显示多少个下个月的日期
            const totalDaysShown = Math.ceil((firstDayOfMonth + daysInMonth) / 7) * 7;
            const nextMonthDays = totalDaysShown - firstDayOfMonth - daysInMonth;

            // 生成下个月的日期
            for (let day = 1; day <= nextMonthDays; day++) {
                const dayElement = document.createElement('div');
                dayElement.className = 'calendar-day other-month';
                dayElement.textContent = day;
                dayElement.addEventListener('click', () => {
                    const nextMonth = month === 12 ? 1 : month + 1;
                    const nextYear = month === 12 ? year + 1 : year;
                    const date = new Date(nextYear, nextMonth - 1, day);
                    date.setHours(12, 0, 0, 0);
                    selectRecordDate(date);
                });
                recordCalendarDays.appendChild(dayElement);
            }
        }

        // 选择记录日期
        function selectRecordDate(date) {
            date.setHours(12, 0, 0, 0);

            const year = date.getFullYear();
            const month = String(date.getMonth() + 1).padStart(2, '0');
            const day = String(date.getDate()).padStart(2, '0');
            const formattedDate = `${year}-${month}-${day}`;

            recordDateInput.value = formattedDate;

            // 隐藏日历
            const recordDatePicker = document.getElementById('recordDatePicker');
            if (recordDatePicker) {
                recordDatePicker.classList.remove('show');
            }
        }
    </script>
</body>

</html>