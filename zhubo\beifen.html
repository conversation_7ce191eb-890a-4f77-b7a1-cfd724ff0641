<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>主播经营日报 - 电商后台管理系统</title>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <style>
         :root {
            /* 品牌色系 - 专业蓝调 */
            --brand-primary: #2563eb;
            --brand-primary-light: #3b82f6;
            --brand-primary-dark: #1d4ed8;
            --brand-secondary: #f59e0b;
            /* 中性色系 - 精心调配的灰度 */
            --neutral-0: #ffffff;
            --neutral-50: #fafbfc;
            --neutral-100: #f4f6f8;
            --neutral-200: #e1e5e9;
            --neutral-300: #c7d2da;
            --neutral-400: #9aa5b1;
            --neutral-500: #7b8794;
            --neutral-600: #616e7c;
            --neutral-700: #52606d;
            --neutral-800: #3e4c59;
            --neutral-900: #1f2937;
            /* 功能色系 */
            --success: #059669;
            --success-light: #d1fae5;
            --warning: #d97706;
            --warning-light: #fef3c7;
            --error: #dc2626;
            --error-light: #fee2e2;
            --info: #0891b2;
            --info-light: #cffafe;
            /* 阴影系统 */
            --shadow-xs: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
            --shadow-sm: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px -1px rgba(0, 0, 0, 0.1);
            --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -2px rgba(0, 0, 0, 0.1);
            --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -4px rgba(0, 0, 0, 0.1);
            --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 8px 10px -6px rgba(0, 0, 0, 0.1);
            /* 边角系统 */
            --radius-sm: 6px;
            --radius-md: 8px;
            --radius-lg: 12px;
            --radius-xl: 16px;
            /* 间距系统 */
            --space-1: 4px;
            --space-2: 8px;
            --space-3: 12px;
            --space-4: 16px;
            --space-5: 20px;
            --space-6: 24px;
            --space-8: 32px;
            --space-10: 40px;
            --space-12: 48px;
            --space-16: 64px;
        }
        
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', system-ui, sans-serif;
            background: linear-gradient(135deg, var(--neutral-50) 0%, var(--neutral-100) 100%);
            color: var(--neutral-900);
            line-height: 1.6;
            font-size: 14px;
            font-weight: 400;
            -webkit-font-smoothing: antialiased;
            -moz-osx-font-smoothing: grayscale;
        }
        
        .app-container {
            min-height: 100vh;
            display: flex;
            flex-direction: column;
        }
        /* 顶部导航栏 */
        
        .app-header {
            background: var(--neutral-0);
            border-bottom: 1px solid var(--neutral-200);
            padding: 0 var(--space-6);
            height: 64px;
            display: flex;
            align-items: center;
            justify-content: space-between;
            position: sticky;
            top: 0;
            z-index: 100;
            backdrop-filter: blur(8px);
        }
        
        .header-left {
            display: flex;
            align-items: center;
            gap: var(--space-6);
        }
        
        .app-logo {
            display: flex;
            align-items: center;
            gap: var(--space-3);
            font-weight: 700;
            font-size: 18px;
            color: var(--brand-primary);
        }
        
        .breadcrumb {
            display: flex;
            align-items: center;
            gap: var(--space-2);
            color: var(--neutral-600);
            font-size: 13px;
        }
        
        .breadcrumb-separator {
            color: var(--neutral-400);
        }
        
        .breadcrumb-current {
            color: var(--neutral-900);
            font-weight: 500;
        }
        /* 主要内容区域 */
        
        .app-main {
            flex: 1;
            padding: var(--space-6);
            max-width: 1440px;
            margin: 0 auto;
            width: 100%;
        }
        /* 页面头部 */
        
        .page-header {
            margin-bottom: var(--space-8);
        }
        
        .page-header-top {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: var(--space-4);
        }
        
        .page-title-group {
            display: flex;
            flex-direction: column;
            gap: var(--space-2);
        }
        
        .page-title {
            font-size: 28px;
            font-weight: 700;
            color: var(--neutral-900);
            letter-spacing: -0.02em;
        }
        
        .page-subtitle {
            font-size: 15px;
            color: var(--neutral-600);
            font-weight: 400;
        }
        
        .page-actions {
            display: flex;
            gap: var(--space-3);
            align-items: center;
        }
        /* 按钮系统 */
        
        .btn {
            display: inline-flex;
            align-items: center;
            justify-content: center;
            gap: var(--space-2);
            padding: var(--space-3) var(--space-4);
            border: 1px solid transparent;
            border-radius: var(--radius-md);
            font-size: 14px;
            font-weight: 500;
            line-height: 1.4;
            cursor: pointer;
            transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
            text-decoration: none;
            white-space: nowrap;
            user-select: none;
            position: relative;
            overflow: hidden;
        }
        
        .btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
        }
        
        .btn-primary {
            background: var(--brand-primary);
            color: var(--neutral-0);
            box-shadow: var(--shadow-sm);
        }
        
        .btn-primary:hover:not(:disabled) {
            background: var(--brand-primary-dark);
            box-shadow: var(--shadow-md);
            transform: translateY(-1px);
        }
        
        .btn-secondary {
            background: var(--neutral-0);
            color: var(--neutral-700);
            border-color: var(--neutral-300);
            box-shadow: var(--shadow-xs);
        }
        
        .btn-secondary:hover:not(:disabled) {
            background: var(--neutral-50);
            border-color: var(--neutral-400);
            box-shadow: var(--shadow-sm);
        }
        
        .btn-ghost {
            background: transparent;
            color: var(--neutral-600);
            border-color: transparent;
        }
        
        .btn-ghost:hover:not(:disabled) {
            background: var(--neutral-100);
            color: var(--neutral-700);
        }
        
        .btn-sm {
            padding: var(--space-2) var(--space-3);
            font-size: 13px;
        }
        
        .btn-lg {
            padding: var(--space-4) var(--space-6);
            font-size: 15px;
        }
        
        .btn-icon-only {
            padding: var(--space-3);
            width: 40px;
            height: 40px;
        }
        /* 卡片系统 */
        
        .card {
            background: var(--neutral-0);
            border: 1px solid var(--neutral-200);
            border-radius: var(--radius-lg);
            box-shadow: var(--shadow-sm);
            overflow: hidden;
            transition: all 0.2s ease;
        }
        
        .card:hover {
            box-shadow: var(--shadow-md);
        }
        
        .card-header {
            padding: var(--space-6);
            border-bottom: 1px solid var(--neutral-200);
            background: var(--neutral-50);
        }
        
        .card-body {
            padding: var(--space-6);
        }
        
        .card-footer {
            padding: var(--space-6);
            border-top: 1px solid var(--neutral-200);
            background: var(--neutral-50);
        }
        /* 主要内容卡片 */
        
        .main-card {
            background: var(--neutral-0);
            border-radius: var(--radius-xl);
            box-shadow: var(--shadow-lg);
            overflow: hidden;
            min-height: 600px;
        }
        
        .main-card-header {
            padding: var(--space-8);
            background: linear-gradient(135deg, var(--neutral-50) 0%, var(--neutral-100) 100%);
            border-bottom: 1px solid var(--neutral-200);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .main-card-title {
            font-size: 20px;
            font-weight: 600;
            color: var(--neutral-900);
            display: flex;
            align-items: center;
            gap: var(--space-3);
        }
        
        .main-card-body {
            padding: var(--space-8);
            display: flex;
            align-items: center;
            justify-content: center;
            min-height: 500px;
        }
        
        .placeholder-content {
            text-align: center;
            max-width: 400px;
        }
        
        .placeholder-icon {
            width: 80px;
            height: 80px;
            background: linear-gradient(135deg, var(--brand-primary) 0%, var(--brand-primary-light) 100%);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto var(--space-6);
            color: var(--neutral-0);
            font-size: 32px;
        }
        
        .placeholder-title {
            font-size: 18px;
            font-weight: 600;
            color: var(--neutral-900);
            margin-bottom: var(--space-3);
        }
        
        .placeholder-description {
            color: var(--neutral-600);
            line-height: 1.6;
            margin-bottom: var(--space-6);
        }
        /* 设置按钮特殊样式 */
        
        .settings-trigger {
            position: relative;
            background: linear-gradient(135deg, var(--brand-primary) 0%, var(--brand-primary-light) 100%);
            color: var(--neutral-0);
            border: none;
            padding: var(--space-3) var(--space-5);
            border-radius: var(--radius-lg);
            font-weight: 500;
            box-shadow: var(--shadow-md);
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }
        
        .settings-trigger:hover {
            transform: translateY(-2px);
            box-shadow: var(--shadow-xl);
        }
        
        .settings-trigger::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(135deg, rgba(255, 255, 255, 0.2) 0%, rgba(255, 255, 255, 0) 100%);
            border-radius: var(--radius-lg);
            opacity: 0;
            transition: opacity 0.2s ease;
        }
        
        .settings-trigger:hover::before {
            opacity: 1;
        }
        /* 模态框系统 */
        
        .modal-overlay {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(15, 23, 42, 0.6);
            backdrop-filter: blur(8px);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1000;
            opacity: 0;
            visibility: hidden;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }
        
        .modal-overlay.active {
            opacity: 1;
            visibility: visible;
        }
        
        .modal {
            background: var(--neutral-0);
            border-radius: var(--radius-xl);
            box-shadow: var(--shadow-xl);
            width: 95%;
            max-width: 1200px;
            max-height: 90vh;
            display: flex;
            flex-direction: column;
            overflow: hidden;
            transform: scale(0.95) translateY(20px);
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }
        
        .modal-overlay.active .modal {
            transform: scale(1) translateY(0);
        }
        
        .modal-header {
            padding: var(--space-8);
            border-bottom: 1px solid var(--neutral-200);
            background: linear-gradient(135deg, var(--neutral-50) 0%, var(--neutral-100) 100%);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .modal-title {
            font-size: 24px;
            font-weight: 700;
            color: var(--neutral-900);
            display: flex;
            align-items: center;
            gap: var(--space-3);
        }
        
        .modal-close {
            background: var(--neutral-100);
            color: var(--neutral-600);
            border: none;
            width: 40px;
            height: 40px;
            border-radius: var(--radius-md);
            cursor: pointer;
            transition: all 0.2s ease;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .modal-close:hover {
            background: var(--neutral-200);
            color: var(--neutral-800);
        }
        /* Tab系统 */
        
        .tab-nav {
            display: flex;
            background: var(--neutral-100);
            border-bottom: 1px solid var(--neutral-200);
            overflow-x: auto;
        }
        
        .tab-button {
            background: none;
            border: none;
            padding: var(--space-4) var(--space-6);
            font-size: 14px;
            font-weight: 500;
            color: var(--neutral-600);
            cursor: pointer;
            border-bottom: 3px solid transparent;
            transition: all 0.2s ease;
            white-space: nowrap;
            position: relative;
        }
        
        .tab-button:hover {
            color: var(--neutral-800);
            background: var(--neutral-50);
        }
        
        .tab-button.active {
            color: var(--brand-primary);
            background: var(--neutral-0);
            border-bottom-color: var(--brand-primary);
        }
        
        .tab-content {
            flex: 1;
            overflow-y: auto;
        }
        
        .tab-panel {
            display: none;
            padding: var(--space-8);
            height: 100%;
        }
        
        .tab-panel.active {
            display: block;
        }
        /* 工具栏 */
        
        .toolbar {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: var(--space-6);
            gap: var(--space-4);
        }
        
        .toolbar-left {
            display: flex;
            align-items: center;
            gap: var(--space-4);
        }
        
        .toolbar-right {
            display: flex;
            align-items: center;
            gap: var(--space-3);
        }
        /* 表格系统 */
        
        .table-container {
            border-radius: var(--radius-lg);
            overflow: hidden;
            border: 1px solid var(--neutral-200);
            background: var(--neutral-0);
        }
        
        .data-table {
            width: 100%;
            border-collapse: collapse;
        }
        
        .data-table th {
            background: var(--neutral-50);
            padding: var(--space-4) var(--space-6);
            text-align: left;
            font-weight: 600;
            font-size: 13px;
            color: var(--neutral-700);
            text-transform: uppercase;
            letter-spacing: 0.05em;
            border-bottom: 1px solid var(--neutral-200);
        }
        
        .data-table td {
            padding: var(--space-5) var(--space-6);
            border-bottom: 1px solid var(--neutral-100);
            font-size: 14px;
            color: var(--neutral-900);
        }
        
        .data-table tr:hover {
            background: var(--neutral-50);
        }
        
        .data-table tr:last-child td {
            border-bottom: none;
        }
        /* 状态标签 */
        
        .status-badge {
            display: inline-flex;
            align-items: center;
            gap: var(--space-1);
            padding: var(--space-1) var(--space-3);
            border-radius: var(--radius-sm);
            font-size: 12px;
            font-weight: 500;
            text-transform: uppercase;
            letter-spacing: 0.05em;
        }
        
        .status-active {
            background: var(--success-light);
            color: var(--success);
        }
        
        .status-inactive {
            background: var(--error-light);
            color: var(--error);
        }
        
        .status-badge::before {
            content: '';
            width: 6px;
            height: 6px;
            border-radius: 50%;
            background: currentColor;
        }
        /* 操作按钮组 */
        
        .action-group {
            display: flex;
            gap: var(--space-2);
        }
        
        .action-btn {
            padding: var(--space-2) var(--space-3);
            border: none;
            border-radius: var(--radius-sm);
            font-size: 12px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.2s ease;
            display: flex;
            align-items: center;
            gap: var(--space-1);
        }
        
        .action-btn-edit {
            background: var(--info-light);
            color: var(--info);
        }
        
        .action-btn-edit:hover {
            background: var(--info);
            color: var(--neutral-0);
        }
        
        .action-btn-toggle {
            background: var(--warning-light);
            color: var(--warning);
        }
        
        .action-btn-toggle:hover {
            background: var(--warning);
            color: var(--neutral-0);
        }
        
        .action-btn-delete {
            background: var(--error-light);
            color: var(--error);
        }
        
        .action-btn-delete:hover {
            background: var(--error);
            color: var(--neutral-0);
        }
        /* 表单系统 */
        
        .form-modal {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(15, 23, 42, 0.6);
            backdrop-filter: blur(8px);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1001;
            opacity: 0;
            visibility: hidden;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }
        
        .form-modal.active {
            opacity: 1;
            visibility: visible;
        }
        
        .form-container {
            background: var(--neutral-0);
            border-radius: var(--radius-xl);
            box-shadow: var(--shadow-xl);
            width: 95%;
            max-width: 500px;
            max-height: 80vh;
            overflow-y: auto;
            transform: scale(0.95) translateY(20px);
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }
        
        .form-modal.active .form-container {
            transform: scale(1) translateY(0);
        }
        
        .form-header {
            padding: var(--space-6);
            border-bottom: 1px solid var(--neutral-200);
            background: linear-gradient(135deg, var(--neutral-50) 0%, var(--neutral-100) 100%);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .form-title {
            font-size: 18px;
            font-weight: 600;
            color: var(--neutral-900);
        }
        
        .form-body {
            padding: var(--space-6);
        }
        
        .form-group {
            margin-bottom: var(--space-5);
        }
        
        .form-label {
            display: block;
            margin-bottom: var(--space-2);
            font-weight: 500;
            font-size: 14px;
            color: var(--neutral-700);
        }
        
        .form-label .required {
            color: var(--error);
            margin-left: var(--space-1);
        }
        
        .form-input,
        .form-select {
            width: 100%;
            padding: var(--space-3) var(--space-4);
            border: 1px solid var(--neutral-300);
            border-radius: var(--radius-md);
            font-size: 14px;
            background: var(--neutral-0);
            transition: all 0.2s ease;
        }
        
        .form-input:focus,
        .form-select:focus {
            outline: none;
            border-color: var(--brand-primary);
            box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
        }
        
        .form-footer {
            padding: var(--space-6);
            border-top: 1px solid var(--neutral-200);
            background: var(--neutral-50);
            display: flex;
            justify-content: flex-end;
            gap: var(--space-3);
        }
        /* 空状态 */
        
        .empty-state {
            text-align: center;
            padding: var(--space-16) var(--space-6);
        }
        
        .empty-icon {
            width: 64px;
            height: 64px;
            background: var(--neutral-100);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto var(--space-4);
            color: var(--neutral-400);
            font-size: 24px;
        }
        
        .empty-title {
            font-size: 16px;
            font-weight: 600;
            color: var(--neutral-900);
            margin-bottom: var(--space-2);
        }
        
        .empty-description {
            color: var(--neutral-600);
            font-size: 14px;
        }
        /* 通知系统 */
        
        .notification {
            position: fixed;
            top: var(--space-6);
            right: var(--space-6);
            background: var(--neutral-0);
            border: 1px solid var(--neutral-200);
            border-radius: var(--radius-lg);
            box-shadow: var(--shadow-xl);
            padding: var(--space-4) var(--space-5);
            min-width: 300px;
            z-index: 10000;
            transform: translateX(100%);
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }
        
        .notification.show {
            transform: translateX(0);
        }
        
        .notification-success {
            border-left: 4px solid var(--success);
        }
        
        .notification-error {
            border-left: 4px solid var(--error);
        }
        
        .notification-warning {
            border-left: 4px solid var(--warning);
        }
        
        .notification-content {
            display: flex;
            align-items: center;
            gap: var(--space-3);
        }
        
        .notification-icon {
            width: 20px;
            height: 20px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
            color: var(--neutral-0);
        }
        
        .notification-success .notification-icon {
            background: var(--success);
        }
        
        .notification-error .notification-icon {
            background: var(--error);
        }
        
        .notification-warning .notification-icon {
            background: var(--warning);
        }
        
        .notification-message {
            font-size: 14px;
            font-weight: 500;
            color: var(--neutral-900);
        }
        /* 响应式设计 */
        
        @media (max-width: 1024px) {
            .app-main {
                padding: var(--space-4);
            }
            .modal {
                width: 98%;
                max-height: 95vh;
            }
        }
        
        @media (max-width: 768px) {
            .app-header {
                padding: 0 var(--space-4);
            }
            .page-header-top {
                flex-direction: column;
                gap: var(--space-4);
                align-items: stretch;
            }
            .toolbar {
                flex-direction: column;
                gap: var(--space-4);
                align-items: stretch;
            }
            .toolbar-left,
            .toolbar-right {
                justify-content: space-between;
            }
            .tab-nav {
                overflow-x: auto;
            }
            .data-table {
                font-size: 13px;
            }
            .data-table th,
            .data-table td {
                padding: var(--space-3) var(--space-4);
            }
        }
        /* 滚动条美化 */
        
         ::-webkit-scrollbar {
            width: 6px;
            height: 6px;
        }
        
         ::-webkit-scrollbar-track {
            background: var(--neutral-100);
        }
        
         ::-webkit-scrollbar-thumb {
            background: var(--neutral-300);
            border-radius: 3px;
        }
        
         ::-webkit-scrollbar-thumb:hover {
            background: var(--neutral-400);
        }
        /* 加载状态 */
        
        .loading {
            display: inline-flex;
            align-items: center;
            gap: var(--space-2);
        }
        
        .loading-spinner {
            width: 16px;
            height: 16px;
            border: 2px solid var(--neutral-300);
            border-top: 2px solid var(--brand-primary);
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }
        
        @keyframes spin {
            0% {
                transform: rotate(0deg);
            }
            100% {
                transform: rotate(360deg);
            }
        }
        /* 微交互动画 */
        
        @keyframes fadeIn {
            from {
                opacity: 0;
                transform: translateY(10px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }
        
        .fade-in {
            animation: fadeIn 0.3s ease-out;
        }
        /* 高级视觉效果 */
        
        .glass-effect {
            background: rgba(255, 255, 255, 0.8);
            backdrop-filter: blur(12px);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        
        .gradient-border {
            position: relative;
            background: var(--neutral-0);
            border-radius: var(--radius-lg);
        }
        
        .gradient-border::before {
            content: '';
            position: absolute;
            inset: 0;
            padding: 1px;
            background: linear-gradient(135deg, var(--brand-primary), var(--brand-secondary));
            border-radius: inherit;
            mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
            mask-composite: xor;
        }
    </style>
</head>

<body>
    <div class="app-container">
        <!-- 顶部导航栏 -->
        <header class="app-header">
            <div class="header-left">
                <div class="app-logo">
                    <i class="fas fa-chart-line"></i> 电商管理系统
                </div>
                <nav class="breadcrumb">
                    <span>数据分析</span>
                    <i class="fas fa-chevron-right breadcrumb-separator"></i>
                    <span>主播管理</span>
                    <i class="fas fa-chevron-right breadcrumb-separator"></i>
                    <span class="breadcrumb-current">经营日报</span>
                </nav>
            </div>
        </header>

        <!-- 主要内容区域 -->
        <main class="app-main">
            <!-- 页面头部 -->
            <div class="page-header">
                <div class="page-header-top">
                    <div class="page-title-group">
                        <h1 class="page-title">主播经营日报</h1>
                        <p class="page-subtitle">管理主播业务数据，优化运营策略，提升整体绩效</p>
                    </div>
                    <div class="page-actions">
                        <button class="btn btn-secondary">
                            <i class="fas fa-download"></i>
                            导出报表
                        </button>
                        <button class="btn settings-trigger" onclick="openSettingsModal()">
                            <i class="fas fa-cog"></i>
                            基础设置
                        </button>
                    </div>
                </div>
            </div>

            <!-- 主要内容卡片 -->
            <div class="main-card">
                <div class="main-card-header">
                    <h2 class="main-card-title">
                        <i class="fas fa-chart-bar"></i> 数据概览
                    </h2>
                    <div class="btn btn-ghost">
                        <i class="fas fa-refresh"></i> 刷新数据
                    </div>
                </div>
                <div class="main-card-body">
                    <div class="placeholder-content">
                        <div class="placeholder-icon">
                            <i class="fas fa-chart-line"></i>
                        </div>
                        <h3 class="placeholder-title">主播经营数据面板</h3>
                        <p class="placeholder-description">
                            这里将展示主播的详细经营数据，包括收入分析、成本统计、绩效指标等关键信息。 请先完成基础设置配置，然后系统将自动生成相应的数据报表。
                        </p>
                        <button class="btn btn-primary" onclick="openSettingsModal()">
                            <i class="fas fa-settings"></i>
                            开始配置
                        </button>
                    </div>
                </div>
            </div>
        </main>
    </div>

    <!-- 设置模态框 -->
    <div class="modal-overlay" id="settingsModal">
        <div class="modal">
            <div class="modal-header">
                <h2 class="modal-title">
                    <i class="fas fa-cog"></i> 主播基础设置
                </h2>
                <button class="modal-close" onclick="closeSettingsModal()">
                    <i class="fas fa-times"></i>
                </button>
            </div>

            <!-- Tab导航 -->
            <div class="tab-nav">
                <button class="tab-button active" onclick="switchTab('income-expense')">
                    <i class="fas fa-dollar-sign"></i>
                    收入支出配置
                </button>
                <button class="tab-button" onclick="switchTab('personnel')">
                    <i class="fas fa-users"></i>
                    人员管理
                </button>
                <button class="tab-button" onclick="switchTab('level')">
                    <i class="fas fa-layer-group"></i>
                    级别维护
                </button>
                <button class="tab-button" onclick="switchTab('shifts')">
                    <i class="fas fa-clock"></i>
                    客服班次设置
                </button>
            </div>

            <!-- Tab内容 -->
            <div class="tab-content">
                <!-- 收入支出配置 -->
                <div class="tab-panel active" id="income-expense-panel">
                    <div class="toolbar">
                        <div class="toolbar-left">
                        </div>
                        <div class="toolbar-right">
                            <button class="btn btn-primary" onclick="openIncomeExpenseForm('add')">
                                <i class="fas fa-plus"></i>
                                新增配置
                            </button>
                        </div>
                    </div>

                    <div class="table-container">
                        <table class="data-table">
                            <thead>
                                <tr>
                                    <th>主播级别</th>
                                    <th>收入时薪（元/小时）</th>
                                    <th>支出时薪（元/小时）</th>
                                    <th>利润率</th>
                                    <th>状态</th>
                                    <th>操作</th>
                                </tr>
                            </thead>
                            <tbody id="incomeExpenseTableBody">
                                <!-- 数据将通过JavaScript动态生成 -->
                            </tbody>
                        </table>
                    </div>
                </div>

                <!-- 人员管理 -->
                <div class="tab-panel" id="personnel-panel">
                    <div class="toolbar">
                        <div class="toolbar-left">
                        </div>
                        <div class="toolbar-right">
                            <button class="btn btn-primary" onclick="openPersonnelForm('add')">
                                <i class="fas fa-user-plus"></i>
                                新增人员
                            </button>
                        </div>
                    </div>

                    <div class="table-container">
                        <table class="data-table">
                            <thead>
                                <tr>
                                    <th>主播姓名</th>
                                    <th>主播级别</th>
                                    <th>年限工资（元）</th>
                                    <th>入职时间</th>
                                    <th>状态</th>
                                    <th>操作</th>
                                </tr>
                            </thead>
                            <tbody id="personnelTableBody">
                                <!-- 数据将通过JavaScript动态生成 -->
                            </tbody>
                        </table>
                    </div>
                </div>

                <!-- 级别维护 -->
                <div class="tab-panel" id="level-panel">
                    <div class="toolbar">
                        <div class="toolbar-left">
                        </div>
                        <div class="toolbar-right">
                            <button class="btn btn-primary" onclick="openLevelForm('add')">
                                <i class="fas fa-plus"></i>
                                新增级别
                            </button>
                        </div>
                    </div>

                    <div class="table-container">
                        <table class="data-table">
                            <thead>
                                <tr>
                                    <th>级别名称</th>
                                    <th>创建时间</th>
                                    <th>关联人员数</th>
                                    <th>状态</th>
                                    <th>操作</th>
                                </tr>
                            </thead>
                            <tbody id="levelTableBody">
                                <!-- 数据将通过JavaScript动态生成 -->
                            </tbody>
                        </table>
                    </div>
                </div>

                <!-- 客服班次设置 -->
                <div class="tab-panel" id="shifts-panel">
                    <div class="toolbar">
                        <div class="toolbar-left">
                        </div>
                        <div class="toolbar-right">
                            <button class="btn btn-primary" onclick="openShiftForm('add')">
                                <i class="fas fa-plus"></i>
                                新增班次
                            </button>
                        </div>
                    </div>

                    <div class="table-container">
                        <table class="data-table">
                            <thead>
                                <tr>
                                    <th>ID</th>
                                    <th>班次名称</th>
                                    <th>时间段</th>
                                    <th>状态</th>
                                    <th>操作</th>
                                </tr>
                            </thead>
                            <tbody id="shiftsTableBody">
                                <!-- 数据将通过JavaScript动态生成 -->
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 收入支出表单模态框 -->
    <div class="form-modal" id="incomeExpenseFormModal">
        <div class="form-container">
            <div class="form-header">
                <h3 class="form-title" id="incomeExpenseFormTitle">新增收入支出配置</h3>
                <button class="modal-close" onclick="closeIncomeExpenseForm()">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="form-body">
                <form id="incomeExpenseForm">
                    <div class="form-group">
                        <label class="form-label">
                            主播级别
                            <span class="required">*</span>
                        </label>
                        <select class="form-select" id="incomeExpenseLevel" required>
                            <option value="">请选择主播级别</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label class="form-label">
                            收入时薪（元/小时）
                            <span class="required">*</span>
                        </label>
                        <input type="number" class="form-input" id="incomeHourlyRate" placeholder="请输入收入时薪" step="0.01" min="0" required>
                    </div>
                    <div class="form-group">
                        <label class="form-label">
                            支出时薪（元/小时）
                            <span class="required">*</span>
                        </label>
                        <input type="number" class="form-input" id="expenseHourlyRate" placeholder="请输入支出时薪" step="0.01" min="0" required>
                    </div>
                </form>
            </div>
            <div class="form-footer">
                <button class="btn btn-secondary" onclick="closeIncomeExpenseForm()">取消</button>
                <button class="btn btn-primary" onclick="saveIncomeExpense()">
                    <span class="loading" id="incomeExpenseLoading" style="display: none;">
                        <div class="loading-spinner"></div>
                        保存中...
                    </span>
                    <span id="incomeExpenseSaveText">确定保存</span>
                </button>
            </div>
        </div>
    </div>

    <!-- 人员表单模态框 -->
    <div class="form-modal" id="personnelFormModal">
        <div class="form-container">
            <div class="form-header">
                <h3 class="form-title" id="personnelFormTitle">新增主播人员</h3>
                <button class="modal-close" onclick="closePersonnelForm()">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="form-body">
                <form id="personnelForm">
                    <div class="form-group">
                        <label class="form-label">
                            主播姓名
                            <span class="required">*</span>
                        </label>
                        <input type="text" class="form-input" id="personnelName" placeholder="请输入主播姓名" required>
                    </div>
                    <div class="form-group">
                        <label class="form-label">
                            主播级别
                            <span class="required">*</span>
                        </label>
                        <select class="form-select" id="personnelLevel" required>
                            <option value="">请选择主播级别</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label class="form-label">
                            年限工资（元）
                            <span class="required">*</span>
                        </label>
                        <input type="number" class="form-input" id="personnelSalary" placeholder="请输入年限工资" step="0.01" min="0" required>
                    </div>
                    <div class="form-group">
                        <label class="form-label">入职时间</label>
                        <input type="date" class="form-input" id="personnelJoinDate">
                    </div>
                </form>
            </div>
            <div class="form-footer">
                <button class="btn btn-secondary" onclick="closePersonnelForm()">取消</button>
                <button class="btn btn-primary" onclick="savePersonnel()">
                    <span class="loading" id="personnelLoading" style="display: none;">
                        <div class="loading-spinner"></div>
                        保存中...
                    </span>
                    <span id="personnelSaveText">确定保存</span>
                </button>
            </div>
        </div>
    </div>

    <!-- 级别表单模态框 -->
    <div class="form-modal" id="levelFormModal">
        <div class="form-container">
            <div class="form-header">
                <h3 class="form-title" id="levelFormTitle">新增主播级别</h3>
                <button class="modal-close" onclick="closeLevelForm()">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="form-body">
                <form id="levelForm">
                    <div class="form-group">
                        <label class="form-label">
                            级别名称
                            <span class="required">*</span>
                        </label>
                        <input type="text" class="form-input" id="levelName" placeholder="请输入主播级别名称" required>
                    </div>
                    <div class="form-group">
                        <label class="form-label">级别描述</label>
                        <textarea class="form-input" id="levelDescription" placeholder="请输入级别描述（可选）" rows="3"></textarea>
                    </div>
                </form>
            </div>
            <div class="form-footer">
                <button class="btn btn-secondary" onclick="closeLevelForm()">取消</button>
                <button class="btn btn-primary" onclick="saveLevel()">
                    <span class="loading" id="levelLoading" style="display: none;">
                        <div class="loading-spinner"></div>
                        保存中...
                    </span>
                    <span id="levelSaveText">确定保存</span>
                </button>
            </div>
        </div>
    </div>

    <!-- 班次表单模态框 -->
    <div class="form-modal" id="shiftFormModal">
        <div class="form-container">
            <div class="form-header">
                <h3 class="form-title" id="shiftFormTitle">新增班次</h3>
                <button class="modal-close" onclick="closeShiftForm()">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="form-body">
                <form id="shiftForm">
                    <div class="form-group">
                        <label class="form-label">
                            班次名称
                            <span class="required">*</span>
                        </label>
                        <input type="text" class="form-input" id="shiftName" placeholder="请输入班次名称" required>
                    </div>
                    <div class="form-group">
                        <label class="form-label">
                            开始时间
                            <span class="required">*</span>
                        </label>
                        <input type="time" class="form-input" id="shiftStartTime" required>
                    </div>
                    <div class="form-group">
                        <label class="form-label">
                            结束时间
                            <span class="required">*</span>
                        </label>
                        <input type="time" class="form-input" id="shiftEndTime" required>
                    </div>
                </form>
            </div>
            <div class="form-footer">
                <button class="btn btn-secondary" onclick="closeShiftForm()">取消</button>
                <button class="btn btn-primary" onclick="saveShift()">
                    <span class="loading" id="shiftLoading" style="display: none;">
                        <div class="loading-spinner"></div>
                        保存中...
                    </span>
                    <span id="shiftSaveText">确定保存</span>
                </button>
            </div>
        </div>
    </div>

    <script>
        // 数据存储
        let anchorLevels = [{
            id: 1,
            name: '初级主播',
            description: '新入职主播，具备基础直播技能',
            status: 'active',
            createdAt: '2024-01-15',
            personnelCount: 5
        }, {
            id: 2,
            name: '中级主播',
            description: '具备一定经验，能够独立完成直播任务',
            status: 'active',
            createdAt: '2024-01-15',
            personnelCount: 3
        }, {
            id: 3,
            name: '高级主播',
            description: '经验丰富，具备带货能力和粉丝号召力',
            status: 'active',
            createdAt: '2024-01-15',
            personnelCount: 2
        }, {
            id: 4,
            name: '金牌主播',
            description: '顶级主播，具备强大的商业价值',
            status: 'inactive',
            createdAt: '2024-02-01',
            personnelCount: 0
        }];

        let incomeExpenseData = [{
            id: 1,
            levelId: 1,
            levelName: '初级主播',
            incomeRate: 50.00,
            expenseRate: 30.00,
            profitRate: 40.0,
            status: 'active'
        }, {
            id: 2,
            levelId: 2,
            levelName: '中级主播',
            incomeRate: 80.00,
            expenseRate: 50.00,
            profitRate: 37.5,
            status: 'active'
        }, {
            id: 3,
            levelId: 3,
            levelName: '高级主播',
            incomeRate: 120.00,
            expenseRate: 80.00,
            profitRate: 33.3,
            status: 'active'
        }];

        let personnelData = [{
            id: 1,
            name: '张小美',
            levelId: 1,
            levelName: '初级主播',
            salary: 5000.00,
            joinDate: '2024-01-20',
            status: 'active'
        }, {
            id: 2,
            name: '李明星',
            levelId: 2,
            levelName: '中级主播',
            salary: 8000.00,
            joinDate: '2023-12-15',
            status: 'active'
        }, {
            id: 3,
            name: '王大咖',
            levelId: 3,
            levelName: '高级主播',
            salary: 12000.00,
            joinDate: '2023-10-10',
            status: 'inactive'
        }, {
            id: 4,
            name: '刘晓雨',
            levelId: 1,
            levelName: '初级主播',
            salary: 4800.00,
            joinDate: '2024-02-01',
            status: 'active'
        }, {
            id: 5,
            name: '陈思思',
            levelId: 2,
            levelName: '中级主播',
            salary: 7500.00,
            joinDate: '2023-11-20',
            status: 'active'
        }];

        // 当前编辑的数据
        let currentEditData = null;
        let currentEditType = null;

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            updatePersonnelCount();
            renderIncomeExpenseTable();
            renderPersonnelTable();
            renderLevelTable();
            updateLevelOptions();

            // 添加表格行的淡入动画
            setTimeout(() => {
                document.querySelectorAll('.data-table tbody tr').forEach((row, index) => {
                    row.style.animationDelay = `${index * 0.05}s`;
                    row.classList.add('fade-in');
                });
            }, 100);
        });

        // 更新人员统计
        function updatePersonnelCount() {
            anchorLevels.forEach(level => {
                level.personnelCount = personnelData.filter(p =>
                    p.levelId === level.id && p.status === 'active'
                ).length;
            });
        }

        // 计算利润率
        function calculateProfitRate(income, expense) {
            if (income === 0) return 0;
            return ((income - expense) / income * 100);
        }

        // 打开设置模态框
        function openSettingsModal() {
            document.getElementById('settingsModal').classList.add('active');
            document.body.style.overflow = 'hidden';
        }

        // 关闭设置模态框
        function closeSettingsModal() {
            document.getElementById('settingsModal').classList.remove('active');
            document.body.style.overflow = 'auto';
        }

        // 切换Tab
        function switchTab(tabName) {
            // 移除所有active类
            document.querySelectorAll('.tab-button').forEach(btn => btn.classList.remove('active'));
            document.querySelectorAll('.tab-panel').forEach(panel => panel.classList.remove('active'));

            // 添加active类到当前tab
            event.target.classList.add('active');
            document.getElementById(tabName + '-panel').classList.add('active');
        }

        // 渲染收入支出表格
        function renderIncomeExpenseTable() {
            const tbody = document.getElementById('incomeExpenseTableBody');

            if (incomeExpenseData.length === 0) {
                tbody.innerHTML = `
                    <tr>
                        <td colspan="6" class="empty-state">
                            <div class="empty-icon">
                                <i class="fas fa-inbox"></i>
                            </div>
                            <div class="empty-title">暂无数据</div>
                            <div class="empty-description">还没有配置任何收入支出信息</div>
                        </td>
                    </tr>
                `;
                return;
            }

            tbody.innerHTML = incomeExpenseData.map(item => `
                <tr>
                    <td>
                        <div style="font-weight: 500;">${item.levelName}</div>
                    </td>
                    <td>
                        <div style="font-weight: 600; color: var(--success);">
                            ¥${item.incomeRate.toFixed(2)}
                        </div>
                    </td>
                    <td>
                        <div style="font-weight: 600; color: var(--error);">
                            ¥${item.expenseRate.toFixed(2)}
                        </div>
                    </td>
                    <td>
                        <div style="font-weight: 600; color: var(--brand-primary);">
                            ${item.profitRate.toFixed(1)}%
                        </div>
                    </td>
                    <td>
                        <span class="status-badge ${item.status === 'active' ? 'status-active' : 'status-inactive'}">
                            ${item.status === 'active' ? '启用' : '停用'}
                        </span>
                    </td>
                    <td>
                        <div class="action-group">
                            <button class="action-btn action-btn-edit" onclick="openIncomeExpenseForm('edit', ${item.id})">
                                <i class="fas fa-edit"></i>
                                编辑
                            </button>
                            <button class="action-btn action-btn-toggle" onclick="toggleIncomeExpenseStatus(${item.id})">
                                <i class="fas fa-${item.status === 'active' ? 'pause' : 'play'}"></i>
                                ${item.status === 'active' ? '停用' : '启用'}
                            </button>
                        </div>
                    </td>
                </tr>
            `).join('');
        }

        // 渲染人员表格
        function renderPersonnelTable() {
            const tbody = document.getElementById('personnelTableBody');

            if (personnelData.length === 0) {
                tbody.innerHTML = `
                    <tr>
                        <td colspan="6" class="empty-state">
                            <div class="empty-icon">
                                <i class="fas fa-users"></i>
                            </div>
                            <div class="empty-title">暂无人员</div>
                            <div class="empty-description">还没有添加任何主播人员信息</div>
                        </td>
                    </tr>
                `;
                return;
            }

            tbody.innerHTML = personnelData.map(item => `
                <tr>
                    <td>
                        <div style="font-weight: 500;">${item.name}</div>
                    </td>
                    <td>
                        <span class="status-badge status-active" style="background: var(--info-light); color: var(--info);">
                            ${item.levelName}
                        </span>
                    </td>
                    <td>
                        <div style="font-weight: 600; color: var(--success);">
                            ¥${item.salary.toLocaleString()}
                        </div>
                    </td>
                    <td>
                        <div style="color: var(--neutral-600);">
                            ${item.joinDate}
                        </div>
                    </td>
                    <td>
                        <span class="status-badge ${item.status === 'active' ? 'status-active' : 'status-inactive'}">
                            ${item.status === 'active' ? '在职' : '离职'}
                        </span>
                    </td>
                    <td>
                        <div class="action-group">
                            <button class="action-btn action-btn-edit" onclick="openPersonnelForm('edit', ${item.id})">
                                <i class="fas fa-edit"></i>
                                编辑
                            </button>
                            <button class="action-btn action-btn-toggle" onclick="togglePersonnelStatus(${item.id})">
                                <i class="fas fa-${item.status === 'active' ? 'user-times' : 'user-check'}"></i>
                                ${item.status === 'active' ? '离职' : '复职'}
                            </button>
                        </div>
                    </td>
                </tr>
            `).join('');
        }

        // 渲染级别表格
        function renderLevelTable() {
            const tbody = document.getElementById('levelTableBody');

            if (anchorLevels.length === 0) {
                tbody.innerHTML = `
                    <tr>
                        <td colspan="5" class="empty-state">
                            <div class="empty-icon">
                                <i class="fas fa-layer-group"></i>
                            </div>
                            <div class="empty-title">暂无级别</div>
                            <div class="empty-description">还没有创建任何主播级别</div>
                        </td>
                    </tr>
                `;
                return;
            }

            tbody.innerHTML = anchorLevels.map(item => `
                <tr>
                    <td>
                        <div>
                            <div style="font-weight: 500; margin-bottom: 4px;">${item.name}</div>
                            <div style="font-size: 12px; color: var(--neutral-500);">${item.description || '暂无描述'}</div>
                        </div>
                    </td>
                    <td>
                        <div style="color: var(--neutral-600);">
                            ${item.createdAt}
                        </div>
                    </td>
                    <td>
                        <div style="font-weight: 600; color: var(--brand-primary);">
                            ${item.personnelCount} 人
                        </div>
                    </td>
                    <td>
                        <span class="status-badge ${item.status === 'active' ? 'status-active' : 'status-inactive'}">
                            ${item.status === 'active' ? '启用' : '停用'}
                        </span>
                    </td>
                    <td>
                        <div class="action-group">
                            <button class="action-btn action-btn-edit" onclick="openLevelForm('edit', ${item.id})">
                                <i class="fas fa-edit"></i>
                                编辑
                            </button>
                            <button class="action-btn action-btn-toggle" onclick="toggleLevelStatus(${item.id})">
                                <i class="fas fa-${item.status === 'active' ? 'pause' : 'play'}"></i>
                                ${item.status === 'active' ? '停用' : '启用'}
                            </button>
                        </div>
                    </td>
                </tr>
            `).join('');
        }

        // 更新级别选项
        function updateLevelOptions() {
            const activeLevels = anchorLevels.filter(level => level.status === 'active');

            // 更新收入支出表单的级别选项
            const incomeExpenseLevelSelect = document.getElementById('incomeExpenseLevel');
            incomeExpenseLevelSelect.innerHTML = '<option value="">请选择主播级别</option>' +
                activeLevels.map(level => `<option value="${level.id}">${level.name}</option>`).join('');

            // 更新人员表单的级别选项
            const personnelLevelSelect = document.getElementById('personnelLevel');
            personnelLevelSelect.innerHTML = '<option value="">请选择主播级别</option>' +
                activeLevels.map(level => `<option value="${level.id}">${level.name}</option>`).join('');
        }

        // 收入支出相关函数
        function openIncomeExpenseForm(type, id = null) {
            currentEditType = type;
            currentEditData = id ? incomeExpenseData.find(item => item.id === id) : null;

            const modal = document.getElementById('incomeExpenseFormModal');
            const title = document.getElementById('incomeExpenseFormTitle');
            const form = document.getElementById('incomeExpenseForm');

            title.textContent = type === 'add' ? '新增收入支出配置' : '编辑收入支出配置';

            if (type === 'edit' && currentEditData) {
                document.getElementById('incomeExpenseLevel').value = currentEditData.levelId;
                document.getElementById('incomeHourlyRate').value = currentEditData.incomeRate;
                document.getElementById('expenseHourlyRate').value = currentEditData.expenseRate;
            } else {
                form.reset();
            }

            modal.classList.add('active');
            document.body.style.overflow = 'hidden';
        }

        function closeIncomeExpenseForm() {
            document.getElementById('incomeExpenseFormModal').classList.remove('active');
            document.body.style.overflow = 'auto';
            currentEditData = null;
            currentEditType = null;
        }

        function saveIncomeExpense() {
            const saveButton = document.querySelector('#incomeExpenseFormModal .btn-primary');
            const loadingElement = document.getElementById('incomeExpenseLoading');
            const saveTextElement = document.getElementById('incomeExpenseSaveText');

            // 显示加载状态
            loadingElement.style.display = 'flex';
            saveTextElement.style.display = 'none';
            saveButton.disabled = true;

            // 模拟异步保存
            setTimeout(() => {
                const levelId = parseInt(document.getElementById('incomeExpenseLevel').value);
                const incomeRate = parseFloat(document.getElementById('incomeHourlyRate').value);
                const expenseRate = parseFloat(document.getElementById('expenseHourlyRate').value);

                if (!levelId || !incomeRate || !expenseRate) {
                    showNotification('请填写所有必填字段', 'error');
                    resetSaveButton();
                    return;
                }

                // 检查是否已存在该级别的配置（编辑时排除自己）
                const existingItem = incomeExpenseData.find(item =>
                    item.levelId === levelId &&
                    (currentEditType === 'add' || item.id !== currentEditData.id)
                );

                if (existingItem) {
                    showNotification('该主播级别已存在配置，请选择其他级别', 'error');
                    resetSaveButton();
                    return;
                }

                const levelName = anchorLevels.find(level => level.id === levelId).name;
                const profitRate = calculateProfitRate(incomeRate, expenseRate);

                if (currentEditType === 'add') {
                    const newId = Math.max(...incomeExpenseData.map(item => item.id)) + 1;
                    incomeExpenseData.push({
                        id: newId,
                        levelId: levelId,
                        levelName: levelName,
                        incomeRate: incomeRate,
                        expenseRate: expenseRate,
                        profitRate: profitRate,
                        status: 'active'
                    });
                } else {
                    const index = incomeExpenseData.findIndex(item => item.id === currentEditData.id);
                    incomeExpenseData[index] = {
                        ...incomeExpenseData[index],
                        levelId: levelId,
                        levelName: levelName,
                        incomeRate: incomeRate,
                        expenseRate: expenseRate,
                        profitRate: profitRate
                    };
                }

                renderIncomeExpenseTable();
                closeIncomeExpenseForm();
                showNotification(currentEditType === 'add' ? '新增成功' : '编辑成功', 'success');
                resetSaveButton();
            }, 1000);

            function resetSaveButton() {
                loadingElement.style.display = 'none';
                saveTextElement.style.display = 'inline';
                saveButton.disabled = false;
            }
        }

        function toggleIncomeExpenseStatus(id) {
            const item = incomeExpenseData.find(item => item.id === id);
            if (item) {
                item.status = item.status === 'active' ? 'inactive' : 'active';
                renderIncomeExpenseTable();
                showNotification(`${item.status === 'active' ? '启用' : '停用'}成功`, 'success');
            }
        }

        // 人员相关函数
        function openPersonnelForm(type, id = null) {
            currentEditType = type;
            currentEditData = id ? personnelData.find(item => item.id === id) : null;

            const modal = document.getElementById('personnelFormModal');
            const title = document.getElementById('personnelFormTitle');
            const form = document.getElementById('personnelForm');

            title.textContent = type === 'add' ? '新增主播人员' : '编辑主播人员';

            if (type === 'edit' && currentEditData) {
                document.getElementById('personnelName').value = currentEditData.name;
                document.getElementById('personnelLevel').value = currentEditData.levelId;
                document.getElementById('personnelSalary').value = currentEditData.salary;
                document.getElementById('personnelJoinDate').value = currentEditData.joinDate;
            } else {
                form.reset();
                // 设置默认入职时间为今天
                document.getElementById('personnelJoinDate').value = new Date().toISOString().split('T')[0];
            }

            modal.classList.add('active');
            document.body.style.overflow = 'hidden';
        }

        function closePersonnelForm() {
            document.getElementById('personnelFormModal').classList.remove('active');
            document.body.style.overflow = 'auto';
            currentEditData = null;
            currentEditType = null;
        }

        function savePersonnel() {
            const saveButton = document.querySelector('#personnelFormModal .btn-primary');
            const loadingElement = document.getElementById('personnelLoading');
            const saveTextElement = document.getElementById('personnelSaveText');

            // 显示加载状态
            loadingElement.style.display = 'flex';
            saveTextElement.style.display = 'none';
            saveButton.disabled = true;

            // 模拟异步保存
            setTimeout(() => {
                const name = document.getElementById('personnelName').value.trim();
                const levelId = parseInt(document.getElementById('personnelLevel').value);
                const salary = parseFloat(document.getElementById('personnelSalary').value);
                const joinDate = document.getElementById('personnelJoinDate').value;

                if (!name || !levelId || !salary) {
                    showNotification('请填写所有必填字段', 'error');
                    resetSaveButton();
                    return;
                }

                // 检查是否已存在该人员（编辑时排除自己）
                const existingItem = personnelData.find(item =>
                    item.name === name &&
                    (currentEditType === 'add' || item.id !== currentEditData.id)
                );

                if (existingItem) {
                    showNotification('该主播人员已存在，请使用其他姓名', 'error');
                    resetSaveButton();
                    return;
                }

                const levelName = anchorLevels.find(level => level.id === levelId).name;

                if (currentEditType === 'add') {
                    const newId = Math.max(...personnelData.map(item => item.id)) + 1;
                    personnelData.push({
                        id: newId,
                        name: name,
                        levelId: levelId,
                        levelName: levelName,
                        salary: salary,
                        joinDate: joinDate || new Date().toISOString().split('T')[0],
                        status: 'active'
                    });
                } else {
                    const index = personnelData.findIndex(item => item.id === currentEditData.id);
                    personnelData[index] = {
                        ...personnelData[index],
                        name: name,
                        levelId: levelId,
                        levelName: levelName,
                        salary: salary,
                        joinDate: joinDate || personnelData[index].joinDate
                    };
                }

                updatePersonnelCount();
                renderPersonnelTable();
                renderLevelTable();
                closePersonnelForm();
                showNotification(currentEditType === 'add' ? '新增成功' : '编辑成功', 'success');
                resetSaveButton();
            }, 1000);

            function resetSaveButton() {
                loadingElement.style.display = 'none';
                saveTextElement.style.display = 'inline';
                saveButton.disabled = false;
            }
        }

        function togglePersonnelStatus(id) {
            const item = personnelData.find(item => item.id === id);
            if (item) {
                item.status = item.status === 'active' ? 'inactive' : 'active';
                updatePersonnelCount();
                renderPersonnelTable();
                renderLevelTable();
                showNotification(`${item.status === 'active' ? '复职' : '离职'}操作成功`, 'success');
            }
        }

        // 级别相关函数
        function openLevelForm(type, id = null) {
            currentEditType = type;
            currentEditData = id ? anchorLevels.find(item => item.id === id) : null;

            const modal = document.getElementById('levelFormModal');
            const title = document.getElementById('levelFormTitle');
            const form = document.getElementById('levelForm');

            title.textContent = type === 'add' ? '新增主播级别' : '编辑主播级别';

            if (type === 'edit' && currentEditData) {
                document.getElementById('levelName').value = currentEditData.name;
                document.getElementById('levelDescription').value = currentEditData.description || '';
            } else {
                form.reset();
            }

            modal.classList.add('active');
            document.body.style.overflow = 'hidden';
        }

        function closeLevelForm() {
            document.getElementById('levelFormModal').classList.remove('active');
            document.body.style.overflow = 'auto';
            currentEditData = null;
            currentEditType = null;
        }

        function saveLevel() {
            const saveButton = document.querySelector('#levelFormModal .btn-primary');
            const loadingElement = document.getElementById('levelLoading');
            const saveTextElement = document.getElementById('levelSaveText');

            // 显示加载状态
            loadingElement.style.display = 'flex';
            saveTextElement.style.display = 'none';
            saveButton.disabled = true;

            // 模拟异步保存
            setTimeout(() => {
                const name = document.getElementById('levelName').value.trim();
                const description = document.getElementById('levelDescription').value.trim();

                if (!name) {
                    showNotification('请填写主播级别名称', 'error');
                    resetSaveButton();
                    return;
                }

                // 检查是否已存在该级别名称（编辑时排除自己）
                const existingItem = anchorLevels.find(item =>
                    item.name === name &&
                    (currentEditType === 'add' || item.id !== currentEditData.id)
                );

                if (existingItem) {
                    showNotification('该主播级别名称已存在，请使用其他名称', 'error');
                    resetSaveButton();
                    return;
                }

                if (currentEditType === 'add') {
                    const newId = Math.max(...anchorLevels.map(item => item.id)) + 1;
                    anchorLevels.push({
                        id: newId,
                        name: name,
                        description: description,
                        status: 'active',
                        createdAt: new Date().toISOString().split('T')[0],
                        personnelCount: 0
                    });
                } else {
                    const index = anchorLevels.findIndex(item => item.id === currentEditData.id);
                    anchorLevels[index] = {
                        ...anchorLevels[index],
                        name: name,
                        description: description
                    };

                    // 更新其他表中对应的级别名称
                    incomeExpenseData.forEach(item => {
                        if (item.levelId === currentEditData.id) {
                            item.levelName = name;
                        }
                    });

                    personnelData.forEach(item => {
                        if (item.levelId === currentEditData.id) {
                            item.levelName = name;
                        }
                    });
                }

                renderLevelTable();
                renderIncomeExpenseTable();
                renderPersonnelTable();
                updateLevelOptions();
                closeLevelForm();
                showNotification(currentEditType === 'add' ? '新增成功' : '编辑成功', 'success');
                resetSaveButton();
            }, 1000);

            function resetSaveButton() {
                loadingElement.style.display = 'none';
                saveTextElement.style.display = 'inline';
                saveButton.disabled = false;
            }
        }

        function toggleLevelStatus(id) {
            const item = anchorLevels.find(item => item.id === id);
            if (item) {
                // 如果要停用级别，检查是否有关联数据
                if (item.status === 'active') {
                    const hasIncomeExpenseData = incomeExpenseData.some(data => data.levelId === id && data.status === 'active');
                    const hasPersonnelData = personnelData.some(data => data.levelId === id && data.status === 'active');

                    if (hasIncomeExpenseData || hasPersonnelData) {
                        if (!confirm('该级别下还有启用的配置数据，停用级别后相关数据也将被停用，确定要继续吗？')) {
                            return;
                        }

                        // 停用相关的收入支出配置
                        incomeExpenseData.forEach(data => {
                            if (data.levelId === id) {
                                data.status = 'inactive';
                            }
                        });

                        // 停用相关的人员配置
                        personnelData.forEach(data => {
                            if (data.levelId === id) {
                                data.status = 'inactive';
                            }
                        });
                    }
                }

                item.status = item.status === 'active' ? 'inactive' : 'active';
                updatePersonnelCount();
                renderLevelTable();
                renderIncomeExpenseTable();
                renderPersonnelTable();
                updateLevelOptions();
                showNotification(`${item.status === 'active' ? '启用' : '停用'}成功`, 'success');
            }
        }

        // 通知系统
        function showNotification(message, type = 'success') {
            // 移除现有通知
            const existingNotification = document.querySelector('.notification');
            if (existingNotification) {
                existingNotification.remove();
            }

            // 创建新通知
            const notification = document.createElement('div');
            notification.className = `notification notification-${type}`;

            const iconMap = {
                success: 'fas fa-check',
                error: 'fas fa-times',
                warning: 'fas fa-exclamation-triangle',
                info: 'fas fa-info'
            };

            notification.innerHTML = `
                <div class="notification-content">
                    <div class="notification-icon">
                        <i class="${iconMap[type]}"></i>
                    </div>
                    <div class="notification-message">${message}</div>
                </div>
            `;

            document.body.appendChild(notification);

            // 显示动画
            setTimeout(() => {
                notification.classList.add('show');
            }, 100);

            // 自动隐藏
            setTimeout(() => {
                notification.classList.remove('show');
                setTimeout(() => {
                    if (notification.parentNode) {
                        notification.parentNode.removeChild(notification);
                    }
                }, 300);
            }, 3000);
        }

        // 点击模态框外部关闭
        document.getElementById('settingsModal').addEventListener('click', function(e) {
            if (e.target === this) {
                closeSettingsModal();
            }
        });

        document.getElementById('incomeExpenseFormModal').addEventListener('click', function(e) {
            if (e.target === this) {
                closeIncomeExpenseForm();
            }
        });

        document.getElementById('personnelFormModal').addEventListener('click', function(e) {
            if (e.target === this) {
                closePersonnelForm();
            }
        });

        document.getElementById('levelFormModal').addEventListener('click', function(e) {
            if (e.target === this) {
                closeLevelForm();
            }
        });

        // ESC键关闭模态框
        document.addEventListener('keydown', function(e) {
            if (e.key === 'Escape') {
                if (document.getElementById('levelFormModal').classList.contains('active')) {
                    closeLevelForm();
                } else if (document.getElementById('personnelFormModal').classList.contains('active')) {
                    closePersonnelForm();
                } else if (document.getElementById('incomeExpenseFormModal').classList.contains('active')) {
                    closeIncomeExpenseForm();
                } else if (document.getElementById('settingsModal').classList.contains('active')) {
                    closeSettingsModal();
                }
            }
        });

        // 表单验证增强
        document.querySelectorAll('.form-input, .form-select').forEach(input => {
            input.addEventListener('blur', function() {
                if (this.hasAttribute('required') && !this.value.trim()) {
                    this.style.borderColor = 'var(--error)';
                    this.style.boxShadow = '0 0 0 3px rgba(220, 38, 38, 0.1)';
                } else {
                    this.style.borderColor = 'var(--neutral-300)';
                    this.style.boxShadow = 'none';
                }
            });

            input.addEventListener('focus', function() {
                this.style.borderColor = 'var(--brand-primary)';
                this.style.boxShadow = '0 0 0 3px rgba(37, 99, 235, 0.1)';
            });
        });

        // 数字输入框格式化
        document.querySelectorAll('input[type="number"]').forEach(input => {
            input.addEventListener('input', function() {
                if (this.value < 0) {
                    this.value = 0;
                }
            });
        });
    </script>
</body>

</html>