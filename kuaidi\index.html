<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>快递阶梯费用规则维护系统</title>
    <!-- 引入Element UI样式 -->
    <link rel="stylesheet" href="https://unpkg.com/element-ui@2.15.14/lib/theme-chalk/index.css">
    <link rel="icon" href="https://panhan.xin/assets/favicon-DfI5CYqk.ico">
    <!-- 引入Vue和Element UI组件库 -->
    <script src="https://unpkg.com/vue@2.6.14/dist/vue.js"></script>
    <script src="https://unpkg.com/element-ui@2.15.14/lib/index.js"></script>
    <!-- 引入Echarts图表库 -->
    <script src="https://cdn.jsdelivr.net/npm/echarts@5.4.3/dist/echarts.min.js"></script>
    <!-- 引入SheetJS库用于导出Excel -->
    <script src="https://cdn.jsdelivr.net/npm/xlsx@0.18.5/dist/xlsx.full.min.js"></script>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
            color: #333;
            margin: 0;
            padding: 20px;
            background-color: #f5f7fa;
        }
        /* 日期显示样式 */
        
        .date-container {
            position: relative;
            width: 100%;
        }
        
        .date-container .el-input__inner {
            visibility: hidden;
            height: 0;
            padding: 0;
            border: none;
        }
        
        .date-container .el-input__prefix,
        .date-container .el-input__suffix {
            display: none;
        }
        
        .date-container .el-date-editor {
            width: 100%;
            margin-bottom: 10px;
        }
        
        .date-display {
            height: 36px;
            line-height: 36px;
            padding: 0 15px;
            border: 1px solid #dcdfe6;
            border-radius: 4px;
            background-color: #f5f7fa;
            font-size: 14px;
            color: #606266;
            cursor: pointer;
            transition: all .3s;
            display: flex;
            align-items: center;
        }
        
        .date-display:hover {
            border-color: #c0c4cc;
        }
        
        .date-display::before {
            content: '\e908';
            font-family: 'element-icons';
            margin-right: 8px;
            color: #c0c4cc;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .header {
            background-color: #fff;
            padding: 15px 20px;
            border-radius: 4px;
            box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
            margin-bottom: 20px;
        }
        
        .content {
            background-color: #fff;
            padding: 20px;
            border-radius: 4px;
            box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
        }
        
        .section {
            margin-bottom: 20px;
        }
        
        .section-title {
            font-weight: 600;
            margin-bottom: 15px;
            color: #303133;
            position: relative;
            padding-left: 10px;
        }
        
        .section-title:before {
            content: '';
            position: absolute;
            left: 0;
            top: 50%;
            transform: translateY(-50%);
            width: 4px;
            height: 16px;
            background-color: #409EFF;
            border-radius: 2px;
        }
        
        .form-actions {
            text-align: right;
            margin-top: 20px;
        }
        
        .fee-rules-table .el-table__header th {
            background-color: #f5f7fa;
        }
        
        .custom-tabs .el-tabs__nav {
            background-color: #f5f7fa;
            border-radius: 4px;
            padding: 5px;
        }
        
        .custom-tabs .el-tabs__item {
            height: 40px;
            line-height: 40px;
        }
        
        .custom-tabs .el-tabs__item.is-active {
            background-color: #fff;
            border-radius: 4px;
        }
        
        .stat-card {
            background-color: #fff;
            border-radius: 4px;
            padding: 15px;
            box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
            margin-bottom: 20px;
        }
        
        .stat-card .title {
            font-size: 14px;
            color: #909399;
        }
        
        .stat-card .value {
            font-size: 24px;
            font-weight: bold;
            color: #303133;
            margin: 10px 0;
        }
        
        .stat-card .footer {
            font-size: 12px;
            color: #909399;
        }
        
        .highlight {
            color: #409EFF;
        }
        /* 表格单元格内容样式 */
        
        .fee-rules-table .cell {
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }
        /* 表格操作时间列样式 */
        
        .operate-time-column {
            white-space: nowrap;
        }
        /* 不换行的单元格内容 */
        
        .nowrap-cell {
            white-space: nowrap;
            display: inline-block;
        }
        /* 控制Element UI表格全局单元格样式 */
        
        .el-table .cell {
            white-space: nowrap !important;
        }
        /* 右侧滚动时的样式 */
        
        .el-table--enable-right-scrolling .el-table__fixed-right {
            box-shadow: none !important;
            border-left: 1px solid #EBEEF5;
            background-color: rgba(255, 255, 255, 0.9);
            transition: box-shadow 0.3s ease-in-out;
        }
        /* 表格内容区域允许滚动 */
        
        .fee-rules-table .el-table__body-wrapper {
            overflow-x: auto !important;
        }
        /* 确保固定列可见 */
        
        .el-table__fixed-right {
            height: auto !important;
            z-index: 2;
            box-shadow: -2px 0 10px rgba(0, 0, 0, 0.12);
        }
        /* 滚动到最右侧时隐藏固定列 */
        
        .fee-rules-table .el-table__body-wrapper.is-scrolling-right .el-table__fixed-right {
            visibility: hidden;
            opacity: 0;
            transition: opacity 0.3s;
        }
        /* 图表容器样式 */
        
        .chart-container {
            padding: 20px;
            background-color: #fff;
            border-radius: 4px;
            box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
            margin-top: 15px;
        }
        
        .chart-container>div {
            background-color: #fff;
            border-radius: 4px;
            padding: 15px;
            margin-bottom: 20px;
            box-shadow: 0 1px 6px rgba(0, 0, 0, 0.05);
        }
        /* 统计分析页面样式 */
        
        .analysis-intro {
            background-color: #f0f9eb;
            padding: 15px;
            border-radius: 4px;
            margin-bottom: 20px;
            display: flex;
            align-items: center;
        }
        
        .analysis-intro i {
            font-size: 24px;
            color: #67C23A;
            margin-right: 10px;
        }
        
        .analysis-intro span {
            color: #606266;
            line-height: 1.6;
        }
        
        .statistics-form {
            background-color: #f5f7fa;
            padding: 15px;
            border-radius: 4px;
            margin-bottom: 15px;
        }
        
        .chart-title {
            font-size: 16px;
            font-weight: bold;
            color: #303133;
            margin-bottom: 10px;
            padding-left: 10px;
            border-left: 3px solid #409EFF;
        }
        
        .chart-subtitle {
            font-size: 12px;
            font-weight: normal;
            color: #909399;
            margin-left: 10px;
        }
        /* 调整类型相关样式 */
        
        .adjustment-type-item {
            display: flex;
            align-items: center;
        }
        
        .adjustment-type-icon {
            display: inline-flex;
            align-items: center;
            justify-content: center;
            width: 20px;
            height: 20px;
            border-radius: 4px;
            margin-right: 8px;
            color: #fff;
        }
        
        .increase-ratio {
            background-color: #409EFF;
        }
        
        .increase-amount {
            background-color: #67C23A;
        }
        
        .decrease-ratio {
            background-color: #F56C6C;
        }
        
        .decrease-amount {
            background-color: #E6A23C;
        }
        /* 设置El-select组件选中项的样式 */
        
        .el-select .el-input.is-focus .el-input__inner {
            border-color: #DCDFE6;
        }
        
        .adjustment-type-hint {
            margin-top: 5px;
            color: #909399;
            font-size: 12px;
            line-height: 1.4;
        }
        /* 表格中的调整类型样式 */
        
        .adjustment-type-label {
            display: inline-flex;
            align-items: center;
            padding: 2px 8px;
            border-radius: 3px;
            color: #fff;
            font-size: 12px;
            white-space: nowrap;
        }
        
        .adjustment-type-label i {
            margin-right: 4px;
        }
        
        .calculation-example {
            margin-top: 8px;
            font-size: 12px;
            color: #409EFF;
            display: flex;
            align-items: flex-start;
        }
        
        .calculation-example .el-icon-info {
            margin-right: 5px;
            font-size: 14px;
        }
        /* 下拉框内的调整类型项也应用样式标签 */
        
        .el-select-dropdown__item .adjustment-type-icon {
            display: inline-flex;
            align-items: center;
            justify-content: center;
            width: 20px;
            height: 20px;
            border-radius: 4px;
            margin-right: 8px;
            color: #fff;
        }
        /* 添加Ant Design风格的样式 */
        
        .ant-form-item {
            margin-bottom: 20px;
        }
        
        .ant-form-item-label {
            text-align: right;
            color: rgba(0, 0, 0, 0.85);
            font-weight: normal;
            font-size: 14px;
            line-height: 1.5;
            padding-right: 12px;
        }
        
        .ant-form-item-tip {
            color: rgba(0, 0, 0, 0.45);
            font-size: 12px;
            margin-top: 4px;
            line-height: 1.5;
        }
        
        .ant-form-item-warning {
            color: #faad14;
        }
        
        .ant-form-item-error {
            color: #f5222d;
        }
        
        .ant-input-number {
            width: 100%;
            padding: 4px 11px;
            border: 1px solid #d9d9d9;
            border-radius: 2px;
            transition: all 0.3s;
        }
        
        .ant-input-number:hover {
            border-color: #40a9ff;
        }
        
        .ant-input-number:focus {
            border-color: #40a9ff;
            outline: 0;
            box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
        }
        
        .ant-btn {
            line-height: 1.5;
            display: inline-block;
            font-weight: 400;
            text-align: center;
            touch-action: manipulation;
            cursor: pointer;
            background-image: none;
            border: 1px solid transparent;
            white-space: nowrap;
            padding: 0 15px;
            font-size: 14px;
        }
        /* 日期值显示样式 */
        
        .date-value-display {
            margin-top: 8px;
            padding: 8px 12px;
            background-color: #f8f9fa;
            border-radius: 4px;
            border-left: 3px solid #409EFF;
            font-size: 13px;
            color: #606266;
        }
        
        .date-highlight {
            color: #409EFF;
            font-weight: 500;
            margin-left: 4px;
        }
        /* 月份日期显示样式 */
        
        .month-date-display {
            margin-top: 5px;
            padding: 3px 8px;
            background-color: #f5f7fa;
            border-radius: 3px;
            color: #606266;
            font-size: 12px;
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
        }
        /* 模拟日期输入框样式 */
        
        .date-input-wrapper {
            position: relative;
            width: 100%;
        }
        
        .fake-date-input {
            position: relative;
            display: inline-block;
            width: 100%;
            height: 32px;
            /* 根据Element UI small尺寸调整 */
            padding: 0 30px 0 15px;
            /* 留出图标空间 */
            line-height: 30px;
            /* 调整行高使其垂直居中 */
            font-size: 13px;
            /* 根据Element UI small尺寸调整 */
            color: #606266;
            background-color: #fff;
            background-image: none;
            border: 1px solid #dcdfe6;
            border-radius: 4px;
            box-sizing: border-box;
            cursor: pointer;
            transition: border-color .2s cubic-bezier(.645, .045, .355, 1);
            overflow: hidden;
            white-space: nowrap;
            text-overflow: ellipsis;
        }
        
        .fake-date-input:hover {
            border-color: #c0c4cc;
        }
        
        .fake-date-input .el-input__icon {
            position: absolute;
            right: 10px;
            top: 50%;
            transform: translateY(-50%);
            color: #c0c4cc;
            line-height: normal;
            /* 确保图标垂直居中 */
            width: auto;
            /* 覆盖可能存在的继承宽度 */
            height: auto;
            /* 覆盖可能存在的继承高度 */
        }
        /* 隐藏真实的日期选择器输入部分 */
        
        .hidden-date-picker {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            opacity: 0;
            z-index: -1;
            /* 确保在下层 */
            pointer-events: none;
            /* 不直接响应事件 */
        }
        
        .hidden-date-picker .el-input__inner {
            height: 0 !important;
            padding: 0 !important;
            border: none !important;
            opacity: 0;
        }
        
        .hidden-date-picker .el-input__prefix,
        .hidden-date-picker .el-input__suffix {
            display: none !important;
        }
    </style>
</head>

<body>
    <div id="app">
        <div class="container">
            <div class="header">
                <h2 style="margin: 0;">快递阶梯费用规则维护</h2>
            </div>

            <div class="content">
                <el-tabs v-model="activeTab" class="custom-tabs" @tab-click="handleTabClick">
                    <el-tab-pane label="规则配置" name="rules">
                        <div class="section">
                            <div class="section-title">阶梯费用规则列表</div>
                            <div style="display: flex; margin-bottom: 15px;">
                                <el-button type="primary" @click="handleAddRule" size="small">新增规则</el-button>
                                <!-- <el-button size="small" @click="handleBatchImport">批量导入</el-button> -->
                                <el-button size="small" @click="handleExport">导出</el-button>
                            </div>
                            <el-table :data="feeRules" border style="width: 100%" class="fee-rules-table" :max-height="500">
                                <el-table-column prop="id" label="规则ID" width="80"></el-table-column>
                                <el-table-column label="单量区间" width="160">
                                    <template slot-scope="scope">
                                        <span class="nowrap-cell">{{ scope.row.minOrders }}-{{ scope.row.maxOrders || '∞' }}单</span>
                                    </template>
                                </el-table-column>
                                <el-table-column label="重量区间" width="160">
                                    <template slot-scope="scope">
                                        <span class="nowrap-cell">{{ scope.row.minWeight }}-{{ scope.row.maxWeight || '∞' }}kg</span>
                                    </template>
                                </el-table-column>
                                <el-table-column label="价格" width="120">
                                    <template slot-scope="scope">
                                        <span class="nowrap-cell">{{ scope.row.price }}元/单</span>
                                    </template>
                                </el-table-column>
                                <el-table-column prop="effectiveDate" label="生效日期" width="100">
                                    <template slot-scope="scope">
                                        {{ formatDate(scope.row.effectiveDate) }}
                                    </template>
                                </el-table-column>
                                <el-table-column label="结束日期" width="120">
                                    <template slot-scope="scope">
                                        {{ formatDate(scope.row.endDate) }}
                                    </template>
                                </el-table-column>
                                <el-table-column prop="remark" label="备注" width="150" show-overflow-tooltip></el-table-column>
                                <el-table-column prop="attachment" label="附件" width="100">
                                    <template slot-scope="scope">
                                        <el-button v-if="scope.row.attachment" type="text" size="small" @click="downloadAttachment(scope.row)">
                                            <i class="el-icon-download"></i> 下载
                                        </el-button>
                                    </template>
                                </el-table-column>
                                <el-table-column prop="operateTime" label="操作时间" width="180">
                                    <template slot-scope="scope">
                                        <span class="nowrap-cell">{{ formatOperateTime(scope.row.operateTime) }}</span>
                                    </template>
                                </el-table-column>
                                <el-table-column prop="operator" label="操作人" width="100"></el-table-column>
                                <el-table-column prop="status" label="状态" width="80" fixed="right">
                                    <template slot-scope="scope">
                                        <el-tag :type="scope.row.status === '生效中' ? 'success' : 'info'">
                                            {{ scope.row.status }}
                                        </el-tag>
                                    </template>
                                </el-table-column>
                                <el-table-column label="操作" width="180" fixed="right">
                                    <template slot-scope="scope">
                                        <el-button type="text" size="small" @click="handleEditRule(scope.row)">编辑/停用</el-button>
                                        <el-button type="text" size="small" @click="handleCopyRule(scope.row)">复制</el-button>
                                    </template>
                                </el-table-column>
                            </el-table>
                            <div style="margin-top: 15px; text-align: right;">
                                <el-pagination @size-change="handleSizeChange" @current-change="handleCurrentChange" :current-page="pagination.currentPage" :page-sizes="[10, 20, 50, 100]" :page-size="pagination.pageSize" layout="total, sizes, prev, pager, next, jumper" :total="pagination.total">
                                </el-pagination>
                            </div>
                        </div>
                    </el-tab-pane>

                    <el-tab-pane label="附加规则" name="additional-rules">
                        <div class="section">
                            <div class="section-title">附加规则列表</div>
                            <div style="display: flex; margin-bottom: 15px;">
                                <el-button type="primary" @click="handleAddAdditionalRule" size="small">新增附加规则</el-button>
                                <!-- <el-button size="small" @click="handleBatchImportAdditional">批量导入</el-button> -->
                                <el-button size="small" @click="handleExportAdditional">导出</el-button>
                            </div>
                            <el-table :data="additionalRules" border style="width: 100%" class="fee-rules-table" :max-height="500">
                                <el-table-column prop="id" label="规则ID" width="80"></el-table-column>
                                <el-table-column prop="adjustmentType" label="增减类型" width="120">
                                    <template slot-scope="scope">
                                        <span class="adjustment-type-label" :class="getAdjustmentTypeClass(scope.row.adjustmentType)">
                                            <i :class="getAdjustmentTypeIcon(scope.row.adjustmentType)"></i>
                                            {{ scope.row.adjustmentType }}
                                        </span>
                                    </template>
                                </el-table-column>
                                <el-table-column prop="adjustmentValue" label="增减幅度" width="120">
                                    <template slot-scope="scope">
                                        <span>
                                            {{ scope.row.adjustmentValue }}
                                            <span v-if="scope.row.adjustmentType && scope.row.adjustmentType.includes('比率')">%</span>
                                            <span v-else-if="scope.row.adjustmentType && scope.row.adjustmentType.includes('金额')">元</span>
                                        </span>
                                    </template>
                                </el-table-column>
                                <el-table-column prop="region" label="区域" width="120"></el-table-column>
                                <el-table-column prop="teamName" label="团队" width="120"></el-table-column>
                                <el-table-column prop="shopName" label="店铺" width="120"></el-table-column>
                                <el-table-column label="重量阶梯" width="180">
                                    <template slot-scope="scope">
                                        <span class="nowrap-cell">{{ scope.row.minWeight }}-{{ scope.row.maxWeight || '∞' }}kg</span>
                                    </template>
                                </el-table-column>
                                <el-table-column label="生效期间" width="240">
                                    <template slot-scope="scope">
                                        <span class="nowrap-cell">{{ scope.row.startDate }} 至 {{ scope.row.endDate || '长期' }}</span>
                                    </template>
                                </el-table-column>
                                <el-table-column prop="status" label="状态" width="80" fixed="right">
                                    <template slot-scope="scope">
                                        <el-tag :type="scope.row.status === '生效中' ? 'success' : 'info'">
                                            {{ scope.row.status }}
                                        </el-tag>
                                    </template>
                                </el-table-column>
                                <el-table-column label="操作" width="180" fixed="right">
                                    <template slot-scope="scope">
                                        <el-button type="text" size="small" @click="handleEditAdditionalRule(scope.row)">编辑</el-button>
                                        <el-button type="text" size="small" @click="handleCopyAdditionalRule(scope.row)">复制</el-button>
                                        <el-button type="text" size="small" @click="handleDeleteAdditionalRule(scope.row)" style="color: #F56C6C;">删除</el-button>
                                    </template>
                                </el-table-column>
                            </el-table>
                            <div style="margin-top: 15px; text-align: right;">
                                <el-pagination @size-change="handleAdditionalSizeChange" @current-change="handleAdditionalCurrentChange" :current-page="additionalPagination.currentPage" :page-sizes="[10, 20, 50, 100]" :page-size="additionalPagination.pageSize" layout="total, sizes, prev, pager, next, jumper"
                                    :total="additionalPagination.total">
                                </el-pagination>
                            </div>
                        </div>
                    </el-tab-pane>

                    <el-tab-pane label="费用计算" name="calculation">
                        <div class="section">
                            <div class="section-title">团队月度单量统计</div>
                            <el-form :model="calculationForm" label-width="100px" size="small">
                                <el-row :gutter="20">
                                    <el-col :span="12">
                                        <el-form-item label="选择团队">
                                            <el-select v-model="calculationForm.teamId" placeholder="请选择团队" style="width: 100%">
                                                <el-option v-for="team in teams" :key="team.id" :label="team.name" :value="team.id">
                                                </el-option>
                                            </el-select>
                                        </el-form-item>
                                    </el-col>
                                    <el-col :span="12">
                                        <el-form-item label="统计月份">
                                            <el-date-picker v-model="calculationForm.month" type="month" placeholder="选择月份" style="width: 100%">
                                            </el-date-picker>
                                        </el-form-item>
                                    </el-col>
                                </el-row>
                                <div class="form-actions">
                                    <el-button type="primary" @click="calculateFee">计算费用</el-button>
                                </div>
                            </el-form>
                        </div>

                        <div class="section" v-if="calculationResult.show">
                            <div class="section-title">计算结果</div>
                            <el-row :gutter="20">
                                <el-col :span="8">
                                    <div class="stat-card">
                                        <div class="title">团队总单量</div>
                                        <div class="value">{{ calculationResult.totalOrders }} <span style="font-size: 14px;">单</span></div>
                                        <div class="footer">
                                            较上月 <span :class="{ highlight: calculationResult.orderGrowth > 0 }">
                                                {{ calculationResult.orderGrowth > 0 ? '+' : '' }}{{ calculationResult.orderGrowth }}%
                                            </span>
                                        </div>
                                    </div>
                                </el-col>
                                <el-col :span="8">
                                    <div class="stat-card">
                                        <div class="title">当前所属阶梯</div>
                                        <div class="value">{{ calculationResult.currentTier }}</div>
                                        <div class="footer">
                                            费率: <span class="highlight">{{ calculationResult.currentRate }}元/单</span>
                                        </div>
                                    </div>
                                </el-col>
                                <el-col :span="8">
                                    <div class="stat-card">
                                        <div class="title">预计总费用</div>
                                        <div class="value">¥ {{ calculationResult.totalFee }}</div>
                                        <div class="footer">
                                            较上月 <span :class="{ highlight: calculationResult.feeGrowth < 0 }">
                                                {{ calculationResult.feeGrowth > 0 ? '+' : '' }}{{ calculationResult.feeGrowth }}%
                                            </span>
                                        </div>
                                    </div>
                                </el-col>
                            </el-row>

                            <el-divider content-position="left">适用规则</el-divider>
                            <el-descriptions :column="2" border>
                                <el-descriptions-item label="规则ID">{{ calculationResult.rule.id }}</el-descriptions-item>
                                <el-descriptions-item label="单量区间">{{ calculationResult.rule.minOrders }}-{{ calculationResult.rule.maxOrders || '∞' }}单</el-descriptions-item>
                                <el-descriptions-item label="重量区间">{{ calculationResult.rule.minWeight }}-{{ calculationResult.rule.maxWeight || '∞' }}kg</el-descriptions-item>
                                <el-descriptions-item label="价格">{{ calculationResult.rule.price }}元/单</el-descriptions-item>
                                <el-descriptions-item label="生效日期">{{ calculationResult.rule.effectiveDate }}</el-descriptions-item>
                                <el-descriptions-item label="备注">{{ calculationResult.rule.remark || '无' }}</el-descriptions-item>
                            </el-descriptions>

                            <el-divider content-position="left">店铺明细</el-divider>

                            <el-table :data="calculationResult.shopDetails" border style="width: 100%">
                                <el-table-column prop="name" label="店铺名称"></el-table-column>
                                <el-table-column prop="orders" label="单量" width="100">
                                    <template slot-scope="scope">
                                        {{ scope.row.orders }} 单
                                    </template>
                                </el-table-column>
                                <el-table-column prop="percentage" label="占比" width="100">
                                    <template slot-scope="scope">
                                        <el-progress :percentage="scope.row.percentage" :color="scope.row.color"></el-progress>
                                    </template>
                                </el-table-column>
                                <el-table-column prop="fee" label="费用" width="120">
                                    <template slot-scope="scope">
                                        ¥ {{ scope.row.fee }}
                                    </template>
                                </el-table-column>
                            </el-table>
                        </div>
                    </el-tab-pane>

                    <el-tab-pane label="统计分析" name="statistics">
                        <div class="section">
                            <div class="section-title">单量与费用走势分析</div>
                            <el-row :gutter="20">
                                <el-col :span="24">
                                    <div class="analysis-intro">
                                        <i class="el-icon-data-analysis"></i>
                                        <span>通过选择团队和时间范围，您可以查看详细的订单量和费用走势数据，帮助您进行业务决策和成本控制。</span>
                                    </div>
                                </el-col>
                            </el-row>
                            <el-form :inline="true" :model="statisticsForm" size="small" class="statistics-form">
                                <el-form-item label="团队">
                                    <el-select v-model="statisticsForm.teamId" placeholder="请选择团队" style="width: 160px;">
                                        <el-option v-for="team in teams" :key="team.id" :label="team.name" :value="team.id">
                                        </el-option>
                                    </el-select>
                                </el-form-item>
                                <el-form-item label="时间范围">
                                    <el-date-picker v-model="statisticsForm.dateRange" type="daterange" range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期" style="width: 320px;">
                                    </el-date-picker>
                                </el-form-item>
                                <el-form-item>
                                    <el-button type="primary" @click="loadStatistics">
                                        <i class="el-icon-search"></i> 查询
                                    </el-button>
                                    <el-button type="success" @click="exportStatistics">
                                        <i class="el-icon-download"></i> 导出Excel
                                    </el-button>
                                </el-form-item>
                            </el-form>

                            <div class="chart-container">
                                <div class="chart-title">订单量走势<span class="chart-subtitle">查看每月订单量变化趋势</span></div>
                                <div id="ordersChart" style="height: 350px;"></div>

                                <div class="chart-title">费用走势<span class="chart-subtitle">查看每月快递费用支出情况</span></div>
                                <div id="feeChart" style="height: 350px;"></div>
                            </div>
                        </div>
                    </el-tab-pane>
                </el-tabs>
            </div>
        </div>

        <!-- 规则编辑对话框 -->
        <el-dialog :title="dialogTitle" :visible.sync="dialogVisible" width="650px">
            <el-form :model="ruleForm" label-width="100px" size="small">
                <el-form-item label="单量区间" required>
                    <div style="display: flex; align-items: center;">
                        <el-input-number v-model="ruleForm.minOrders" :min="0" :controls="false" placeholder="最小单量" :disabled="dialogTitle === '编辑规则'"></el-input-number>
                        <span style="margin: 0 10px;">-</span>
                        <el-input-number v-model="ruleForm.maxOrders" :min="ruleForm.minOrders ? ruleForm.minOrders + 1 : 1" :controls="false" placeholder="最大单量" :disabled="dialogTitle === '编辑规则'"></el-input-number>
                        <span style="margin-left: 10px;">单</span>
                    </div>
                    <div class="el-form-item__tip" style="color: #909399; font-size: 12px; margin-top: 5px;">
                        起始单量必须小于结束单量
                        <span v-if="dialogTitle === '编辑规则'" style="color: #F56C6C;">（单量区间在编辑模式下不可修改）</span>
                    </div>
                </el-form-item>
                <el-form-item label="重量区间" required>
                    <div style="display: flex; align-items: center;">
                        <el-input-number v-model="ruleForm.minWeight" :min="0" :precision="2" :step="0.1" :controls="false" placeholder="最小重量" :disabled="dialogTitle === '编辑规则'"></el-input-number>
                        <span style="margin: 0 10px;">-</span>
                        <el-input-number v-model="ruleForm.maxWeight" :min="ruleForm.minWeight ? ruleForm.minWeight + 0.01 : 0.01" :precision="2" :step="0.1" :controls="false" placeholder="最大重量" :disabled="dialogTitle === '编辑规则'"></el-input-number>
                        <span style="margin-left: 10px;">kg</span>
                    </div>
                    <div class="el-form-item__tip" style="color: #909399; font-size: 12px; margin-top: 5px;">
                        起始重量必须小于结束重量
                        <span v-if="dialogTitle === '编辑规则'" style="color: #F56C6C;">（重量区间在编辑模式下不可修改）</span>
                    </div>
                </el-form-item>
                <el-form-item label="价格">
                    <div style="display: flex; align-items: center;">
                        <el-input-number v-model="ruleForm.price" :precision="2" :step="0.01" :min="0" :controls="false" placeholder="单价"></el-input-number>
                        <span style="margin-left: 10px;">元/单</span>
                    </div>
                </el-form-item>
                <el-form-item label="生效日期" required>
                    <div class="date-input-wrapper">
                        <div class="fake-date-input" @click="openDatePicker('effectiveDatePicker')">
                            <i class="el-input__icon el-icon-date"></i>
                            <span>{{ ruleForm.effectiveDate || '选择日期' }}</span>
                        </div>
                        <el-date-picker ref="effectiveDatePicker" v-model="ruleForm.effectiveDate" type="date" placeholder="选择日期" class="hidden-date-picker" value-format="yyyy-MM-dd" :picker-options="{
                                disabledDate(time) {
                                    const now = new Date();
                                    const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());
                                    return time.getTime() < today.getTime();
                                }
                            }" @change="handleEffectiveDateChange">
                        </el-date-picker>
                    </div>
                </el-form-item>
                <el-form-item label="结束日期">
                    <div class="date-input-wrapper">
                        <div class="fake-date-input" @click="openDatePicker('endDatePicker')">
                            <i class="el-input__icon el-icon-date"></i>
                            <span>{{ ruleForm.endDate || '选择日期（不选则长期有效）' }}</span>
                        </div>
                        <el-date-picker ref="endDatePicker" v-model="ruleForm.endDate" type="date" placeholder="选择日期（不选则长期有效）" class="hidden-date-picker" value-format="yyyy-MM-dd" :picker-options="{
                                disabledDate: (time) => {
                                    const now = new Date();
                                    const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());
                                    // 禁用当前日期之前的日期
                                    if (time.getTime() < today.getTime()) {
                                        return true;
                                    }
                                    // 如果生效日期已选，则禁用生效日期之前的日期
                                    if (this.ruleForm.effectiveDate) {
                                        const startDate = new Date(this.ruleForm.effectiveDate);
                                        return time.getTime() < startDate.getTime();
                                    }
                                    return false;
                                }
                            }" @change="handleEndDateChange">
                        </el-date-picker>
                    </div>
                </el-form-item>
                <el-form-item label="备注">
                    <el-input type="textarea" v-model="ruleForm.remark" placeholder="请输入备注信息" :rows="3"></el-input>
                </el-form-item>
            </el-form>
            <span slot="footer" class="dialog-footer">
                <el-button @click="dialogVisible = false">取 消</el-button>
                <el-button type="primary" @click="saveRule">确 定</el-button>
            </span>
        </el-dialog>

        <!-- 附加规则编辑对话框 -->
        <el-dialog :title="additionalDialogTitle" :visible.sync="additionalDialogVisible" width="700px">
            <el-form :model="additionalRuleForm" label-width="120px" size="small">
                <el-form-item label="增减类型" required>
                    <el-select v-model="additionalRuleForm.adjustmentType" placeholder="请选择类型" style="width: 100%">
                        <el-option v-for="item in adjustmentTypes" :key="item.value" :label="item.label" :value="item.label">
                            <div class="adjustment-type-item">
                                <span class="adjustment-type-icon" :class="getAdjustmentTypeClass(item.label)">
                                    <i :class="getAdjustmentTypeIcon(item.label)"></i>
                                </span>
                                <span>{{ item.label }}</span>
                            </div>
                        </el-option>
                    </el-select>
                    <div class="adjustment-type-hint" v-if="additionalRuleForm.adjustmentType">
                        <span v-if="additionalRuleForm.adjustmentType === '增加比率'">运费将按照原价增加指定百分比</span>
                        <span v-else-if="additionalRuleForm.adjustmentType === '增加金额'">运费将在原价基础上增加固定金额</span>
                        <span v-else-if="additionalRuleForm.adjustmentType === '减少比率'">运费将按照原价降低指定百分比</span>
                        <span v-else-if="additionalRuleForm.adjustmentType === '减少金额'">运费将在原价基础上减少固定金额</span>
                    </div>
                </el-form-item>
                <el-form-item label="增减幅度" required>
                    <div style="display: flex; align-items: center; position: relative;">
                        <el-input-number v-model="additionalRuleForm.adjustmentValue" :precision="2" :step="0.1" :min="0" style="width: 100%" controls-position="right"></el-input-number>
                        <span style="position: absolute; right: 50px; color: #606266; font-size: 14px;" v-if="additionalRuleForm.adjustmentType && additionalRuleForm.adjustmentType.includes('比率')">%</span>
                        <span style="position: absolute; right: 50px; color: #606266; font-size: 14px;" v-else-if="additionalRuleForm.adjustmentType && additionalRuleForm.adjustmentType.includes('金额')">元</span>
                    </div>
                    <div class="calculation-example" v-if="additionalRuleForm.adjustmentType && additionalRuleForm.adjustmentValue > 0">
                        <i class="el-icon-info" style="margin-top: 3px;"></i>
                        <span v-if="additionalRuleForm.adjustmentType === '增加比率'">示例：原价10元，增加{{additionalRuleForm.adjustmentValue}}%，最终价格为{{(10 * (1 + additionalRuleForm.adjustmentValue / 100)).toFixed(2)}}元</span>
                        <span v-else-if="additionalRuleForm.adjustmentType === '增加金额'">示例：原价10元，增加{{additionalRuleForm.adjustmentValue}}元，最终价格为{{(10 + additionalRuleForm.adjustmentValue).toFixed(2)}}元</span>
                        <span v-else-if="additionalRuleForm.adjustmentType === '减少比率'">示例：原价10元，降低{{additionalRuleForm.adjustmentValue}}%，最终价格为{{(10 * (1 - additionalRuleForm.adjustmentValue / 100)).toFixed(2)}}元</span>
                        <span v-else-if="additionalRuleForm.adjustmentType === '减少金额'">示例：原价10元，减少{{additionalRuleForm.adjustmentValue}}元，最终价格为{{Math.max(0, (10 - additionalRuleForm.adjustmentValue)).toFixed(2)}}元</span>
                    </div>
                </el-form-item>
                <el-form-item label="适用区域" prop="regionIds">
                    <el-cascader v-model="additionalRuleForm.regionIds" :options="regions" :props="{ multiple: true, checkStrictly: true, emitPath: false }" placeholder="请选择适用区域" clearable collapse-tags style="width: 100%">
                    </el-cascader>
                </el-form-item>
                <el-form-item label="适用团队">
                    <el-select v-model="additionalRuleForm.teamId" placeholder="全部团队" clearable @change="handleTeamChange" style="width: 100%">
                        <el-option v-for="team in teams" :key="team.id" :label="team.name" :value="team.id">
                        </el-option>
                    </el-select>
                </el-form-item>
                <el-form-item label="适用店铺">
                    <el-select v-model="additionalRuleForm.shopIds" multiple placeholder="全部店铺" style="width: 100%">
                        <el-option v-for="shop in shops.filter(s => !additionalRuleForm.teamId || s.teamId === additionalRuleForm.teamId)" :key="shop.id" :label="shop.name" :value="shop.id">
                        </el-option>
                    </el-select>
                </el-form-item>
                <el-form-item label="重量区间">
                    <el-select v-model="additionalRuleForm.weightRangeIds" multiple placeholder="全部重量区间" style="width: 100%">
                        <el-option v-for="range in weightRanges" :key="range.id" :label="range.minWeight + (range.maxWeight ? '-' + range.maxWeight : '以上') + 'kg'" :value="range.id">
                        </el-option>
                    </el-select>
                </el-form-item>
                <el-form-item label="生效日期" style="margin-bottom: 10px;">
                    <el-date-picker v-model="additionalRuleForm.dateRange" type="daterange" range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期" style="width: 100%">
                    </el-date-picker>
                </el-form-item>
                <el-form-item label="备注">
                    <el-input v-model="additionalRuleForm.remark" type="textarea" :rows="2" placeholder="请输入备注信息"></el-input>
                </el-form-item>
            </el-form>
            <span slot="footer" class="dialog-footer">
                <el-button @click="additionalDialogVisible = false">取 消</el-button>
                <el-button type="primary" @click="saveAdditionalRule">确 定</el-button>
            </span>
        </el-dialog>
    </div>

    <script>
        new Vue({
            el: '#app',
            data() {
                return {
                    activeTab: 'rules',
                    // 规则列表数据
                    feeRules: JSON.parse(localStorage.getItem('feeRules')) || [{
                        id: 1,
                        minOrders: 0,
                        maxOrders: 1000,
                        minWeight: 0,
                        maxWeight: 1,
                        price: 12.5,
                        effectiveDate: '2023-10-01',
                        endDate: '2023-10-31',
                        status: '生效中',
                        remark: '标准单件快递费',
                        attachment: '规则说明.pdf',
                        operateTime: '2023-09-15 10:30:45',
                        operator: '张经理'
                    }, {
                        id: 2,
                        minOrders: 1001,
                        maxOrders: 5000,
                        minWeight: 0,
                        maxWeight: 1,
                        price: 11.8,
                        effectiveDate: '2023-10-01',
                        endDate: '2023-10-31',
                        status: '生效中',
                        remark: '批量订单优惠价',
                        attachment: '优惠政策.docx',
                        operateTime: '2023-09-15 11:20:15',
                        operator: '张经理'
                    }, {
                        id: 3,
                        minOrders: 5001,
                        maxOrders: 10000,
                        minWeight: 0,
                        maxWeight: 1,
                        price: 11.0,
                        effectiveDate: '2023-10-01',
                        endDate: '2023-10-31',
                        status: '生效中',
                        remark: '大客户价格',
                        attachment: null,
                        operateTime: '2023-09-15 14:05:37',
                        operator: '李总监'
                    }, {
                        id: 4,
                        minOrders: 10001,
                        maxOrders: null,
                        minWeight: 0,
                        maxWeight: 1,
                        price: 10.5,
                        effectiveDate: '2023-10-01',
                        endDate: '2023-10-31',
                        status: '生效中',
                        remark: '战略合作伙伴特惠',
                        attachment: '合作协议.pdf',
                        operateTime: '2023-09-16 09:15:22',
                        operator: '李总监'
                    }, {
                        id: 5,
                        minOrders: 0,
                        maxOrders: 2000,
                        minWeight: 0,
                        maxWeight: 2,
                        price: 6.5,
                        effectiveDate: '2023-10-01',
                        endDate: '2023-10-31',
                        status: '生效中',
                        remark: '产品促销期价格',
                        attachment: null,
                        operateTime: '2023-09-18 16:42:10',
                        operator: '王主管'
                    }, {
                        id: 6,
                        minOrders: 2001,
                        maxOrders: 8000,
                        minWeight: 0,
                        maxWeight: 2,
                        price: 6.0,
                        effectiveDate: '2023-10-01',
                        endDate: '2023-10-31',
                        status: '生效中',
                        remark: '双十一活动价',
                        attachment: '活动计划.xlsx',
                        operateTime: '2023-09-20 11:30:45',
                        operator: '赵经理'
                    }, {
                        id: 7,
                        minOrders: 8001,
                        maxOrders: null,
                        minWeight: 0,
                        maxWeight: 2,
                        price: 5.5,
                        effectiveDate: '2023-10-01',
                        status: '生效中',
                        remark: '大宗订单特惠',
                        attachment: null,
                        operateTime: '2023-09-22 14:25:18',
                        operator: '张经理'
                    }],
                    // 分页信息
                    pagination: {
                        currentPage: 1,
                        pageSize: 10,
                        total: 10
                    },
                    // 编辑对话框
                    dialogVisible: false,
                    dialogTitle: '新增规则',
                    ruleForm: {
                        id: null,
                        minOrders: 0,
                        maxOrders: null,
                        minWeight: 0,
                        maxWeight: null,
                        price: 0,
                        monthStart: '', // 绑定生效月份选择器 YYYY-MM
                        effectiveDate: '', // 存储计算后的生效日期 YYYY-MM-01
                        monthEnd: '', // 绑定结束月份选择器 YYYY-MM
                        endDate: null, // 存储计算后的结束日期 YYYY-MM-DD 或 null
                        remark: '',
                        attachment: null
                    },
                    // 团队列表
                    teams: [{
                        id: 1,
                        name: '电商一组'
                    }, {
                        id: 2,
                        name: '电商二组'
                    }, {
                        id: 3,
                        name: '电商三组'
                    }, {
                        id: 4,
                        name: '电商四组'
                    }],
                    // 费用计算表单
                    calculationForm: {
                        teamId: '',
                        month: ''
                    },
                    // 计算结果
                    calculationResult: {
                        show: false,
                        totalOrders: 0,
                        orderGrowth: 0,
                        currentTier: '',
                        currentRate: 0,
                        totalFee: 0,
                        feeGrowth: 0,
                        rule: {},
                        shopDetails: []
                    },
                    // 统计分析表单
                    statisticsForm: {
                        teamId: '1',
                        dateRange: [new Date(new Date().getFullYear(), new Date().getMonth() - 5, 1), new Date()]
                    },
                    // 附加规则相关数据
                    additionalRules: JSON.parse(localStorage.getItem('additionalRules')) || [{
                        id: 1,
                        adjustmentType: '增幅比率',
                        adjustmentValue: 0.05,
                        region: '上海、江苏、浙江',
                        regionIds: ['上海', '江苏', '浙江'],
                        teamName: '顺丰团队',
                        teamId: '1',
                        shopName: '所有店铺',
                        shopIds: [],
                        weightRangeText: '所有重量',
                        weightRangeIds: [],
                        dateRange: [new Date(), new Date(new Date().setMonth(new Date().getMonth() + 3))],
                        startDate: this.formatDate ? this.formatDate(new Date()) : '23-05-15',
                        endDate: this.formatDate ? this.formatDate(new Date(new Date().setMonth(new Date().getMonth() + 3))) : '23-08-15',
                        status: '待生效',
                        remark: '上海、江苏、浙江地区顺丰团队价格调整',
                        operateTime: new Date().toISOString().split('T')[0] + ' ' + new Date().toTimeString().split(' ')[0],
                        operator: '系统管理员'
                    }, {
                        id: 2,
                        adjustmentType: '降幅金额',
                        adjustmentValue: 2,
                        region: '全国',
                        regionIds: ['全国'],
                        teamName: '电商二组',
                        teamId: 2,
                        shopName: '所有店铺',
                        shopIds: [4, 5, 6],
                        weightRangeId: 2,
                        minWeight: 2,
                        maxWeight: 5,
                        dateRange: ['2023-11-01', null],
                        startDate: '23-11-01',
                        endDate: null,
                        status: '生效中',
                        remark: '双十一活动'
                    }, {
                        id: 3,
                        adjustmentType: '增幅金额',
                        adjustmentValue: 1.5,
                        region: '广东、广西、海南',
                        regionIds: ['广东', '广西', '海南'],
                        teamName: '电商三组',
                        teamId: 3,
                        shopName: '专卖店',
                        shopIds: [7],
                        weightRangeId: 3,
                        minWeight: 5,
                        maxWeight: null,
                        dateRange: ['2023-12-01', '2024-01-31'],
                        startDate: '23-12-01',
                        endDate: '24-01-31',
                        status: '待生效',
                        remark: '元旦春节旺季'
                    }],
                    additionalPagination: {
                        currentPage: 1,
                        pageSize: 10,
                        total: 3
                    },
                    additionalDialogVisible: false,
                    additionalDialogTitle: '新增附加规则',
                    additionalRuleForm: {
                        adjustmentType: '',
                        adjustmentValue: 0,
                        regionIds: [],
                        teamId: '',
                        shopIds: [],
                        weightRangeIds: [],
                        dateRange: [new Date(), new Date(new Date().setMonth(new Date().getMonth() + 3))], // 默认3个月有效期
                        remark: ''
                    },
                    // 重量区间选项，从规则提取
                    weightRanges: [{
                        id: 1,
                        minWeight: 0,
                        maxWeight: 1
                    }, {
                        id: 2,
                        minWeight: 0,
                        maxWeight: 2
                    }, {
                        id: 3,
                        minWeight: 0,
                        maxWeight: 3
                    }, {
                        id: 4,
                        minWeight: 2,
                        maxWeight: 5
                    }, {
                        id: 5,
                        minWeight: 5,
                        maxWeight: null
                    }],
                    // 区域数据
                    regions: [{
                        value: '北京',
                        label: '北京',
                        children: [{
                            value: '北京市',
                            label: '北京市'
                        }]
                    }, {
                        value: '天津',
                        label: '天津',
                        children: [{
                            value: '天津市',
                            label: '天津市'
                        }]
                    }, {
                        value: '河北',
                        label: '河北',
                        children: [{
                            value: '石家庄市',
                            label: '石家庄市'
                        }, {
                            value: '唐山市',
                            label: '唐山市'
                        }, {
                            value: '秦皇岛市',
                            label: '秦皇岛市'
                        }, {
                            value: '邯郸市',
                            label: '邯郸市'
                        }, {
                            value: '邢台市',
                            label: '邢台市'
                        }]
                    }, {
                        value: '山西',
                        label: '山西',
                        children: [{
                            value: '太原市',
                            label: '太原市'
                        }, {
                            value: '大同市',
                            label: '大同市'
                        }]
                    }, {
                        value: '内蒙古',
                        label: '内蒙古自治区',
                        children: [{
                            value: '呼和浩特市',
                            label: '呼和浩特市'
                        }, {
                            value: '包头市',
                            label: '包头市'
                        }]
                    }, {
                        value: '上海',
                        label: '上海',
                        children: [{
                            value: '上海市',
                            label: '上海市'
                        }]
                    }, {
                        value: '江苏',
                        label: '江苏',
                        children: [{
                            value: '南京市',
                            label: '南京市'
                        }, {
                            value: '无锡市',
                            label: '无锡市'
                        }, {
                            value: '徐州市',
                            label: '徐州市'
                        }, {
                            value: '常州市',
                            label: '常州市'
                        }, {
                            value: '苏州市',
                            label: '苏州市'
                        }]
                    }, {
                        value: '浙江',
                        label: '浙江',
                        children: [{
                            value: '杭州市',
                            label: '杭州市'
                        }, {
                            value: '宁波市',
                            label: '宁波市'
                        }]
                    }, {
                        value: '安徽',
                        label: '安徽',
                        children: [{
                            value: '合肥市',
                            label: '合肥市'
                        }, {
                            value: '芜湖市',
                            label: '芜湖市'
                        }]
                    }, {
                        value: '福建',
                        label: '福建',
                        children: [{
                            value: '福州市',
                            label: '福州市'
                        }, {
                            value: '厦门市',
                            label: '厦门市'
                        }]
                    }, {
                        value: '江西',
                        label: '江西',
                        children: [{
                            value: '南昌市',
                            label: '南昌市'
                        }, {
                            value: '景德镇市',
                            label: '景德镇市'
                        }]
                    }, {
                        value: '山东',
                        label: '山东',
                        children: [{
                            value: '济南市',
                            label: '济南市'
                        }, {
                            value: '青岛市',
                            label: '青岛市'
                        }]
                    }, {
                        value: '广东',
                        label: '广东',
                        children: [{
                            value: '广州市',
                            label: '广州市'
                        }, {
                            value: '深圳市',
                            label: '深圳市'
                        }, {
                            value: '珠海市',
                            label: '珠海市'
                        }, {
                            value: '汕头市',
                            label: '汕头市'
                        }, {
                            value: '佛山市',
                            label: '佛山市'
                        }]
                    }, {
                        value: '广西',
                        label: '广西壮族自治区',
                        children: [{
                            value: '南宁市',
                            label: '南宁市'
                        }, {
                            value: '柳州市',
                            label: '柳州市'
                        }]
                    }, {
                        value: '海南',
                        label: '海南',
                        children: [{
                            value: '海口市',
                            label: '海口市'
                        }, {
                            value: '三亚市',
                            label: '三亚市'
                        }]
                    }, {
                        value: '重庆',
                        label: '重庆',
                        children: [{
                            value: '重庆市',
                            label: '重庆市'
                        }]
                    }, {
                        value: '四川',
                        label: '四川',
                        children: [{
                            value: '成都市',
                            label: '成都市'
                        }, {
                            value: '自贡市',
                            label: '自贡市'
                        }]
                    }, {
                        value: '贵州',
                        label: '贵州',
                        children: [{
                            value: '贵阳市',
                            label: '贵阳市'
                        }, {
                            value: '遵义市',
                            label: '遵义市'
                        }]
                    }, {
                        value: '云南',
                        label: '云南',
                        children: [{
                            value: '昆明市',
                            label: '昆明市'
                        }, {
                            value: '曲靖市',
                            label: '曲靖市'
                        }]
                    }, {
                        value: '西藏',
                        label: '西藏自治区',
                        children: [{
                            value: '拉萨市',
                            label: '拉萨市'
                        }, {
                            value: '日喀则市',
                            label: '日喀则市'
                        }]
                    }, {
                        value: '辽宁',
                        label: '辽宁',
                        children: [{
                            value: '沈阳市',
                            label: '沈阳市'
                        }, {
                            value: '大连市',
                            label: '大连市'
                        }]
                    }, {
                        value: '吉林',
                        label: '吉林',
                        children: [{
                            value: '长春市',
                            label: '长春市'
                        }, {
                            value: '吉林市',
                            label: '吉林市'
                        }]
                    }, {
                        value: '黑龙江',
                        label: '黑龙江',
                        children: [{
                            value: '哈尔滨市',
                            label: '哈尔滨市'
                        }, {
                            value: '齐齐哈尔市',
                            label: '齐齐哈尔市'
                        }]
                    }, {
                        value: '陕西',
                        label: '陕西',
                        children: [{
                            value: '西安市',
                            label: '西安市'
                        }, {
                            value: '铜川市',
                            label: '铜川市'
                        }]
                    }, {
                        value: '甘肃',
                        label: '甘肃',
                        children: [{
                            value: '兰州市',
                            label: '兰州市'
                        }, {
                            value: '嘉峪关市',
                            label: '嘉峪关市'
                        }]
                    }, {
                        value: '青海',
                        label: '青海',
                        children: [{
                            value: '西宁市',
                            label: '西宁市'
                        }, {
                            value: '海东市',
                            label: '海东市'
                        }]
                    }, {
                        value: '宁夏',
                        label: '宁夏回族自治区',
                        children: [{
                            value: '银川市',
                            label: '银川市'
                        }, {
                            value: '石嘴山市',
                            label: '石嘴山市'
                        }]
                    }, {
                        value: '新疆',
                        label: '新疆维吾尔自治区',
                        children: [{
                            value: '乌鲁木齐市',
                            label: '乌鲁木齐市'
                        }, {
                            value: '克拉玛依市',
                            label: '克拉玛依市'
                        }]
                    }],
                    // 店铺列表
                    shops: [{
                        id: 1,
                        name: '旗舰店',
                        teamId: 1
                    }, {
                        id: 2,
                        name: '直营店',
                        teamId: 1
                    }, {
                        id: 3,
                        name: '体验店',
                        teamId: 1
                    }, {
                        id: 4,
                        name: '京东店',
                        teamId: 2
                    }, {
                        id: 5,
                        name: '天猫店',
                        teamId: 2
                    }, {
                        id: 6,
                        name: '拼多多店',
                        teamId: 2
                    }, {
                        id: 7,
                        name: '专卖店',
                        teamId: 3
                    }, {
                        id: 8,
                        name: '批发店',
                        teamId: 3
                    }, {
                        id: 9,
                        name: '折扣店',
                        teamId: 4
                    }],
                    // 附加规则数据相关变量
                    additionalRulesFilter: {
                        teamId: '',
                        status: '',
                        dateRange: null
                    },
                    additionalRules: [], // 附加规则列表数据
                    filteredAdditionalRules: [], // 过滤后的附加规则
                    selectedAdditionalRuleIds: [], // 已选择的附加规则ID列表
                    additionalRulePagination: {
                        currentPage: 1,
                        pageSize: 10
                    },
                    // 添加图表对象管理
                    charts: {
                        orders: null,
                        fees: null
                    },
                    // 调整类型选项
                    adjustmentTypes: [{
                        value: 'increase-ratio',
                        label: '增加比率',
                        icon: 'el-icon-top'
                    }, {
                        value: 'increase-amount',
                        label: '增加金额',
                        icon: 'el-icon-plus'
                    }, {
                        value: 'decrease-ratio',
                        label: '减少比率',
                        icon: 'el-icon-bottom'
                    }, {
                        value: 'decrease-amount',
                        label: '减少金额',
                        icon: 'el-icon-minus'
                    }]
                }
            },
            created() {
                // 初始化或加载数据
                this.initData();
            },
            mounted() {
                // 初始化时生成重量区间选项
                this.initWeightRanges();
                // 初始化聚焦到搜索框
                this.$nextTick(() => {
                    const searchInput = this.$refs.searchInput;
                    if (searchInput) {
                        searchInput.focus();
                    }
                });

                // 添加表格滚动监听
                this.$nextTick(() => {
                    this.addTableScrollListener();
                });

                // 页面加载时更新规则状态
                this.updateRulesStatus();

                // 定时检查规则状态（每小时检查一次）
                setInterval(() => {
                    this.updateRulesStatus();
                }, 3600000);
            },
            methods: {
                // 添加表格滚动监听
                addTableScrollListener() {
                    const tableBodyWrappers = document.querySelectorAll('.fee-rules-table .el-table__body-wrapper');

                    tableBodyWrappers.forEach(wrapper => {
                        wrapper.addEventListener('scroll', function() {
                            // 判断是否滚动到最右侧
                            const isScrollingRight = this.scrollLeft + this.clientWidth >= this.scrollWidth - 5;

                            if (isScrollingRight) {
                                this.classList.add('is-scrolling-right');
                            } else {
                                this.classList.remove('is-scrolling-right');
                            }
                        });
                    });
                },

                // 切换标签页时重新添加滚动监听
                handleTabClick(tab) {
                    this.$nextTick(() => {
                        this.addTableScrollListener();
                    });
                },

                initData() {
                    this.initTeamsData();
                    this.initShopsData();
                    this.initRulesData();
                    this.initAdditionalRulesData();
                    this.initWeightRangesData();
                    this.initRegionsData();
                },

                // 初始化团队数据
                initTeamsData() {
                    // 直接使用data中定义的默认团队数据
                    this.teams = [{
                        id: 1,
                        name: '电商一组'
                    }, {
                        id: 2,
                        name: '电商二组'
                    }, {
                        id: 3,
                        name: '电商三组'
                    }, {
                        id: 4,
                        name: '电商四组'
                    }];
                    // 保存到本地存储
                    localStorage.setItem('expressDeliveryTeams', JSON.stringify(this.teams));
                },

                // 初始化店铺数据
                initShopsData() {
                    // 直接使用data中定义的默认店铺数据
                    this.shops = [{
                        id: 1,
                        name: '旗舰店',
                        teamId: 1
                    }, {
                        id: 2,
                        name: '直营店',
                        teamId: 1
                    }, {
                        id: 3,
                        name: '体验店',
                        teamId: 1
                    }, {
                        id: 4,
                        name: '京东店',
                        teamId: 2
                    }, {
                        id: 5,
                        name: '天猫店',
                        teamId: 2
                    }, {
                        id: 6,
                        name: '拼多多店',
                        teamId: 2
                    }, {
                        id: 7,
                        name: '专卖店',
                        teamId: 3
                    }, {
                        id: 8,
                        name: '批发店',
                        teamId: 3
                    }, {
                        id: 9,
                        name: '折扣店',
                        teamId: 4
                    }];
                    // 保存到本地存储
                    localStorage.setItem('expressDeliveryShops', JSON.stringify(this.shops));
                },

                // 初始化快递费规则数据
                initRulesData() {
                    // 从本地存储获取规则数据，如果没有则使用默认数据
                    const savedRules = localStorage.getItem('expressDeliveryRules');
                    if (savedRules) {
                        try {
                            this.rules = JSON.parse(savedRules);
                        } catch (error) {
                            console.error('解析规则数据失败:', error);
                            this.rules = this.getDefaultRules();
                        }
                    } else {
                        this.rules = this.getDefaultRules();
                        localStorage.setItem('expressDeliveryRules', JSON.stringify(this.rules));
                    }
                    this.filteredRules = [...this.rules];
                },

                // 获取默认规则数据
                getDefaultRules() {
                    return [{
                        id: 1,
                        teamId: '1',
                        teamName: '顺丰团队',
                        minOrder: 0,
                        maxOrder: 100,
                        minWeight: 0,
                        maxWeight: 1,
                        price: 15,
                        startDate: new Date().toISOString().split('T')[0],
                        endDate: '',
                        status: '生效中',
                        remark: '顺丰小件快递基础价格',
                        attachments: []
                    }, {
                        id: 2,
                        teamId: '1',
                        teamName: '顺丰团队',
                        minOrder: 101,
                        maxOrder: 500,
                        minWeight: 0,
                        maxWeight: 1,
                        price: 14,
                        startDate: new Date().toISOString().split('T')[0],
                        endDate: '',
                        status: '生效中',
                        remark: '顺丰小件批量优惠价格',
                        attachments: []
                    }, {
                        id: 3,
                        teamId: '2',
                        teamName: '京东团队',
                        minOrder: 0,
                        maxOrder: 200,
                        minWeight: 0,
                        maxWeight: 2,
                        price: 12,
                        startDate: new Date().toISOString().split('T')[0],
                        endDate: '',
                        status: '生效中',
                        remark: '京东常规快递价格',
                        attachments: []
                    }, {
                        id: 4,
                        teamId: '3',
                        teamName: '韵达团队',
                        minOrder: 0,
                        maxOrder: 300,
                        minWeight: 0,
                        maxWeight: 3,
                        price: 10,
                        startDate: new Date().toISOString().split('T')[0],
                        endDate: '',
                        status: '生效中',
                        remark: '韵达普通快递价格',
                        attachments: []
                    }];
                },

                // 初始化附加规则数据
                initAdditionalRulesData() {
                    // 从本地存储获取附加规则数据，如果没有则使用默认数据
                    const savedAdditionalRules = localStorage.getItem('expressDeliveryAdditionalRules');
                    if (savedAdditionalRules) {
                        try {
                            this.additionalRules = JSON.parse(savedAdditionalRules);
                            // 更新状态（判断是否过期、生效等）
                            this.updateAdditionalRulesStatus();
                        } catch (error) {
                            console.error('解析附加规则数据失败:', error);
                            this.additionalRules = this.getDefaultAdditionalRules();
                        }
                    } else {
                        this.additionalRules = this.getDefaultAdditionalRules();
                    }
                    this.filterAdditionalRules();
                },

                // 获取默认附加规则数据
                getDefaultAdditionalRules() {
                    // 辅助函数，获取YY-MM-DD格式的日期
                    const formatYYMMDD = (date) => {
                        const d = new Date(date);
                        const year = d.getFullYear().toString().substr(-2);
                        const month = (d.getMonth() + 1).toString().padStart(2, '0');
                        const day = d.getDate().toString().padStart(2, '0');
                        return `${year}-${month}-${day}`;
                    };

                    // 返回一些示例附加规则数据
                    return [{
                        id: 1,
                        adjustmentType: '增幅比率',
                        adjustmentValue: 0.05,
                        region: '上海、江苏、浙江',
                        regionIds: ['上海', '江苏', '浙江'],
                        teamName: '顺丰团队',
                        teamId: '1',
                        shopName: '所有店铺',
                        shopIds: [],
                        weightRangeText: '所有重量',
                        weightRangeIds: [],
                        dateRange: [new Date(), new Date(new Date().setMonth(new Date().getMonth() + 3))],
                        startDate: formatYYMMDD(new Date()),
                        endDate: formatYYMMDD(new Date(new Date().setMonth(new Date().getMonth() + 3))),
                        status: '待生效',
                        remark: '上海、江苏、浙江地区顺丰团队价格调整',
                        operateTime: new Date().toISOString().split('T')[0] + ' ' + new Date().toTimeString().split(' ')[0],
                        operator: '系统管理员'
                    }, {
                        id: 2,
                        adjustmentType: '降幅金额',
                        adjustmentValue: 2,
                        region: '全国',
                        regionIds: ['全国'],
                        teamName: '电商二组',
                        teamId: 2,
                        shopName: '所有店铺',
                        shopIds: [4, 5, 6],
                        weightRangeText: '0-1kg',
                        weightRangeIds: ['1'],
                        dateRange: [new Date(new Date().setDate(new Date().getDate() - 30)), new Date(new Date().setDate(new Date().getDate() + 60))],
                        startDate: formatYYMMDD(new Date(new Date().setDate(new Date().getDate() - 30))),
                        endDate: formatYYMMDD(new Date(new Date().setDate(new Date().getDate() + 60))),
                        status: '生效中',
                        remark: '京东自营店轻量包裹降价促销',
                        operateTime: new Date(new Date().setDate(new Date().getDate() - 30)).toISOString().split('T')[0] + ' ' + new Date().toTimeString().split(' ')[0],
                        operator: '系统管理员'
                    }];
                },

                // 初始化重量区间数据
                initWeightRangesData() {
                    // 从本地存储获取重量区间数据，如果没有则使用默认数据
                    const savedWeightRanges = localStorage.getItem('expressDeliveryWeightRanges');
                    if (savedWeightRanges) {
                        try {
                            this.weightRanges = JSON.parse(savedWeightRanges);
                        } catch (error) {
                            console.error('解析重量区间数据失败:', error);
                            this.weightRanges = this.getDefaultWeightRanges();
                        }
                    } else {
                        this.weightRanges = this.getDefaultWeightRanges();
                        localStorage.setItem('expressDeliveryWeightRanges', JSON.stringify(this.weightRanges));
                    }
                },

                // 获取默认重量区间数据
                getDefaultWeightRanges() {
                    return [{
                        id: '1',
                        minWeight: 0,
                        maxWeight: 1,
                        label: '0-1kg'
                    }, {
                        id: '2',
                        minWeight: 1,
                        maxWeight: 3,
                        label: '1-3kg'
                    }, {
                        id: '3',
                        minWeight: 3,
                        maxWeight: 5,
                        label: '3-5kg'
                    }, {
                        id: '4',
                        minWeight: 5,
                        maxWeight: 10,
                        label: '5-10kg'
                    }, {
                        id: '5',
                        minWeight: 10,
                        maxWeight: null,
                        label: '10kg以上'
                    }];
                },

                // 初始化区域数据
                initRegionsData() {
                    // 从本地存储获取区域数据，如果没有则使用默认数据
                    const savedRegions = localStorage.getItem('expressDeliveryRegions');
                    if (savedRegions) {
                        try {
                            this.regions = JSON.parse(savedRegions);
                        } catch (error) {
                            console.error('解析区域数据失败:', error);
                            this.regions = this.getDefaultRegions();
                        }
                    } else {
                        this.regions = this.getDefaultRegions();
                        localStorage.setItem('expressDeliveryRegions', JSON.stringify(this.regions));
                    }
                },

                // 获取默认区域数据
                getDefaultRegions() {
                    return [{
                        value: '北京',
                        label: '北京',
                        children: [{
                            value: '北京市',
                            label: '北京市'
                        }]
                    }, {
                        value: '天津',
                        label: '天津',
                        children: [{
                            value: '天津市',
                            label: '天津市'
                        }]
                    }, {
                        value: '河北',
                        label: '河北',
                        children: [{
                            value: '石家庄市',
                            label: '石家庄市'
                        }, {
                            value: '唐山市',
                            label: '唐山市'
                        }, {
                            value: '秦皇岛市',
                            label: '秦皇岛市'
                        }, {
                            value: '邯郸市',
                            label: '邯郸市'
                        }, {
                            value: '邢台市',
                            label: '邢台市'
                        }, {
                            value: '保定市',
                            label: '保定市'
                        }, {
                            value: '张家口市',
                            label: '张家口市'
                        }, {
                            value: '承德市',
                            label: '承德市'
                        }, {
                            value: '沧州市',
                            label: '沧州市'
                        }, {
                            value: '廊坊市',
                            label: '廊坊市'
                        }, {
                            value: '衡水市',
                            label: '衡水市'
                        }]
                    }, {
                        value: '山西',
                        label: '山西',
                        children: [{
                            value: '太原市',
                            label: '太原市'
                        }, {
                            value: '大同市',
                            label: '大同市'
                        }, {
                            value: '阳泉市',
                            label: '阳泉市'
                        }, {
                            value: '长治市',
                            label: '长治市'
                        }, {
                            value: '晋城市',
                            label: '晋城市'
                        }, {
                            value: '朔州市',
                            label: '朔州市'
                        }, {
                            value: '晋中市',
                            label: '晋中市'
                        }, {
                            value: '运城市',
                            label: '运城市'
                        }, {
                            value: '忻州市',
                            label: '忻州市'
                        }, {
                            value: '临汾市',
                            label: '临汾市'
                        }, {
                            value: '吕梁市',
                            label: '吕梁市'
                        }]
                    }, {
                        value: '上海',
                        label: '上海',
                        children: [{
                            value: '上海市',
                            label: '上海市'
                        }]
                    }, {
                        value: '江苏',
                        label: '江苏',
                        children: [{
                            value: '南京市',
                            label: '南京市'
                        }, {
                            value: '苏州市',
                            label: '苏州市'
                        }, {
                            value: '无锡市',
                            label: '无锡市'
                        }, {
                            value: '常州市',
                            label: '常州市'
                        }, {
                            value: '南通市',
                            label: '南通市'
                        }, {
                            value: '扬州市',
                            label: '扬州市'
                        }, {
                            value: '镇江市',
                            label: '镇江市'
                        }, {
                            value: '徐州市',
                            label: '徐州市'
                        }, {
                            value: '淮安市',
                            label: '淮安市'
                        }, {
                            value: '盐城市',
                            label: '盐城市'
                        }, {
                            value: '宿迁市',
                            label: '宿迁市'
                        }, {
                            value: '泰州市',
                            label: '泰州市'
                        }, {
                            value: '连云港市',
                            label: '连云港市'
                        }]
                    }, {
                        value: '浙江',
                        label: '浙江',
                        children: [{
                            value: '杭州市',
                            label: '杭州市'
                        }, {
                            value: '宁波市',
                            label: '宁波市'
                        }, {
                            value: '温州市',
                            label: '温州市'
                        }, {
                            value: '嘉兴市',
                            label: '嘉兴市'
                        }, {
                            value: '湖州市',
                            label: '湖州市'
                        }, {
                            value: '绍兴市',
                            label: '绍兴市'
                        }, {
                            value: '金华市',
                            label: '金华市'
                        }, {
                            value: '衢州市',
                            label: '衢州市'
                        }, {
                            value: '舟山市',
                            label: '舟山市'
                        }, {
                            value: '台州市',
                            label: '台州市'
                        }, {
                            value: '丽水市',
                            label: '丽水市'
                        }]
                    }, {
                        value: '安徽',
                        label: '安徽',
                        children: [{
                            value: '合肥市',
                            label: '合肥市'
                        }, {
                            value: '芜湖市',
                            label: '芜湖市'
                        }, {
                            value: '蚌埠市',
                            label: '蚌埠市'
                        }, {
                            value: '淮南市',
                            label: '淮南市'
                        }, {
                            value: '马鞍山市',
                            label: '马鞍山市'
                        }, {
                            value: '淮北市',
                            label: '淮北市'
                        }, {
                            value: '铜陵市',
                            label: '铜陵市'
                        }, {
                            value: '安庆市',
                            label: '安庆市'
                        }, {
                            value: '黄山市',
                            label: '黄山市'
                        }, {
                            value: '阜阳市',
                            label: '阜阳市'
                        }, {
                            value: '宿州市',
                            label: '宿州市'
                        }, {
                            value: '滁州市',
                            label: '滁州市'
                        }, {
                            value: '六安市',
                            label: '六安市'
                        }, {
                            value: '宣城市',
                            label: '宣城市'
                        }, {
                            value: '池州市',
                            label: '池州市'
                        }, {
                            value: '亳州市',
                            label: '亳州市'
                        }]
                    }, {
                        value: '福建',
                        label: '福建',
                        children: [{
                            value: '福州市',
                            label: '福州市'
                        }, {
                            value: '厦门市',
                            label: '厦门市'
                        }, {
                            value: '莆田市',
                            label: '莆田市'
                        }, {
                            value: '三明市',
                            label: '三明市'
                        }, {
                            value: '泉州市',
                            label: '泉州市'
                        }, {
                            value: '漳州市',
                            label: '漳州市'
                        }, {
                            value: '南平市',
                            label: '南平市'
                        }, {
                            value: '龙岩市',
                            label: '龙岩市'
                        }, {
                            value: '宁德市',
                            label: '宁德市'
                        }]
                    }, {
                        value: '江西',
                        label: '江西',
                        children: [{
                            value: '南昌市',
                            label: '南昌市'
                        }, {
                            value: '景德镇市',
                            label: '景德镇市'
                        }, {
                            value: '萍乡市',
                            label: '萍乡市'
                        }, {
                            value: '九江市',
                            label: '九江市'
                        }, {
                            value: '新余市',
                            label: '新余市'
                        }, {
                            value: '鹰潭市',
                            label: '鹰潭市'
                        }, {
                            value: '赣州市',
                            label: '赣州市'
                        }, {
                            value: '吉安市',
                            label: '吉安市'
                        }, {
                            value: '宜春市',
                            label: '宜春市'
                        }, {
                            value: '抚州市',
                            label: '抚州市'
                        }, {
                            value: '上饶市',
                            label: '上饶市'
                        }]
                    }, {
                        value: '山东',
                        label: '山东',
                        children: [{
                            value: '济南市',
                            label: '济南市'
                        }, {
                            value: '青岛市',
                            label: '青岛市'
                        }, {
                            value: '淄博市',
                            label: '淄博市'
                        }, {
                            value: '枣庄市',
                            label: '枣庄市'
                        }, {
                            value: '东营市',
                            label: '东营市'
                        }, {
                            value: '烟台市',
                            label: '烟台市'
                        }, {
                            value: '潍坊市',
                            label: '潍坊市'
                        }, {
                            value: '济宁市',
                            label: '济宁市'
                        }, {
                            value: '泰安市',
                            label: '泰安市'
                        }, {
                            value: '威海市',
                            label: '威海市'
                        }, {
                            value: '日照市',
                            label: '日照市'
                        }, {
                            value: '临沂市',
                            label: '临沂市'
                        }, {
                            value: '德州市',
                            label: '德州市'
                        }, {
                            value: '聊城市',
                            label: '聊城市'
                        }, {
                            value: '滨州市',
                            label: '滨州市'
                        }, {
                            value: '菏泽市',
                            label: '菏泽市'
                        }]
                    }]
                },

                // 更新附加规则的状态（生效、过期等）
                updateAdditionalRulesStatus() {
                    const today = new Date();
                    today.setHours(0, 0, 0, 0);

                    this.additionalRules.forEach(rule => {
                        if (rule.startDate && rule.endDate) {
                            const startDate = new Date(rule.startDate);
                            const endDate = new Date(rule.endDate);
                            startDate.setHours(0, 0, 0, 0);
                            endDate.setHours(23, 59, 59, 999);

                            if (today < startDate) {
                                rule.status = '待生效';
                            } else if (today > endDate) {
                                rule.status = '已过期';
                            } else {
                                rule.status = '生效中';
                            }
                        } else if (rule.startDate) {
                            const startDate = new Date(rule.startDate);
                            startDate.setHours(0, 0, 0, 0);

                            if (today < startDate) {
                                rule.status = '待生效';
                            } else {
                                rule.status = '生效中';
                            }
                        } else {
                            // 如果没有日期限制，默认为生效中
                            rule.status = '生效中';
                        }
                    });

                    // 将更新后的数据保存到本地存储
                    this.saveAdditionalRulesToLocalStorage();
                },

                // 保存附加规则数据到本地存储
                saveAdditionalRulesToLocalStorage() {
                    localStorage.setItem('expressDeliveryAdditionalRules', JSON.stringify(this.additionalRules));
                },

                // 添加附加规则
                handleAddAdditionalRule() {
                    this.additionalRuleForm = {
                        adjustmentType: '',
                        adjustmentValue: 0,
                        regionIds: [],
                        teamId: '',
                        shopIds: [],
                        weightRangeIds: [],
                        dateRange: [new Date(), new Date(new Date().setMonth(new Date().getMonth() + 3))], // 默认3个月有效期
                        remark: ''
                    };
                    this.additionalRuleFormTitle = '新增附加规则';
                    this.additionalDialogVisible = true;
                },

                // 编辑附加规则
                handleEditAdditionalRule(row) {
                    this.additionalRuleForm = {
                        id: row.id,
                        adjustmentType: row.adjustmentType,
                        adjustmentValue: row.adjustmentValue,
                        regionIds: row.regionIds || [],
                        teamId: row.teamId || '',
                        shopIds: row.shopIds || [],
                        weightRangeIds: row.weightRangeIds || [],
                        dateRange: row.dateRange || (row.startDate ? [new Date(row.startDate), row.endDate ? new Date(row.endDate) : null] : []),
                        remark: row.remark || ''
                    };
                    this.additionalRuleFormTitle = '编辑附加规则';
                    this.additionalDialogVisible = true;
                },

                // 复制附加规则
                handleCopyAdditionalRule(row) {
                    this.additionalRuleForm = {
                        // 不要包含id，确保创建新记录时生成新ID
                        adjustmentType: row.adjustmentType,
                        adjustmentValue: row.adjustmentValue,
                        regionIds: row.regionIds || [],
                        teamId: row.teamId || '',
                        shopIds: row.shopIds || [],
                        weightRangeIds: row.weightRangeIds || [],
                        dateRange: row.dateRange || (row.startDate ? [new Date(row.startDate), row.endDate ? new Date(row.endDate) : null] : []),
                        remark: row.remark ? row.remark + '(复制)' : '(复制)'
                    };
                    this.additionalDialogTitle = '复制附加规则';
                    this.additionalDialogVisible = true;
                },

                // 处理团队变更
                handleTeamChange(teamId) {
                    // 当团队变化时，重置店铺选择
                    this.additionalRuleForm.shopIds = [];
                },

                // 保存附加规则
                saveAdditionalRule() {
                    // 验证必填字段
                    if (!this.additionalRuleForm.adjustmentType || !this.additionalRuleForm.adjustmentValue) {
                        this.$message.error('请填写必填字段');
                        return;
                    }

                    // 验证日期范围
                    if (this.additionalRuleForm.dateRange &&
                        this.additionalRuleForm.dateRange[0] &&
                        this.additionalRuleForm.dateRange[1]) {
                        const startDate = new Date(this.additionalRuleForm.dateRange[0]);
                        const endDate = new Date(this.additionalRuleForm.dateRange[1]);

                        if (startDate > endDate) {
                            this.$message.error('结束日期不能早于开始日期');
                            return;
                        }
                    }

                    // 生成新规则ID：如果是编辑现有规则则保留ID，否则生成新ID（最大ID+1）
                    let newId;
                    if (this.additionalRuleForm.id) {
                        // 编辑模式，保留原ID
                        newId = this.additionalRuleForm.id;
                    } else {
                        // 新增模式，找到当前最大ID并+1
                        const maxId = this.additionalRules.length > 0 ?
                            Math.max(...this.additionalRules.map(rule => Number(rule.id))) : 0;
                        newId = maxId + 1;
                    }

                    const newRule = {
                        id: newId,
                        adjustmentType: this.additionalRuleForm.adjustmentType,
                        adjustmentValue: this.additionalRuleForm.adjustmentValue,
                        // 处理区域信息
                        regionIds: this.additionalRuleForm.regionIds || [],
                        region: this.additionalRuleForm.regionIds && this.additionalRuleForm.regionIds.length > 0 ?
                            this.getRegionNames(this.additionalRuleForm.regionIds).join(',') : '所有区域',
                        // 处理团队信息
                        teamId: this.additionalRuleForm.teamId || '',
                        teamName: this.additionalRuleForm.teamId ?
                            (this.teams.find(t => t.id === this.additionalRuleForm.teamId) ?
                                this.teams.find(t => t.id === this.additionalRuleForm.teamId).name || '' : '') : '所有团队',
                        // 处理店铺信息
                        shopIds: this.additionalRuleForm.shopIds || [],
                        shopName: this.additionalRuleForm.shopIds && this.additionalRuleForm.shopIds.length > 0 ?
                            this.getShopNames(this.additionalRuleForm.shopIds).join(',') : '所有店铺',
                        // 处理重量区间信息
                        weightRangeIds: this.additionalRuleForm.weightRangeIds || [],
                        minWeight: 0,
                        maxWeight: null,
                        // 如果选择了具体的重量区间，则使用第一个区间的值
                        ...(this.additionalRuleForm.weightRangeIds && this.additionalRuleForm.weightRangeIds.length > 0 ?
                            this.getWeightRangeInfo(this.additionalRuleForm.weightRangeIds[0]) : {}),
                        // 处理日期区间
                        dateRange: this.additionalRuleForm.dateRange || [],
                        startDate: this.additionalRuleForm.dateRange && this.additionalRuleForm.dateRange[0] ?
                            this.formatDate(this.additionalRuleForm.dateRange[0]) : '',
                        endDate: this.additionalRuleForm.dateRange && this.additionalRuleForm.dateRange[1] ?
                            this.formatDate(this.additionalRuleForm.dateRange[1]) : null,
                        // 其他信息
                        remark: this.additionalRuleForm.remark || '',
                        status: '待处理', // 初始状态，会在保存后更新
                        operateTime: new Date().toLocaleDateString() + ' ' + new Date().toLocaleTimeString(),
                        operator: '当前用户'
                    };

                    // 判断是新增还是编辑
                    if (this.additionalRuleForm.id) {
                        // 编辑现有规则
                        const index = this.additionalRules.findIndex(r => r.id === this.additionalRuleForm.id);
                        if (index !== -1) {
                            this.additionalRules.splice(index, 1, newRule);
                        }
                    } else {
                        // 新增规则
                        this.additionalRules.push(newRule);
                    }

                    // 更新规则状态
                    this.updateAdditionalRulesStatus();

                    // 关闭对话框
                    this.additionalDialogVisible = false;

                    // 提示保存成功
                    this.$message.success('保存成功');

                    // 保存到本地存储
                    localStorage.setItem('additionalRules', JSON.stringify(this.additionalRules));
                },

                // 新增规则
                handleAddRule() {
                    const today = new Date();
                    const currentMonth = today.getFullYear() + '-' + String(today.getMonth() + 1).padStart(2, '0');

                    this.ruleForm = {
                        id: null,
                        minOrders: 0,
                        maxOrders: null,
                        minWeight: 0,
                        maxWeight: null,
                        price: 0,
                        monthStart: currentMonth, // 默认当前月份
                        effectiveDate: currentMonth + '-01', // 默认当前月份第一天
                        monthEnd: '',
                        endDate: null,
                        remark: '',
                        attachment: null
                    };
                    this.dialogTitle = '新增规则';
                    this.dialogVisible = true;
                },

                // 编辑规则
                handleEditRule(row) {
                    // 如果规则状态是"已停用"，询问是否要重新启用
                    if (row.status === '已停用') {
                        this.$confirm('该规则已停用，是否要重新启用?', '提示', {
                            confirmButtonText: '确定',
                            cancelButtonText: '取消',
                            type: 'warning'
                        }).then(() => {
                            // 用户确认重新启用规则
                            const index = this.feeRules.findIndex(r => r.id === row.id);
                            if (index !== -1) {
                                this.feeRules[index].status = new Date(this.feeRules[index].effectiveDate) <= new Date() ? '生效中' : '待生效';
                                localStorage.setItem('feeRules', JSON.stringify(this.feeRules));
                                this.$message.success('规则已重新启用');
                            }
                        }).catch(() => {
                            // 取消操作
                        });
                        return;
                    }

                    // 如果规则状态不是"已停用"，则弹出编辑表单
                    const effectiveMonth = row.effectiveDate ? row.effectiveDate.substring(0, 7) : '';
                    const endMonth = row.endDate ? row.endDate.substring(0, 7) : '';

                    this.ruleForm = {
                        id: row.id,
                        minOrders: row.minOrders,
                        maxOrders: row.maxOrders,
                        minWeight: row.minWeight,
                        maxWeight: row.maxWeight,
                        price: row.price,
                        monthStart: effectiveMonth,
                        effectiveDate: row.effectiveDate,
                        monthEnd: endMonth,
                        endDate: row.endDate, // 不特殊处理2099-12-31
                        remark: row.remark,
                        attachment: row.attachment
                    };
                    this.dialogTitle = '编辑规则';
                    this.dialogVisible = true;
                },

                // 复制规则
                handleCopyRule(row) {
                    this.ruleForm = {
                        // 不要包含id，确保创建新记录时生成新ID
                        minOrders: row.minOrders,
                        maxOrders: row.maxOrders,
                        minWeight: row.minWeight,
                        maxWeight: row.maxWeight,
                        price: row.price,
                        effectiveDate: row.effectiveDate,
                        remark: row.remark ? row.remark + '(复制)' : '(复制)',
                        attachment: null
                    };
                    this.dialogTitle = '复制规则';
                    this.dialogVisible = true;
                },

                // 保存规则
                saveRule() {
                    // 验证表单填写
                    if (!this.ruleForm.minOrders && this.ruleForm.minOrders !== 0) {
                        this.$message.error('请输入最小订单量');
                        return;
                    }
                    if (!this.ruleForm.minWeight && this.ruleForm.minWeight !== 0) {
                        this.$message.error('请输入最小重量');
                        return;
                    }
                    if (!this.ruleForm.price) {
                        this.$message.error('请输入价格');
                        return;
                    }
                    if (!this.ruleForm.effectiveDate) {
                        this.$message.error('请选择生效日期');
                        return;
                    }

                    // 判断是编辑还是新增
                    const isEdit = !!this.ruleForm.id;
                    const newRule = {
                        id: isEdit ? this.ruleForm.id : Date.now(), // 编辑保留ID，新增用时间戳
                        minOrders: this.ruleForm.minOrders,
                        maxOrders: this.ruleForm.maxOrders,
                        minWeight: this.ruleForm.minWeight,
                        maxWeight: this.ruleForm.maxWeight,
                        price: this.ruleForm.price,
                        effectiveDate: this.ruleForm.effectiveDate,
                        endDate: this.ruleForm.endDate || '2099-12-31', // 如果未设置结束日期，则设为长期有效
                        status: '待处理', // 初始状态，会在updateRulesStatus中更新
                        remark: this.ruleForm.remark || '',
                        attachment: this.ruleForm.attachment,
                        operateTime: new Date().toLocaleDateString() + ' ' + new Date().toLocaleTimeString(),
                        operator: '当前用户' // 实际项目中应为当前登录用户
                    };

                    if (isEdit) {
                        // 编辑模式：替换现有规则
                        const index = this.feeRules.findIndex(rule => rule.id === this.ruleForm.id);
                        if (index !== -1) {
                            this.$set(this.feeRules, index, newRule);
                        }
                    } else {
                        // 新增模式：添加到规则列表
                        this.feeRules.push(newRule);
                    }

                    // 更新规则状态
                    this.updateRulesStatus();

                    // 保存到本地存储
                    localStorage.setItem('feeRules', JSON.stringify(this.feeRules));

                    // 关闭对话框并提示
                    this.dialogVisible = false;
                    this.$message({
                        type: 'success',
                        message: isEdit ? '规则更新成功' : '规则添加成功'
                    });
                },

                // 删除规则
                handleDeleteRule(row) {
                    this.$confirm('确认删除该规则?', '提示', {
                        confirmButtonText: '确定',
                        cancelButtonText: '取消',
                        type: 'warning'
                    }).then(() => {
                        const index = this.feeRules.findIndex(r => r.id === row.id);
                        if (index !== -1) {
                            this.feeRules.splice(index, 1);
                            this.pagination.total = this.feeRules.length;
                            localStorage.setItem('feeRules', JSON.stringify(this.feeRules));
                            this.$message.success('删除成功');
                        }
                    }).catch(() => {
                        // 取消删除
                    });
                },

                // 辅助方法：格式化日期
                formatDate(date) {
                    if (!date) return '';

                    // 注意：不再将2099-12-31转换为"长期有效"

                    // 如果是字符串日期，先尝试转换为日期对象
                    if (typeof date === 'string') {
                        // 如果已经是格式化的日期字符串，直接返回
                        if (/^\d{4}-\d{2}-\d{2}$/.test(date)) {
                            return date;
                        }

                        try {
                            date = new Date(date);
                        } catch (e) {
                            console.error('日期格式化错误:', e);
                            return date;
                        }
                    }

                    // 处理日期对象
                    try {
                        const d = new Date(date);
                        if (isNaN(d.getTime())) {
                            return date;
                        }

                        return d.getFullYear() + '-' +
                            String(d.getMonth() + 1).padStart(2, '0') + '-' +
                            String(d.getDate()).padStart(2, '0');
                    } catch (e) {
                        console.error('日期格式化错误:', e);
                        return date;
                    }
                },

                // 辅助方法：格式化操作时间
                formatOperateTime(datetime) {
                    if (!datetime) return '';

                    // 如果传入的是字符串
                    if (typeof datetime === 'string') {
                        // 检查是否已经是标准格式 YYYY-MM-DD HH:MM:SS
                        if (/^\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}$/.test(datetime)) {
                            return datetime;
                        }

                        // 尝试处理 YYYY/MM/DD HH:MM:SS 格式
                        if (/^\d{4}\/\d{2}\/\d{2} \d{2}:\d{2}:\d{2}$/.test(datetime)) {
                            return datetime.replace(/\//g, '-');
                        }

                        // 尝试处理 YYYY-MM-DD 格式，添加时间部分
                        if (/^\d{4}-\d{2}-\d{2}$/.test(datetime)) {
                            return `${datetime} 00:00:00`;
                        }

                        // 尝试将其他格式转换为标准格式
                        try {
                            const d = new Date(datetime);
                            if (!isNaN(d.getTime())) {
                                const year = d.getFullYear();
                                const month = (d.getMonth() + 1).toString().padStart(2, '0');
                                const day = d.getDate().toString().padStart(2, '0');
                                const hours = d.getHours().toString().padStart(2, '0');
                                const minutes = d.getMinutes().toString().padStart(2, '0');
                                const seconds = d.getSeconds().toString().padStart(2, '0');
                                return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
                            }
                        } catch (e) {
                            console.error('无法解析日期时间字符串', datetime);
                        }

                        return datetime;
                    }

                    // 处理日期对象
                    const d = new Date(datetime);
                    const year = d.getFullYear();
                    const month = (d.getMonth() + 1).toString().padStart(2, '0');
                    const day = d.getDate().toString().padStart(2, '0');
                    const hours = d.getHours().toString().padStart(2, '0');
                    const minutes = d.getMinutes().toString().padStart(2, '0');
                    const seconds = d.getSeconds().toString().padStart(2, '0');

                    return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
                },

                // 辅助方法：获取区域名称
                getRegionNames(regionIds) {
                    if (!regionIds || !regionIds.length) return ['所有区域'];
                    return regionIds.map(id => {
                        const region = this.regions.find(r => r.value === id);
                        return region ? region.label : id;
                    });
                },

                // 辅助方法：获取店铺名称
                getShopNames(shopIds) {
                    if (!shopIds || !shopIds.length) return ['所有店铺'];
                    return shopIds.map(id => {
                        const shop = this.shops.find(s => s.id == id);
                        return shop ? shop.name : id;
                    });
                },
                // 辅助方法：获取重量区间信息
                getWeightRangeInfo(rangeId) {
                    const range = this.weightRanges.find(r => r.id == rangeId);
                    if (!range) return {
                        minWeight: 0,
                        maxWeight: null
                    };
                    return {
                        minWeight: range.minWeight,
                        maxWeight: range.maxWeight
                    };
                },

                // 辅助方法：根据调整类型获取标签类型
                getAdjustmentTypeTag(type) {
                    if (type.includes('增幅')) return 'danger';
                    if (type.includes('降幅')) return 'success';
                    return 'info';
                },

                // 分页相关方法
                handleSizeChange(size) {
                    this.pagination.pageSize = size;
                },

                handleCurrentChange(page) {
                    this.pagination.currentPage = page;
                },

                handleAdditionalSizeChange(size) {
                    this.additionalPagination.pageSize = size;
                },

                handleAdditionalCurrentChange(page) {
                    this.additionalPagination.currentPage = page;
                },

                // 初始化重量区间
                initWeightRanges() {
                    // 从已有的规则中提取不同的重量区间
                    const weightRanges = [];
                    const seen = new Set();
                    this.feeRules.forEach(rule => {
                        const key = `${rule.minWeight}-${rule.maxWeight || '∞'}`;
                        if (!seen.has(key)) {
                            seen.add(key);
                            weightRanges.push({
                                id: weightRanges.length + 1,
                                minWeight: rule.minWeight,
                                maxWeight: rule.maxWeight
                            });
                        }
                    });
                    if (weightRanges.length > 0) {
                        this.weightRanges = weightRanges;
                    }
                },

                // 批量导入
                /* 
                handleBatchImport() {
                    this.$message.info('批量导入功能开发中...');
                },
                
                handleBatchImportAdditional() {
                    this.$message.info('批量导入附加规则功能开发中...');
                },
                */

                // 导出
                handleExport() {
                    // 准备导出数据
                    const exportData = this.feeRules.map(rule => ({
                        '规则ID': rule.id,
                        '起始单量': rule.minOrders,
                        '结束单量': rule.maxOrders || '无上限',
                        '起始重量(kg)': rule.minWeight,
                        '结束重量(kg)': rule.maxWeight || '无上限',
                        '价格(元/单)': rule.price,
                        '生效日期': rule.effectiveDate,
                        '状态': rule.status,
                        '备注': rule.remark || '',
                        '操作时间': rule.operateTime,
                        '操作人': rule.operator
                    }));

                    this.exportToExcel(exportData, '阶梯费用规则列表');
                    this.$message.success('导出成功');
                },

                // 导出附加规则
                handleExportAdditional() {
                    // 准备导出数据
                    const exportData = this.additionalRules.map(rule => ({
                        '规则ID': rule.id,
                        '调整类型': rule.adjustmentType,
                        '调整幅度': rule.adjustmentValue,
                        '适用区域': rule.region || '所有区域',
                        '适用团队': rule.teamName || '所有团队',
                        '适用店铺': rule.shopName || '所有店铺',
                        '起始重量(kg)': rule.minWeight,
                        '结束重量(kg)': rule.maxWeight || '无上限',
                        '生效开始日期': rule.startDate,
                        '生效结束日期': rule.endDate || '无结束日期',
                        '状态': rule.status,
                        '备注': rule.remark || '',
                        '操作时间': rule.operateTime,
                        '操作人': rule.operator
                    }));

                    this.exportToExcel(exportData, '附加费用规则列表');
                    this.$message.success('导出成功');
                },

                // 通用Excel导出函数
                exportToExcel(data, fileName) {
                    // 创建工作簿
                    const wb = XLSX.utils.book_new();

                    // 创建工作表
                    const ws = XLSX.utils.json_to_sheet(data);

                    // 设置列宽
                    const wscols = Object.keys(data[0] || {}).map(() => ({
                        wch: 15
                    }));
                    ws['!cols'] = wscols;

                    // 将工作表添加到工作簿
                    XLSX.utils.book_append_sheet(wb, ws, 'Sheet1');

                    // 生成Excel文件并下载
                    XLSX.writeFile(wb, `${fileName}_${this.formatDate(new Date())}.xlsx`);
                },

                // 下载附件
                downloadAttachment(row) {
                    if (row.attachment) {
                        this.$message.info('附件下载功能开发中...');
                    }
                },

                // 删除附加规则
                handleDeleteAdditionalRule(row) {
                    this.$confirm('确认删除该附加规则?', '提示', {
                        confirmButtonText: '确定',
                        cancelButtonText: '取消',
                        type: 'warning'
                    }).then(() => {
                        const index = this.additionalRules.findIndex(r => r.id === row.id);
                        if (index !== -1) {
                            this.additionalRules.splice(index, 1);
                            this.additionalPagination.total = this.additionalRules.length;
                            localStorage.setItem('additionalRules', JSON.stringify(this.additionalRules));
                            this.$message.success('删除成功');
                        }
                    }).catch(() => {
                        // 取消删除
                    });
                },

                // 计算费用
                calculateFee() {
                    if (!this.calculationForm.teamId || !this.calculationForm.month) {
                        this.$message.error('请选择团队和月份');
                        return;
                    }

                    // 模拟计算结果
                    const selectedTeam = this.teams.find(t => t.id === this.calculationForm.teamId);

                    this.calculationResult = {
                        show: true,
                        totalOrders: Math.floor(Math.random() * 5000) + 1000,
                        orderGrowth: Math.floor(Math.random() * 30) - 10,
                        currentTier: '1001-5000单',
                        currentRate: 11.8,
                        totalFee: ((Math.floor(Math.random() * 5000) + 1000) * 11.8).toFixed(2),
                        feeGrowth: Math.floor(Math.random() * 20) - 15,
                        rule: this.feeRules.find(r => r.id === 2) || this.feeRules[0],
                        shopDetails: [{
                            name: '旗舰店',
                            orders: Math.floor(Math.random() * 2000) + 500,
                            percentage: Math.floor(Math.random() * 60) + 20,
                            color: '#409EFF',
                            fee: ((Math.floor(Math.random() * 2000) + 500) * 11.8).toFixed(2)
                        }, {
                            name: '专卖店',
                            orders: Math.floor(Math.random() * 1500) + 300,
                            percentage: Math.floor(Math.random() * 40) + 10,
                            color: '#67C23A',
                            fee: ((Math.floor(Math.random() * 1500) + 300) * 11.8).toFixed(2)
                        }, {
                            name: '直营店',
                            orders: Math.floor(Math.random() * 1000) + 200,
                            percentage: Math.floor(Math.random() * 30) + 5,
                            color: '#E6A23C',
                            fee: ((Math.floor(Math.random() * 1000) + 200) * 11.8).toFixed(2)
                        }]
                    };
                },

                // 加载统计数据
                loadStatistics() {
                    if (!this.statisticsForm.teamId || !this.statisticsForm.dateRange) {
                        this.$message.error('请选择团队和时间范围');
                        return;
                    }

                    // 生成模拟数据
                    const months = this.generateMonthsArray(this.statisticsForm.dateRange);
                    const ordersData = this.generateRandomData(months.length, 1000, 5000);
                    const feeData = this.generateFeeData(ordersData);

                    // 渲染图表
                    this.$nextTick(() => {
                        this.renderOrdersChart(months, ordersData);
                        this.renderFeeChart(months, feeData);
                    });

                    this.$message.success('统计数据加载成功');
                },

                // 生成月份数组
                generateMonthsArray(dateRange) {
                    if (!dateRange || dateRange.length !== 2) return [];

                    const startDate = new Date(dateRange[0]);
                    const endDate = new Date(dateRange[1]);

                    const months = [];
                    let currentDate = new Date(startDate);

                    while (currentDate <= endDate) {
                        months.push(currentDate.getFullYear() + '-' +
                            (currentDate.getMonth() + 1).toString().padStart(2, '0'));
                        currentDate.setMonth(currentDate.getMonth() + 1);
                    }

                    return months;
                },

                // 生成随机数据
                generateRandomData(length, min, max) {
                    return Array.from({
                        length
                    }, () => Math.floor(Math.random() * (max - min + 1)) + min);
                },

                // 根据订单量生成费用数据
                generateFeeData(ordersData) {
                    return ordersData.map(orders => {
                        let price = 12.5; // 默认价格

                        // 根据订单量套用不同价格
                        if (orders > 10000) {
                            price = 10.5;
                        } else if (orders > 5000) {
                            price = 11.0;
                        } else if (orders > 1000) {
                            price = 11.8;
                        }

                        return Number((orders * price).toFixed(2));
                    });
                },

                // 渲染订单量图表
                renderOrdersChart(months, data) {
                    const chartDom = document.getElementById('ordersChart');

                    // 如果图表已存在，先销毁
                    if (this.charts.orders) {
                        this.charts.orders.dispose();
                    }

                    // 找出最大值和最小值
                    let maxValue = data[0],
                        minValue = data[0];
                    let maxIndex = 0,
                        minIndex = 0;

                    for (let i = 1; i < data.length; i++) {
                        if (data[i] > maxValue) {
                            maxValue = data[i];
                            maxIndex = i;
                        }
                        if (data[i] < minValue) {
                            minValue = data[i];
                            minIndex = i;
                        }
                    }

                    const myChart = echarts.init(chartDom);
                    this.charts.orders = myChart;

                    // 处理数据，为最大值和最小值添加标记
                    const processedData = data.map((value, index) => {
                        if (index === maxIndex || index === minIndex) {
                            return {
                                value: value,
                                symbol: index === maxIndex ? 'triangle' : 'circle',
                                symbolSize: 12,
                                itemStyle: {
                                    color: index === maxIndex ? '#409EFF' : '#67C23A'
                                },
                                label: {
                                    show: true,
                                    position: index === maxIndex ? 'top' : 'bottom',
                                    formatter: '{c} 单',
                                    fontSize: 12,
                                    color: index === maxIndex ? '#409EFF' : '#67C23A',
                                    fontWeight: 'bold',
                                    distance: 10
                                }
                            };
                        } else {
                            return value;
                        }
                    });

                    const option = {
                        title: {
                            text: '月度订单量趋势',
                            left: 'center',
                            textStyle: {
                                fontWeight: 'bold',
                                fontSize: 16,
                                color: '#303133'
                            }
                        },
                        tooltip: {
                            trigger: 'axis',
                            formatter: '{b}<br />{a}: {c} 单',
                            backgroundColor: 'rgba(255, 255, 255, 0.9)',
                            borderColor: '#eee',
                            borderWidth: 1,
                            textStyle: {
                                color: '#333'
                            },
                            axisPointer: {
                                type: 'shadow',
                                shadowStyle: {
                                    color: 'rgba(0, 0, 0, 0.03)'
                                }
                            }
                        },
                        xAxis: {
                            type: 'category',
                            data: months,
                            axisLine: {
                                lineStyle: {
                                    color: '#ddd'
                                }
                            },
                            axisLabel: {
                                color: '#606266'
                            }
                        },
                        yAxis: {
                            type: 'value',
                            name: '订单量',
                            nameTextStyle: {
                                color: '#606266'
                            },
                            axisLine: {
                                show: false
                            },
                            axisTick: {
                                show: false
                            },
                            axisLabel: {
                                formatter: '{value} 单',
                                color: '#606266'
                            },
                            splitLine: {
                                lineStyle: {
                                    color: '#eee',
                                    type: 'dashed'
                                }
                            }
                        },
                        series: [{
                            name: '订单量',
                            type: 'line',
                            data: data,
                            smooth: true,
                            symbol: 'circle',
                            symbolSize: 8,
                            lineStyle: {
                                width: 3,
                                color: '#409EFF'
                            },
                            itemStyle: {
                                color: '#409EFF',
                                borderWidth: 2,
                                borderColor: '#fff'
                            },
                            label: {
                                show: true,
                                position: 'top',
                                formatter: '{c} 单',
                                fontSize: 12,
                                offset: [0, -5],
                                color: '#606266'
                            },
                            markPoint: {
                                symbolSize: 60,
                                data: [{
                                    type: 'max',
                                    name: '最高值',
                                    itemStyle: {
                                        color: '#F56C6C'
                                    },
                                    label: {
                                        fontSize: 12,
                                        position: 'top',
                                        distance: 10,
                                        formatter: '最高: {c} 单'
                                    }
                                }, {
                                    type: 'min',
                                    name: '最低值',
                                    itemStyle: {
                                        color: '#67C23A'
                                    },
                                    label: {
                                        fontSize: 12,
                                        position: 'bottom',
                                        distance: 10,
                                        formatter: '最低: {c} 单'
                                    }
                                }]
                            },
                            markLine: {
                                symbol: 'none',
                                lineStyle: {
                                    type: 'solid',
                                    width: 2,
                                    color: '#E6A23C'
                                },
                                label: {
                                    formatter: '平均: {c} 单',
                                    color: '#E6A23C',
                                    fontSize: 12,
                                    fontWeight: 'bold',
                                    backgroundColor: 'rgba(255, 255, 255, 0.8)',
                                    padding: [4, 6],
                                    borderRadius: 3,
                                    position: 'insideEndTop',
                                    distance: 10
                                },
                                data: [{
                                    type: 'average',
                                    name: '平均值'
                                }]
                            },
                            emphasis: {
                                scale: true,
                                focus: 'series'
                            }
                        }],

                        grid: {
                            left: '5%',
                            right: '5%',
                            bottom: '10%',
                            top: '15%',
                            containLabel: true
                        },
                        toolbox: {
                            feature: {
                                saveAsImage: {
                                    title: '保存为图片',
                                    pixelRatio: 2
                                }
                            },
                            right: 20
                        }
                    };

                    myChart.setOption(option);
                    window.addEventListener('resize', function() {
                        myChart.resize();
                    });
                },

                // 渲染费用图表
                renderFeeChart(months, data) {
                    const chartDom = document.getElementById('feeChart');

                    // 如果图表已存在，先销毁
                    if (this.charts.fees) {
                        this.charts.fees.dispose();
                    }

                    // 找出最大值和最小值
                    let maxValue = data[0],
                        minValue = data[0];
                    let maxIndex = 0,
                        minIndex = 0;

                    for (let i = 1; i < data.length; i++) {
                        if (data[i] > maxValue) {
                            maxValue = data[i];
                            maxIndex = i;
                        }
                        if (data[i] < minValue) {
                            minValue = data[i];
                            minIndex = i;
                        }
                    }

                    const myChart = echarts.init(chartDom);
                    this.charts.fees = myChart;

                    const option = {
                        title: {
                            text: '月度费用支出趋势',
                            left: 'center',
                            textStyle: {
                                fontWeight: 'bold',
                                fontSize: 16,
                                color: '#303133'
                            }
                        },
                        tooltip: {
                            trigger: 'axis',
                            formatter: '{b}<br />{a}: ¥{c}',
                            backgroundColor: 'rgba(255, 255, 255, 0.9)',
                            borderColor: '#eee',
                            borderWidth: 1,
                            textStyle: {
                                color: '#333'
                            },
                            axisPointer: {
                                type: 'shadow',
                                shadowStyle: {
                                    color: 'rgba(0, 0, 0, 0.03)'
                                }
                            }
                        },
                        xAxis: {
                            type: 'category',
                            data: months,
                            axisLine: {
                                lineStyle: {
                                    color: '#ddd'
                                }
                            },
                            axisLabel: {
                                color: '#606266'
                            }
                        },
                        yAxis: {
                            type: 'value',
                            name: '费用',
                            nameTextStyle: {
                                color: '#606266'
                            },
                            axisLine: {
                                show: false
                            },
                            axisTick: {
                                show: false
                            },
                            axisLabel: {
                                formatter: '¥{value}',
                                color: '#606266'
                            },
                            splitLine: {
                                lineStyle: {
                                    color: '#eee',
                                    type: 'dashed'
                                }
                            }
                        },
                        series: [{
                            name: '费用',
                            type: 'bar',
                            data: data,
                            barWidth: '40%',
                            label: {
                                show: true,
                                position: 'top',
                                formatter: '¥{c}',
                                fontSize: 12,
                                offset: [0, 0],
                                color: '#606266'
                            },
                            markPoint: {
                                symbolSize: 40,
                                data: [{
                                    type: 'max',
                                    name: '最高值',
                                    itemStyle: {
                                        color: '#F56C6C'
                                    },
                                    label: {
                                        fontSize: 12,
                                        position: 'top',
                                        distance: 10,
                                        formatter: '最高: ¥{c}'
                                    }
                                }, {
                                    type: 'min',
                                    name: '最低值',
                                    itemStyle: {
                                        color: '#409EFF'
                                    },
                                    label: {
                                        fontSize: 12,
                                        position: 'bottom',
                                        distance: 10,
                                        formatter: '最低: ¥{c}'
                                    }
                                }]
                            },
                            markLine: {
                                data: [{
                                    type: 'average',
                                    name: '平均值',
                                    lineStyle: {
                                        type: 'solid',
                                        width: 1,
                                        color: '#E6A23C'
                                    },
                                    label: {
                                        formatter: '平均: ¥{c}',
                                        color: '#E6A23C',
                                        fontSize: 12,
                                        fontWeight: 'bold',
                                        backgroundColor: 'rgba(255, 255, 255, 0.8)',
                                        padding: [4, 6],
                                        borderRadius: 3,
                                        position: 'insideEndTop',
                                        distance: 10
                                    }
                                }]
                            },
                            itemStyle: {
                                color: {
                                    type: 'linear',
                                    x: 0,
                                    y: 0,
                                    x2: 0,
                                    y2: 1,
                                    colorStops: [{
                                        offset: 0,
                                        color: '#F56C6C'
                                    }, {
                                        offset: 1,
                                        color: '#FFA8A8'
                                    }]
                                },
                                borderRadius: [4, 4, 0, 0],
                                shadowColor: 'rgba(0, 0, 0, 0.1)',
                                shadowBlur: 10,
                                shadowOffsetY: 2
                            },
                            emphasis: {
                                itemStyle: {
                                    color: {
                                        type: 'linear',
                                        x: 0,
                                        y: 0,
                                        x2: 0,
                                        y2: 1,
                                        colorStops: [{
                                            offset: 0,
                                            color: '#E64C4C'
                                        }, {
                                            offset: 1,
                                            color: '#F09898'
                                        }]
                                    }
                                }
                            }
                        }],
                        grid: {
                            left: '5%',
                            right: '5%',
                            bottom: '10%',
                            top: '15%',
                            containLabel: true
                        },
                        toolbox: {
                            feature: {
                                saveAsImage: {
                                    title: '保存为图片',
                                    pixelRatio: 2
                                }
                            },
                            right: 20
                        }
                    };

                    myChart.setOption(option);
                    window.addEventListener('resize', function() {
                        myChart.resize();
                    });
                },

                // 初始化表格滚动处理
                initTableScrollHandler() {
                    this.$nextTick(() => {
                        // 延迟执行，确保表格已经渲染完成
                        setTimeout(() => {
                            const tables = document.querySelectorAll('.el-table__body-wrapper');
                            tables.forEach(table => {
                                table.addEventListener('scroll', function() {
                                    const maxScrollLeft = this.scrollWidth - this.clientWidth;
                                    const scrollPercentage = this.scrollLeft / maxScrollLeft;
                                    if (scrollPercentage > 0.95) {
                                        // 已滚动到接近最右侧
                                        this.closest('.el-table').classList.add('el-table--enable-right-scrolling');
                                        this.classList.add('is-scrolling-right');
                                    } else {
                                        // 未滚动到最右侧
                                        this.closest('.el-table').classList.remove('el-table--enable-right-scrolling');
                                        this.classList.remove('is-scrolling-right');
                                    }
                                });

                                // 触发一次滚动事件，初始状态检查
                                table.dispatchEvent(new Event('scroll'));
                            });
                        }, 300);
                    });
                },

                // 处理标签页切换
                handleTabClick(tab) {
                    if (tab.name === 'statistics') {
                        // 切换到统计分析标签页时，自动加载统计数据
                        this.$nextTick(() => {
                            this.loadStatistics();
                        });
                    }

                    // 重新绑定表格滚动事件
                    this.$nextTick(() => {
                        this.initTableScrollHandler();
                    });
                },

                // 导出统计数据
                exportStatistics() {
                    if (!this.statisticsForm.teamId || !this.statisticsForm.dateRange) {
                        this.$message.warning('请先选择团队和时间范围');
                        return;
                    }

                    // 获取团队名称
                    const teamName = this.teams.find(t => t.id === this.statisticsForm.teamId).name;
                    const startDate = this.formatDate(this.statisticsForm.dateRange[0]);
                    const endDate = this.formatDate(this.statisticsForm.dateRange[1]);

                    // 准备导出数据 - 订单量数据
                    const months = this.generateMonthsArray(this.statisticsForm.dateRange);
                    const ordersData = this.generateRandomData(months.length, 1000, 5000);
                    const feeData = this.generateFeeData(ordersData);

                    const exportData = months.map((month, index) => ({
                        '月份': month,
                        '订单量(单)': ordersData[index],
                        '费用(元)': feeData[index],
                        '平均单价(元/单)': (feeData[index] / ordersData[index]).toFixed(2)
                    }));

                    // 导出Excel
                    this.exportToExcel(exportData, `${teamName}_统计数据_${startDate}_${endDate}`);

                    this.$message({
                        message: '数据导出成功',
                        type: 'success',
                        duration: 2000
                    });
                },

                // 获取调整类型的类名
                getAdjustmentTypeClass(type) {
                    if (type === '增加比率') return 'increase-ratio';
                    if (type === '增加金额') return 'increase-amount';
                    if (type === '减少比率') return 'decrease-ratio';
                    if (type === '减少金额') return 'decrease-amount';
                    return '';
                },

                // 获取调整类型的图标
                getAdjustmentTypeIcon(type) {
                    switch (type) {
                        case '增加比率':
                            return 'el-icon-top';
                        case '增加金额':
                            return 'el-icon-plus';
                        case '减少比率':
                            return 'el-icon-bottom';
                        case '减少金额':
                            return 'el-icon-minus';
                        default:
                            return '';
                    }
                },

                // 查找初始化附加规则数据方法并更新生成逻辑
                generateMockAdditionalRules() {
                    const types = this.adjustmentTypes.map(item => item.label);
                    return Array.from({
                        length: 15
                    }, (_, i) => ({
                        id: 'AR' + (1000 + i),
                        adjustmentType: types[i % 4],
                        adjustmentValue: parseFloat((Math.random() * 15 + 5).toFixed(2)),
                        regions: this.getRandomRegions(),
                        teamName: i % 5 === 0 ? '所有团队' : '团队' + ((i % 5) + 1),
                        shopName: i % 3 === 0 ? '所有店铺' : '店铺' + ((i % 10) + 1),
                        weightRangeText: i % 4 === 0 ? '所有重量' : (i % 4) + ' kg以上',
                        dateRange: '2023-06-01 至 2023-12-31',
                        remark: i % 2 === 0 ? '重要客户特殊规则' : '临时活动规则',
                        createTime: '2023-06-01',
                        updateTime: '2023-06-' + (10 + i % 20),
                        operatorName: '管理员' + (i % 3 + 1)
                    }));
                },

                // 获取随机地区数据
                getRandomRegions() {
                    const allRegions = this.regions.flatMap(province =>
                        province.children.map(city => ({
                            provinceLabel: province.label,
                            provinceValue: province.value,
                            cityLabel: city.label,
                            cityValue: city.value
                        }))
                    );

                    // 随机选择1-3个地区
                    const count = Math.floor(Math.random() * 3) + 1;
                    const selectedIndices = [];

                    while (selectedIndices.length < count) {
                        const randomIndex = Math.floor(Math.random() * allRegions.length);
                        if (!selectedIndices.includes(randomIndex)) {
                            selectedIndices.push(randomIndex);
                        }
                    }

                    return selectedIndices.map(index => allRegions[index]);
                },

                // 添加新规则
                handleSaveRule() {
                    if (this.validateRuleForm()) {
                        // 克隆表单数据
                        const newRule = Object.assign({}, this.ruleForm);

                        // 生成规则ID
                        newRule.id = this.isEditMode ? newRule.id : this.getNextRuleId();

                        // 设置操作时间和操作人
                        newRule.operateTime = this.formatOperateTime(new Date());
                        newRule.operator = this.currentUser;

                        // 设置规则状态
                        newRule.status = this.determineRuleStatus(newRule.effectiveDate);

                        if (this.isEditMode) {
                            // 更新规则
                            const index = this.feeRules.findIndex(rule => rule.id === newRule.id);
                            if (index !== -1) {
                                this.feeRules.splice(index, 1, newRule);
                            }
                        } else {
                            // 添加新规则
                            this.feeRules.push(newRule);
                        }

                        // 更新本地存储
                        localStorage.setItem('feeRules', JSON.stringify(this.feeRules));

                        this.dialogVisible = false;
                        this.$message.success(this.isEditMode ? '规则已更新' : '规则已添加');

                        // 重置表单
                        this.resetRuleForm();
                    }
                },

                // 添加附加规则
                handleSaveAdditionalRule() {
                    if (this.validateAdditionalRuleForm()) {
                        // 克隆表单数据
                        const newRule = Object.assign({}, this.additionalRuleForm);

                        // 生成规则ID
                        newRule.id = this.isAdditionalEditMode ? newRule.id : this.getNextAdditionalRuleId();

                        // 设置操作时间和操作人
                        newRule.operateTime = this.formatOperateTime(new Date());
                        newRule.operator = this.currentUser;

                        // 处理日期区间
                        if (newRule.dateRange && newRule.dateRange.length === 2) {
                            newRule.startDate = this.formatDate(newRule.dateRange[0]);
                            newRule.endDate = this.formatDate(newRule.dateRange[1]);
                        }

                        // 设置规则状态
                        newRule.status = this.determineAdditionalRuleStatus(newRule.startDate, newRule.endDate);

                        if (this.isAdditionalEditMode) {
                            // 更新规则
                            const index = this.additionalRules.findIndex(rule => rule.id === newRule.id);
                            if (index !== -1) {
                                this.additionalRules.splice(index, 1, newRule);
                            }
                        } else {
                            // 添加新规则
                            this.additionalRules.push(newRule);
                        }

                        // 更新本地存储
                        localStorage.setItem('additionalRules', JSON.stringify(this.additionalRules));

                        this.additionalDialogVisible = false;
                        this.$message.success(this.isAdditionalEditMode ? '规则已更新' : '规则已添加');

                        // 重置表单
                        this.resetAdditionalRuleForm();
                    }
                },

                // 处理生效日期变化
                handleEffectiveDateChange(date) {
                    if (date) {
                        // 直接使用选择的日期，不再需要计算月份的第一天
                        console.log('生效日期已更新为：', date);

                        // 验证结束日期是否有效（如果已设置且早于生效日期，给出提示但不自动修改）
                        if (this.ruleForm.endDate && this.ruleForm.endDate !== '2099-12-31' &&
                            new Date(this.ruleForm.endDate) < new Date(date)) {
                            this.$message.warning('提示：结束日期不能早于生效日期，请调整结束日期');
                        }

                        // 验证生效日期是否早于当前日期，如果是则提示
                        const today = new Date();
                        today.setHours(0, 0, 0, 0);
                        if (new Date(date) < today) {
                            this.$message.warning('提示：生效日期不能早于今天');
                            // 将生效日期设置为今天
                            this.ruleForm.effectiveDate = this.formatDateToString(today);
                        }
                    }
                },

                // 处理结束日期变化
                handleEndDateChange(date) {
                    if (date) {
                        // 直接使用选择的日期，不再需要计算月份的最后一天
                        console.log('结束日期已更新为：', date);

                        // 验证结束日期是否有效（如果早于生效日期，给出提示但不自动修改生效日期）
                        if (this.ruleForm.effectiveDate && new Date(date) < new Date(this.ruleForm.effectiveDate)) {
                            this.$message.warning('提示：结束日期不能早于生效日期，请调整结束日期或生效日期');
                        }
                    } else {
                        // 如果用户清空了结束日期，则设置为null
                        this.ruleForm.endDate = null;
                        console.log('结束日期已清空，保存时将默认为2099-12-31');
                    }
                },

                // 获取月份第一天的工具函数
                getFirstDayOfMonth(date) {
                    return new Date(date.getFullYear(), date.getMonth(), 1).toISOString().split('T')[0];
                },

                // 获取月份最后一天的工具函数
                getLastDayOfMonth(date) {
                    return new Date(date.getFullYear(), date.getMonth() + 1, 0).toISOString().split('T')[0];
                },

                // 日期转字符串格式 YYYY-MM-DD
                formatDateToString(date) {
                    return date.toISOString().split('T')[0];
                },

                // 确定规则状态
                determineRuleStatus(effectiveDate, endDate) {
                    const now = new Date();
                    const startDate = new Date(effectiveDate);
                    const expiryDate = new Date(endDate);

                    if (now < startDate) {
                        return '待生效';
                    } else if (now > expiryDate) {
                        return '已过期';
                    } else {
                        return '生效中';
                    }
                },

                // 处理月份开始（生效日期）选择变化
                /*
                handleMonthStartChange(month) {
                    if (month) {
                        // 将月份转换为该月第一天 (YYYY-MM-01)
                        this.ruleForm.effectiveDate = month + '-01';
                        console.log('生效日期设置为月初：', this.ruleForm.effectiveDate);
                    } else {
                        this.ruleForm.effectiveDate = null;
                    }
                },
                */

                // 处理月份结束（结束日期）选择变化
                /*
                handleMonthEndChange(month) {
                    if (!month) {
                        this.ruleForm.endDate = '';
                        return;
                    }
                    let date = new Date(month);
                    let year = date.getFullYear();
                    let monthIndex = date.getMonth();
                    // 获取下个月的第一天
                    let nextMonth = new Date(year, monthIndex + 1, 1);
                    // 获取当前月的最后一天 (下个月第一天的前一天)
                    let lastDay = new Date(nextMonth - 1);

                    let formattedDate = `${year}-${String(monthIndex + 1).padStart(2, '0')}-${String(lastDay.getDate()).padStart(2, '0')}`;
                    this.ruleForm.endDate = formattedDate;
                    console.log('设置结束日期:', formattedDate);
                },
                */

                // 打开日期选择器
                openDatePicker(pickerId) {
                    this.$nextTick(() => {
                        // 查找并模拟点击隐藏的日期选择器
                        const datePicker = this.$refs[pickerId];
                        if (datePicker && typeof datePicker.focus === 'function') {
                            datePicker.focus();
                        }
                    });
                },

                // 更新规则状态（检查是否已失效）
                updateRulesStatus() {
                    const today = new Date();
                    today.setHours(0, 0, 0, 0);

                    this.feeRules.forEach(rule => {
                        // 检查结束日期
                        if (rule.endDate && rule.status !== '已停用') {
                            const endDate = new Date(rule.endDate);
                            endDate.setHours(23, 59, 59, 999);

                            if (today > endDate) {
                                rule.status = '已失效';
                            } else if (rule.effectiveDate) {
                                const effectiveDate = new Date(rule.effectiveDate);
                                effectiveDate.setHours(0, 0, 0, 0);

                                if (today < effectiveDate) {
                                    rule.status = '待生效';
                                } else if (today <= endDate) {
                                    rule.status = '生效中';
                                }
                            }
                        }
                    });

                    // 保存更新后的规则到本地存储
                    localStorage.setItem('feeRules', JSON.stringify(this.feeRules));
                }
            }
        });
    </script>
</body>

</html>