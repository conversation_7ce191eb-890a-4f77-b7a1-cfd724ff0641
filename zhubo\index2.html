<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>主播经营日报管理系统</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <style>
        /* 全局样式重置 */
        
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', 'Helvetica Neue', Helvetica, Arial, sans-serif;
            background-color: #f5f7fa;
            color: #333;
            line-height: 1.6;
        }
        /* 主容器 */
        
        .main-container {
            min-height: 100vh;
            padding: 24px;
            max-width: 1400px;
            margin: 0 auto;
        }
        /* 页面头部 */
        
        .page-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 32px;
            padding: 0 4px;
        }
        
        .header-left {
            display: flex;
            flex-direction: column;
            gap: 8px;
        }
        
        .page-title {
            font-size: 28px;
            font-weight: 600;
            color: #1a202c;
            display: flex;
            align-items: center;
            gap: 12px;
        }
        
        .page-title i {
            color: #3182ce;
            font-size: 24px;
        }
        
        .page-subtitle {
            color: #718096;
            font-size: 14px;
            margin-left: 36px;
        }
        
        .header-right {
            display: flex;
            gap: 16px;
        }
        
        .btn-settings {
            display: flex;
            align-items: center;
            gap: 8px;
            padding: 12px 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            border-radius: 8px;
            font-size: 14px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
        }
        
        .btn-settings:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 16px rgba(102, 126, 234, 0.4);
        }
        /* 主内容区域 */
        
        .main-content {
            display: flex;
            flex-direction: column;
            gap: 24px;
        }
        
        .content-card {
            background: white;
            border-radius: 16px;
            padding: 32px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
            border: 1px solid #e2e8f0;
        }
        
        .card-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 32px;
            padding-bottom: 20px;
            border-bottom: 2px solid #f7fafc;
        }
        
        .card-header h3 {
            font-size: 20px;
            font-weight: 600;
            color: #2d3748;
        }
        
        .date-selector {
            display: flex;
            align-items: center;
            gap: 12px;
        }
        
        .date-input {
            padding: 10px 16px;
            border: 2px solid #e2e8f0;
            border-radius: 8px;
            font-size: 14px;
            color: #4a5568;
            background: #f7fafc;
            transition: all 0.3s ease;
        }
        
        .date-input:focus {
            outline: none;
            border-color: #3182ce;
            background: white;
            box-shadow: 0 0 0 3px rgba(49, 130, 206, 0.1);
        }
        /* 统计网格 */
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 24px;
        }
        
        .stat-item {
            display: flex;
            align-items: center;
            gap: 20px;
            padding: 24px;
            background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
            border-radius: 12px;
            border: 1px solid #e2e8f0;
            transition: all 0.3s ease;
        }
        
        .stat-item:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 24px rgba(0, 0, 0, 0.1);
        }
        
        .stat-icon {
            width: 56px;
            height: 56px;
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 24px;
            color: white;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
        
        .stat-info {
            flex: 1;
        }
        
        .stat-info h4 {
            font-size: 14px;
            color: #718096;
            margin-bottom: 8px;
            font-weight: 500;
        }
        
        .stat-number {
            font-size: 24px;
            font-weight: 700;
            color: #2d3748;
        }
        /* 侧边栏样式 */
        
        .settings-sidebar {
            position: fixed;
            top: 0;
            right: -480px;
            width: 480px;
            height: 100vh;
            background: white;
            box-shadow: -4px 0 20px rgba(0, 0, 0, 0.15);
            transition: right 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            z-index: 1001;
            display: flex;
            flex-direction: column;
        }
        
        .settings-sidebar.active {
            right: 0;
        }
        
        .sidebar-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 24px 32px;
            border-bottom: 2px solid #f7fafc;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }
        
        .sidebar-header h2 {
            font-size: 20px;
            font-weight: 600;
        }
        
        .close-btn {
            background: none;
            border: none;
            color: white;
            font-size: 18px;
            cursor: pointer;
            padding: 8px;
            border-radius: 6px;
            transition: background-color 0.3s ease;
        }
        
        .close-btn:hover {
            background: rgba(255, 255, 255, 0.1);
        }
        /* Tab导航 */
        
        .tab-navigation {
            display: flex;
            flex-direction: column;
            padding: 24px 0;
            border-bottom: 2px solid #f7fafc;
        }
        
        .tab-btn {
            display: flex;
            align-items: center;
            gap: 12px;
            padding: 16px 32px;
            background: none;
            border: none;
            font-size: 14px;
            font-weight: 500;
            color: #718096;
            cursor: pointer;
            transition: all 0.3s ease;
            position: relative;
        }
        
        .tab-btn:hover {
            background: #f7fafc;
            color: #3182ce;
        }
        
        .tab-btn.active {
            color: #3182ce;
            background: #ebf8ff;
            border-right: 3px solid #3182ce;
        }
        
        .tab-btn i {
            font-size: 16px;
        }
        /* Tab内容 */
        
        .tab-content {
            flex: 1;
            overflow-y: auto;
        }
        
        .tab-pane {
            display: none;
            padding: 32px;
            height: 100%;
        }
        
        .tab-pane.active {
            display: block;
        }
        
        .pane-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 24px;
            padding-bottom: 16px;
            border-bottom: 2px solid #f7fafc;
        }
        
        .pane-header h3 {
            font-size: 18px;
            font-weight: 600;
            color: #2d3748;
        }
        /* 按钮样式 */
        
        .btn-primary {
            display: flex;
            align-items: center;
            gap: 8px;
            padding: 10px 16px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            border-radius: 6px;
            font-size: 14px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .btn-primary:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
        }
        
        .btn-secondary {
            padding: 10px 16px;
            background: #e2e8f0;
            color: #4a5568;
            border: none;
            border-radius: 6px;
            font-size: 14px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .btn-secondary:hover {
            background: #cbd5e0;
        }
        
        .btn-edit {
            padding: 6px 12px;
            background: #48bb78;
            color: white;
            border: none;
            border-radius: 4px;
            font-size: 12px;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .btn-edit:hover {
            background: #38a169;
        }
        
        .btn-disable {
            padding: 6px 12px;
            background: #f56565;
            color: white;
            border: none;
            border-radius: 4px;
            font-size: 12px;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .btn-disable:hover {
            background: #e53e3e;
        }
        /* 表格样式 */
        
        .table-container {
            background: white;
            border-radius: 8px;
            overflow: hidden;
            border: 1px solid #e2e8f0;
        }
        
        .data-table {
            width: 100%;
            border-collapse: collapse;
        }
        
        .data-table thead {
            background: #f7fafc;
        }
        
        .data-table th {
            padding: 16px 12px;
            text-align: left;
            font-weight: 600;
            color: #4a5568;
            font-size: 13px;
            border-bottom: 1px solid #e2e8f0;
        }
        
        .data-table td {
            padding: 16px 12px;
            border-bottom: 1px solid #f1f5f9;
            font-size: 14px;
            color: #2d3748;
        }
        
        .data-table tbody tr:hover {
            background: #f8fafc;
        }
        
        .status-active {
            display: inline-block;
            padding: 4px 8px;
            background: #c6f6d5;
            color: #22543d;
            border-radius: 12px;
            font-size: 12px;
            font-weight: 500;
        }
        
        .status-inactive {
            display: inline-block;
            padding: 4px 8px;
            background: #fed7d7;
            color: #742a2a;
            border-radius: 12px;
            font-size: 12px;
            font-weight: 500;
        }
        /* 遮罩层 */
        
        .overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100vw;
            height: 100vh;
            background: rgba(0, 0, 0, 0.5);
            z-index: 1000;
            opacity: 0;
            visibility: hidden;
            transition: all 0.3s ease;
        }
        
        .overlay.active {
            opacity: 1;
            visibility: visible;
        }
        /* 模态框样式 */
        
        .modal {
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%) scale(0.9);
            background: white;
            border-radius: 12px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
            z-index: 1002;
            opacity: 0;
            visibility: hidden;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            min-width: 400px;
            max-width: 500px;
        }
        
        .modal.active {
            opacity: 1;
            visibility: visible;
            transform: translate(-50%, -50%) scale(1);
        }
        
        .modal-content {
            display: flex;
            flex-direction: column;
        }
        
        .modal-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 24px 32px;
            border-bottom: 1px solid #e2e8f0;
        }
        
        .modal-header h3 {
            font-size: 18px;
            font-weight: 600;
            color: #2d3748;
        }
        
        .modal-close {
            background: none;
            border: none;
            font-size: 18px;
            color: #718096;
            cursor: pointer;
            padding: 4px;
            border-radius: 4px;
            transition: color 0.3s ease;
        }
        
        .modal-close:hover {
            color: #4a5568;
        }
        
        .modal-body {
            padding: 32px;
        }
        
        .modal-footer {
            display: flex;
            justify-content: flex-end;
            gap: 12px;
            padding: 24px 32px;
            border-top: 1px solid #e2e8f0;
        }
        /* 表单样式 */
        
        .form-group {
            margin-bottom: 20px;
        }
        
        .form-label {
            display: block;
            margin-bottom: 8px;
            font-weight: 500;
            color: #4a5568;
            font-size: 14px;
        }
        
        .form-input {
            width: 100%;
            padding: 12px 16px;
            border: 2px solid #e2e8f0;
            border-radius: 6px;
            font-size: 14px;
            color: #2d3748;
            transition: all 0.3s ease;
            background: #f7fafc;
        }
        
        .form-input:focus {
            outline: none;
            border-color: #3182ce;
            background: white;
            box-shadow: 0 0 0 3px rgba(49, 130, 206, 0.1);
        }
        
        .form-select {
            width: 100%;
            padding: 12px 16px;
            border: 2px solid #e2e8f0;
            border-radius: 6px;
            font-size: 14px;
            color: #2d3748;
            background: #f7fafc;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .form-select:focus {
            outline: none;
            border-color: #3182ce;
            background: white;
            box-shadow: 0 0 0 3px rgba(49, 130, 206, 0.1);
        }
        /* 响应式设计 */
        
        @media (max-width: 768px) {
            .main-container {
                padding: 16px;
            }
            .page-header {
                flex-direction: column;
                align-items: stretch;
                gap: 16px;
            }
            .stats-grid {
                grid-template-columns: 1fr;
            }
            .settings-sidebar {
                width: 100vw;
                right: -100vw;
            }
            .modal {
                margin: 20px;
                min-width: auto;
                max-width: calc(100vw - 40px);
            }
        }
        
        @media (max-width: 480px) {
            .content-card {
                padding: 20px;
            }
            .stat-item {
                padding: 16px;
                gap: 12px;
            }
            .stat-icon {
                width: 44px;
                height: 44px;
                font-size: 20px;
            }
            .stat-number {
                font-size: 20px;
            }
            .tab-pane {
                padding: 20px;
            }
            .data-table th,
            .data-table td {
                padding: 12px 8px;
                font-size: 13px;
            }
        }
    </style>
</head>

<body>
    <!-- 主页面容器 -->
    <div class="main-container">
        <!-- 页面头部 -->
        <div class="page-header">
            <div class="header-left">
                <h1 class="page-title">
                    <i class="fas fa-chart-line"></i> 主播经营日报
                </h1>
                <p class="page-subtitle">主播业务数据统计与管理</p>
            </div>
            <div class="header-right">
                <button class="btn-settings" id="settingsBtn">
                    <i class="fas fa-cog"></i>
                    基础设置
                </button>
            </div>
        </div>

        <!-- 主播经营日报内容区域 -->
        <div class="main-content">
            <div class="content-card">
                <div class="card-header">
                    <h3>今日经营概况</h3>
                    <div class="date-selector">
                        <input type="date" id="reportDate" class="date-input">
                    </div>
                </div>
                <div class="stats-grid">
                    <div class="stat-item">
                        <div class="stat-icon">
                            <i class="fas fa-users"></i>
                        </div>
                        <div class="stat-info">
                            <h4>在线主播</h4>
                            <span class="stat-number">28</span>
                        </div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-icon">
                            <i class="fas fa-dollar-sign"></i>
                        </div>
                        <div class="stat-info">
                            <h4>总收入</h4>
                            <span class="stat-number">¥12,580</span>
                        </div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-icon">
                            <i class="fas fa-credit-card"></i>
                        </div>
                        <div class="stat-info">
                            <h4>总支出</h4>
                            <span class="stat-number">¥8,420</span>
                        </div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-icon">
                            <i class="fas fa-chart-pie"></i>
                        </div>
                        <div class="stat-info">
                            <h4>净利润</h4>
                            <span class="stat-number">¥4,160</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 基础设置侧边栏 -->
    <div class="settings-sidebar" id="settingsSidebar">
        <div class="sidebar-header">
            <h2>基础设置</h2>
            <button class="close-btn" id="closeSidebar">
                <i class="fas fa-times"></i>
            </button>
        </div>

        <!-- Tab导航 -->
        <div class="tab-navigation">
            <button class="tab-btn active" data-tab="income-expense">
                <i class="fas fa-money-bill-wave"></i>
                收入支出维护
            </button>
            <button class="tab-btn" data-tab="personnel">
                <i class="fas fa-user-friends"></i>
                人员维护
            </button>
            <button class="tab-btn" data-tab="level">
                <i class="fas fa-layer-group"></i>
                级别维护
            </button>
        </div>

        <!-- Tab内容区域 -->
        <div class="tab-content">
            <!-- 主播业务收入支出基础维护 -->
            <div class="tab-pane active" id="income-expense">
                <div class="pane-header">
                    <h3>主播业务收入支出基础维护</h3>
                    <button class="btn-primary" id="addIncomeExpenseBtn">
                        <i class="fas fa-plus"></i>
                        新增
                    </button>
                </div>
                <div class="table-container">
                    <table class="data-table" id="incomeExpenseTable">
                        <thead>
                            <tr>
                                <th>主播级别</th>
                                <th>收入级别时薪（元/小时）</th>
                                <th>支出级别时薪（元/小时）</th>
                                <th>状态</th>
                                <th>操作</th>
                            </tr>
                        </thead>
                        <tbody>
                            <!-- 数据行将通过JS动态生成 -->
                        </tbody>
                    </table>
                </div>
            </div>

            <!-- 主播人员基础维护 -->
            <div class="tab-pane" id="personnel">
                <div class="pane-header">
                    <h3>主播人员基础维护</h3>
                    <button class="btn-primary" id="addPersonnelBtn">
                        <i class="fas fa-plus"></i>
                        新增
                    </button>
                </div>
                <div class="table-container">
                    <table class="data-table" id="personnelTable">
                        <thead>
                            <tr>
                                <th>主播人员</th>
                                <th>主播级别</th>
                                <th>主播年限工资</th>
                                <th>状态</th>
                                <th>操作</th>
                            </tr>
                        </thead>
                        <tbody>
                            <!-- 数据行将通过JS动态生成 -->
                        </tbody>
                    </table>
                </div>
            </div>

            <!-- 主播级别维护 -->
            <div class="tab-pane" id="level">
                <div class="pane-header">
                    <h3>主播级别维护</h3>
                    <button class="btn-primary" id="addLevelBtn">
                        <i class="fas fa-plus"></i>
                        新增
                    </button>
                </div>
                <div class="table-container">
                    <table class="data-table" id="levelTable">
                        <thead>
                            <tr>
                                <th>主播级别名称</th>
                                <th>状态</th>
                                <th>操作</th>
                            </tr>
                        </thead>
                        <tbody>
                            <!-- 数据行将通过JS动态生成 -->
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>

    <!-- 遮罩层 -->
    <div class="overlay" id="overlay"></div>

    <!-- 弹窗模态框 -->
    <div class="modal" id="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3 id="modalTitle">新增</h3>
                <button class="modal-close" id="modalClose">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="modal-body" id="modalBody">
                <!-- 表单内容将通过JS动态生成 -->
            </div>
            <div class="modal-footer">
                <button class="btn-secondary" id="modalCancel">取消</button>
                <button class="btn-primary" id="modalConfirm">确定</button>
            </div>
        </div>
    </div>

    <script>
        // 全局变量
        let currentTab = 'income-expense';
        let currentEditId = null;
        let currentEditType = null;

        // 数据存储
        const data = {
            levels: [{
                id: 1,
                name: '初级主播',
                status: 'active'
            }, {
                id: 2,
                name: '中级主播',
                status: 'active'
            }, {
                id: 3,
                name: '高级主播',
                status: 'active'
            }, {
                id: 4,
                name: '金牌主播',
                status: 'active'
            }],
            incomeExpense: [{
                id: 1,
                levelId: 1,
                levelName: '初级主播',
                incomeRate: 50,
                expenseRate: 30,
                status: 'active'
            }, {
                id: 2,
                levelId: 2,
                levelName: '中级主播',
                incomeRate: 80,
                expenseRate: 50,
                status: 'active'
            }, {
                id: 3,
                levelId: 3,
                levelName: '高级主播',
                incomeRate: 120,
                expenseRate: 80,
                status: 'active'
            }, {
                id: 4,
                levelId: 4,
                levelName: '金牌主播',
                incomeRate: 200,
                expenseRate: 120,
                status: 'active'
            }],
            personnel: [{
                id: 1,
                name: '张小美',
                levelId: 2,
                levelName: '中级主播',
                yearlySalary: 120000,
                status: 'active'
            }, {
                id: 2,
                name: '李娜娜',
                levelId: 3,
                levelName: '高级主播',
                yearlySalary: 180000,
                status: 'active'
            }, {
                id: 3,
                name: '王美丽',
                levelId: 1,
                levelName: '初级主播',
                yearlySalary: 80000,
                status: 'active'
            }, {
                id: 4,
                name: '赵雅雅',
                levelId: 4,
                levelName: '金牌主播',
                yearlySalary: 300000,
                status: 'active'
            }]
        };

        // DOM 元素
        const settingsBtn = document.getElementById('settingsBtn');
        const settingsSidebar = document.getElementById('settingsSidebar');
        const closeSidebar = document.getElementById('closeSidebar');
        const overlay = document.getElementById('overlay');
        const modal = document.getElementById('modal');
        const modalTitle = document.getElementById('modalTitle');
        const modalBody = document.getElementById('modalBody');
        const modalClose = document.getElementById('modalClose');
        const modalCancel = document.getElementById('modalCancel');
        const modalConfirm = document.getElementById('modalConfirm');

        // 初始化
        document.addEventListener('DOMContentLoaded', function() {
            initEventListeners();
            initDatePicker();
            renderAllTables();
        });

        // 事件监听器初始化
        function initEventListeners() {
            // 设置按钮点击
            settingsBtn.addEventListener('click', openSettings);

            // 关闭侧边栏
            closeSidebar.addEventListener('click', closeSettings);
            overlay.addEventListener('click', closeSettings);

            // Tab切换
            document.querySelectorAll('.tab-btn').forEach(btn => {
                btn.addEventListener('click', function() {
                    switchTab(this.dataset.tab);
                });
            });

            // 新增按钮
            document.getElementById('addIncomeExpenseBtn').addEventListener('click', () => openAddModal('incomeExpense'));
            document.getElementById('addPersonnelBtn').addEventListener('click', () => openAddModal('personnel'));
            document.getElementById('addLevelBtn').addEventListener('click', () => openAddModal('level'));

            // 模态框控制
            modalClose.addEventListener('click', closeModal);
            modalCancel.addEventListener('click', closeModal);
            modalConfirm.addEventListener('click', confirmModal);

            // 点击模态框外部关闭
            modal.addEventListener('click', function(e) {
                if (e.target === modal) {
                    closeModal();
                }
            });

            // ESC键关闭
            document.addEventListener('keydown', function(e) {
                if (e.key === 'Escape') {
                    if (modal.classList.contains('active')) {
                        closeModal();
                    } else if (settingsSidebar.classList.contains('active')) {
                        closeSettings();
                    }
                }
            });
        }

        // 初始化日期选择器
        function initDatePicker() {
            const dateInput = document.getElementById('reportDate');
            const today = new Date();
            dateInput.value = today.toISOString().split('T')[0];
        }

        // 打开设置面板
        function openSettings() {
            settingsSidebar.classList.add('active');
            overlay.classList.add('active');
            document.body.style.overflow = 'hidden';
        }

        // 关闭设置面板
        function closeSettings() {
            settingsSidebar.classList.remove('active');
            overlay.classList.remove('active');
            document.body.style.overflow = 'auto';
        }

        // Tab切换
        function switchTab(tabName) {
            // 更新tab按钮状态
            document.querySelectorAll('.tab-btn').forEach(btn => {
                btn.classList.toggle('active', btn.dataset.tab === tabName);
            });

            // 更新tab内容
            document.querySelectorAll('.tab-pane').forEach(pane => {
                pane.classList.toggle('active', pane.id === tabName);
            });

            currentTab = tabName;
        }

        // 渲染所有表格
        function renderAllTables() {
            renderIncomeExpenseTable();
            renderPersonnelTable();
            renderLevelTable();
        }

        // 渲染收入支出表格
        function renderIncomeExpenseTable() {
            const tbody = document.querySelector('#incomeExpenseTable tbody');
            tbody.innerHTML = '';

            data.incomeExpense.forEach(item => {
                const row = document.createElement('tr');
                row.innerHTML = `
            <td>${item.levelName}</td>
            <td>¥${item.incomeRate}</td>
            <td>¥${item.expenseRate}</td>
            <td>
                <span class="status-${item.status}">
                    ${item.status === 'active' ? '启用' : '停用'}
                </span>
            </td>
            <td>
                <div style="display: flex; gap: 8px;">
                    <button class="btn-edit" onclick="editItem('incomeExpense', ${item.id})">
                        <i class="fas fa-edit"></i> 编辑
                    </button>
                    <button class="btn-disable" onclick="toggleStatus('incomeExpense', ${item.id})">
                        <i class="fas fa-ban"></i> ${item.status === 'active' ? '停用' : '启用'}
                    </button>
                </div>
            </td>
        `;
                tbody.appendChild(row);
            });
        }

        // 渲染人员表格
        function renderPersonnelTable() {
            const tbody = document.querySelector('#personnelTable tbody');
            tbody.innerHTML = '';

            data.personnel.forEach(item => {
                const row = document.createElement('tr');
                row.innerHTML = `
            <td>${item.name}</td>
            <td>${item.levelName}</td>
            <td>¥${item.yearlySalary.toLocaleString()}</td>
            <td>
                <span class="status-${item.status}">
                    ${item.status === 'active' ? '启用' : '停用'}
                </span>
            </td>
            <td>
                <div style="display: flex; gap: 8px;">
                    <button class="btn-edit" onclick="editItem('personnel', ${item.id})">
                        <i class="fas fa-edit"></i> 编辑
                    </button>
                    <button class="btn-disable" onclick="toggleStatus('personnel', ${item.id})">
                        <i class="fas fa-ban"></i> ${item.status === 'active' ? '停用' : '启用'}
                    </button>
                </div>
            </td>
        `;
                tbody.appendChild(row);
            });
        }

        // 渲染级别表格
        function renderLevelTable() {
            const tbody = document.querySelector('#levelTable tbody');
            tbody.innerHTML = '';

            data.levels.forEach(item => {
                const row = document.createElement('tr');
                row.innerHTML = `
            <td>${item.name}</td>
            <td>
                <span class="status-${item.status}">
                    ${item.status === 'active' ? '启用' : '停用'}
                </span>
            </td>
            <td>
                <div style="display: flex; gap: 8px;">
                    <button class="btn-edit" onclick="editItem('level', ${item.id})">
                        <i class="fas fa-edit"></i> 编辑
                    </button>
                    <button class="btn-disable" onclick="toggleStatus('level', ${item.id})">
                        <i class="fas fa-ban"></i> ${item.status === 'active' ? '停用' : '启用'}
                    </button>
                </div>
            </td>
        `;
                tbody.appendChild(row);
            });
        }

        // 获取启用的级别选项
        function getActiveLevels() {
            return data.levels.filter(level => level.status === 'active');
        }

        // 生成级别下拉选项
        function generateLevelOptions(selectedId = null) {
            const levels = getActiveLevels();
            return levels.map(level =>
                `<option value="${level.id}" ${selectedId === level.id ? 'selected' : ''}>${level.name}</option>`
            ).join('');
        }

        // 打开新增模态框
        function openAddModal(type) {
            currentEditId = null;
            currentEditType = type;

            let title = '';
            let formHtml = '';

            switch (type) {
                case 'incomeExpense':
                    title = '新增收入支出配置';
                    formHtml = `
                <div class="form-group">
                    <label class="form-label">主播级别 *</label>
                    <select class="form-select" id="levelId" required>
                        <option value="">请选择主播级别</option>
                        ${generateLevelOptions()}
                    </select>
                </div>
                <div class="form-group">
                    <label class="form-label">收入级别时薪（元/小时） *</label>
                    <input type="number" class="form-input" id="incomeRate" placeholder="请输入收入时薪" min="0" step="0.01" required>
                </div>
                <div class="form-group">
                    <label class="form-label">支出级别时薪（元/小时） *</label>
                    <input type="number" class="form-input" id="expenseRate" placeholder="请输入支出时薪" min="0" step="0.01" required>
                </div>
            `;
                    break;

                case 'personnel':
                    title = '新增主播人员';
                    formHtml = `
                <div class="form-group">
                    <label class="form-label">主播人员 *</label>
                    <input type="text" class="form-input" id="name" placeholder="请输入主播姓名" required>
                </div>
                <div class="form-group">
                    <label class="form-label">主播级别 *</label>
                    <select class="form-select" id="levelId" required>
                        <option value="">请选择主播级别</option>
                        ${generateLevelOptions()}
                    </select>
                </div>
                <div class="form-group">
                    <label class="form-label">主播年限工资 *</label>
                    <input type="number" class="form-input" id="yearlySalary" placeholder="请输入年限工资" min="0" step="1000" required>
                </div>
            `;
                    break;

                case 'level':
                    title = '新增主播级别';
                    formHtml = `
                <div class="form-group">
                    <label class="form-label">主播级别名称 *</label>
                    <input type="text" class="form-input" id="name" placeholder="请输入级别名称" required>
                </div>
            `;
                    break;
            }

            modalTitle.textContent = title;
            modalBody.innerHTML = formHtml;
            openModal();
        }

        // 编辑项目
        function editItem(type, id) {
            currentEditId = id;
            currentEditType = type;

            let title = '';
            let formHtml = '';
            let item = null;

            switch (type) {
                case 'incomeExpense':
                    item = data.incomeExpense.find(i => i.id === id);
                    title = '编辑收入支出配置';
                    formHtml = `
                <div class="form-group">
                    <label class="form-label">主播级别 *</label>
                    <select class="form-select" id="levelId" required>
                        <option value="">请选择主播级别</option>
                        ${generateLevelOptions(item.levelId)}
                    </select>
                </div>
                <div class="form-group">
                    <label class="form-label">收入级别时薪（元/小时） *</label>
                    <input type="number" class="form-input" id="incomeRate" value="${item.incomeRate}" min="0" step="0.01" required>
                </div>
                <div class="form-group">
                    <label class="form-label">支出级别时薪（元/小时） *</label>
                    <input type="number" class="form-input" id="expenseRate" value="${item.expenseRate}" min="0" step="0.01" required>
                </div>
            `;
                    break;

                case 'personnel':
                    item = data.personnel.find(i => i.id === id);
                    title = '编辑主播人员';
                    formHtml = `
                <div class="form-group">
                    <label class="form-label">主播人员 *</label>
                    <input type="text" class="form-input" id="name" value="${item.name}" required>
                </div>
                <div class="form-group">
                    <label class="form-label">主播级别 *</label>
                    <select class="form-select" id="levelId" required>
                        <option value="">请选择主播级别</option>
                        ${generateLevelOptions(item.levelId)}
                    </select>
                </div>
                <div class="form-group">
                    <label class="form-label">主播年限工资 *</label>
                    <input type="number" class="form-input" id="yearlySalary" value="${item.yearlySalary}" min="0" step="1000" required>
                </div>
            `;
                    break;

                case 'level':
                    item = data.levels.find(i => i.id === id);
                    title = '编辑主播级别';
                    formHtml = `
                <div class="form-group">
                    <label class="form-label">主播级别名称 *</label>
                    <input type="text" class="form-input" id="name" value="${item.name}" required>
                </div>
            `;
                    break;
            }

            modalTitle.textContent = title;
            modalBody.innerHTML = formHtml;
            openModal();
        }

        // 切换状态
        function toggleStatus(type, id) {
            const item = data[type].find(i => i.id === id);
            if (item) {
                item.status = item.status === 'active' ? 'inactive' : 'active';

                // 如果是级别被停用，需要检查并处理相关数据
                if (type === 'level' && item.status === 'inactive') {
                    handleLevelDisabled(id);
                }

                renderAllTables();
                showToast(item.status === 'active' ? '启用成功' : '停用成功');
            }
        }

        // 处理级别停用后的相关数据
        function handleLevelDisabled(levelId) {
            // 停用相关的收入支出配置
            data.incomeExpense.forEach(item => {
                if (item.levelId === levelId) {
                    item.status = 'inactive';
                }
            });

            // 停用相关的人员
            data.personnel.forEach(item => {
                if (item.levelId === levelId) {
                    item.status = 'inactive';
                }
            });
        }

        // 打开模态框
        function openModal() {
            modal.classList.add('active');
            overlay.classList.add('active');
            document.body.style.overflow = 'hidden';

            // 聚焦第一个输入框
            setTimeout(() => {
                const firstInput = modal.querySelector('input, select');
                if (firstInput) {
                    firstInput.focus();
                }
            }, 100);
        }

        // 关闭模态框
        function closeModal() {
            modal.classList.remove('active');
            overlay.classList.remove('active');
            document.body.style.overflow = 'auto';
            currentEditId = null;
            currentEditType = null;
        }

        // 确认模态框
        function confirmModal() {
            if (!validateForm()) {
                return;
            }

            const formData = getFormData();

            if (currentEditId) {
                updateData(currentEditType, currentEditId, formData);
                showToast('更新成功');
            } else {
                addData(currentEditType, formData);
                showToast('添加成功');
            }

            renderAllTables();
            closeModal();
        }

        // 表单验证
        function validateForm() {
            const requiredFields = modal.querySelectorAll('[required]');
            let isValid = true;

            requiredFields.forEach(field => {
                if (!field.value.trim()) {
                    field.style.borderColor = '#f56565';
                    isValid = false;
                } else {
                    field.style.borderColor = '#e2e8f0';
                }
            });

            // 检查级别名称重复
            if (currentEditType === 'level') {
                const nameInput = document.getElementById('name');
                const name = nameInput.value.trim();
                const existingLevel = data.levels.find(level =>
                    level.name === name && level.id !== currentEditId
                );

                if (existingLevel) {
                    nameInput.style.borderColor = '#f56565';
                    showToast('级别名称已存在', 'error');
                    isValid = false;
                }
            }

            // 检查人员名称重复
            if (currentEditType === 'personnel') {
                const nameInput = document.getElementById('name');
                const name = nameInput.value.trim();
                const existingPersonnel = data.personnel.find(person =>
                    person.name === name && person.id !== currentEditId
                );

                if (existingPersonnel) {
                    nameInput.style.borderColor = '#f56565';
                    showToast('主播人员已存在', 'error');
                    isValid = false;
                }
            }

            // 检查收入支出配置重复
            if (currentEditType === 'incomeExpense') {
                const levelId = parseInt(document.getElementById('levelId').value);
                const existingConfig = data.incomeExpense.find(config =>
                    config.levelId === levelId && config.id !== currentEditId
                );

                if (existingConfig) {
                    document.getElementById('levelId').style.borderColor = '#f56565';
                    showToast('该级别的配置已存在', 'error');
                    isValid = false;
                }
            }

            if (!isValid) {
                showToast('请检查表单输入', 'error');
            }

            return isValid;
        }

        // 获取表单数据
        function getFormData() {
            const formData = {};

            modal.querySelectorAll('input, select').forEach(field => {
                let value = field.value.trim();

                // 数字类型转换
                if (field.type === 'number') {
                    value = parseFloat(value) || 0;
                } else if (field.id === 'levelId') {
                    value = parseInt(value) || null;
                }

                formData[field.id] = value;
            });

            return formData;
        }

        // 添加数据
        function addData(type, formData) {
            const newId = Math.max(...data[type].map(item => item.id)) + 1;

            let newItem = {
                id: newId,
                status: 'active',
                ...formData
            };

            // 特殊处理
            if (type === 'incomeExpense' || type === 'personnel') {
                const level = data.levels.find(l => l.id === formData.levelId);
                newItem.levelName = level ? level.name : '';
            }

            data[type].push(newItem);
        }

        // 更新数据
        function updateData(type, id, formData) {
            const index = data[type].findIndex(item => item.id === id);
            if (index !== -1) {
                data[type][index] = {
                    ...data[type][index],
                    ...formData
                };

                // 特殊处理
                if (type === 'incomeExpense' || type === 'personnel') {
                    const level = data.levels.find(l => l.id === formData.levelId);
                    data[type][index].levelName = level ? level.name : '';
                }

                // 如果是级别名称更新，需要同步更新相关数据
                if (type === 'level') {
                    updateRelatedLevelNames(id, formData.name);
                }
            }
        }

        // 更新相关的级别名称
        function updateRelatedLevelNames(levelId, newName) {
            data.incomeExpense.forEach(item => {
                if (item.levelId === levelId) {
                    item.levelName = newName;
                }
            });

            data.personnel.forEach(item => {
                if (item.levelId === levelId) {
                    item.levelName = newName;
                }
            });
        }

        // 显示提示消息
        function showToast(message, type = 'success') {
            // 移除已存在的toast
            const existingToast = document.querySelector('.toast');
            if (existingToast) {
                existingToast.remove();
            }

            const toast = document.createElement('div');
            toast.className = `toast toast-${type}`;
            toast.innerHTML = `
        <i class="fas fa-${type === 'success' ? 'check-circle' : 'exclamation-circle'}"></i>
        <span>${message}</span>
    `;

            // 添加样式
            const style = document.createElement('style');
            style.textContent = `
        .toast {
            position: fixed;
            top: 20px;
            right: 20px;
            padding: 16px 20px;
            border-radius: 8px;
            color: white;
            font-weight: 500;
            z-index: 10000;
            display: flex;
            align-items: center;
            gap: 10px;
            transform: translateX(100%);
            transition: transform 0.3s ease;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        }
        
        .toast-success {
            background: linear-gradient(135deg, #48bb78, #38a169);
        }
        
        .toast-error {
            background: linear-gradient(135deg, #f56565, #e53e3e);
        }
        
        .toast.show {
            transform: translateX(0);
        }
    `;

            if (!document.querySelector('style[data-toast]')) {
                style.setAttribute('data-toast', 'true');
                document.head.appendChild(style);
            }

            document.body.appendChild(toast);

            // 显示动画
            setTimeout(() => {
                toast.classList.add('show');
            }, 100);

            // 自动隐藏
            setTimeout(() => {
                toast.classList.remove('show');
                setTimeout(() => {
                    toast.remove();
                }, 300);
            }, 3000);
        }

        // 键盘快捷键支持
        document.addEventListener('keydown', function(e) {
            // Ctrl/Cmd + S 保存
            if ((e.ctrlKey || e.metaKey) && e.key === 's') {
                e.preventDefault();
                if (modal.classList.contains('active')) {
                    confirmModal();
                }
            }

            // Enter在模态框中确认
            if (e.key === 'Enter' && modal.classList.contains('active')) {
                const activeElement = document.activeElement;
                if (activeElement.tagName !== 'TEXTAREA') {
                    e.preventDefault();
                    confirmModal();
                }
            }
        });

        // 窗口大小改变时的响应
        window.addEventListener('resize', function() {
            // 如果是移动端且侧边栏打开，调整样式
            if (window.innerWidth <= 768 && settingsSidebar.classList.contains('active')) {
                settingsSidebar.style.width = '100vw';
            } else if (window.innerWidth > 768) {
                settingsSidebar.style.width = '480px';
            }
        });

        // 数据导出功能（可选扩展）
        function exportData() {
            const exportData = {
                levels: data.levels,
                incomeExpense: data.incomeExpense,
                personnel: data.personnel,
                exportTime: new Date().toISOString()
            };

            const dataStr = JSON.stringify(exportData, null, 2);
            const dataBlob = new Blob([dataStr], {
                type: 'application/json'
            });

            const link = document.createElement('a');
            link.href = URL.createObjectURL(dataBlob);
            link.download = `主播管理配置_${new Date().toISOString().split('T')[0]}.json`;
            link.click();
        }

        // 初始化完成提示
        console.log('%c主播经营日报管理系统已初始化完成', 'color: #4CAF50; font-weight: bold; font-size: 14px;');
        console.log('%c• 支持响应式设计', 'color: #2196F3; font-size: 12px;');
        console.log('%c• 支持键盘快捷键操作', 'color: #2196F3; font-size: 12px;');
        console.log('%c• 数据实时验证与同步', 'color: #2196F3; font-size: 12px;');
    </script>
</body>

</html>