<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>刷单成本设置 - 电商后台管理系统</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/remixicon@3.5.0/fonts/remixicon.css">
    <link rel="icon" href="https://panhan.xin/assets/favicon-DfI5CYqk.ico">
    <style>
         :root {
            --color-primary: #2563EB;
            --color-primary-light: #DBEAFE;
            --color-primary-dark: #1D4ED8;
            --color-success: #059669;
            --color-warning: #EAB308;
            --color-danger: #DC2626;
            --color-bg: #F9FAFB;
            --color-white: #FFFFFF;
            --color-card: #FFFFFF;
            --color-text: #111827;
            --color-text-secondary: #4B5563;
            --color-text-light: #6B7280;
            --color-border: #E5E7EB;
            --color-border-light: #F3F4F6;
            --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
            --shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px -1px rgba(0, 0, 0, 0.1);
            --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -2px rgba(0, 0, 0, 0.1);
            --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -4px rgba(0, 0, 0, 0.1);
            --radius-sm: 0.25rem;
            --radius: 0.375rem;
            --radius-md: 0.5rem;
            --radius-lg: 0.75rem;
            --transition: all 0.2s ease-in-out;
            --font-sans: 'Inter', system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
        }
        
        @keyframes fadeIn {
            from {
                opacity: 0;
                transform: translateY(-10px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }
        
        @keyframes slideIn {
            from {
                transform: translateY(20px);
                opacity: 0;
            }
            to {
                transform: translateY(0);
                opacity: 1;
            }
        }
        
        @keyframes pulse {
            0% {
                transform: scale(1);
            }
            50% {
                transform: scale(1.05);
            }
            100% {
                transform: scale(1);
            }
        }
        
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: var(--font-sans);
            background-color: var(--color-bg);
            color: var(--color-text);
            font-size: 0.875rem;
            line-height: 1.5;
            -webkit-font-smoothing: antialiased;
            -moz-osx-font-smoothing: grayscale;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 1.5rem;
        }
        
        .page-header {
            margin-bottom: 1.5rem;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .page-title {
            font-size: 1.5rem;
            font-weight: 600;
            color: var(--color-text);
            display: flex;
            align-items: center;
            gap: 0.75rem;
        }
        
        .page-title .icon {
            background-color: var(--color-primary-light);
            color: var(--color-primary);
            width: 2.5rem;
            height: 2.5rem;
            border-radius: 0.75rem;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.25rem;
        }
        
        .card {
            background-color: var(--color-card);
            border-radius: var(--radius-lg);
            box-shadow: var(--shadow);
            margin-bottom: 1.5rem;
            overflow: hidden;
            transition: var(--transition);
        }
        
        .card-header {
            padding: 1.25rem 1.5rem;
            border-bottom: 1px solid var(--color-border-light);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .card-title {
            font-size: 1.125rem;
            font-weight: 600;
        }
        
        .card-body {
            padding: 1.5rem;
        }
        
        .data-table-wrapper {
            overflow-x: auto;
        }
        
        .data-table {
            width: 100%;
            border-collapse: separate;
            border-spacing: 0;
            font-size: 0.875rem;
        }
        
        .data-table th {
            background-color: var(--color-bg);
            padding: 0.875rem 1.5rem;
            text-align: left;
            font-weight: 600;
            color: var(--color-text-secondary);
            border-bottom: 1px solid var(--color-border);
            white-space: nowrap;
        }
        
        .data-table td {
            padding: 1.125rem 1.5rem;
            border-bottom: 1px solid var(--color-border-light);
            vertical-align: middle;
        }
        
        .data-table tr:hover td {
            background-color: rgba(59, 130, 246, 0.04);
        }
        
        .data-table tr:last-child td {
            border-bottom: none;
        }
        
        .flag-badge {
            display: inline-flex;
            align-items: center;
            padding: 0.25rem 0.75rem;
            border-radius: 2rem;
            font-weight: 500;
            font-size: 0.75rem;
            margin-right: 0.5rem;
            margin-bottom: 0.5rem;
            border: 1px solid transparent;
            background-color: var(--bg-color, #f3f4f6);
            color: var(--text-color, #4b5563);
        }
        
        .flag-badge .flag-icon {
            width: 1.5rem;
            height: 1.5rem;
            border-radius: 50%;
            margin-right: 0.5rem;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: 700;
            color: white;
            background-color: var(--icon-bg, #4b5563);
        }
        
        .empty-state {
            text-align: center;
            padding: 3rem 1.5rem;
        }
        
        .empty-state-icon {
            width: 4rem;
            height: 4rem;
            border-radius: 50%;
            background-color: var(--color-primary-light);
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 1.5rem;
            color: var(--color-primary);
            font-size: 1.5rem;
        }
        
        .empty-state-title {
            font-size: 1.125rem;
            font-weight: 600;
            margin-bottom: 0.5rem;
            color: var(--color-text);
        }
        
        .empty-state-description {
            color: var(--color-text-secondary);
            max-width: 28rem;
            margin: 0 auto;
        }
        
        .button {
            display: inline-flex;
            align-items: center;
            justify-content: center;
            gap: 0.5rem;
            padding: 0.625rem 1.25rem;
            border-radius: var(--radius);
            font-weight: 500;
            font-size: 0.875rem;
            transition: var(--transition);
            cursor: pointer;
            border: none;
            outline: none;
            white-space: nowrap;
        }
        
        .button-primary {
            background-color: var(--color-primary);
            color: white;
        }
        
        .button-primary:hover {
            background-color: var(--color-primary-dark);
        }
        
        .button-outline {
            background-color: transparent;
            color: var(--color-text-secondary);
            border: 1px solid var(--color-border);
        }
        
        .button-outline:hover {
            color: var(--color-text);
            border-color: var(--color-text-secondary);
        }
        
        .button-sm {
            padding: 0.375rem 0.75rem;
            font-size: 0.75rem;
        }
        
        .button-lg {
            padding: 0.75rem 1.5rem;
            font-size: 1rem;
        }
        
        .button-icon {
            width: 1.25rem;
            height: 1.25rem;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        /* Modal styles */
        
        .modal-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.5);
            backdrop-filter: blur(4px);
            z-index: 50;
            display: flex;
            align-items: center;
            justify-content: center;
            opacity: 0;
            visibility: hidden;
            transition: opacity 0.3s ease, visibility 0.3s ease;
        }
        
        .modal-overlay.active {
            opacity: 1;
            visibility: visible;
        }
        
        .modal {
            width: 100%;
            max-width: 550px;
            background-color: var(--color-card);
            border-radius: var(--radius-lg);
            box-shadow: var(--shadow-lg);
            overflow: hidden;
            transform: translateY(20px);
            transition: transform 0.3s ease;
        }
        
        .modal-overlay.active .modal {
            transform: translateY(0);
        }
        
        .modal-header {
            padding: 1.25rem 1.5rem;
            border-bottom: 1px solid var(--color-border-light);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .modal-title {
            font-size: 1.125rem;
            font-weight: 600;
            color: var(--color-text);
        }
        
        .modal-close {
            width: 2rem;
            height: 2rem;
            border-radius: var(--radius);
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            color: var(--color-text-secondary);
            transition: var(--transition);
            background-color: transparent;
            border: none;
        }
        
        .modal-close:hover {
            background-color: var(--color-border-light);
            color: var(--color-text);
        }
        
        .modal-body {
            padding: 1.5rem;
        }
        
        .modal-footer {
            padding: 1rem 1.5rem;
            border-top: 1px solid var(--color-border-light);
            display: flex;
            justify-content: flex-end;
            gap: 0.75rem;
        }
        /* Form styles */
        
        .form-group {
            margin-bottom: 1.5rem;
        }
        
        .form-group:last-child {
            margin-bottom: 0;
        }
        
        .form-label {
            display: block;
            margin-bottom: 0.5rem;
            font-weight: 500;
            color: var(--color-text);
        }
        
        .form-label-required::after {
            content: '*';
            color: var(--color-danger);
            margin-left: 0.25rem;
        }
        
        .form-hint {
            font-size: 0.75rem;
            color: var(--color-text-light);
            margin-bottom: 0.75rem;
        }
        
        .form-control {
            width: 100%;
            padding: 0.625rem 1rem;
            border: 1px solid var(--color-border);
            border-radius: var(--radius);
            font-family: inherit;
            font-size: 0.875rem;
            color: var(--color-text);
            background-color: var(--color-white);
            transition: var(--transition);
        }
        
        .form-control:focus {
            outline: none;
            border-color: var(--color-primary);
            box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
        }
        
        .form-control::placeholder {
            color: var(--color-text-light);
        }
        
        .form-control-error {
            border-color: var(--color-danger);
        }
        
        .form-control-error:focus {
            box-shadow: 0 0 0 3px rgba(220, 38, 38, 0.1);
        }
        
        .form-error {
            font-size: 0.75rem;
            color: var(--color-danger);
            margin-top: 0.5rem;
            display: none;
        }
        
        .form-error.visible {
            display: block;
        }
        
        .input-group {
            position: relative;
            display: flex;
            align-items: center;
        }
        
        .input-group .form-control {
            flex: 1;
        }
        
        .input-group-text {
            padding: 0.625rem 1rem;
            background-color: var(--color-border-light);
            color: var(--color-text-secondary);
            font-size: 0.875rem;
            border: 1px solid var(--color-border);
            border-left: none;
            border-radius: 0 var(--radius) var(--radius) 0;
        }
        /* Flag grid styles */
        
        .flag-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(90px, 1fr));
            gap: 0.75rem;
            margin-top: 0.5rem;
        }
        
        .flag-option {
            position: relative;
        }
        
        .flag-option input {
            position: absolute;
            opacity: 0;
            width: 0;
            height: 0;
        }
        
        .flag-option-content {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            padding: 1rem 0.75rem;
            border: 1px solid var(--color-border);
            border-radius: var(--radius);
            transition: var(--transition);
            cursor: pointer;
            text-align: center;
            height: 100%;
        }
        
        .flag-icon-wrapper {
            width: 2.5rem;
            height: 2.5rem;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-bottom: 0.5rem;
            font-weight: 700;
            color: white;
        }
        
        .flag-option-name {
            font-size: 0.75rem;
            font-weight: 500;
            color: var(--color-text-secondary);
        }
        
        .flag-option input:checked+.flag-option-content {
            border-color: var(--color-primary);
            background-color: rgba(37, 99, 235, 0.05);
            box-shadow: 0 0 0 2px rgba(37, 99, 235, 0.2);
        }
        
        .flag-option input:focus+.flag-option-content {
            box-shadow: 0 0 0 2px rgba(37, 99, 235, 0.2);
        }
        
        .flag-option:hover .flag-option-content {
            border-color: var(--color-primary);
        }
        /* 替换为 */
        /* Flag list styles */
        
        .flag-list {
            display: flex;
            flex-wrap: wrap;
            gap: 1rem;
            margin-top: 0.75rem;
            margin-bottom: 0.5rem;
        }
        
        .flag-item {
            position: relative;
            margin-bottom: 0.5rem;
        }
        
        .flag-checkbox {
            position: absolute;
            opacity: 0;
            width: 0;
            height: 0;
        }
        
        .flag-box {
            display: flex;
            flex-direction: column;
            align-items: center;
            border: 2px solid var(--color-border);
            border-radius: var(--radius);
            padding: 0.75rem 0.5rem;
            width: 4.5rem;
            transition: all 0.2s;
            cursor: pointer;
            position: relative;
        }
        
        .flag-box:hover:not(.disabled) {
            border-color: var(--color-primary);
            transform: translateY(-2px);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }
        
        .flag-img {
            width: 2.5rem;
            height: 2.5rem;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: 700;
            color: white;
            margin-bottom: 0.5rem;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
        }
        
        .flag-value {
            font-size: 0.875rem;
            color: var(--color-text);
            font-weight: 600;
        }
        
        .flag-checkbox:checked+.flag-box {
            border-color: var(--color-primary);
            background-color: rgba(37, 99, 235, 0.05);
            box-shadow: 0 0 0 2px rgba(37, 99, 235, 0.2);
        }
        
        .flag-checkbox:checked+.flag-box:after {
            content: '✓';
            position: absolute;
            top: -8px;
            right: -8px;
            width: 20px;
            height: 20px;
            background-color: var(--color-primary);
            color: white;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
            font-weight: bold;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
        }
        
        .flag-box.disabled {
            cursor: not-allowed;
            opacity: 0.5;
            background-color: var(--color-border-light);
            border-color: var(--color-border);
        }
        /* 注释掉禁用旗帜的x图标样式
        .flag-box.disabled:after {
            content: '×';
            position: absolute;
            top: -8px;
            right: -8px;
            width: 20px;
            height: 20px;
            background-color: var(--color-text-secondary);
            color: white;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
            font-weight: bold;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
        }
        */
        /* Tooltip */
        
        .tooltip {
            position: relative;
            display: inline-block;
            margin-left: 0.5rem;
            color: var(--color-text-light);
        }
        
        .tooltip .tooltip-text {
            visibility: hidden;
            width: 200px;
            background-color: #333;
            color: #fff;
            text-align: center;
            border-radius: var(--radius);
            padding: 0.5rem;
            position: absolute;
            z-index: 1;
            bottom: 125%;
            left: 50%;
            transform: translateX(-50%);
            opacity: 0;
            transition: opacity 0.3s;
            font-size: 0.75rem;
            pointer-events: none;
        }
        
        .tooltip:hover .tooltip-text {
            visibility: visible;
            opacity: 1;
        }
        /* Filter styles */
        
        .filter-container {
            display: flex;
            align-items: center;
        }
        
        .filter-label {
            font-size: 0.875rem;
            color: var(--color-text-secondary);
            margin-right: 0.5rem;
        }
        
        .filter-select {
            padding: 0.375rem 0.75rem;
            border: 1px solid var(--color-border);
            border-radius: var(--radius);
            font-size: 0.875rem;
            color: var(--color-text);
            background-color: var(--color-white);
            cursor: pointer;
        }
        /* 添加状态筛选按钮样式 */
        
        .status-filter-group {
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }
        
        .status-filter-btn {
            padding: 0.5rem 1.25rem;
            border-radius: var(--radius);
            font-size: 0.875rem;
            font-weight: 500;
            cursor: pointer;
            background-color: var(--color-white);
            color: var(--color-text-secondary);
            border: 1px solid var(--color-border);
            transition: var(--transition);
        }
        
        .status-filter-btn:hover {
            color: var(--color-text);
            border-color: var(--color-text);
        }
        
        .status-filter-btn.active {
            background-color: var(--color-primary);
            color: white;
            border-color: var(--color-primary);
        }
        /* Status badges */
        
        .status-badge {
            display: inline-flex;
            align-items: center;
            padding: 0.25rem 0.75rem;
            border-radius: 2rem;
            font-weight: 500;
            font-size: 0.75rem;
        }
        
        .status-enabled {
            background-color: rgba(16, 185, 129, 0.1);
            color: var(--color-success);
        }
        
        .status-disabled {
            background-color: rgba(107, 114, 128, 0.1);
            color: var(--color-text-light);
        }
        /* Actions */
        
        .action-buttons {
            display: flex;
            gap: 0.5rem;
        }
        
        .action-btn {
            width: 2rem;
            height: 2rem;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: var(--radius);
            cursor: pointer;
            border: 1px solid var(--color-border);
            background-color: var(--color-white);
            color: var(--color-text-secondary);
            transition: var(--transition);
        }
        
        .action-btn:hover {
            background-color: var(--color-bg);
            color: var(--color-text);
        }
        
        .action-btn.edit {
            color: var(--color-primary);
        }
        
        .action-btn.enable {
            color: var(--color-success);
        }
        
        .action-btn.disable {
            color: var(--color-warning);
        }
        /* Toast styles */
        
        .toast-container {
            position: fixed;
            bottom: 1.5rem;
            right: 1.5rem;
            z-index: 100;
        }
        
        .toast {
            display: flex;
            align-items: center;
            gap: 0.75rem;
            background-color: var(--color-white);
            border-radius: var(--radius);
            box-shadow: var(--shadow-lg);
            padding: 1rem;
            min-width: 300px;
            max-width: 450px;
            margin-top: 0.75rem;
            animation: slideIn 0.3s ease forwards;
            border-left: 4px solid var(--color-primary);
        }
        
        .toast-icon {
            width: 1.5rem;
            height: 1.5rem;
            display: flex;
            align-items: center;
            justify-content: center;
            color: var(--color-primary);
        }
        
        .toast-content {
            flex: 1;
        }
        
        .toast-title {
            font-weight: 600;
            font-size: 0.875rem;
            margin-bottom: 0.25rem;
        }
        
        .toast-message {
            color: var(--color-text-secondary);
            font-size: 0.75rem;
        }
        
        .toast-close {
            color: var(--color-text-light);
            cursor: pointer;
            padding: 0.25rem;
        }
        
        .toast-close:hover {
            color: var(--color-text);
        }
        
        .toast.success {
            border-left-color: var(--color-success);
        }
        
        .toast.success .toast-icon {
            color: var(--color-success);
        }
        /* Responsive */
        
        @media (max-width: 768px) {
            .page-header {
                flex-direction: column;
                align-items: flex-start;
                gap: 1rem;
            }
            .data-table {
                min-width: 600px;
            }
            .flag-grid {
                grid-template-columns: repeat(auto-fill, minmax(80px, 1fr));
                gap: 0.5rem;
            }
        }
        /* 新增空数据状态样式 */
        
        .text-center {
            text-align: center;
        }
        
        .text-muted {
            color: var(--color-text-secondary);
        }
        
        .py-3 {
            padding-top: 1rem;
            padding-bottom: 1rem;
        }
    </style>
</head>

<body>
    <div class="container">
        <div class="page-header">
            <h1 class="page-title">
                <span class="icon"><i class="ri-coins-line"></i></span> 刷单非实物产品成本
            </h1>
            <button id="addNewBtn" class="button button-primary">
                <span class="button-icon"><i class="ri-add-line"></i></span>
                新增设置
            </button>
        </div>

        <div class="card">
            <div class="card-header">
                <div class="status-filter-group">
                    <button type="button" class="status-filter-btn" data-status="all">全部</button>
                    <button type="button" class="status-filter-btn active" data-status="enabled">启用</button>
                    <button type="button" class="status-filter-btn" data-status="disabled">停用</button>
                </div>
            </div>
            <div class="card-body">
                <div class="data-table-wrapper">
                    <table id="dataTable" class="data-table">
                        <thead>
                            <tr>
                                <th>订单旗帜</th>
                                <th>刷单非实物产品成本(元)</th>
                                <th>操作时间</th>
                                <th>操作人员</th>
                                <th>状态</th>
                                <th>操作</th>
                            </tr>
                        </thead>
                        <tbody id="tableBody">
                            <!-- 数据将通过JS动态生成 -->
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>

    <!-- 新增设置模态框 -->
    <div id="addModal" class="modal-overlay">
        <div class="modal">
            <div class="modal-header">
                <h3 class="modal-title">新增刷单成本设置</h3>
                <button class="modal-close" id="closeModalBtn">
                    <i class="ri-close-line"></i>
                </button>
            </div>
            <div class="modal-body">
                <form id="addForm">
                    <div class="form-group">
                        <label class="form-label form-label-required">订单旗帜</label>
                        <div class="form-hint">请选择需要设置刷单非实物产品成本的订单旗帜（可多选）</div>

                        <div class="flag-list" id="flagOptions">
                            <!-- 订单旗帜选项将通过JS动态生成 -->
                        </div>
                        <div id="flagError" class="form-error">请至少选择一个订单旗帜</div>
                    </div>

                    <div class="form-group">
                        <label for="brushingCost" class="form-label form-label-required">刷单非实物产品成本</label>
                        <div class="input-group">
                            <input type="number" id="brushingCost" class="form-control" placeholder="请输入刷单非实物产品成本" min="0" step="0.01">
                            <span class="input-group-text">元</span>
                        </div>
                        <div id="costError" class="form-error">请输入有效的刷单非实物产品成本（必须大于0）</div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button id="cancelBtn" class="button button-outline">取消</button>
                <button id="submitBtn" class="button button-primary">提交</button>
            </div>
        </div>
    </div>

    <!-- Toast提示 -->
    <div class="toast-container" id="toastContainer"></div>

    <script>
        // 定义订单旗帜数据（数字0-10对应不同颜色的图标）
        const flagOptions = [{
                id: 0,
                name: '旗帜 0',
                color: '#3b82f6'
            }, // 蓝色
            {
                id: 1,
                name: '旗帜 1',
                color: '#ef4444'
            }, // 红色
            {
                id: 2,
                name: '旗帜 2',
                color: '#10b981'
            }, // 绿色
            {
                id: 3,
                name: '旗帜 3',
                color: '#f59e0b'
            }, // 黄色
            {
                id: 4,
                name: '旗帜 4',
                color: '#8b5cf6'
            }, // 紫色
            {
                id: 5,
                name: '旗帜 5',
                color: '#ec4899'
            }, // 粉色
            {
                id: 6,
                name: '旗帜 6',
                color: '#6366f1'
            }, // 靛蓝色
            {
                id: 7,
                name: '旗帜 7',
                color: '#84cc16'
            }, // 亮绿色
            {
                id: 8,
                name: '旗帜 8',
                color: '#14b8a6'
            }, // 青色
            {
                id: 9,
                name: '旗帜 9',
                color: '#f97316'
            }, // 橙色
            {
                id: 10,
                name: '旗帜 10',
                color: '#64748b'
            } // 灰蓝色
        ];

        // 模拟数据 - 已有的刷单成本设置
        let brushingCostSettings = [{
            id: 1,
            flags: [1, 2],
            cost: 5.50,
            operationTime: '2023-10-15 09:30:45',
            operator: '李四',
            status: 'enabled'
        }, {
            id: 2,
            flags: [3, 4, 5],
            cost: 8.75,
            operationTime: '2023-10-14 14:20:10',
            operator: '王五',
            status: 'disabled'
        }, {
            id: 3,
            flags: [0, 10],
            cost: 3.25,
            operationTime: '2023-10-13 16:45:22',
            operator: '赵六',
            status: 'enabled'
        }];

        // 模拟当前登录用户
        const currentUser = {
            name: '张三',
            id: 'zhangsan001',
            department: '运营部'
        };

        // DOM元素
        const tableBody = document.getElementById('tableBody');
        const addModal = document.getElementById('addModal');
        const addNewBtn = document.getElementById('addNewBtn');
        const closeModalBtn = document.getElementById('closeModalBtn'); // 修改变量名称
        const cancelBtn = document.getElementById('cancelBtn');
        const submitBtn = document.getElementById('submitBtn');
        const flagOptionsElement = document.getElementById('flagOptions');
        const brushingCost = document.getElementById('brushingCost');
        const flagError = document.getElementById('flagError');
        const costError = document.getElementById('costError');
        const toastContainer = document.getElementById('toastContainer');

        // 检查旗帜是否在启用状态的规则中被使用
        function isRuleFlagUsed(flagId, currentRuleId = null) {
            return brushingCostSettings.some(setting =>
                setting.status === 'enabled' &&
                setting.flags.includes(flagId) &&
                setting.id !== currentRuleId
            );
        }

        // 初始化订单旗帜选项
        function initFlagOptions(currentRuleId = null) {
            flagOptionsElement.innerHTML = '';

            flagOptions.forEach(flag => {
                // 检查此旗帜是否已被使用
                const isUsed = isRuleFlagUsed(flag.id, currentRuleId);

                const flagItem = document.createElement('div');
                flagItem.className = 'flag-item';

                const checkbox = document.createElement('input');
                checkbox.type = 'checkbox';
                checkbox.name = 'flag';
                checkbox.id = `flag-${flag.id}`;
                checkbox.className = 'flag-checkbox';
                checkbox.value = flag.id;
                checkbox.disabled = isUsed;

                const flagBox = document.createElement('label');
                flagBox.htmlFor = `flag-${flag.id}`;
                flagBox.className = isUsed ? 'flag-box disabled' : 'flag-box';

                // 图片/颜色区域
                const flagImg = document.createElement('div');
                flagImg.className = 'flag-img';
                flagImg.style.backgroundColor = flag.color;
                flagImg.textContent = flag.id;

                // 数字值
                const flagValue = document.createElement('div');
                flagValue.className = 'flag-value';
                flagValue.textContent = flag.id;

                flagBox.appendChild(flagImg);
                flagBox.appendChild(flagValue);

                flagItem.appendChild(checkbox);
                flagItem.appendChild(flagBox);

                flagOptionsElement.appendChild(flagItem);
            });
        }

        // 更新表格数据
        function updateTable() {
            // 获取筛选状态
            const statusFilterEl = document.querySelector('.status-filter-btn.active');
            const statusFilter = statusFilterEl ? statusFilterEl.getAttribute('data-status') : 'enabled';

            // 筛选数据
            let filteredSettings = brushingCostSettings;
            if (statusFilter !== 'all') {
                filteredSettings = brushingCostSettings.filter(setting => setting.status === statusFilter);
            }

            // 始终显示表格，即使没有数据
            document.getElementById('dataTable').style.display = 'table';
            tableBody.innerHTML = '';

            // 如果没有数据，可以选择添加一个空行或直接保持表格为空
            if (filteredSettings.length === 0) {
                const emptyRow = document.createElement('tr');
                const emptyCell = document.createElement('td');
                emptyCell.setAttribute('colspan', '6');
                emptyCell.className = 'text-center text-muted py-3';
                emptyCell.textContent = '暂无数据';
                emptyRow.appendChild(emptyCell);
                tableBody.appendChild(emptyRow);
                return;
            }

            filteredSettings.forEach(setting => {
                const row = document.createElement('tr');

                // 订单旗帜单元格
                const flagsCell = document.createElement('td');
                const flagsContainer = document.createElement('div');
                flagsContainer.style.display = 'flex';
                flagsContainer.style.flexWrap = 'wrap';

                setting.flags.forEach(flagId => {
                    const flag = flagOptions.find(f => f.id === flagId);
                    if (flag) {
                        const badge = document.createElement('div');
                        badge.className = 'flag-badge';
                        badge.style.setProperty('--bg-color', `${flag.color}20`);
                        badge.style.setProperty('--text-color', flag.color);
                        badge.innerHTML = `
                            <div class="flag-icon" style="background-color: ${flag.color}">${flagId}</div>
                            ${flag.name}
                        `;
                        flagsContainer.appendChild(badge);
                    }
                });

                flagsCell.appendChild(flagsContainer);

                // 创建其他单元格
                const costCell = document.createElement('td');
                costCell.textContent = setting.cost.toFixed(2) + ' 元';

                const timeCell = document.createElement('td');
                timeCell.textContent = setting.operationTime;

                const operatorCell = document.createElement('td');
                operatorCell.textContent = setting.operator;

                // 状态单元格
                const statusCell = document.createElement('td');
                const statusBadge = document.createElement('div');
                statusBadge.className = `status-badge ${setting.status === 'enabled' ? 'status-enabled' : 'status-disabled'}`;
                statusBadge.textContent = setting.status === 'enabled' ? '启用' : '停用';
                statusCell.appendChild(statusBadge);

                // 操作单元格
                const actionsCell = document.createElement('td');
                const actionsContainer = document.createElement('div');
                actionsContainer.className = 'action-buttons';

                // 编辑按钮
                const editBtn = document.createElement('button');
                editBtn.className = 'action-btn edit';
                editBtn.innerHTML = '<i class="ri-edit-line"></i>';
                editBtn.title = '编辑';
                editBtn.setAttribute('data-id', setting.id);
                editBtn.addEventListener('click', function() {
                    editSetting(setting.id);
                });

                // 启用/停用按钮
                const toggleBtn = document.createElement('button');
                if (setting.status === 'enabled') {
                    toggleBtn.className = 'action-btn disable';
                    toggleBtn.innerHTML = '<i class="ri-pause-line"></i>';
                    toggleBtn.title = '停用';
                } else {
                    toggleBtn.className = 'action-btn enable';
                    toggleBtn.innerHTML = '<i class="ri-play-line"></i>';
                    toggleBtn.title = '启用';
                }
                toggleBtn.setAttribute('data-id', setting.id);
                toggleBtn.addEventListener('click', function() {
                    toggleStatus(setting.id);
                });

                actionsContainer.appendChild(editBtn);
                actionsContainer.appendChild(toggleBtn);
                actionsCell.appendChild(actionsContainer);

                // 添加到行
                row.appendChild(flagsCell);
                row.appendChild(costCell);
                row.appendChild(timeCell);
                row.appendChild(operatorCell);
                row.appendChild(statusCell);
                row.appendChild(actionsCell);

                // 添加到表格
                tableBody.appendChild(row);
            });
        }

        // 打开模态框
        function openModal() {
            addModal.classList.add('active');
            // 重置表单
            document.getElementById('addForm').reset();
            // 隐藏错误提示
            flagError.style.display = 'none';
            costError.style.display = 'none';
            // 确保标题为新增
            document.querySelector('.modal-title').textContent = '新增刷单非实物产品成本设置';
            // 重置编辑状态
            editingId = null;

            // 初始化旗帜选择
            initFlagOptions(null);
        }

        // 显示Toast提示
        function showToast(type, title, message) {
            const toast = document.createElement('div');
            toast.className = `toast ${type}`;
            toast.innerHTML = `
                <div class="toast-icon">
                    <i class="ri-check-line"></i>
                </div>
                <div class="toast-content">
                    <div class="toast-title">${title}</div>
                    <div class="toast-message">${message}</div>
                </div>
                <div class="toast-close">
                    <i class="ri-close-line"></i>
                </div>
            `;

            toastContainer.appendChild(toast);

            // 自动关闭
            setTimeout(() => {
                toast.style.opacity = '0';
                setTimeout(() => {
                    toast.remove();
                }, 300);
            }, 3000);

            // 点击关闭
            toast.querySelector('.toast-close').addEventListener('click', () => {
                toast.style.opacity = '0';
                setTimeout(() => {
                    toast.remove();
                }, 300);
            });
        }

        // 验证表单
        function validateForm() {
            let isValid = true;

            // 验证订单旗帜
            const selectedFlags = document.querySelectorAll('input[name="flag"]:checked');
            if (selectedFlags.length === 0) {
                flagError.style.display = 'block';
                isValid = false;
            } else {
                flagError.style.display = 'none';
            }

            // 验证刷单成本
            const cost = parseFloat(brushingCost.value);
            if (isNaN(cost) || cost <= 0 || brushingCost.value === '') {
                costError.style.display = 'block';
                brushingCost.classList.add('form-control-error');
                isValid = false;
            } else {
                costError.style.display = 'none';
                brushingCost.classList.remove('form-control-error');
            }

            return isValid;
        }

        // 提交表单
        function submitForm() {
            if (!validateForm()) {
                return;
            }

            // 获取选中的订单旗帜
            const selectedFlags = Array.from(document.querySelectorAll('input[name="flag"]:checked'))
                .map(input => parseInt(input.value, 10));

            // 获取刷单成本
            const cost = parseFloat(brushingCost.value);

            // 获取当前时间
            const operationTime = getCurrentDateTime();

            if (editingId !== null) {
                // 编辑现有设置
                const index = brushingCostSettings.findIndex(setting => setting.id === editingId);
                if (index !== -1) {
                    brushingCostSettings[index].flags = selectedFlags;
                    brushingCostSettings[index].cost = cost;
                    brushingCostSettings[index].operationTime = operationTime;
                    brushingCostSettings[index].operator = currentUser.name;

                    // 保持原有状态不变

                    showToast('success', '操作成功', '刷单成本设置已成功更新');
                }
                // 重置编辑状态
                editingId = null;
            } else {
                // 添加新设置
                const newSetting = {
                    id: Date.now(),
                    flags: selectedFlags,
                    cost: cost,
                    operationTime: operationTime,
                    operator: currentUser.name,
                    status: 'enabled' // 新设置默认启用
                };

                // 添加到设置列表
                brushingCostSettings.push(newSetting);

                // 显示成功提示
                showToast('success', '操作成功', '刷单成本设置已成功保存');
            }

            // 关闭模态框
            closeModal();

            // 更新表格
            updateTable();
        }

        // 事件监听
        addNewBtn.addEventListener('click', openModal);
        closeModalBtn.addEventListener('click', closeModal);
        cancelBtn.addEventListener('click', closeModal);
        submitBtn.addEventListener('click', submitForm);

        // 状态筛选变化事件
        const statusFilterEls = document.querySelectorAll('.status-filter-btn');
        statusFilterEls.forEach(el => {
            el.addEventListener('click', function() {
                statusFilterEls.forEach(btn => btn.classList.remove('active'));
                el.classList.add('active');
                updateTable();
            });
        });

        // 点击模态框外部关闭
        addModal.addEventListener('click', function(e) {
            if (e.target === addModal) {
                closeModal();
            }
        });

        // 切换状态功能
        function toggleStatus(id) {
            const index = brushingCostSettings.findIndex(setting => setting.id === id);
            if (index === -1) return;

            const setting = brushingCostSettings[index];
            const newStatus = setting.status === 'enabled' ? 'disabled' : 'enabled';
            setting.status = newStatus;

            // 更新操作时间和操作人
            setting.operationTime = getCurrentDateTime();
            setting.operator = currentUser.name;

            // 更新表格
            updateTable();

            // 显示提示
            const actionText = newStatus === 'enabled' ? '启用' : '停用';
            showToast('success', '状态已更新', `刷单成本设置已${actionText}`);
        }

        // 编辑设置
        let editingId = null;

        function editSetting(id) {
            const setting = brushingCostSettings.find(setting => setting.id === id);
            if (!setting) return;

            // 设置编辑状态
            editingId = id;

            // 打开模态框
            addModal.classList.add('active');

            // 修改标题
            document.querySelector('.modal-title').textContent = '编辑刷单成本设置';

            // 填充表单数据
            brushingCost.value = setting.cost;

            // 初始化旗帜选择，传递当前规则ID
            initFlagOptions(id);

            // 选中对应的旗帜
            const checkboxes = document.querySelectorAll('input[name="flag"]');
            checkboxes.forEach(checkbox => {
                checkbox.checked = setting.flags.includes(parseInt(checkbox.value, 10));
            });

            // 隐藏错误提示
            flagError.style.display = 'none';
            costError.style.display = 'none';
        }

        // 获取当前时间格式化字符串
        function getCurrentDateTime() {
            const now = new Date();
            return `${now.getFullYear()}-${String(now.getMonth() + 1).padStart(2, '0')}-${String(now.getDate()).padStart(2, '0')} ${String(now.getHours()).padStart(2, '0')}:${String(now.getMinutes()).padStart(2, '0')}:${String(now.getSeconds()).padStart(2, '0')}`;
        }

        // 重置模态框
        function resetModal() {
            document.getElementById('addForm').reset();
            flagError.style.display = 'none';
            costError.style.display = 'none';
            editingId = null;
            document.querySelector('.modal-title').textContent = '新增刷单成本设置';
            // 初始化旗帜选择
            initFlagOptions(null);
        }

        // 修改关闭模态框函数，加入重置逻辑
        function closeModal() {
            addModal.classList.remove('active');
            resetModal();
        }

        // 初始化页面
        updateTable();

        // 输入框输入时移除错误状态
        brushingCost.addEventListener('input', function() {
            this.classList.remove('form-control-error');
            costError.style.display = 'none';
        });
    </script>
</body>

</html>