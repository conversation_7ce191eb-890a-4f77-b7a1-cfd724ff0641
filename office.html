<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>办公室成本分摊系统</title>
    <style>
         :root {
            --primary-color: #2c3e50;
            --secondary-color: #3498db;
            --accent-color: #1abc9c;
            --light-bg: #f5f7fa;
            --border-color: #e2e8f0;
            --text-color: #333;
            --text-light: #718096;
            --shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
            --card-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
        
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: 'Segoe UI', 'Microsoft YaHei', sans-serif;
        }
        
        body {
            background-color: var(--light-bg);
            color: var(--text-color);
        }
        
        a {
            text-decoration: none;
            color: inherit;
        }
        
        ul {
            list-style: none;
        }
        
        .layout {
            display: flex;
            min-height: 100vh;
        }
        
        .header {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            height: 60px;
            background-color: var(--primary-color);
            color: white;
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0 20px;
            z-index: 100;
            box-shadow: var(--shadow);
        }
        
        .header-title {
            font-size: 18px;
            font-weight: 500;
        }
        
        .header-actions {
            display: flex;
            align-items: center;
        }
        
        .header-actions button {
            background-color: var(--secondary-color);
            color: white;
            border: none;
            padding: 6px 12px;
            border-radius: 4px;
            margin-left: 10px;
            cursor: pointer;
            font-size: 14px;
        }
        
        .sidebar {
            position: fixed;
            top: 60px;
            left: 0;
            bottom: 0;
            width: 220px;
            background-color: white;
            border-right: 1px solid var(--border-color);
            padding: 20px 0;
            overflow-y: auto;
            z-index: 90;
        }
        
        .menu-item {
            padding: 12px 20px;
            display: flex;
            align-items: center;
            color: var(--text-color);
            text-decoration: none;
            transition: all 0.3s;
            border-left: 3px solid transparent;
            cursor: pointer;
        }
        
        .menu-item.active {
            background-color: rgba(52, 152, 219, 0.1);
            color: var(--secondary-color);
            border-left-color: var(--secondary-color);
        }
        
        .menu-item:hover {
            background-color: rgba(52, 152, 219, 0.05);
        }
        
        .menu-icon {
            margin-right: 10px;
            width: 20px;
            height: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .content {
            margin-left: 220px;
            margin-top: 60px;
            padding: 20px;
            width: calc(100% - 220px);
        }
        
        .page-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
        }
        
        .page-title {
            font-size: 22px;
            font-weight: 500;
        }
        
        .filter-bar {
            background-color: white;
            padding: 15px;
            border-radius: 6px;
            margin-bottom: 20px;
            display: flex;
            flex-wrap: wrap;
            gap: 15px;
            align-items: center;
            box-shadow: var(--shadow);
        }
        
        .filter-group {
            display: flex;
            align-items: center;
        }
        
        .filter-label {
            font-size: 14px;
            margin-right: 8px;
            color: var(--text-light);
        }
        
        .filter-input {
            padding: 8px 12px;
            border: 1px solid var(--border-color);
            border-radius: 4px;
            font-size: 14px;
            min-width: 120px;
        }
        
        .filter-actions {
            margin-left: auto;
        }
        
        .btn {
            padding: 8px 16px;
            border-radius: 4px;
            font-size: 14px;
            cursor: pointer;
            border: none;
            transition: all 0.3s;
        }
        
        .btn-primary {
            background-color: var(--secondary-color);
            color: white;
        }
        
        .btn-primary:hover {
            background-color: #2980b9;
        }
        
        .btn-outline {
            background-color: transparent;
            border: 1px solid var(--border-color);
            color: var(--text-color);
        }
        
        .btn-outline:hover {
            background-color: var(--light-bg);
        }
        
        .card-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 20px;
        }
        
        .card {
            background-color: white;
            border-radius: 6px;
            padding: 20px;
            box-shadow: var(--card-shadow);
        }
        
        .card-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
        }
        
        .card-title {
            font-size: 16px;
            font-weight: 500;
        }
        
        .card-badge {
            background-color: rgba(26, 188, 156, 0.1);
            color: var(--accent-color);
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
        }
        
        .card-content {
            color: var(--text-light);
        }
        
        .data-item {
            display: flex;
            justify-content: space-between;
            margin-bottom: 8px;
        }
        
        .data-label {
            font-size: 14px;
        }
        
        .data-value {
            font-weight: 500;
        }
        
        .table-container {
            background-color: white;
            border-radius: 6px;
            overflow: hidden;
            box-shadow: var(--shadow);
        }
        
        table {
            width: 100%;
            border-collapse: collapse;
        }
        
        th,
        td {
            padding: 12px 15px;
            text-align: left;
            border-bottom: 1px solid var(--border-color);
        }
        
        th {
            background-color: var(--light-bg);
            font-weight: 500;
            color: var(--text-light);
            font-size: 14px;
        }
        
        td {
            font-size: 14px;
        }
        
        tr:hover {
            background-color: rgba(245, 247, 250, 0.5);
        }
        
        .table-footer {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 15px;
            background-color: white;
            border-top: 1px solid var(--border-color);
        }
        
        .pagination {
            display: flex;
            align-items: center;
        }
        
        .page-btn {
            width: 32px;
            height: 32px;
            display: flex;
            align-items: center;
            justify-content: center;
            border: 1px solid var(--border-color);
            background-color: white;
            margin: 0 5px;
            border-radius: 4px;
            cursor: pointer;
            transition: all 0.3s;
        }
        
        .page-btn:hover {
            background-color: var(--light-bg);
        }
        
        .page-btn.active {
            background-color: var(--secondary-color);
            color: white;
            border-color: var(--secondary-color);
        }
        
        .tab-container {
            margin-bottom: 20px;
        }
        
        .tabs {
            display: flex;
            border-bottom: 1px solid var(--border-color);
            margin-bottom: 20px;
        }
        
        .tab {
            padding: 10px 20px;
            cursor: pointer;
            border-bottom: 2px solid transparent;
            transition: all 0.3s;
        }
        
        .tab.active {
            border-bottom-color: var(--secondary-color);
            color: var(--secondary-color);
        }
        
        .tab:hover {
            color: var(--secondary-color);
        }
        
        .summary-row {
            background-color: rgba(52, 152, 219, 0.05);
            font-weight: 500;
        }
        
        .trend-indicator {
            display: flex;
            align-items: center;
        }
        
        .trend-up {
            color: #e74c3c;
        }
        
        .trend-down {
            color: #2ecc71;
        }
        
        .cost-high {
            color: #e74c3c;
        }
        
        .cost-medium {
            color: #f39c12;
        }
        
        .cost-low {
            color: #2ecc71;
        }
        
        .module {
            display: none;
        }
        
        .module.active {
            display: block;
        }
        
        .tab-content {
            display: none;
        }
        
        .tab-content.active {
            display: block;
        }
        /* 弹窗样式 */
        
        .modal {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-color: rgba(0, 0, 0, 0.5);
            z-index: 1000;
            align-items: center;
            justify-content: center;
        }
        
        .modal.active {
            display: flex;
        }
        
        .modal-content {
            background-color: white;
            border-radius: 6px;
            width: 500px;
            max-width: 90%;
            max-height: 90vh;
            overflow-y: auto;
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
        }
        
        .modal-header {
            padding: 15px 20px;
            border-bottom: 1px solid var(--border-color);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .modal-title {
            font-size: 18px;
            font-weight: 500;
        }
        
        .modal-close {
            background: none;
            border: none;
            font-size: 20px;
            cursor: pointer;
            color: var(--text-light);
        }
        
        .modal-body {
            padding: 20px;
        }
        
        .modal-footer {
            padding: 15px 20px;
            border-top: 1px solid var(--border-color);
            display: flex;
            justify-content: flex-end;
            gap: 10px;
        }
        
        .form-group {
            margin-bottom: 15px;
        }
        
        .form-label {
            display: block;
            margin-bottom: 5px;
            font-size: 14px;
            color: var(--text-light);
        }
        
        .form-input {
            width: 100%;
            padding: 8px 12px;
            border: 1px solid var(--border-color);
            border-radius: 4px;
            font-size: 14px;
        }
        
        .form-select {
            width: 100%;
            padding: 8px 12px;
            border: 1px solid var(--border-color);
            border-radius: 4px;
            font-size: 14px;
            background-color: white;
        }
    </style>
</head>

<body>
    <!-- 顶部导航 -->
    <header class="header">
        <div class="header-title">办公室成本分摊系统</div>
        <div class="header-actions">
            <button id="exportBtn">导出数据</button>
            <button id="helpBtn">帮助</button>
        </div>
    </header>

    <div class="layout">
        <!-- 侧边菜单 -->
        <aside class="sidebar">
            <div class="menu-item" onclick="switchModule('overview')">
                <span class="menu-icon">📊</span>
                <span>总览</span>
            </div>
            <div class="menu-item active" onclick="switchModule('workstation')">
                <span class="menu-icon">💺</span>
                <span>工位分摊</span>
            </div>
            <div class="menu-item" onclick="switchModule('studio')">
                <span class="menu-icon">🎬</span>
                <span>直播间分摊</span>
            </div>
            <div class="menu-item" onclick="switchModule('electricity')">
                <span class="menu-icon">⚡</span>
                <span>电费分摊</span>
            </div>
            <div class="menu-item">
                <span class="menu-icon">📝</span>
                <span>报表管理</span>
            </div>
            <div class="menu-item">
                <span class="menu-icon">⚙️</span>
                <span>系统设置</span>
            </div>
        </aside>

        <!-- 主内容区 -->
        <main class="content">
            <!-- 工位分摊页面 -->
            <div id="workstation" class="module active">
                <div class="page-header">
                    <h1 class="page-title">工位分摊</h1>
                    <button class="btn btn-primary" id="addWorkstationBtn">新增记录</button>
                </div>

                <!-- 筛选栏 -->
                <div class="filter-bar">
                    <div class="filter-group">
                        <span class="filter-label">区域</span>
                        <select class="filter-input" id="area-filter">
              <option value="">全部区域</option>
              <option value="A">A区</option>
              <option value="B">B区</option>
              <option value="C">C区</option>
            </select>
                    </div>
                    <div class="filter-group">
                        <span class="filter-label">团队</span>
                        <select class="filter-input" id="team-filter">
              <option value="">全部团队</option>
              <option value="product">产品团队</option>
              <option value="dev">研发团队</option>
              <option value="design">设计团队</option>
              <option value="operation">运营团队</option>
              <option value="market">市场团队</option>
              <option value="admin">行政团队</option>
              <option value="finance">财务团队</option>
              <option value="hr">人力资源</option>
            </select>
                    </div>
                    <div class="filter-group">
                        <span class="filter-label">时间范围</span>
                        <input type="month" class="filter-input" id="date-filter" value="2025-04">
                    </div>
                    <div class="filter-actions">
                        <button class="btn btn-primary" id="searchBtn">查询</button>
                        <button class="btn btn-outline" id="resetBtn">重置</button>
                    </div>
                </div>

                <!-- 区域卡片 -->
                <div class="card-grid">
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">A区工位概览</h3>
                            <span class="card-badge">使用率 85%</span>
                        </div>
                        <div class="card-content">
                            <div class="data-item">
                                <span class="data-label">工位单价</span>
                                <span class="data-value">¥50.00/位</span>
                            </div>
                            <div class="data-item">
                                <span class="data-label">总工位数</span>
                                <span class="data-value">40</span>
                            </div>
                            <div class="data-item">
                                <span class="data-label">已使用工位</span>
                                <span class="data-value">34</span>
                            </div>
                            <div class="data-item">
                                <span class="data-label">未使用工位</span>
                                <span class="data-value">6</span>
                            </div>
                            <div class="data-item">
                                <span class="data-label">未使用工位费用</span>
                                <span class="data-value">¥300.00</span>
                            </div>
                            <div class="data-item">
                                <span class="data-label">总费用</span>
                                <span class="data-value">¥2,000.00</span>
                            </div>
                        </div>
                    </div>

                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">B区工位概览</h3>
                            <span class="card-badge">使用率 70%</span>
                        </div>
                        <div class="card-content">
                            <div class="data-item">
                                <span class="data-label">工位单价</span>
                                <span class="data-value">¥60.00/位</span>
                            </div>
                            <div class="data-item">
                                <span class="data-label">总工位数</span>
                                <span class="data-value">30</span>
                            </div>
                            <div class="data-item">
                                <span class="data-label">已使用工位</span>
                                <span class="data-value">21</span>
                            </div>
                            <div class="data-item">
                                <span class="data-label">未使用工位</span>
                                <span class="data-value">9</span>
                            </div>
                            <div class="data-item">
                                <span class="data-label">未使用工位费用</span>
                                <span class="data-value">¥450.00</span>
                            </div>
                            <div class="data-item">
                                <span class="data-label">总费用</span>
                                <span class="data-value">¥1,800.00</span>
                            </div>
                        </div>
                    </div>

                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">C区工位概览</h3>
                            <span class="card-badge">使用率 90%</span>
                        </div>
                        <div class="card-content">
                            <div class="data-item">
                                <span class="data-label">工位单价</span>
                                <span class="data-value">¥45.00/位</span>
                            </div>
                            <div class="data-item">
                                <span class="data-label">总工位数</span>
                                <span class="data-value">20</span>
                            </div>
                            <div class="data-item">
                                <span class="data-label">已使用工位</span>
                                <span class="data-value">18</span>
                            </div>
                            <div class="data-item">
                                <span class="data-label">未使用工位</span>
                                <span class="data-value">2</span>
                            </div>
                            <div class="data-item">
                                <span class="data-label">未使用工位费用</span>
                                <span class="data-value">¥90.00</span>
                            </div>
                            <div class="data-item">
                                <span class="data-label">总费用</span>
                                <span class="data-value">¥900.00</span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 团队分摊表格 -->
                <div class="tab-container">
                    <div class="tabs">
                        <div class="tab active" id="tab-A" onclick="showTab('A')">A区分摊明细</div>
                        <div class="tab" id="tab-B" onclick="showTab('B')">B区分摊明细</div>
                        <div class="tab" id="tab-C" onclick="showTab('C')">C区分摊明细</div>
                    </div>

                    <div id="content-A" class="tab-content active">
                        <div class="table-container">
                            <table>
                                <thead>
                                    <tr>
                                        <th>团队名称</th>
                                        <th>人数</th>
                                        <th>使用工位数</th>
                                        <th>已使用费用</th>
                                        <th>未使用分摊比例</th>
                                        <th>未使用分摊费用</th>
                                        <th>总费用</th>
                                        <th>操作</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr>
                                        <td>产品团队</td>
                                        <td>12</td>
                                        <td>10</td>
                                        <td>¥500.00</td>
                                        <td>35%</td>
                                        <td>¥105.00</td>
                                        <td>¥605.00</td>
                                        <td>
                                            <button class="btn btn-outline" onclick="editRecord('A', 'product')">编辑</button>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td>研发团队</td>
                                        <td>15</td>
                                        <td>14</td>
                                        <td>¥700.00</td>
                                        <td>45%</td>
                                        <td>¥135.00</td>
                                        <td>¥835.00</td>
                                        <td>
                                            <button class="btn btn-outline" onclick="editRecord('A', 'dev')">编辑</button>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td>设计团队</td>
                                        <td>8</td>
                                        <td>6</td>
                                        <td>¥300.00</td>
                                        <td>20%</td>
                                        <td>¥60.00</td>
                                        <td>¥360.00</td>
                                        <td>
                                            <button class="btn btn-outline" onclick="editRecord('A', 'design')">编辑</button>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td>运营团队</td>
                                        <td>4</td>
                                        <td>4</td>
                                        <td>¥200.00</td>
                                        <td>0%</td>
                                        <td>¥0.00</td>
                                        <td>¥200.00</td>
                                        <td>
                                            <button class="btn btn-outline" onclick="editRecord('A', 'operation')">编辑</button>
                                        </td>
                                    </tr>
                                    <tr class="summary-row">
                                        <td>合计</td>
                                        <td>39</td>
                                        <td>34</td>
                                        <td>¥1,700.00</td>
                                        <td>100%</td>
                                        <td>¥300.00</td>
                                        <td>¥2,000.00</td>
                                        <td></td>
                                    </tr>
                                </tbody>
                            </table>
                            <div class="table-footer">
                                <div>共 4 条记录</div>
                                <div class="pagination">
                                    <button class="page-btn">«</button>
                                    <button class="page-btn active">1</button>
                                    <button class="page-btn">»</button>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- B区分摊明细 -->
                    <div id="content-B" class="tab-content">
                        <div class="table-container">
                            <table>
                                <thead>
                                    <tr>
                                        <th>团队名称</th>
                                        <th>人数</th>
                                        <th>使用工位数</th>
                                        <th>已使用费用</th>
                                        <th>未使用分摊比例</th>
                                        <th>未使用分摊费用</th>
                                        <th>总费用</th>
                                        <th>操作</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr>
                                        <td>产品团队</td>
                                        <td>8</td>
                                        <td>7</td>
                                        <td>¥420.00</td>
                                        <td>30%</td>
                                        <td>¥135.00</td>
                                        <td>¥555.00</td>
                                        <td>
                                            <button class="btn btn-outline" onclick="editRecord('B', 'product')">编辑</button>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td>研发团队</td>
                                        <td>10</td>
                                        <td>9</td>
                                        <td>¥540.00</td>
                                        <td>40%</td>
                                        <td>¥180.00</td>
                                        <td>¥720.00</td>
                                        <td>
                                            <button class="btn btn-outline" onclick="editRecord('B', 'dev')">编辑</button>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td>市场团队</td>
                                        <td>6</td>
                                        <td>5</td>
                                        <td>¥300.00</td>
                                        <td>30%</td>
                                        <td>¥135.00</td>
                                        <td>¥435.00</td>
                                        <td>
                                            <button class="btn btn-outline" onclick="editRecord('B', 'market')">编辑</button>
                                        </td>
                                    </tr>
                                    <tr class="summary-row">
                                        <td>合计</td>
                                        <td>24</td>
                                        <td>21</td>
                                        <td>¥1,260.00</td>
                                        <td>100%</td>
                                        <td>¥450.00</td>
                                        <td>¥1,710.00</td>
                                        <td></td>
                                    </tr>
                                </tbody>
                            </table>
                            <div class="table-footer">
                                <div>共 3 条记录</div>
                                <div class="pagination">
                                    <button class="page-btn">«</button>
                                    <button class="page-btn active">1</button>
                                    <button class="page-btn">»</button>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- C区分摊明细 -->
                    <div id="content-C" class="tab-content">
                        <div class="table-container">
                            <table>
                                <thead>
                                    <tr>
                                        <th>团队名称</th>
                                        <th>人数</th>
                                        <th>使用工位数</th>
                                        <th>已使用费用</th>
                                        <th>未使用分摊比例</th>
                                        <th>未使用分摊费用</th>
                                        <th>总费用</th>
                                        <th>操作</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr>
                                        <td>行政团队</td>
                                        <td>10</td>
                                        <td>10</td>
                                        <td>¥450.00</td>
                                        <td>55%</td>
                                        <td>¥49.50</td>
                                        <td>¥499.50</td>
                                        <td>
                                            <button class="btn btn-outline" onclick="editRecord('C', 'admin')">编辑</button>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td>财务团队</td>
                                        <td>5</td>
                                        <td>5</td>
                                        <td>¥225.00</td>
                                        <td>30%</td>
                                        <td>¥27.00</td>
                                        <td>¥252.00</td>
                                        <td>
                                            <button class="btn btn-outline" onclick="editRecord('C', 'finance')">编辑</button>
                                        </td>
                                        <button class="btn btn-outline" onclick="editRecord('C', 'finance')">编辑</button>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td>人力资源</td>
                                        <td>3</td>
                                        <td>3</td>
                                        <td>¥135.00</td>
                                        <td>15%</td>
                                        <td>¥13.50</td>
                                        <td>¥148.50</td>
                                        <td>
                                            <button class="btn btn-outline" onclick="editRecord('C', 'hr')">编辑</button>
                                        </td>
                                    </tr>
                                    <tr class="summary-row">
                                        <td>合计</td>
                                        <td>18</td>
                                        <td>18</td>
                                        <td>¥810.00</td>
                                        <td>100%</td>
                                        <td>¥90.00</td>
                                        <td>¥900.00</td>
                                        <td></td>
                                    </tr>
                                </tbody>
                            </table>
                            <div class="table-footer">
                                <div>共 3 条记录</div>
                                <div class="pagination">
                                    <button class="page-btn">«</button>
                                    <button class="page-btn active">1</button>
                                    <button class="page-btn">»</button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </main>
    </div>

    <!-- 新增工位分摊记录弹窗 -->
    <div class="modal" id="addWorkstationModal">
        <div class="modal-content">
            <div class="modal-header">
                <h3 class="modal-title">新增工位分摊记录</h3>
                <button class="modal-close" id="addModalClose">&times;</button>
            </div>
            <div class="modal-body">
                <div class="form-group">
                    <label class="form-label">区域</label>
                    <select class="form-select" id="add-area">
                            <option value="A">A区</option>
                            <option value="B">B区</option>
                            <option value="C">C区</option>
                          </select>
                </div>
                <div class="form-group">
                    <label class="form-label">团队</label>
                    <select class="form-select" id="add-team">
                            <option value="product">产品团队</option>
                            <option value="dev">研发团队</option>
                            <option value="design">设计团队</option>
                            <option value="operation">运营团队</option>
                            <option value="market">市场团队</option>
                            <option value="admin">行政团队</option>
                            <option value="finance">财务团队</option>
                            <option value="hr">人力资源</option>
                          </select>
                </div>
                <div class="form-group">
                    <label class="form-label">人数</label>
                    <input type="number" class="form-input" id="add-people" min="1" value="1">
                </div>
                <div class="form-group">
                    <label class="form-label">使用工位数</label>
                    <input type="number" class="form-input" id="add-workstations" min="0" value="1">
                </div>
                <div class="form-group">
                    <label class="form-label">未使用分摊比例 (%)</label>
                    <input type="number" class="form-input" id="add-ratio" min="0" max="100" value="0">
                </div>
                <div class="form-group">
                    <label class="form-label">时间</label>
                    <input type="month" class="form-input" id="add-date" value="2025-04">
                </div>
            </div>
            <div class="modal-footer">
                <button class="btn btn-outline" id="addModalCancel">取消</button>
                <button class="btn btn-primary" id="addModalSubmit">提交</button>
            </div>
        </div>
    </div>

    <!-- 编辑工位分摊记录弹窗 -->
    <div class="modal" id="editWorkstationModal">
        <div class="modal-content">
            <div class="modal-header">
                <h3 class="modal-title">编辑工位分摊记录</h3>
                <button class="modal-close" id="editModalClose">&times;</button>
            </div>
            <div class="modal-body">
                <div class="form-group">
                    <label class="form-label">区域</label>
                    <select class="form-select" id="edit-area" disabled>
                            <option value="A">A区</option>
                            <option value="B">B区</option>
                            <option value="C">C区</option>
                          </select>
                </div>
                <div class="form-group">
                    <label class="form-label">团队</label>
                    <select class="form-select" id="edit-team" disabled>
                            <option value="product">产品团队</option>
                            <option value="dev">研发团队</option>
                            <option value="design">设计团队</option>
                            <option value="operation">运营团队</option>
                            <option value="market">市场团队</option>
                            <option value="admin">行政团队</option>
                            <option value="finance">财务团队</option>
                            <option value="hr">人力资源</option>
                          </select>
                </div>
                <div class="form-group">
                    <label class="form-label">人数</label>
                    <input type="number" class="form-input" id="edit-people" min="1">
                </div>
                <div class="form-group">
                    <label class="form-label">使用工位数</label>
                    <input type="number" class="form-input" id="edit-workstations" min="0">
                </div>
                <div class="form-group">
                    <label class="form-label">未使用分摊比例 (%)</label>
                    <input type="number" class="form-input" id="edit-ratio" min="0" max="100">
                </div>
                <div class="form-group">
                    <label class="form-label">时间</label>
                    <input type="month" class="form-input" id="edit-date" value="2025-04">
                </div>
            </div>
            <div class="modal-footer">
                <button class="btn btn-outline" id="editModalCancel">取消</button>
                <button class="btn btn-primary" id="editModalSubmit">保存</button>
            </div>
        </div>
    </div>

    <!-- 帮助弹窗 -->
    <div class="modal" id="helpModal">
        <div class="modal-content">
            <div class="modal-header">
                <h3 class="modal-title">使用帮助</h3>
                <button class="modal-close" id="helpModalClose">&times;</button>
            </div>
            <div class="modal-body">
                <h4>工位分摊系统使用说明</h4>
                <p>本系统用于管理和计算办公室各区域工位的成本分摊情况。</p>
                <h5>基本概念</h5>
                <ul style="list-style-type: disc; padding-left: 20px; margin-bottom: 15px;">
                    <li>工位单价：每个区域的工位每月的基准费用</li>
                    <li>已使用费用：团队实际使用的工位产生的费用</li>
                    <li>未使用分摊比例：团队需要承担的未使用工位费用的比例</li>
                    <li>未使用分摊费用：根据分摊比例计算的团队需要承担的未使用工位费用</li>
                    <li>总费用：已使用费用 + 未使用分摊费用</li>
                </ul>
                <h5>操作指南</h5>
                <ul style="list-style-type: disc; padding-left: 20px;">
                    <li>筛选：可以按区域、团队和时间筛选数据</li>
                    <li>新增记录：点击右上角"新增记录"按钮</li>
                    <li>编辑记录：在表格中点击对应记录的"编辑"按钮</li>
                    <li>导出数据：点击顶部导航栏的"导出数据"按钮</li>
                </ul>
            </div>
            <div class="modal-footer">
                <button class="btn btn-primary" id="helpModalOk">我知道了</button>
            </div>
        </div>
    </div>

    <script>
        // 模块切换
        function switchModule(moduleId) {
            // 隐藏所有模块
            document.querySelectorAll('.module').forEach(module => {
                module.classList.remove('active');
            });

            // 显示选中的模块
            const selectedModule = document.getElementById(moduleId);
            if (selectedModule) {
                selectedModule.classList.add('active');
            }

            // 更新菜单项状态
            document.querySelectorAll('.menu-item').forEach(item => {
                item.classList.remove('active');
            });

            // 激活当前菜单项
            event.currentTarget.classList.add('active');
        }

        // 标签页切换
        function showTab(tabId) {
            // 隐藏所有标签内容
            document.querySelectorAll('.tab-content').forEach(content => {
                content.classList.remove('active');
            });

            // 显示选中的标签内容
            const selectedContent = document.getElementById('content-' + tabId);
            if (selectedContent) {
                selectedContent.classList.add('active');
            }

            // 更新标签状态
            document.querySelectorAll('.tab').forEach(tab => {
                tab.classList.remove('active');
            });

            // 激活当前标签
            document.getElementById('tab-' + tabId).classList.add('active');
        }

        // 编辑记录
        function editRecord(area, team) {
            // 根据区域和团队获取数据
            let people = 0;
            let workstations = 0;
            let ratio = 0;

            // 模拟数据获取，实际应用中应该从后端获取
            if (area === 'A') {
                if (team === 'product') {
                    people = 12;
                    workstations = 10;
                    ratio = 35;
                } else if (team === 'dev') {
                    people = 15;
                    workstations = 14;
                    ratio = 45;
                } else if (team === 'design') {
                    people = 8;
                    workstations = 6;
                    ratio = 20;
                } else if (team === 'operation') {
                    people = 4;
                    workstations = 4;
                    ratio = 0;
                }
            } else if (area === 'B') {
                if (team === 'product') {
                    people = 8;
                    workstations = 7;
                    ratio = 30;
                } else if (team === 'dev') {
                    people = 10;
                    workstations = 9;
                    ratio = 40;
                } else if (team === 'market') {
                    people = 6;
                    workstations = 5;
                    ratio = 30;
                }
            } else if (area === 'C') {
                if (team === 'admin') {
                    people = 10;
                    workstations = 10;
                    ratio = 55;
                } else if (team === 'finance') {
                    people = 5;
                    workstations = 5;
                    ratio = 30;
                } else if (team === 'hr') {
                    people = 3;
                    workstations = 3;
                    ratio = 15;
                }
            }

            // 填充表单数据
            document.getElementById('edit-area').value = area;
            document.getElementById('edit-team').value = team;
            document.getElementById('edit-people').value = people;
            document.getElementById('edit-workstations').value = workstations;
            document.getElementById('edit-ratio').value = ratio;

            // 显示编辑弹窗
            document.getElementById('editWorkstationModal').classList.add('active');
        }

        // 页面加载完成后执行
        document.addEventListener('DOMContentLoaded', function() {
            // 新增记录按钮点击事件
            document.getElementById('addWorkstationBtn').addEventListener('click', function() {
                document.getElementById('addWorkstationModal').classList.add('active');
            });

            // 新增弹窗关闭按钮
            document.getElementById('addModalClose').addEventListener('click', function() {
                document.getElementById('addWorkstationModal').classList.remove('active');
            });

            // 新增弹窗取消按钮
            document.getElementById('addModalCancel').addEventListener('click', function() {
                document.getElementById('addWorkstationModal').classList.remove('active');
            });

            // 新增弹窗提交按钮
            document.getElementById('addModalSubmit').addEventListener('click', function() {
                // 获取表单数据
                const area = document.getElementById('add-area').value;
                const team = document.getElementById('add-team').value;
                const people = document.getElementById('add-people').value;
                const workstations = document.getElementById('add-workstations').value;
                const ratio = document.getElementById('add-ratio').value;
                const date = document.getElementById('add-date').value;

                // 表单验证
                if (!area || !team || !people || people < 1 || workstations < 0 || ratio < 0 || ratio > 100) {
                    alert('请填写正确的表单数据');
                    return;
                }

                // 模拟提交数据，实际应用中应该发送到后端
                alert('数据提交成功！');

                // 关闭弹窗
                document.getElementById('addWorkstationModal').classList.remove('active');

                // 重置表单
                document.getElementById('add-people').value = 1;
                document.getElementById('add-workstations').value = 1;
                document.getElementById('add-ratio').value = 0;
            });

            // 编辑弹窗关闭按钮
            document.getElementById('editModalClose').addEventListener('click', function() {
                document.getElementById('editWorkstationModal').classList.remove('active');
            });

            // 编辑弹窗取消按钮
            document.getElementById('editModalCancel').addEventListener('click', function() {
                document.getElementById('editWorkstationModal').classList.remove('active');
            });

            // 编辑弹窗提交按钮
            document.getElementById('editModalSubmit').addEventListener('click', function() {
                // 获取表单数据
                const area = document.getElementById('edit-area').value;
                const team = document.getElementById('edit-team').value;
                const people = document.getElementById('edit-people').value;
                const workstations = document.getElementById('edit-workstations').value;
                const ratio = document.getElementById('edit-ratio').value;
                const date = document.getElementById('edit-date').value;

                // 表单验证
                if (!area || !team || !people || people < 1 || workstations < 0 || ratio < 0 || ratio > 100) {
                    alert('请填写正确的表单数据');
                    return;
                }

                // 模拟提交数据，实际应用中应该发送到后端
                alert('数据更新成功！');

                // 关闭弹窗
                document.getElementById('editWorkstationModal').classList.remove('active');
            });

            // 帮助按钮点击事件
            document.getElementById('helpBtn').addEventListener('click', function() {
                document.getElementById('helpModal').classList.add('active');
            });

            // 帮助弹窗关闭按钮
            document.getElementById('helpModalClose').addEventListener('click', function() {
                document.getElementById('helpModal').classList.remove('active');
            });

            // 帮助弹窗确认按钮
            document.getElementById('helpModalOk').addEventListener('click', function() {
                document.getElementById('helpModal').classList.remove('active');
            });

            // 导出数据按钮点击事件
            document.getElementById('exportBtn').addEventListener('click', function() {
                alert('数据导出功能将在实际应用中实现');
            });

            // 查询按钮点击事件
            document.getElementById('searchBtn').addEventListener('click', function() {
                const area = document.getElementById('area-filter').value;
                const team = document.getElementById('team-filter').value;
                const date = document.getElementById('date-filter').value;

                // 模拟查询，实际应用中应该发送到后端
                alert(`查询条件：区域=${area || '全部'}，团队=${team || '全部'}，时间=${date}`);
            });

            // 重置按钮点击事件
            document.getElementById('resetBtn').addEventListener('click', function() {
                document.getElementById('area-filter').value = '';
                document.getElementById('team-filter').value = '';
                document.getElementById('date-filter').value = '2025-04';
            });
        });
    </script>
</body>

</html>