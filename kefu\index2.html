<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>客服经营日报管理系统</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
            background: #f5f7fa;
            color: #333;
            line-height: 1.6;
        }
        
        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .page-header {
            background: #fff;
            padding: 24px;
            border-radius: 12px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
            margin-bottom: 24px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .page-title {
            font-size: 24px;
            font-weight: 600;
            color: #1a1a1a;
        }
        
        .settings-btn {
            background: #667eea;
            border: none;
            padding: 12px;
            border-radius: 8px;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .settings-btn:hover {
            background: #5a6fd8;
            transform: translateY(-1px);
        }
        
        .settings-icon {
            width: 20px;
            height: 20px;
            fill: white;
        }
        
        .main-content {
            background: #fff;
            border-radius: 12px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
            overflow: hidden;
        }
        
        .report-header {
            padding: 24px 32px;
            border-bottom: 1px solid #e8e8e8;
            background: #fafafa;
        }
        
        .report-controls {
            display: flex;
            justify-content: space-between;
            align-items: center;
            flex-wrap: wrap;
            gap: 16px;
        }
        
        .filter-section {
            display: flex;
            align-items: center;
            gap: 12px;
            flex-wrap: wrap;
        }
        
        .filter-group {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .filter-label {
            font-size: 14px;
            color: #666;
            font-weight: 500;
        }
        
        .date-input {
            padding: 8px 12px;
            border: 1px solid #d9d9d9;
            border-radius: 6px;
            font-size: 14px;
            transition: border-color 0.3s ease;
        }
        
        .date-input:focus {
            outline: none;
            border-color: #667eea;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }
        
        .action-section {
            display: flex;
            gap: 12px;
            align-items: center;
        }
        
        .btn-import,
        .btn-add,
        .btn-export {
            padding: 10px 16px;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 500;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 6px;
        }
        
        .btn-import {
            background: #52c41a;
            color: white;
        }
        
        .btn-import:hover {
            background: #45a321;
            transform: translateY(-1px);
        }
        
        .btn-add {
            background: #667eea;
            color: white;
        }
        
        .btn-add:hover {
            background: #5a6fd8;
            transform: translateY(-1px);
        }
        
        .btn-export {
            background: #f0f0f0;
            color: #666;
        }
        
        .btn-export:hover {
            background: #e0e0e0;
        }
        
        .report-table-container {
            overflow-x: auto;
            max-height: calc(100vh - 300px);
        }
        
        .report-table {
            width: 100%;
            border-collapse: collapse;
            min-width: 1500px;
        }
        
        .report-table thead {
            background: #f8f9fa;
            position: sticky;
            top: 0;
            z-index: 10;
        }
        
        .report-table th,
        .report-table td {
            padding: 12px 8px;
            text-align: center;
            border-bottom: 1px solid #e8e8e8;
            font-size: 13px;
            white-space: nowrap;
        }
        
        .report-table th {
            font-weight: 600;
            color: #1a1a1a;
            background: #f8f9fa;
        }
        
        .report-table td {
            color: #333;
        }
        
        .report-table tbody tr:hover {
            background: #f8f9fa;
        }
        
        .text-left {
            text-align: left !important;
        }
        
        .text-right {
            text-align: right !important;
        }
        
        .number-cell {
            font-family: 'Monaco', 'Menlo', monospace;
            color: #1890ff;
            font-weight: 500;
        }
        
        .percentage-cell {
            font-family: 'Monaco', 'Menlo', monospace;
            color: #52c41a;
            font-weight: 500;
        }
        
        .time-cell {
            font-family: 'Monaco', 'Menlo', monospace;
            color: #fa8c16;
            font-weight: 500;
        }
        
        .money-cell {
            font-family: 'Monaco', 'Menlo', monospace;
            color: #f5222d;
            font-weight: 600;
        }
        
        .action-buttons {
            display: flex;
            gap: 6px;
            justify-content: center;
        }
        
        .btn-edit,
        .btn-delete {
            padding: 4px 8px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 12px;
            font-weight: 500;
            transition: all 0.3s ease;
        }
        
        .btn-edit {
            background: #e8f4fd;
            color: #1890ff;
        }
        
        .btn-edit:hover {
            background: #bae7ff;
        }
        
        .btn-delete {
            background: #fff1f0;
            color: #ff4d4f;
        }
        
        .btn-delete:hover {
            background: #ffccc7;
        }
        /* Modal样式继续使用之前的 */
        
        .modal-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.5);
            display: none;
            justify-content: center;
            align-items: center;
            z-index: 1000;
            backdrop-filter: blur(8px);
        }
        
        .modal-content {
            background: #fff;
            border-radius: 16px;
            width: 90%;
            max-width: 1000px;
            max-height: 90vh;
            overflow: hidden;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
            position: relative;
        }
        
        .modal-header {
            padding: 24px 32px;
            border-bottom: 1px solid #e8e8e8;
            display: flex;
            justify-content: space-between;
            align-items: center;
            background: #fafafa;
        }
        
        .modal-title {
            font-size: 20px;
            font-weight: 600;
            color: #1a1a1a;
        }
        
        .close-btn {
            background: none;
            border: none;
            font-size: 24px;
            cursor: pointer;
            color: #999;
            padding: 4px;
            border-radius: 4px;
            transition: color 0.3s ease;
        }
        
        .close-btn:hover {
            color: #666;
            background: #f0f0f0;
        }
        
        .tab-container {
            display: flex;
            border-bottom: 1px solid #e8e8e8;
            background: #fafafa;
        }
        
        .tab-btn {
            flex: 1;
            padding: 16px 24px;
            border: none;
            background: none;
            font-size: 16px;
            cursor: pointer;
            color: #666;
            transition: all 0.3s ease;
            position: relative;
        }
        
        .tab-btn.active {
            color: #667eea;
            background: #fff;
        }
        
        .tab-btn.active::after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 0;
            width: 100%;
            height: 3px;
            background: #667eea;
        }
        
        .tab-content {
            display: none;
            padding: 32px;
            max-height: 60vh;
            overflow-y: auto;
        }
        
        .tab-content.active {
            display: block;
        }
        
        .content-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 24px;
        }
        
        .content-title {
            font-size: 18px;
            font-weight: 600;
            color: #1a1a1a;
        }
        
        .add-btn {
            background: #667eea;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 500;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .add-btn:hover {
            background: #5a6fd8;
            transform: translateY(-1px);
        }
        
        .table-container {
            background: #fff;
            border-radius: 8px;
            border: 1px solid #e8e8e8;
            overflow: hidden;
        }
        
        table {
            width: 100%;
            border-collapse: collapse;
        }
        
        thead {
            background: #f8f9fa;
        }
        
        th,
        td {
            padding: 16px;
            text-align: left;
            border-bottom: 1px solid #e8e8e8;
        }
        
        th {
            font-weight: 600;
            color: #1a1a1a;
            font-size: 14px;
        }
        
        td {
            color: #333;
            font-size: 14px;
        }
        
        tbody tr:hover {
            background: #f8f9fa;
        }
        
        .status-active {
            color: #52c41a;
            background: #f6ffed;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
        }
        
        .status-disabled {
            color: #ff4d4f;
            background: #fff1f0;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
        }
        /* 表单样式 */
        
        .form-modal {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.5);
            display: none;
            justify-content: center;
            align-items: center;
            z-index: 1001;
        }
        
        .form-content {
            background: #fff;
            border-radius: 12px;
            width: 90%;
            max-width: 800px;
            max-height: 90vh;
            overflow-y: auto;
            padding: 32px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
        }
        
        .form-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 16px;
            margin-bottom: 24px;
        }
        
        .form-group {
            margin-bottom: 20px;
        }
        
        .form-label {
            display: block;
            margin-bottom: 8px;
            font-weight: 500;
            color: #333;
            font-size: 14px;
        }
        
        .form-input,
        .form-select {
            width: 100%;
            padding: 12px;
            border: 1px solid #d9d9d9;
            border-radius: 8px;
            font-size: 14px;
            transition: border-color 0.3s ease;
        }
        
        .form-input:focus,
        .form-select:focus {
            outline: none;
            border-color: #667eea;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }
        
        .form-buttons {
            display: flex;
            gap: 12px;
            justify-content: flex-end;
            margin-top: 24px;
            padding-top: 24px;
            border-top: 1px solid #e8e8e8;
        }
        
        .btn-cancel,
        .btn-save {
            padding: 10px 20px;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 500;
            transition: all 0.3s ease;
        }
        
        .btn-cancel {
            background: #f0f0f0;
            color: #666;
        }
        
        .btn-cancel:hover {
            background: #e0e0e0;
        }
        
        .btn-save {
            background: #667eea;
            color: white;
        }
        
        .btn-save:hover {
            background: #5a6fd8;
        }
        
        .file-input-wrapper {
            position: relative;
            display: inline-block;
        }
        
        .file-input {
            position: absolute;
            opacity: 0;
            width: 0;
            height: 0;
        }
        
        .upload-area {
            border: 2px dashed #d9d9d9;
            border-radius: 8px;
            padding: 40px;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s ease;
            background: #fafafa;
        }
        
        .upload-area:hover {
            border-color: #667eea;
            background: #f0f2ff;
        }
        
        .upload-icon {
            font-size: 48px;
            color: #d9d9d9;
            margin-bottom: 12px;
        }
        
        .upload-text {
            color: #666;
            font-size: 14px;
        }
        
        @media (max-width: 768px) {
            .container {
                padding: 10px;
            }
            .report-controls {
                flex-direction: column;
                align-items: stretch;
            }
            .filter-section,
            .action-section {
                justify-content: center;
            }
            .form-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>

<body>
    <div class="container">
        <div class="page-header">
            <h1 class="page-title">客服经营日报</h1>
            <button class="settings-btn" onclick="openSettingsModal()">
                <svg class="settings-icon" viewBox="0 0 24 24">
                    <path d="M19.14,12.94c0.04-0.3,0.06-0.61,0.06-0.94c0-0.32-0.02-0.64-0.07-0.94l2.03-1.58c0.18-0.14,0.23-0.41,0.12-0.61 l-1.92-3.32c-0.12-0.22-0.37-0.29-0.59-0.22l-2.39,0.96c-0.5-0.38-1.03-0.7-1.62-0.94L14.4,2.81c-0.04-0.24-0.24-0.41-0.48-0.41 h-3.84c-0.24,0-0.43,0.17-0.47,0.41L9.25,5.35C8.66,5.59,8.12,5.92,7.63,6.29L5.24,5.33c-0.22-0.08-0.47,0-0.59,0.22L2.74,8.87 C2.62,9.08,2.66,9.34,2.86,9.48l2.03,1.58C4.84,11.36,4.8,11.69,4.8,12s0.02,0.64,0.07,0.94l-2.03,1.58 c-0.18,0.14-0.23,0.41-0.12,0.61l1.92,3.32c0.12,0.22,0.37,0.29,0.59,0.22l2.39-0.96c0.5,0.38,1.03,0.7,1.62,0.94l0.36,2.54 c0.05,0.24,0.24,0.41,0.48,0.41h3.84c0.24,0,0.44-0.17,0.47-0.41l0.36-2.54c0.59-0.24,1.13-0.56,1.62-0.94l2.39,0.96 c0.22,0.08,0.47,0,0.59-0.22l1.92-3.32c0.12-0.22,0.07-0.47-0.12-0.61L19.14,12.94z M12,15.6c-1.98,0-3.6-1.62-3.6-3.6 s1.62-3.6,3.6-3.6s3.6,1.62,3.6,3.6S13.98,15.6,12,15.6z"/>
                </svg>
            </button>
        </div>

        <div class="main-content">
            <div class="report-header">
                <div class="report-controls">
                    <div class="filter-section">
                        <div class="filter-group">
                            <label class="filter-label">日期筛选：</label>
                            <input type="date" class="date-input" id="startDate" onchange="filterByDate()">
                            <span style="color: #999;">至</span>
                            <input type="date" class="date-input" id="endDate" onchange="filterByDate()">
                        </div>
                    </div>
                    <div class="action-section">
                        <button class="btn-import" onclick="showImportModal()">
                            <span>📁</span> 导入数据
                        </button>
                        <button class="btn-add" onclick="showReportForm()">
                            <span>+</span> 新增日报
                        </button>
                        <button class="btn-export" onclick="exportData()">
                            <span>📊</span> 导出数据
                        </button>
                    </div>
                </div>
            </div>

            <div class="report-table-container">
                <table class="report-table">
                    <thead>
                        <tr>
                            <th>填报日期</th>
                            <th>团队名称</th>
                            <th>店铺名称</th>
                            <th>客服班次</th>
                            <th>客服人员</th>
                            <th>接待人数</th>
                            <th>首次响应时长<br><small>(均值/秒)</small></th>
                            <th>平均响应时长<br><small>(均值/秒)</small></th>
                            <th>3分钟人工<br>回复率(%)</th>
                            <th>支付人数</th>
                            <th>询单转化率<br>(%)</th>
                            <th>客服销售额<br><small>(元)</small></th>
                            <th>客服满意率<br>(%)</th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody id="reportTableBody">
                        <!-- 模拟数据将通过JavaScript生成 -->
                    </tbody>
                </table>
            </div>
        </div>
    </div>
    <!-- Settings Modal -->
    <div class="modal-overlay" id="settingsModal">
        <div class="modal-content">
            <div class="modal-header">
                <h2 class="modal-title">基础设置</h2>
                <button class="close-btn" onclick="closeSettingsModal()">&times;</button>
            </div>

            <div class="tab-container">
                <button class="tab-btn active" onclick="showTab('income')">客服业务收入基础维护</button>
                <button class="tab-btn" onclick="showTab('expense')">客服业务支出基础维护</button>
            </div>

            <!-- Income Tab -->
            <div class="tab-content active" id="incomeTab">
                <div class="content-header">
                    <h3 class="content-title">客服业务收入设置</h3>
                    <button class="add-btn" onclick="showIncomeForm()">
                            <span>+</span> 新增收入项
                        </button>
                </div>
                <div class="table-container">
                    <table>
                        <thead>
                            <tr>
                                <th>ID</th>
                                <th>客服班次费用（元/天）</th>
                                <th>店铺基础费用（元/天/店）</th>
                                <th>状态</th>
                                <th>操作</th>
                            </tr>
                        </thead>
                        <tbody id="incomeTableBody">
                            <tr>
                                <td>1</td>
                                <td>300.00</td>
                                <td>150.00</td>
                                <td><span class="status-active">启用</span></td>
                                <td>
                                    <div class="action-buttons">
                                        <button class="btn-edit" onclick="editIncomeItem(1)">编辑</button>
                                        <button class="btn-delete" onclick="disableItem('income', 1)">停用</button>
                                    </div>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>

            <!-- Expense Tab -->
            <div class="tab-content" id="expenseTab">
                <div class="content-header">
                    <h3 class="content-title">客服业务支出设置</h3>
                    <button class="add-btn" onclick="showExpenseForm()">
                            <span>+</span> 新增支出项
                        </button>
                </div>
                <div class="table-container">
                    <table>
                        <thead>
                            <tr>
                                <th>ID</th>
                                <th>客服人员</th>
                                <th>预估工资（元/天）</th>
                                <th>年限工资（元/天）</th>
                                <th>状态</th>
                                <th>操作</th>
                            </tr>
                        </thead>
                        <tbody id="expenseTableBody">
                            <tr>
                                <td>1</td>
                                <td>张小明</td>
                                <td>200.00</td>
                                <td>220.00</td>
                                <td><span class="status-active">启用</span></td>
                                <td>
                                    <div class="action-buttons">
                                        <button class="btn-edit" onclick="editExpenseItem(1)">编辑</button>
                                        <button class="btn-delete" onclick="disableItem('expense', 1)">停用</button>
                                    </div>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>

    <!-- Report Form Modal -->
    <div class="form-modal" id="reportFormModal">
        <div class="form-content">
            <h3 style="margin-bottom: 24px; font-size: 18px;" id="reportFormTitle">新增客服经营日报</h3>
            <form id="reportForm">
                <div class="form-grid">
                    <div class="form-group">
                        <label class="form-label">填报日期 <span style="color: red;">*</span></label>
                        <input type="date" class="form-input" id="reportDate" required>
                    </div>
                    <div class="form-group">
                        <label class="form-label">团队名称 <span style="color: red;">*</span></label>
                        <select class="form-select" id="teamName" required>
                                <option value="">请选择团队</option>
                                <option value="A组">A组</option>
                                <option value="B组">B组</option>
                                <option value="C组">C组</option>
                                <option value="D组">D组</option>
                            </select>
                    </div>
                    <div class="form-group">
                        <label class="form-label">店铺名称 <span style="color: red;">*</span></label>
                        <select class="form-select" id="shopName" required>
                                <option value="">请选择店铺</option>
                                <option value="旗舰店">旗舰店</option>
                                <option value="专营店A">专营店A</option>
                                <option value="专营店B">专营店B</option>
                                <option value="品牌直营店">品牌直营店</option>
                            </select>
                    </div>
                    <div class="form-group">
                        <label class="form-label">客服班次 <span style="color: red;">*</span></label>
                        <select class="form-select" id="serviceShift" required>
                                <option value="">请选择班次</option>
                                <option value="早班">早班</option>
                                <option value="中班">中班</option>
                                <option value="晚班">晚班</option>
                                <option value="全天">全天</option>
                            </select>
                    </div>
                    <div class="form-group">
                        <label class="form-label">客服人员 <span style="color: red;">*</span></label>
                        <input type="text" class="form-input" id="serviceStaff" placeholder="请输入客服人员姓名" required>
                    </div>
                    <div class="form-group">
                        <label class="form-label">接待人数 <span style="color: red;">*</span></label>
                        <input type="number" class="form-input" id="receptionCount" placeholder="请输入接待人数" min="0" required>
                    </div>
                    <div class="form-group">
                        <label class="form-label">首次响应时长（秒）</label>
                        <input type="number" class="form-input" id="firstResponseTime" placeholder="请输入首次响应时长" min="0" step="0.1">
                    </div>
                    <div class="form-group">
                        <label class="form-label">平均响应时长（秒）</label>
                        <input type="number" class="form-input" id="avgResponseTime" placeholder="请输入平均响应时长" min="0" step="0.1">
                    </div>
                    <div class="form-group">
                        <label class="form-label">3分钟人工回复率（%）</label>
                        <input type="number" class="form-input" id="threeMinReplyRate" placeholder="请输入回复率" min="0" max="100" step="0.01">
                    </div>
                    <div class="form-group">
                        <label class="form-label">支付人数</label>
                        <input type="number" class="form-input" id="paymentCount" placeholder="请输入支付人数" min="0">
                    </div>
                    <div class="form-group">
                        <label class="form-label">询单转化率（%）</label>
                        <input type="number" class="form-input" id="inquiryConversionRate" placeholder="请输入转化率" min="0" max="100" step="0.01">
                    </div>
                    <div class="form-group">
                        <label class="form-label">客服销售额（元）</label>
                        <input type="number" class="form-input" id="salesAmount" placeholder="请输入销售额" min="0" step="0.01">
                    </div>
                    <div class="form-group">
                        <label class="form-label">客服满意率（%）</label>
                        <input type="number" class="form-input" id="satisfactionRate" placeholder="请输入满意率" min="0" max="100" step="0.01">
                    </div>
                </div>
                <div class="form-buttons">
                    <button type="button" class="btn-cancel" onclick="closeReportForm()">取消</button>
                    <button type="submit" class="btn-save">保存</button>
                </div>
            </form>
        </div>
    </div>

    <!-- Import Modal -->
    <div class="form-modal" id="importModal">
        <div class="form-content">
            <h3 style="margin-bottom: 24px; font-size: 18px;">导入客服经营日报数据</h3>
            <div class="upload-area" onclick="document.getElementById('fileInput').click()">
                <div class="upload-icon">📁</div>
                <div class="upload-text">
                    <p>点击选择文件或拖拽文件到此处</p>
                    <p style="color: #999; font-size: 12px; margin-top: 8px;">支持 .xlsx, .xls, .csv 格式</p>
                </div>
                <input type="file" id="fileInput" class="file-input" accept=".xlsx,.xls,.csv" onchange="handleFileSelect(this)">
            </div>
            <div id="fileInfo" style="margin-top: 16px; padding: 12px; background: #f0f2ff; border-radius: 6px; display: none;">
                <p style="color: #667eea; font-size: 14px; margin: 0;"></p>
            </div>
            <div class="form-buttons">
                <button type="button" class="btn-cancel" onclick="closeImportModal()">取消</button>
                <button type="button" class="btn-save" onclick="importData()" id="importBtn" disabled>确认导入</button>
            </div>
        </div>
    </div>

    <!-- Income Form Modal -->
    <div class="form-modal" id="incomeFormModal">
        <div class="form-content">
            <h3 style="margin-bottom: 24px; font-size: 18px;">编辑收入设置</h3>
            <form id="incomeForm">
                <div class="form-group">
                    <label class="form-label">客服班次费用（元/天）</label>
                    <input type="number" class="form-input" id="shiftCost" placeholder="请输入客服班次费用" step="0.01" required>
                </div>
                <div class="form-group">
                    <label class="form-label">店铺基础费用（元/天/店）</label>
                    <input type="number" class="form-input" id="shopCost" placeholder="请输入店铺基础费用" step="0.01" required>
                </div>
                <div class="form-buttons">
                    <button type="button" class="btn-cancel" onclick="closeIncomeForm()">取消</button>
                    <button type="submit" class="btn-save">保存</button>
                </div>
            </form>
        </div>
    </div>

    <!-- Expense Form Modal -->
    <div class="form-modal" id="expenseFormModal">
        <div class="form-content">
            <h3 style="margin-bottom: 24px; font-size: 18px;">编辑支出设置</h3>
            <form id="expenseForm">
                <div class="form-group">
                    <label class="form-label">客服人员</label>
                    <input type="text" class="form-input" id="staffName" placeholder="请输入客服人员姓名" required>
                </div>
                <div class="form-group">
                    <label class="form-label">预估工资（元/天）</label>
                    <input type="number" class="form-input" id="estimatedSalary" placeholder="请输入预估工资" step="0.01" required>
                </div>
                <div class="form-group">
                    <label class="form-label">年限工资（元/天）</label>
                    <input type="number" class="form-input" id="yearsSalary" placeholder="请输入年限工资" step="0.01" required>
                </div>
                <div class="form-buttons">
                    <button type="button" class="btn-cancel" onclick="closeExpenseForm()">取消</button>
                    <button type="submit" class="btn-save">保存</button>
                </div>
            </form>
        </div>
    </div>

    <script>
        // 全局变量
        let currentEditId = null;
        let currentEditType = null;
        let selectedFile = null;

        // 模拟日报数据
        let reportData = [{
            id: 1,
            reportDate: '2024-01-15',
            teamName: 'A组',
            shopName: '旗舰店',
            serviceShift: '全天',
            serviceStaff: '张小明',
            receptionCount: 156,
            firstResponseTime: 23.5,
            avgResponseTime: 45.2,
            threeMinReplyRate: 95.6,
            paymentCount: 42,
            inquiryConversionRate: 26.9,
            salesAmount: 12580.50,
            satisfactionRate: 98.2
        }, {
            id: 2,
            reportDate: '2024-01-15',
            teamName: 'B组',
            shopName: '专营店A',
            serviceShift: '早班',
            serviceStaff: '李小红',
            receptionCount: 89,
            firstResponseTime: 18.3,
            avgResponseTime: 38.7,
            threeMinReplyRate: 97.2,
            paymentCount: 28,
            inquiryConversionRate: 31.5,
            salesAmount: 8950.00,
            satisfactionRate: 96.8
        }, {
            id: 3,
            reportDate: '2024-01-16',
            teamName: 'C组',
            shopName: '品牌直营店',
            serviceShift: '中班',
            serviceStaff: '王大力',
            receptionCount: 123,
            firstResponseTime: 31.2,
            avgResponseTime: 52.8,
            threeMinReplyRate: 92.1,
            paymentCount: 35,
            inquiryConversionRate: 28.5,
            salesAmount: 15680.75,
            satisfactionRate: 94.5
        }];

        // 模拟基础数据
        let incomeData = [{
            id: 1,
            shiftCost: 300.00,
            shopCost: 150.00,
            status: 'active'
        }];

        let expenseData = [{
            id: 1,
            staffName: '张小明',
            estimatedSalary: 200.00,
            yearsSalary: 220.00,
            status: 'active'
        }];

        // 页面初始化
        document.addEventListener('DOMContentLoaded', function() {
            renderReportTable();
            setDefaultDates();
            console.log('客服经营日报管理系统已加载');
        });

        // 设置默认日期（当前月份的第一天到今天）
        function setDefaultDates() {
            const today = new Date();
            const firstDay = new Date(today.getFullYear(), today.getMonth(), 1);

            document.getElementById('startDate').value = firstDay.toISOString().split('T')[0];
            document.getElementById('endDate').value = today.toISOString().split('T')[0];
        }

        // 渲染日报表格
        function renderReportTable(data = reportData) {
            const tbody = document.getElementById('reportTableBody');
            tbody.innerHTML = '';

            if (!data || data.length === 0) {
                const row = document.createElement('tr');
                row.innerHTML = `
                        <td colspan="14" style="text-align: center; color: #999; padding: 40px;">
                            暂无数据
                        </td>
                    `;
                tbody.appendChild(row);
                return;
            }

            data.forEach(item => {
                const row = document.createElement('tr');
                row.innerHTML = `
                        <td>${item.reportDate}</td>
                        <td class="text-left">${item.teamName}</td>
                        <td class="text-left">${item.shopName}</td>
                        <td>${item.serviceShift}</td>
                        <td class="text-left">${item.serviceStaff}</td>
                        <td class="number-cell">${item.receptionCount}</td>
                        <td class="time-cell">${item.firstResponseTime || '-'}</td>
                        <td class="time-cell">${item.avgResponseTime || '-'}</td>
                        <td class="percentage-cell">${item.threeMinReplyRate ? item.threeMinReplyRate + '%' : '-'}</td>
                        <td class="number-cell">${item.paymentCount || '-'}</td>
                        <td class="percentage-cell">${item.inquiryConversionRate ? item.inquiryConversionRate + '%' : '-'}</td>
                        <td class="money-cell">¥${item.salesAmount ? item.salesAmount.toLocaleString('zh-CN', {minimumFractionDigits: 2}) : '0.00'}</td>
                        <td class="percentage-cell">${item.satisfactionRate ? item.satisfactionRate + '%' : '-'}</td>
                        <td>
                            <div class="action-buttons">
                                <button class="btn-edit" onclick="editReport(${item.id})">编辑</button>
                                <button class="btn-delete" onclick="deleteReport(${item.id})">删除</button>
                            </div>
                        </td>
                    `;
                tbody.appendChild(row);
            });
        }

        // 日期筛选
        function filterByDate() {
            const startDate = document.getElementById('startDate').value;
            const endDate = document.getElementById('endDate').value;

            if (!startDate || !endDate) {
                renderReportTable();
                return;
            }

            const filteredData = reportData.filter(item => {
                return item.reportDate >= startDate && item.reportDate <= endDate;
            });

            renderReportTable(filteredData);
        }

        // 显示新增日报表单
        function showReportForm(editData = null) {
            currentEditId = editData ? editData.id : null;

            const form = document.getElementById('reportForm');
            const modal = document.getElementById('reportFormModal');
            const title = document.getElementById('reportFormTitle');

            if (editData) {
                // 编辑模式
                title.textContent = '编辑客服经营日报';
                document.getElementById('reportDate').value = editData.reportDate;
                document.getElementById('teamName').value = editData.teamName;
                document.getElementById('shopName').value = editData.shopName;
                document.getElementById('serviceShift').value = editData.serviceShift;
                document.getElementById('serviceStaff').value = editData.serviceStaff;
                document.getElementById('receptionCount').value = editData.receptionCount;
                document.getElementById('firstResponseTime').value = editData.firstResponseTime || '';
                document.getElementById('avgResponseTime').value = editData.avgResponseTime || '';
                document.getElementById('threeMinReplyRate').value = editData.threeMinReplyRate || '';
                document.getElementById('paymentCount').value = editData.paymentCount || '';
                document.getElementById('inquiryConversionRate').value = editData.inquiryConversionRate || '';
                document.getElementById('salesAmount').value = editData.salesAmount || '';
                document.getElementById('satisfactionRate').value = editData.satisfactionRate || '';
            } else {
                // 新增模式
                title.textContent = '新增客服经营日报';
                form.reset();
                // 设置默认日期为今天
                document.getElementById('reportDate').value = new Date().toISOString().split('T')[0];
            }

            modal.style.display = 'flex';
            document.body.style.overflow = 'hidden';
        }

        // 关闭日报表单
        function closeReportForm() {
            document.getElementById('reportFormModal').style.display = 'none';
            document.body.style.overflow = 'auto';
            currentEditId = null;
        }

        // 编辑日报
        function editReport(id) {
            const item = reportData.find(i => i.id === id);
            if (item) {
                showReportForm(item);
            }
        }

        // 删除日报
        function deleteReport(id) {
            if (confirm('确认要删除这条日报记录吗？删除后不可恢复。')) {
                reportData = reportData.filter(item => item.id !== id);
                renderReportTable();
                filterByDate(); // 重新应用筛选
                alert('删除成功！');
            }
        }

        // 显示导入弹窗
        function showImportModal() {
            document.getElementById('importModal').style.display = 'flex';
            document.body.style.overflow = 'hidden';
        }

        // 关闭导入弹窗
        function closeImportModal() {
            document.getElementById('importModal').style.display = 'none';
            document.body.style.overflow = 'auto';
            selectedFile = null;
            document.getElementById('fileInfo').style.display = 'none';
            document.getElementById('importBtn').disabled = true;
        }

        // 处理文件选择
        function handleFileSelect(input) {
            selectedFile = input.files[0];
            const fileInfo = document.getElementById('fileInfo');
            const importBtn = document.getElementById('importBtn');

            if (selectedFile) {
                fileInfo.style.display = 'block';
                fileInfo.querySelector('p').textContent = `已选择文件: ${selectedFile.name} (${(selectedFile.size / 1024).toFixed(1)} KB)`;
                importBtn.disabled = false;
            } else {
                fileInfo.style.display = 'none';
                importBtn.disabled = true;
            }
        }

        // 导入数据
        function importData() {
            if (!selectedFile) {
                alert('请先选择文件！');
                return;
            }

            // 这里应该是实际的文件处理逻辑
            // 由于是演示，我们模拟导入成功
            setTimeout(() => {
                alert('数据导入成功！共导入 5 条记录。');
                closeImportModal();
                // 可以在这里添加实际的数据到reportData数组中
                renderReportTable();
            }, 1000);
        }

        // 导出数据
        function exportData() {
            // 获取当前筛选的数据
            const startDate = document.getElementById('startDate').value;
            const endDate = document.getElementById('endDate').value;

            let dataToExport = reportData;
            if (startDate && endDate) {
                dataToExport = reportData.filter(item => {
                    return item.reportDate >= startDate && item.reportDate <= endDate;
                });
            }

            if (dataToExport.length === 0) {
                alert('没有数据可以导出！');
                return;
            }

            // 模拟导出功能
            alert(`正在导出 ${dataToExport.length} 条记录...`);

            // 实际项目中，这里应该调用后端接口或使用库如 SheetJS 来生成 Excel 文件
            setTimeout(() => {
                alert('导出成功！');
            }, 500);
        } // 日报表单提交处理
        document.getElementById('reportForm').addEventListener('submit', function(e) {
            e.preventDefault();

            // 获取表单数据
            const formData = {
                reportDate: document.getElementById('reportDate').value,
                teamName: document.getElementById('teamName').value,
                shopName: document.getElementById('shopName').value,
                serviceShift: document.getElementById('serviceShift').value,
                serviceStaff: document.getElementById('serviceStaff').value,
                receptionCount: parseInt(document.getElementById('receptionCount').value),
                firstResponseTime: parseFloat(document.getElementById('firstResponseTime').value) || null,
                avgResponseTime: parseFloat(document.getElementById('avgResponseTime').value) || null,
                threeMinReplyRate: parseFloat(document.getElementById('threeMinReplyRate').value) || null,
                paymentCount: parseInt(document.getElementById('paymentCount').value) || null,
                inquiryConversionRate: parseFloat(document.getElementById('inquiryConversionRate').value) || null,
                salesAmount: parseFloat(document.getElementById('salesAmount').value) || null,
                satisfactionRate: parseFloat(document.getElementById('satisfactionRate').value) || null
            };

            // 表单验证
            if (!formData.reportDate || !formData.teamName || !formData.shopName ||
                !formData.serviceShift || !formData.serviceStaff || !formData.receptionCount) {
                alert('请填写所有必填字段！');
                return;
            }

            // 数据范围验证
            if (formData.threeMinReplyRate && (formData.threeMinReplyRate < 0 || formData.threeMinReplyRate > 100)) {
                alert('3分钟人工回复率必须在0-100%之间！');
                return;
            }

            if (formData.inquiryConversionRate && (formData.inquiryConversionRate < 0 || formData.inquiryConversionRate > 100)) {
                alert('询单转化率必须在0-100%之间！');
                return;
            }

            if (formData.satisfactionRate && (formData.satisfactionRate < 0 || formData.satisfactionRate > 100)) {
                alert('客服满意率必须在0-100%之间！');
                return;
            }

            if (currentEditId) {
                // 编辑模式
                const index = reportData.findIndex(item => item.id === currentEditId);
                if (index !== -1) {
                    reportData[index] = {...formData,
                        id: currentEditId
                    };
                }
                alert('修改成功！');
            } else {
                // 新增模式
                const newId = Math.max(...reportData.map(item => item.id)) + 1;
                reportData.push({...formData,
                    id: newId
                });
                alert('新增成功！');
            }

            renderReportTable();
            filterByDate(); // 重新应用筛选
            closeReportForm();
        });

        // Settings Modal 相关功能
        function openSettingsModal() {
            document.getElementById('settingsModal').style.display = 'flex';
            document.body.style.overflow = 'hidden';
            renderIncomeTable();
            renderExpenseTable();
        }

        function closeSettingsModal() {
            document.getElementById('settingsModal').style.display = 'none';
            document.body.style.overflow = 'auto';
        }

        // Tab切换功能
        function showTab(tabName) {
            // 移除所有active类
            document.querySelectorAll('.tab-btn').forEach(btn => btn.classList.remove('active'));
            document.querySelectorAll('.tab-content').forEach(content => content.classList.remove('active'));

            // 添加active类到对应的tab
            event.target.classList.add('active');
            document.getElementById(tabName + 'Tab').classList.add('active');
        }

        // 渲染收入设置表格
        function renderIncomeTable() {
            const tbody = document.getElementById('incomeTableBody');
            tbody.innerHTML = '';

            incomeData.forEach(item => {
                        const row = document.createElement('tr');
                        row.innerHTML = `
                    <td>${item.id}</td>
                    <td>¥${item.shiftCost.toFixed(2)}</td>
                    <td>¥${item.shopCost.toFixed(2)}</td>
                    <td><span class="status-${item.status}">${item.status === 'active' ? '启用' : '停用'}</span></td>
                    <td>
                        <div class="action-buttons">
                            <button class="btn-edit" onclick="editIncomeItem(${item.id})">编辑</button>
                            ${item.status === 'active' ? 
                                `<button class="btn-delete" onclick="disableItem('income', ${item.id})">停用</button>` :
                                `<button class="btn-edit" onclick="enableItem('income', ${item.id})">启用</button>`
                            }
                        </div>
                    </td>
                `;
                tbody.appendChild(row);
            });
        }

        // 渲染支出设置表格
        function renderExpenseTable() {
            const tbody = document.getElementById('expenseTableBody');
            tbody.innerHTML = '';
            
            expenseData.forEach(item => {
                const row = document.createElement('tr');
                row.innerHTML = `
                    <td>${item.id}</td>
                    <td>${item.staffName}</td>
                    <td>¥${item.estimatedSalary.toFixed(2)}</td>
                    <td>¥${item.yearsSalary.toFixed(2)}</td>
                    <td><span class="status-${item.status}">${item.status === 'active' ? '启用' : '停用'}</span></td>
                    <td>
                        <div class="action-buttons">
                            <button class="btn-edit" onclick="editExpenseItem(${item.id})">编辑</button>
                            ${item.status === 'active' ? 
                                `<button class="btn-delete" onclick="disableItem('expense', ${item.id})">停用</button>` :
                                `<button class="btn-edit" onclick="enableItem('expense', ${item.id})">启用</button>`
                            }
                        </div>
                    </td>
                `;
                tbody.appendChild(row);
            });
        }

        // 显示收入表单
        function showIncomeForm(editData = null) {
            currentEditId = editData ? editData.id : null;
            currentEditType = 'income';
            
            const modal = document.getElementById('incomeFormModal');
            
            if (editData) {
                document.getElementById('shiftCost').value = editData.shiftCost;
                document.getElementById('shopCost').value = editData.shopCost;
            } else {
                document.getElementById('incomeForm').reset();
            }
            
            modal.style.display = 'flex';
            document.body.style.overflow = 'hidden';
        }

        // 关闭收入表单
        function closeIncomeForm() {
            document.getElementById('incomeFormModal').style.display = 'none';
            document.body.style.overflow = 'hidden'; // 保持settings modal的overflow设置
            currentEditId = null;
        }

        // 显示支出表单
        function showExpenseForm(editData = null) {
            currentEditId = editData ? editData.id : null;
            currentEditType = 'expense';
            
            const modal = document.getElementById('expenseFormModal');
            
            if (editData) {
                document.getElementById('staffName').value = editData.staffName;
                document.getElementById('estimatedSalary').value = editData.estimatedSalary;
                document.getElementById('yearsSalary').value = editData.yearsSalary;
            } else {
                document.getElementById('expenseForm').reset();
            }
            
            modal.style.display = 'flex';
            document.body.style.overflow = 'hidden';
        }

        // 关闭支出表单
        function closeExpenseForm() {
            document.getElementById('expenseFormModal').style.display = 'none';
            document.body.style.overflow = 'hidden'; // 保持settings modal的overflow设置
            currentEditId = null;
        }

        // 编辑收入项目
        function editIncomeItem(id) {
            const item = incomeData.find(i => i.id === id);
            if (item) {
                showIncomeForm(item);
            }
        }

        // 编辑支出项目
        function editExpenseItem(id) {
            const item = expenseData.find(i => i.id === id);
            if (item) {
                showExpenseForm(item);
            }
        }

        // 停用项目
        function disableItem(type, id) {
            if (confirm('确认要停用此项目吗？')) {
                if (type === 'income') {
                    const item = incomeData.find(i => i.id === id);
                    if (item) {
                        item.status = 'disabled';
                        renderIncomeTable();
                    }
                } else if (type === 'expense') {
                    const item = expenseData.find(i => i.id === id);
                    if (item) {
                        item.status = 'disabled';
                        renderExpenseTable();
                    }
                }
            }
        }

        // 启用项目
        function enableItem(type, id) {
            if (confirm('确认要启用此项目吗？')) {
                if (type === 'income') {
                    const item = incomeData.find(i => i.id === id);
                    if (item) {
                        item.status = 'active';
                        renderIncomeTable();
                    }
                } else if (type === 'expense') {
                    const item = expenseData.find(i => i.id === id);
                    if (item) {
                        item.status = 'active';
                        renderExpenseTable();
                    }
                }
            }
        }

        // 处理收入表单提交
        document.getElementById('incomeForm').addEventListener('submit', function(e) {
            e.preventDefault();
            
            const shiftCost = parseFloat(document.getElementById('shiftCost').value);
            const shopCost = parseFloat(document.getElementById('shopCost').value);
            
            if (isNaN(shiftCost) || isNaN(shopCost)) {
                alert('请输入有效的数值');
                return;
            }
            
            if (shiftCost < 0 || shopCost < 0) {
                alert('费用不能为负数');
                return;
            }
            
            if (currentEditId) {
                // 编辑模式
                const item = incomeData.find(i => i.id === currentEditId);
                if (item) {
                    item.shiftCost = shiftCost;
                    item.shopCost = shopCost;
                }
            } else {
                // 新增模式
                const newId = Math.max(...incomeData.map(i => i.id)) + 1;
                incomeData.push({
                    id: newId,
                    shiftCost: shiftCost,
                    shopCost: shopCost,
                    status: 'active'
                });
            }
            
            renderIncomeTable();
            closeIncomeForm();
            alert('保存成功！');
        });

        // 处理支出表单提交
        document.getElementById('expenseForm').addEventListener('submit', function(e) {
            e.preventDefault();
            
            const staffName = document.getElementById('staffName').value.trim();
            const estimatedSalary = parseFloat(document.getElementById('estimatedSalary').value);
            const yearsSalary = parseFloat(document.getElementById('yearsSalary').value);
            
            if (!staffName) {
                alert('请输入客服人员姓名');
                return;
            }
            
            if (isNaN(estimatedSalary) || isNaN(yearsSalary)) {
                alert('请输入有效的工资数值');
                return;
            }
            
            if (estimatedSalary < 0 || yearsSalary < 0) {
                alert('工资不能为负数');
                return;
            }
            
            // 检查是否重名（编辑时排除自己）
            const existingItem = expenseData.find(item => 
                item.staffName === staffName && item.id !== currentEditId
            );
            if (existingItem) {
                alert('客服人员姓名已存在，请使用其他姓名');
                return;
            }
            
            if (currentEditId) {
                // 编辑模式
                const item = expenseData.find(i => i.id === currentEditId);
                if (item) {
                    item.staffName = staffName;
                    item.estimatedSalary = estimatedSalary;
                    item.yearsSalary = yearsSalary;
                }
            } else {
                // 新增模式
                const newId = Math.max(...expenseData.map(i => i.id)) + 1;
                expenseData.push({
                    id: newId,
                    staffName: staffName,
                    estimatedSalary: estimatedSalary,
                    yearsSalary: yearsSalary,
                    status: 'active'
                });
            }
            
            renderExpenseTable();
            closeExpenseForm();
            alert('保存成功！');
        });

        // 拖拽上传功能
        const uploadArea = document.querySelector('.upload-area');
        
        uploadArea.addEventListener('dragover', function(e) {
            e.preventDefault();
            this.style.borderColor = '#667eea';
            this.style.background = '#f0f2ff';
        });
        
        uploadArea.addEventListener('dragleave', function(e) {
            e.preventDefault();
            this.style.borderColor = '#d9d9d9';
            this.style.background = '#fafafa';
        });
        
        uploadArea.addEventListener('drop', function(e) {
            e.preventDefault();
            this.style.borderColor = '#d9d9d9';
            this.style.background = '#fafafa';
            
            const files = e.dataTransfer.files;
            if (files.length > 0) {
                const fileInput = document.getElementById('fileInput');
                fileInput.files = files;
                handleFileSelect(fileInput);
            }
        });

        // 点击模态框外部关闭功能
        document.querySelectorAll('.modal-overlay, .form-modal').forEach(modal => {
            modal.addEventListener('click', function(e) {
                if (e.target === this) {
                    if (modal.id === 'settingsModal') {
                        closeSettingsModal();
                    } else if (modal.id === 'reportFormModal') {
                        closeReportForm();
                    } else if (modal.id === 'importModal') {
                        closeImportModal();
                    } else if (modal.id === 'incomeFormModal') {
                        closeIncomeForm();
                    } else if (modal.id === 'expenseFormModal') {
                        closeExpenseForm();
                    }
                }
            });
        });

        // 键盘事件处理
        document.addEventListener('keydown', function(e) {
            if (e.key === 'Escape') {
                // 按ESC键关闭弹窗（优先级：子弹窗 > 主弹窗）
                if (document.getElementById('incomeFormModal').style.display === 'flex') {
                    closeIncomeForm();
                } else if (document.getElementById('expenseFormModal').style.display === 'flex') {
                    closeExpenseForm();
                } else if (document.getElementById('reportFormModal').style.display === 'flex') {
                    closeReportForm();
                } else if (document.getElementById('importModal').style.display === 'flex') {
                    closeImportModal();
                } else if (document.getElementById('settingsModal').style.display === 'flex') {
                    closeSettingsModal();
                }
            }
        });

        // 快捷键功能
        document.addEventListener('keydown', function(e) {
            // Ctrl/Cmd + N 新增日报
            if ((e.ctrlKey || e.metaKey) && e.key === 'n') {
                e.preventDefault();
                showReportForm();
            }
            
            // Ctrl/Cmd + I 导入数据
            if ((e.ctrlKey || e.metaKey) && e.key === 'i') {
                e.preventDefault();
                showImportModal();
            }
            
            // Ctrl/Cmd + E 导出数据
            if ((e.ctrlKey || e.metaKey) && e.key === 'e') {
                e.preventDefault();
                exportData();
            }
            
            // Ctrl/Cmd + , 打开设置
            if ((e.ctrlKey || e.metaKey) && e.key === ',') {
                e.preventDefault();
                openSettingsModal();
            }
        });

        // 表格排序功能
        function sortTable(columnIndex, dataKey) {
            // 这里可以实现表格排序功能
            console.log('Sort by:', dataKey);
        }

        // 数据统计功能
        function calculateStats() {
            const filteredData = getFilteredData();
            
            const stats = {
                totalReports: filteredData.length,
                totalReception: filteredData.reduce((sum, item) => sum + item.receptionCount, 0),
                totalPayments: filteredData.reduce((sum, item) => sum + (item.paymentCount || 0), 0),
                totalSales: filteredData.reduce((sum, item) => sum + (item.salesAmount || 0), 0),
                avgConversionRate: 0,
                avgSatisfactionRate: 0
            };
            
            if (filteredData.length > 0) {
                const conversionRates = filteredData.filter(item => item.inquiryConversionRate).map(item => item.inquiryConversionRate);
                const satisfactionRates = filteredData.filter(item => item.satisfactionRate).map(item => item.satisfactionRate);
                
                if (conversionRates.length > 0) {
                    stats.avgConversionRate = conversionRates.reduce((sum, rate) => sum + rate, 0) / conversionRates.length;
                }
                
                if (satisfactionRates.length > 0) {
                    stats.avgSatisfactionRate = satisfactionRates.reduce((sum, rate) => sum + rate, 0) / satisfactionRates.length;
                }
            }
            
            return stats;
        }

        // 获取筛选后的数据
        function getFilteredData() {
            const startDate = document.getElementById('startDate').value;
            const endDate = document.getElementById('endDate').value;
            
            if (!startDate || !endDate) {
                return reportData;
            }
            
            return reportData.filter(item => {
                return item.reportDate >= startDate && item.reportDate <= endDate;
            });
        }

        // 添加数据验证函数
        function validateReportData(data) {
            const errors = [];
            
            if (!data.reportDate) errors.push('填报日期不能为空');
            if (!data.teamName) errors.push('团队名称不能为空');
            if (!data.shopName) errors.push('店铺名称不能为空');
            if (!data.serviceShift) errors.push('客服班次不能为空');
            if (!data.serviceStaff) errors.push('客服人员不能为空');
            if (!data.receptionCount || data.receptionCount < 0) errors.push('接待人数必须大于等于0');
            
            if (data.firstResponseTime && data.firstResponseTime < 0) errors.push('首次响应时长不能为负数');
            if (data.avgResponseTime && data.avgResponseTime < 0) errors.push('平均响应时长不能为负数');
            if (data.threeMinReplyRate && (data.threeMinReplyRate < 0 || data.threeMinReplyRate > 100)) {
                errors.push('3分钟人工回复率必须在0-100%之间');
            }
            if (data.paymentCount && data.paymentCount < 0) errors.push('支付人数不能为负数');
            if (data.inquiryConversionRate && (data.inquiryConversionRate < 0 || data.inquiryConversionRate > 100)) {
                errors.push('询单转化率必须在0-100%之间');
            }
            if (data.salesAmount && data.salesAmount < 0) errors.push('客服销售额不能为负数');
            if (data.satisfactionRate && (data.satisfactionRate < 0 || data.satisfactionRate > 100)) {
                errors.push('客服满意率必须在0-100%之间');
            }
            
            return errors;
        }

        // 页面性能优化：懒加载和虚拟滚动准备
        function optimizeTableRendering() {
            // 如果数据量很大，可以在这里实现虚拟滚动
            console.log('Table rendering optimized');
        }

        // 自动保存草稿功能
        function autoSaveDraft() {
            // 可以实现自动保存表单草稿的功能
            console.log('Auto save draft');
        }
        
        console.log('客服经营日报管理系统初始化完成');
    </script>
</body>

</html>