<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>表格列高亮方案示例</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: 'PingFang SC', 'Microsoft YaHei', sans-serif;
        }

        body {
            background-color: #f5f5f5;
            padding: 20px;
            color: #333;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }

        .header {
            padding: 20px;
            background: #fafafa;
            border-bottom: 1px solid #e8e8e8;
        }

        .header h1 {
            font-size: 18px;
            font-weight: 600;
            margin-bottom: 10px;
        }

        .scheme-selector {
            display: flex;
            gap: 10px;
            margin-bottom: 10px;
        }

        .scheme-btn {
            padding: 8px 16px;
            border: 1px solid #d9d9d9;
            background: white;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            transition: all 0.2s;
        }

        .scheme-btn.active {
            background: #1890ff;
            color: white;
            border-color: #1890ff;
        }

        .scheme-btn:hover {
            border-color: #1890ff;
            color: #1890ff;
        }

        .scheme-btn.active:hover {
            color: white;
        }

        .table-container {
            overflow-x: auto;
        }

        table {
            width: 100%;
            border-collapse: separate;
            border-spacing: 0;
            min-width: 1200px;
        }

        th {
            background: #fafafa;
            font-weight: 600;
            color: #666;
            border-bottom: 1px solid #e8e8e8;
            text-align: center;
            font-size: 14px;
            white-space: nowrap;
            padding: 12px 8px;
        }

        td {
            border-bottom: 1px solid #e8e8e8;
            color: #333;
            text-align: center;
            font-size: 14px;
            padding: 12px 8px;
        }

        tr:last-child td {
            border-bottom: none;
        }

        tr:hover td {
            background: rgba(24, 144, 255, 0.04);
        }

        /* 方案1：统一浅色背景 */
        .scheme-1 .highlight-column {
            background-color: rgba(24, 144, 255, 0.08) !important;
        }

        .scheme-1 .highlight-header {
            background-color: rgba(24, 144, 255, 0.12) !important;
            font-weight: 600;
        }

        .scheme-1 tr:hover .highlight-column {
            background-color: rgba(24, 144, 255, 0.15) !important;
        }

        /* 方案2：不同颜色区分 */
        .scheme-2 .highlight-profit-column {
            background-color: rgba(255, 193, 7, 0.1) !important;
        }

        .scheme-2 .highlight-profit-header {
            background-color: rgba(255, 193, 7, 0.15) !important;
            color: #d48806;
            font-weight: 600;
        }

        .scheme-2 .highlight-ratio-column {
            background-color: rgba(24, 144, 255, 0.1) !important;
        }

        .scheme-2 .highlight-ratio-header {
            background-color: rgba(24, 144, 255, 0.15) !important;
            color: #0958d9;
            font-weight: 600;
        }

        .scheme-2 tr:hover .highlight-profit-column {
            background-color: rgba(255, 193, 7, 0.18) !important;
        }

        .scheme-2 tr:hover .highlight-ratio-column {
            background-color: rgba(24, 144, 255, 0.18) !important;
        }

        /* 金额格式化 */
        .amount {
            font-family: 'Consolas', 'Monaco', monospace;
            font-weight: 500;
        }

        /* 百分比样式 */
        .percentage {
            font-weight: 600;
        }

        .percentage.positive {
            color: #52c41a;
        }

        .percentage.negative {
            color: #ff4d4f;
        }

        /* 总计行 */
        .total-row {
            background-color: #f0f2f5 !important;
            font-weight: 600;
        }

        .total-row td {
            border-top: 2px solid #e8e8e8;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>表格列高亮方案对比</h1>
            <div class="scheme-selector">
                <button class="scheme-btn active" onclick="switchScheme(1)">方案1：统一浅色背景</button>
                <button class="scheme-btn" onclick="switchScheme(2)">方案2：不同颜色区分</button>
            </div>
            <p style="color: #666; font-size: 14px;">点击上方按钮切换不同的高亮方案</p>
        </div>
        
        <div class="table-container">
            <table id="profit-table">
                <thead>
                    <tr>
                        <th>排名</th>
                        <th>团队名称</th>
                        <th>总销售额</th>
                        <th>总成本(不含运费)</th>
                        <th>自由现金流</th>
                        <th id="profit-header">当月边界利润</th>
                        <th id="ratio-header">当月边界比率</th>
                        <th>预计发货单量</th>
                        <th>处中成交额</th>
                        <th>总价</th>
                        <th>边界利润</th>
                        <th>边界比率</th>
                    </tr>
                </thead>
                <tbody id="table-body">
                    <!-- 数据将通过JavaScript动态生成 -->
                </tbody>
            </table>
        </div>
    </div>

    <script>
        // 模拟数据
        const tableData = [
            {
                rank: 1,
                teamName: "林村直营（原始）",
                totalSales: 34080.38,
                totalCost: 31156.38,
                cashFlow: 31156.38,
                monthlyProfit: 2368.72,
                monthlyRatio: 2,
                expectedOrders: 329,
                dealAmount: 34080.38,
                totalPrice: 0,
                boundaryProfit: 2990.78,
                boundaryRatio: 9.41
            },
            {
                rank: 2,
                teamName: "鑫云直营（原始）",
                totalSales: 27153.5,
                totalCost: 27153.5,
                cashFlow: 27153.5,
                monthlyProfit: 20550.45,
                monthlyRatio: 18,
                expectedOrders: 246,
                dealAmount: 27153.5,
                totalPrice: 0,
                boundaryProfit: 5436.69,
                boundaryRatio: 19.98
            },
            {
                rank: 3,
                teamName: "浩然直营（原始）",
                totalSales: 20227.45,
                totalCost: 19531.25,
                cashFlow: 19531.25,
                monthlyProfit: 9132.14,
                monthlyRatio: 12,
                expectedOrders: 243,
                dealAmount: 20227.45,
                totalPrice: 0,
                boundaryProfit: 3377.2,
                boundaryRatio: 17.29
            },
            {
                rank: 4,
                teamName: "凡竹商贸（全域）",
                totalSales: 19408.38,
                totalCost: 18960.88,
                cashFlow: 18960.88,
                monthlyProfit: 4200.03,
                monthlyRatio: 6,
                expectedOrders: 251,
                dealAmount: 19408.38,
                totalPrice: 0,
                boundaryProfit: 2143.63,
                boundaryRatio: 11.3
            },
            {
                rank: 5,
                teamName: "誉兰商贸（过境）",
                totalSales: 17837.1,
                totalCost: 17837.1,
                cashFlow: 17837.1,
                monthlyProfit: 31986.05,
                monthlyRatio: 24,
                expectedOrders: 300,
                dealAmount: 17837.1,
                totalPrice: 0,
                boundaryProfit: 12339.63,
                boundaryRatio: 69.18
            }
        ];

        let currentScheme = 1;

        // 切换方案
        function switchScheme(scheme) {
            currentScheme = scheme;
            
            // 更新按钮状态
            document.querySelectorAll('.scheme-btn').forEach(btn => btn.classList.remove('active'));
            event.target.classList.add('active');
            
            // 更新表格样式
            const container = document.querySelector('.container');
            container.className = `container scheme-${scheme}`;
            
            // 重新渲染表格以应用新样式
            renderTable();
        }

        // 渲染表格数据
        function renderTable() {
            const tbody = document.getElementById('table-body');
            const profitHeader = document.getElementById('profit-header');
            const ratioHeader = document.getElementById('ratio-header');
            
            // 清空现有内容
            tbody.innerHTML = '';
            
            // 设置表头样式
            if (currentScheme === 1) {
                profitHeader.className = 'highlight-header';
                ratioHeader.className = 'highlight-header';
            } else {
                profitHeader.className = 'highlight-profit-header';
                ratioHeader.className = 'highlight-ratio-header';
            }
            
            tableData.forEach(item => {
                const row = document.createElement('tr');
                
                // 判断百分比的正负
                const getRatioClass = (ratio) => {
                    return ratio >= 0 ? 'percentage positive' : 'percentage negative';
                };

                // 设置列样式类名
                let profitColumnClass, ratioColumnClass;
                if (currentScheme === 1) {
                    profitColumnClass = 'amount highlight-column';
                    ratioColumnClass = 'highlight-column';
                } else {
                    profitColumnClass = 'amount highlight-profit-column';
                    ratioColumnClass = 'highlight-ratio-column';
                }
                
                row.innerHTML = `
                    <td>${item.rank}</td>
                    <td>${item.teamName}</td>
                    <td class="amount">${item.totalSales.toFixed(2)}</td>
                    <td class="amount">${item.totalCost.toFixed(2)}</td>
                    <td class="amount">${item.cashFlow.toFixed(2)}</td>
                    <td class="${profitColumnClass}">${item.monthlyProfit.toFixed(2)}</td>
                    <td class="${ratioColumnClass} ${getRatioClass(item.monthlyRatio)}">${item.monthlyRatio}%</td>
                    <td>${item.expectedOrders}</td>
                    <td class="amount">${item.dealAmount.toFixed(2)}</td>
                    <td>${item.totalPrice}</td>
                    <td class="amount">${item.boundaryProfit.toFixed(2)}</td>
                    <td class="${getRatioClass(item.boundaryRatio)}">${item.boundaryRatio}%</td>
                `;
                
                tbody.appendChild(row);
            });
            
            // 添加总计行
            const totalRow = document.createElement('tr');
            totalRow.className = 'total-row';
            
            const totalSales = tableData.reduce((sum, item) => sum + item.totalSales, 0);
            const totalCost = tableData.reduce((sum, item) => sum + item.totalCost, 0);
            const totalCashFlow = tableData.reduce((sum, item) => sum + item.cashFlow, 0);
            const totalMonthlyProfit = tableData.reduce((sum, item) => sum + item.monthlyProfit, 0);
            const totalOrders = tableData.reduce((sum, item) => sum + item.expectedOrders, 0);
            const totalDealAmount = tableData.reduce((sum, item) => sum + item.dealAmount, 0);
            const totalBoundaryProfit = tableData.reduce((sum, item) => sum + item.boundaryProfit, 0);

            // 设置总计行的列样式
            let totalProfitClass, totalRatioClass;
            if (currentScheme === 1) {
                totalProfitClass = 'amount highlight-column';
                totalRatioClass = 'highlight-column';
            } else {
                totalProfitClass = 'amount highlight-profit-column';
                totalRatioClass = 'highlight-ratio-column';
            }
            
            totalRow.innerHTML = `
                <td>合计</td>
                <td></td>
                <td class="amount">${totalSales.toFixed(2)}</td>
                <td class="amount">${totalCost.toFixed(2)}</td>
                <td class="amount">${totalCashFlow.toFixed(2)}</td>
                <td class="${totalProfitClass}">${totalMonthlyProfit.toFixed(2)}</td>
                <td class="${totalRatioClass}"></td>
                <td>${totalOrders}</td>
                <td class="amount">${totalDealAmount.toFixed(2)}</td>
                <td>0</td>
                <td class="amount">${totalBoundaryProfit.toFixed(2)}</td>
                <td></td>
            `;
            
            tbody.appendChild(totalRow);
        }

        // 页面加载完成后渲染表格
        document.addEventListener('DOMContentLoaded', function() {
            document.querySelector('.container').className = 'container scheme-1';
            renderTable();
        });
    </script>
</body>
</html>
