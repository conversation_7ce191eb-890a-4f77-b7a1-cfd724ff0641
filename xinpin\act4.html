<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>商品活动报备管理 - 电商管理系统</title>
    <link rel="stylesheet" href="https://unpkg.com/element-ui/lib/theme-chalk/index.css">
    <link rel="icon" href="https://panhan.xin/assets/favicon-DfI5CYqk.ico">
    <style>
        body {
            font-family: "Helvetica Neue", Helvetica, "PingFang SC", "Hiragino Sans GB", "Microsoft YaHei", sans-serif;
            margin: 0;
            padding: 0;
            background-color: #f5f7fa;
            color: #333;
        }
        
        .app-container {
            padding: 20px;
        }
        
        .page-header {
            background-color: #fff;
            padding: 16px 20px;
            box-shadow: 0 1px 4px rgba(0, 0, 0, 0.1);
            margin-bottom: 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .page-title {
            margin: 0;
            font-size: 20px;
            font-weight: 500;
        }
        
        .action-buttons {
            display: flex;
            gap: 15px;
        }
        
        .card {
            background-color: #fff;
            border-radius: 4px;
            box-shadow: 0 1px 4px rgba(0, 0, 0, 0.1);
            margin-bottom: 20px;
            padding: 20px;
        }
        
        .card-title {
            font-size: 16px;
            font-weight: 500;
            margin: 0 0 20px;
            padding-bottom: 10px;
            border-bottom: 1px solid #ebeef5;
            color: #303133;
        }
        
        .search-area {
            margin-bottom: 20px;
            background: #fff;
            padding: 20px;
            border-radius: 4px;
        }
        
        .form-group {
            margin-bottom: 20px;
        }
        
        .product-card {
            /* 用于弹窗内显示已选商品 */
            border: 1px solid #ebeef5;
            border-radius: 4px;
            padding: 15px;
            margin-bottom: 15px;
            position: relative;
            display: flex;
            align-items: flex-start;
        }
        
        .product-card .remove-btn {
            position: absolute;
            top: 10px;
            right: 10px;
            font-size: 18px;
            color: #f56c6c;
            cursor: pointer;
        }
        
        .product-card .remove-btn.disabled {
            color: #c0c4cc;
            cursor: not-allowed;
        }
        
        .product-image-container {
            /* 弹窗内商品图片 */
            width: 100px;
            height: 100px;
            margin-right: 15px;
            border-radius: 4px;
            overflow: hidden;
            background-color: #f0f2f5;
            flex-shrink: 0;
        }
        
        .product-image-container .el-image {
            width: 100%;
            height: 100%;
        }
        
        .image-slot-display {
            display: flex;
            justify-content: center;
            align-items: center;
            width: 100%;
            height: 100%;
            background: #f5f7fa;
            color: #909399;
            font-size: 12px;
        }
        
        .table-product-image-container {
            /* 表格内商品图片 */
            width: 50px;
            height: 50px;
            margin-right: 10px;
            border-radius: 3px;
            overflow: hidden;
            background-color: #f0f2f5;
            flex-shrink: 0;
        }
        
        .table-product-image-container .el-image {
            width: 100%;
            height: 100%;
        }
        
        .product-details {
            flex: 1;
            display: flex;
            flex-direction: column;
        }
        
        .product-details h4 {
            margin: 0 0 8px;
            font-size: 15px;
            line-height: 1.3;
        }
        
        .product-meta-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
            gap: 8px;
            font-size: 13px;
            color: #606266;
        }
        
        .product-meta-grid .meta-label {
            color: #909399;
            margin-right: 5px;
        }
        
        .activity-params {
            padding-left: 20px;
            margin-top: 10px;
            border-left: 2px solid #ebeef5;
        }
        
        .info-text {
            font-size: 13px;
            color: #909399;
        }
        
        .status-tag {
            padding: 2px 8px;
            border-radius: 2px;
            font-size: 12px;
        }
        
        .status-pending {
            background-color: #e6a23c;
            color: #fff;
        }
        
        .status-approved {
            background-color: #67c23a;
            color: #fff;
        }
        
        .status-rejected {
            background-color: #f56c6c;
            color: #fff;
        }
        
        .form-tip {
            font-size: 13px;
            color: #909399;
            margin-top: 5px;
        }
        
        .error-tip {
            color: #f56c6c;
        }
        
        .table-operations {
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        .table-operations button {
            padding: 4px 8px;
            font-size: 12px;
        }
        
        .dialog-form .el-form-item {
            margin-bottom: 22px;
        }
        
        .activity-list {
            margin-top: 8px;
            padding-left: 0;
        }
        
        .activity-list-item {
            margin-bottom: 5px;
            list-style-type: none;
            display: flex;
            align-items: center;
        }
        
        .activity-icon {
            margin-right: 5px;
            font-size: 14px;
        }
        
        .small-tag {
            height: 20px;
            line-height: 18px;
            font-size: 10px;
            padding: 0 5px;
            margin-left: 5px;
        }
        
        .table-product-entry {
            display: flex;
            align-items: flex-start;
            margin-bottom: 10px;
            padding-bottom: 10px;
            border-bottom: 1px dashed #eee;
        }
        
        .table-product-entry:last-child {
            margin-bottom: 0;
            border-bottom: none;
            padding-bottom: 0;
        }
        
        .table-product-text-details {
            flex: 1;
            font-size: 13px;
            line-height: 1.5;
        }
        
        .table-product-name {
            font-weight: 500;
            color: #303133;
            margin-bottom: 3px;
        }
        
        .table-product-sub-info {
            font-size: 12px;
            color: #606266;
        }
        
        .table-product-sub-info .meta-label {
            color: #909399;
        }
        
        .edit-dialog-section-title {
            font-size: 15px;
            font-weight: bold;
            color: #303133;
            margin-bottom: 10px;
            padding-bottom: 5px;
            border-bottom: 1px solid #eee;
        }
    </style>
</head>

<body>
    <div id="app" class="app-container">
        <div class="page-header">
            <h1 class="page-title">商品活动报备管理</h1>
            <div class="action-buttons">
                <el-button type="primary" icon="el-icon-plus" size="small" @click="showAddReportDialog">新品产品报备</el-button>
                <el-button icon="el-icon-download" size="small">导出数据</el-button>
            </div>
        </div>

        <div class="search-area">
            <el-form :inline="true" :model="searchForm" class="demo-form-inline">
                <el-form-item label="商品名称/ID/代码">
                    <el-input v-model="searchForm.productKeyword" placeholder="商品名称/ID/代码" clearable></el-input>
                </el-form-item>
                <el-form-item label="报备日期">
                    <el-date-picker v-model="searchForm.dateRange" type="daterange" range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期"></el-date-picker>
                </el-form-item>
                <el-form-item label="活动类型">
                    <el-select v-model="searchForm.activityType" placeholder="请选择活动类型" clearable>
                        <el-option label="新品主推让利" value="promo"></el-option>
                        <el-option label="新品赛马锁品" value="race"></el-option>
                        <el-option label="新品亏损扶持" value="support"></el-option>
                    </el-select>
                </el-form-item>
                <el-form-item label="审核状态">
                    <el-select v-model="searchForm.reviewStatus" placeholder="请选择审核状态" clearable>
                        <el-option label="全部" value=""></el-option>
                        <el-option label="待审核" value="pending"></el-option>
                        <el-option label="已通过" value="approved"></el-option>
                        <el-option label="已拒绝" value="rejected"></el-option>
                    </el-select>
                </el-form-item>
                <el-form-item>
                    <el-button type="primary" @click="handleSearch">查询</el-button>
                    <el-button @click="resetSearch">重置</el-button>
                </el-form-item>
            </el-form>
        </div>

        <div class="card">
            <el-table ref="reportTable" :data="reportRecords" style="width: 100%" border v-loading="tableLoading">
                <el-table-column prop="reportId" label="报备ID" width="120" fixed="left"></el-table-column>
                <el-table-column label="商品信息" min-width="380">
                    <template slot-scope="scope">
                        <div v-if="scope.row.products && scope.row.products.length">
                            <div v-for="(product, index) in scope.row.products" :key="product.id + '-' + index" class="table-product-entry">
                                <div class="table-product-image-container">
                                    <el-image :src="product.image" fit="cover" :preview-src-list="scope.row.products.map(p=>p.image)" :initial-index="index">
                                        <div slot="error" class="image-slot-display">无图</div>
                                    </el-image>
                                </div>
                                <div class="table-product-text-details">
                                    <div class="table-product-name">{{ product.name }}</div>
                                    <div class="table-product-sub-info">
                                        <span class="meta-label">ID:</span> {{ product.id }} | 
                                        <span class="meta-label">代码:</span> {{ product.code }} <br>
                                        <span class="meta-label">店铺:</span> {{ product.shop }} | 
                                        <span class="meta-label">平台:</span> {{ product.platform }} | 
                                        <span class="meta-label">团队:</span> {{ product.team }}
                                    </div>
                                </div>
                            </div>
                        </div>
                        <span v-else>--</span>
                    </template>
                </el-table-column>
                <el-table-column label="活动信息" min-width="200">
                    <template slot-scope="scope">
                        <ul class="activity-list" v-if="scope.row.activities && scope.row.activities.length">
                            <li v-for="activity in scope.row.activities" :key="activity.type" class="activity-list-item">
                                <i class="el-icon-promotion activity-icon"></i>
                                {{ activity.name }}
                                <el-tag v-if="activity.requireReview" size="mini" class="small-tag" :type="getStatusType(activity.reviewStatus)">
                                    {{ getStatusText(activity.reviewStatus) }}
                                </el-tag>
                            </li>
                        </ul>
                        <span v-else>--</span>
                    </template>
                </el-table-column>
                <el-table-column label="活动日期" min-width="180">
                    <template slot-scope="scope">
                        <div v-if="scope.row.activities && scope.row.activities.length">
                            <div v-for="activity in scope.row.activities" :key="activity.type" style="margin-bottom:3px; font-size: 13px;">
                                {{ formatDate(activity.startDate) }} 至 {{ formatDate(activity.endDate) }}
                            </div>
                        </div>
                        <span v-else>--</span>
                    </template>
                </el-table-column>
                <el-table-column label="录入信息" min-width="180">
                    <template slot-scope="scope">
                        <div style="font-size:13px;">录入人：{{ scope.row.creator || '--' }}</div>
                        <div style="font-size:13px;">录入时间：{{ formatDateTime(scope.row.createTime) }}</div>
                    </template>
                </el-table-column>
                <el-table-column label="操作" width="190" fixed="right">
                    <template slot-scope="scope">
                        <div class="table-operations">
                            <el-button size="mini" @click="viewReportDetail(scope.row)">查看</el-button>
                            <el-button size="mini" type="primary" @click="handleEditReport(scope.row)">编辑/追加商品</el-button>
                        </div>
                    </template>
                </el-table-column>
            </el-table>
            <div style="margin-top: 20px; text-align: right;">
                <el-pagination @size-change="handleSizeChange" @current-change="handleCurrentChange" :current-page="pagination.currentPage" :page-sizes="[10, 20, 50, 100]" :page-size="pagination.pageSize" layout="total, sizes, prev, pager, next, jumper" :total="pagination.total"></el-pagination>
            </div>
        </div>

        <el-dialog title="新品产品报备" :visible.sync="dialogVisible" width="75%" :before-close="handleDialogClose" top="5vh">
            <div class="dialog-form">
                <div class="card">
                    <h2 class="card-title">选择商品 (最多3个)</h2>
                    <div class="form-group">
                        <el-autocomplete v-model="productIdInput" :fetch-suggestions="queryProductsForAdd" placeholder="搜索商品ID、名称或代码以添加" @select="handleProductSelectForAdd" :disabled="formData.products.length >= 3" style="width: 100%;">
                            <template slot="suffix"> <i class="el-icon-search"></i> </template>
                        </el-autocomplete>
                        <div class="form-tip" v-if="formData.products.length >= 3"> <i class="el-icon-warning-outline"></i> 已达到3个商品上限 </div>
                    </div>

                    <div v-if="formData.products.length > 0">
                        <div class="product-card" v-for="(product, index) in formData.products" :key="product.id + '-' + index">
                            <i class="el-icon-close remove-btn" @click="removeProductFromAdd(index)"></i>
                            <div class="product-image-container">
                                <el-image :src="product.image" fit="cover" :preview-src-list="[product.image]">
                                    <div slot="error" class="image-slot-display">无图</div>
                                </el-image>
                            </div>
                            <div class="product-details">
                                <h4>{{ product.name }}</h4>
                                <div class="product-meta-grid">
                                    <div><span class="meta-label">商品ID:</span><span>{{ product.id }}</span></div>
                                    <div><span class="meta-label">商品代码:</span><span>{{ product.code }}</span></div>
                                    <div><span class="meta-label">店铺名:</span><span>{{ product.shop }}</span></div>
                                    <div><span class="meta-label">平台:</span><span>{{ product.platform }}</span></div>
                                    <div><span class="meta-label">团队:</span><span>{{ product.team }}</span></div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <el-empty v-else description="暂未选择商品"></el-empty>
                </div>

                <div class="card">
                    <h2 class="card-title">活动报备</h2>
                    <div class="form-group">
                        <el-checkbox-group v-model="selectedActivities">
                            <div style="margin-bottom: 15px;" v-for="activity_template in activities" :key="activity_template.type">
                                <el-checkbox :label="activity_template.type">{{ activity_template.name }}</el-checkbox>
                                <div class="activity-params" v-if="selectedActivities.includes(activity_template.type)">
                                    <el-row :gutter="20" style="margin-bottom: 15px;">
                                        <el-col :span="8">
                                            <el-form-item label="生效日期">
                                                <el-date-picker v-model="activities.find(a=>a.type===activity_template.type).startDate" type="date" placeholder="选择生效日期" :disabled="activity_template.type === 'promo' && isAutomaticDateCalculation" style="width: 100%;"></el-date-picker>
                                                <div class="form-tip" v-if="activity_template.type === 'promo' && isAutomaticDateCalculation"><i class="el-icon-info"></i> 系统自动计算</div>
                                            </el-form-item>
                                        </el-col>
                                        <el-col :span="8">
                                            <el-form-item label="结束日期">
                                                <el-date-picker v-model="activities.find(a=>a.type===activity_template.type).endDate" type="date" placeholder="选择结束日期" :disabled="activity_template.type === 'promo' && isAutomaticDateCalculation" style="width: 100%;"></el-date-picker>
                                                <div class="form-tip" v-if="activity_template.type === 'promo' && isAutomaticDateCalculation"><i class="el-icon-info"></i> 系统自动计算</div>
                                            </el-form-item>
                                        </el-col>
                                        <el-col :span="8" v-if="activity_template.type === 'promo'">
                                            <el-form-item label="手动设置日期">
                                                <el-switch v-model="manualDateSetting" @change="toggleDateCalculation"></el-switch>
                                            </el-form-item>
                                        </el-col>
                                    </el-row>
                                    <el-row :gutter="20">
                                        <el-col :span="8">
                                            <el-form-item label="录入人员">
                                                <el-input v-model="formData.creator" disabled></el-input>
                                            </el-form-item>
                                        </el-col>
                                        <el-col :span="8">
                                            <el-form-item label="录入时间">
                                                <el-input v-model="formData.createTimeDisplay" disabled></el-input>
                                            </el-form-item>
                                        </el-col>
                                    </el-row>
                                    <div style="margin-top: 15px;" v-if="activity_template.type === 'promo'">
                                        <div class="form-title">达标条件</div>
                                        <div class="info-text"><i class="el-icon-info"></i> 第一个月广告支出需>300元才能获得活动奖励</div>
                                    </div>
                                    <div v-if="activity_template.requireReview" style="margin-top: 15px;">
                                        <div class="form-title">审核状态</div><span class="status-tag status-pending">待审核</span>
                                        <div class="info-text" style="margin-top: 5px;"><i class="el-icon-info"></i> 该活动需管理员审核后生效</div>
                                    </div>
                                </div>
                            </div>
                        </el-checkbox-group>
                        <div class="error-tip" v-if="!selectedActivities.length && showValidationErrors"><i class="el-icon-warning"></i> 请至少选择一种活动类型</div>
                    </div>
                </div>
            </div>
            <span slot="footer" class="dialog-footer">
                <el-button @click="resetForm">重置</el-button>
                <el-button type="primary" @click="submitForm">提交报备</el-button>
            </span>
        </el-dialog>

        <el-dialog :title="'编辑报备 (ID: ' + editForm.reportId + ') - 追加商品'" :visible.sync="editDialogVisible" width="75%" :before-close="handleEditDialogClose" top="5vh">
            <div class="dialog-form">
                <div class="card">
                    <h3 class="edit-dialog-section-title">已有商品 (不可移除)</h3>
                    <div v-if="editForm.existingProducts && editForm.existingProducts.length > 0">
                        <div class="product-card" v-for="(product) in editForm.existingProducts" :key="'existing-' + product.id">
                            <div class="product-image-container">
                                <el-image :src="product.image" fit="cover" :preview-src-list="[product.image]">
                                    <div slot="error" class="image-slot-display">无图</div>
                                </el-image>
                            </div>
                            <div class="product-details">
                                <h4>{{ product.name }}</h4>
                                <div class="product-meta-grid">
                                    <div><span class="meta-label">商品ID:</span><span>{{ product.id }}</span></div>
                                    <div><span class="meta-label">商品代码:</span><span>{{ product.code }}</span></div>
                                    <div><span class="meta-label">店铺名:</span><span>{{ product.shop }}</span></div>
                                    <div><span class="meta-label">平台:</span><span>{{ product.platform }}</span></div>
                                    <div><span class="meta-label">团队:</span><span>{{ product.team }}</span></div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <el-empty v-else description="暂无已有商品"></el-empty>
                </div>

                <div class="card">
                    <h3 class="edit-dialog-section-title">追加新商品 (当前 {{ editForm.existingProducts.length + editForm.newlyAddedProducts.length }}/3 个商品)</h3>
                    <div class="form-group" v-if="(editForm.existingProducts.length + editForm.newlyAddedProducts.length) < 3">
                        <el-autocomplete v-model="editProductIdInput" :fetch-suggestions="queryProductsForEdit" placeholder="搜索商品ID、名称或代码以添加" @select="handleAddProductToEditForm" style="width: 100%;" :disabled="(editForm.existingProducts.length + editForm.newlyAddedProducts.length) >= 3">
                            <template slot="suffix"><i class="el-icon-search"></i></template>
                        </el-autocomplete>
                    </div>
                    <div class="form-tip error-tip" v-else>已达到3个商品上限，无法继续追加。</div>

                    <div v-if="editForm.newlyAddedProducts && editForm.newlyAddedProducts.length > 0" style="margin-top:15px;">
                        <h4>本次新添加商品：</h4>
                        <div class="product-card" v-for="(product, index) in editForm.newlyAddedProducts" :key="'new-' + product.id + '-' + index">
                            <i class="el-icon-close remove-btn" @click="removeNewlyAddedEditProduct(index)"></i>
                            <div class="product-image-container">
                                <el-image :src="product.image" fit="cover" :preview-src-list="[product.image]">
                                    <div slot="error" class="image-slot-display">无图</div>
                                </el-image>
                            </div>
                            <div class="product-details">
                                <h4>{{ product.name }}</h4>
                                <div class="product-meta-grid">
                                    <div><span class="meta-label">商品ID:</span><span>{{ product.id }}</span></div>
                                    <div><span class="meta-label">商品代码:</span><span>{{ product.code }}</span></div>
                                    <div><span class="meta-label">店铺名:</span><span>{{ product.shop }}</span></div>
                                    <div><span class="meta-label">平台:</span><span>{{ product.platform }}</span></div>
                                    <div><span class="meta-label">团队:</span><span>{{ product.team }}</span></div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="card">
                    <h2 class="card-title">编辑活动报备信息</h2>
                    <div class="form-group">
                        <el-checkbox-group v-model="editForm.selectedActivities" @change="handleEditActivitiesChange">
                            <div style="margin-bottom: 15px;" v-for="activity_template in activities" :key="activity_template.type">
                                <el-checkbox :label="activity_template.type">{{ activity_template.name }}</el-checkbox>
                                <div class="activity-params" v-if="editForm.selectedActivities.includes(activity_template.type)">
                                    <el-row :gutter="20" style="margin-bottom: 15px;">
                                        <el-col :span="8">
                                            <el-form-item label="生效日期">
                                                <el-date-picker v-model="editForm.activitiesData.find(a=>a.type===activity_template.type).startDate" type="date" placeholder="选择生效日期" :disabled="activity_template.type === 'promo' && editIsAutomaticDateCalculation" style="width: 100%;"></el-date-picker>
                                                <div class="form-tip" v-if="activity_template.type === 'promo' && editIsAutomaticDateCalculation"><i class="el-icon-info"></i> 系统自动计算</div>
                                            </el-form-item>
                                        </el-col>
                                        <el-col :span="8">
                                            <el-form-item label="结束日期">
                                                <el-date-picker v-model="editForm.activitiesData.find(a=>a.type===activity_template.type).endDate" type="date" placeholder="选择结束日期" :disabled="activity_template.type === 'promo' && editIsAutomaticDateCalculation" style="width: 100%;"></el-date-picker>
                                                <div class="form-tip" v-if="activity_template.type === 'promo' && editIsAutomaticDateCalculation"><i class="el-icon-info"></i> 系统自动计算</div>
                                            </el-form-item>
                                        </el-col>
                                        <el-col :span="8" v-if="activity_template.type === 'promo'">
                                            <el-form-item label="手动设置日期">
                                                <el-switch v-model="editManualDateSetting" @change="toggleEditDateCalculation"></el-switch>
                                            </el-form-item>
                                        </el-col>
                                    </el-row>
                                    <el-row :gutter="20">
                                        <el-col :span="8">
                                            <el-form-item label="录入人员">
                                                <el-input v-model="editForm.creator" disabled></el-input>
                                            </el-form-item>
                                        </el-col>
                                        <el-col :span="8">
                                            <el-form-item label="录入时间">
                                                <el-input v-model="editForm.createTimeDisplay" disabled></el-input>
                                            </el-form-item>
                                        </el-col>
                                    </el-row>
                                    <div style="margin-top: 15px;" v-if="activity_template.type === 'promo'">
                                        <div class="form-title">达标条件</div>
                                        <div class="info-text"><i class="el-icon-info"></i> 第一个月广告支出需>300元才能获得活动奖励</div>
                                    </div>
                                    <div v-if="activity_template.requireReview" style="margin-top: 15px;">
                                        <div class="form-title">审核状态</div>
                                        <el-tag size="medium" :type="getStatusType(editForm.activitiesData.find(a=>a.type===activity_template.type).reviewStatus)">{{ getStatusText(editForm.activitiesData.find(a=>a.type===activity_template.type).reviewStatus) }}</el-tag>
                                        <div class="info-text" style="margin-top: 5px;" v-if="editForm.activitiesData.find(a=>a.type===activity_template.type).reviewStatus === 'pending'"><i class="el-icon-info"></i> 编辑后若活动日期等关键信息变更，可能需重新审核。</div>
                                    </div>
                                </div>
                            </div>
                        </el-checkbox-group>
                        <div class="error-tip" v-if="editForm.selectedActivities && !editForm.selectedActivities.length && editShowValidationErrors"><i class="el-icon-warning"></i> 请至少选择一种活动类型</div>
                    </div>
                </div>
            </div>
            <span slot="footer" class="dialog-footer">
                <el-button @click="editDialogVisible = false">取消</el-button>
                <el-button type="primary" @click="submitEditForm" :disabled="!isEditFormChanged()">保存更改</el-button> 
            </span>
        </el-dialog>

        <el-dialog title="报备详情" :visible.sync="detailDialogVisible" width="70%" top="5vh">
            <div v-if="currentReport" class="report-detail-view">
                <el-descriptions title="报备基础信息" :column="2" border>
                    <el-descriptions-item label="报备ID" :span="2">{{ currentReport.reportId }}</el-descriptions-item>
                    <el-descriptions-item label="录入人员">{{ currentReport.creator }}</el-descriptions-item>
                    <el-descriptions-item label="录入时间">{{ formatDateTime(currentReport.createTime) }}</el-descriptions-item>
                </el-descriptions>

                <el-divider content-position="left">关联商品信息</el-divider>
                <div v-if="currentReport.products && currentReport.products.length">
                    <div v-for="(product, pIdx) in currentReport.products" :key="'detail-prod-'+pIdx" class="product-card" style="background-color: #fafafa;">
                        <div class="product-image-container">
                            <el-image :src="product.image" fit="cover" :preview-src-list="currentReport.products.map(p => p.image)" :initial-index="pIdx">
                                <div slot="error" class="image-slot-display">无图</div>
                            </el-image>
                        </div>
                        <div class="product-details">
                            <h4>{{ product.name }}</h4>
                            <div class="product-meta-grid">
                                <div><span class="meta-label">商品ID:</span><span>{{ product.id }}</span></div>
                                <div><span class="meta-label">商品代码:</span><span>{{ product.code }}</span></div>
                                <div><span class="meta-label">店铺名:</span><span>{{ product.shop }}</span></div>
                                <div><span class="meta-label">平台:</span><span>{{ product.platform }}</span></div>
                                <div><span class="meta-label">团队:</span><span>{{ product.team }}</span></div>
                            </div>
                        </div>
                    </div>
                </div>
                <el-empty v-else description="暂无关联商品"></el-empty>

                <el-divider content-position="left">活动信息</el-divider>
                <el-descriptions :column="1" border>
                    <el-descriptions-item v-for="activity in currentReport.activities" :key="activity.type" :label="activity.name">
                        <div style="margin-bottom: 5px;">生效时间: {{ formatDate(activity.startDate) }} 至 {{ formatDate(activity.endDate) }}</div>
                        <div v-if="activity.requireReview" style="margin-bottom: 5px;">
                            审核状态:
                            <el-tag size="mini" :type="getStatusType(activity.reviewStatus)">
                                {{ getStatusText(activity.reviewStatus) }}
                            </el-tag>
                        </div>
                    </el-descriptions-item>
                </el-descriptions>
            </div>
            <span slot="footer" class="dialog-footer"> <el-button @click="detailDialogVisible = false">关闭</el-button> </span>
        </el-dialog>
    </div>

    <script src="https://unpkg.com/vue@2.6.14/dist/vue.js"></script>
    <script src="https://unpkg.com/element-ui/lib/index.js"></script>
    <script>
        new Vue({
            el: '#app',
            data() {
                return {
                    searchForm: {
                        productKeyword: '',
                        dateRange: [],
                        activityType: '',
                        reviewStatus: ''
                    },
                    tableLoading: false,
                    reportRecords: [],
                    pagination: {
                        currentPage: 1,
                        pageSize: 10,
                        total: 0
                    },
                    dialogVisible: false,
                    detailDialogVisible: false,
                    currentReport: null,
                    productIdInput: '',
                    formData: {
                        creator: '张小明',
                        createTime: null,
                        createTimeDisplay: '系统自动生成',
                        products: [],
                    },
                    selectedActivities: [],
                    isAutomaticDateCalculation: true,
                    manualDateSetting: false,
                    showValidationErrors: false,
                    activities: [{
                        type: 'promo',
                        name: '新品主推让利',
                        requireReview: false,
                        startDate: null,
                        endDate: null
                    }, {
                        type: 'race',
                        name: '新品赛马锁品',
                        requireReview: true,
                        startDate: null,
                        endDate: null,
                        reviewStatus: 'pending'
                    }, {
                        type: 'support',
                        name: '新品亏损扶持',
                        requireReview: true,
                        startDate: null,
                        endDate: null,
                        reviewStatus: 'pending'
                    }],
                    productDatabase: [],
                    editDialogVisible: false,
                    editingReportOriginal: null,
                    editProductIdInput: '',
                    editForm: {
                        reportId: '',
                        existingProducts: [],
                        newlyAddedProducts: [],
                        selectedActivities: [],
                        activitiesData: [],
                        creator: '',
                        createTimeDisplay: '',
                    },
                    editIsAutomaticDateCalculation: true,
                    editManualDateSetting: false,
                    editShowValidationErrors: false
                }
            },
            created() {
                this.generateProductDatabase(100);
            },
            mounted() {
                this.loadReportData();
                this.activities.forEach(a => {
                    this.$set(a, 'startDate', null);
                    this.$set(a, 'endDate', null);
                });
                this.calculateDefaultDates();
            },
            methods: {
                generateProductDatabase(count) {
                    const productNames = ["时尚连衣裙", "休闲运动鞋", "智能蓝牙音箱", "纯棉婴儿服", "多功能料理机", "高清液晶电视", "办公笔记本电脑", "户外登山包", "家用吸尘器", "陶瓷餐具套装"];
                    const shops = ["潮流旗舰店", "居家生活馆", "数码先锋店", "母婴专护之家", "运动装备营"];
                    const platforms = ["天猫", "京东", "淘宝", "拼多多", "苏宁易购"];
                    const teams = ["A组", "B组", "C组", "D组", "E组"];
                    const imageBaseUrl = "https://images.unsplash.com/photo-";
                    const imageUrls = [
                        "1585250012399-358954688313?ixlib=rb-1.2.1&auto=format&fit=crop&w=300&q=80",
                        "1595950653106-069305c5a326?ixlib=rb-1.2.1&auto=format&fit=crop&w=300&q=80",
                        "1618384887929-16ec33fab9ef?ixlib=rb-1.2.1&auto=format&fit=crop&w=300&q=80",
                        "1522771048208-3d515886623e?ixlib=rb-1.2.1&auto=format&fit=crop&w=300&q=80",
                        "1568207739882-652497a05e72?ixlib=rb-1.2.1&auto=format&fit=crop&w=300&q=80",
                        "1526797593419-4f0f0b87e52c?ixlib=rb-1.2.1&auto=format&fit=crop&w=300&q=80",
                        "1496171699190-0ab39d6a1711?ixlib=rb-1.2.1&auto=format&fit=crop&w=300&q=80",
                        "1501990609044-800958a98f77?ixlib=rb-1.2.1&auto=format&fit=crop&w=300&q=80",
                        "1557052900-00965630000a?ixlib=rb-1.2.1&auto=format&fit=crop&w=300&q=80",
                        "1556901369-0715813585cc?ixlib=rb-1.2.1&auto=format&fit=crop&w=300&q=80"
                    ];
                    // 添加一些商品代码相同的商品用于测试一致性规则
                    const baseProductsForSameCode = [{
                        id_suffix: 'VariantA',
                        name_suffix: '(红)'
                    }, {
                        id_suffix: 'VariantB',
                        name_suffix: '(蓝)'
                    }, {
                        id_suffix: 'VariantC',
                        name_suffix: '(绿)'
                    }];

                    for (let i = 1; i <= count; i++) {
                        const pIndex = i % productNames.length;
                        const baseCode = `SKU-${String(10000 + pIndex).padStart(5, '0')}`; // 确保一些商品有相同的基础代码
                        const variant = baseProductsForSameCode[i % baseProductsForSameCode.length];

                        this.productDatabase.push({
                            id: `P${String(i).padStart(3, '0')}${variant.id_suffix}`,
                            code: baseCode, // 核心：让一些商品共享code
                            name: `${productNames[pIndex]} ${variant.name_suffix} (${teams[i % teams.length]}款)`,
                            image: imageBaseUrl + imageUrls[i % imageUrls.length],
                            shop: shops[i % shops.length],
                            platform: platforms[i % platforms.length],
                            team: teams[i % teams.length]
                        });
                    }
                },
                showAddReportDialog() {
                    this.resetForm();
                    this.dialogVisible = true;
                    this.formData.createTime = new Date();
                    this.formData.createTimeDisplay = this.formatDateTime(this.formData.createTime);
                    this.calculateDefaultDates();
                },
                handleSearch() {
                    this.pagination.currentPage = 1;
                    this.loadReportData();
                },
                resetSearch() {
                    this.searchForm = {
                        productKeyword: '',
                        dateRange: [],
                        activityType: '',
                        reviewStatus: ''
                    };
                    this.loadReportData();
                },
                handleSizeChange(val) {
                    this.pagination.pageSize = val;
                    this.loadReportData();
                },
                handleCurrentChange(val) {
                    this.pagination.currentPage = val;
                    this.loadReportData();
                },
                loadReportData() {
                    this.tableLoading = true;
                    setTimeout(() => {
                        // 修正 mockData 以符合商品代码一致性规则
                        const productWithSameCode1 = this.productDatabase.find(p => p.code === this.productDatabase[0].code && p.id !== this.productDatabase[0].id) || this.productDatabase[10]; // 找一个code相同的，或者随便拿一个
                        const productWithSameCode2 = this.productDatabase.find(p => p.code === this.productDatabase[0].code && p.id !== this.productDatabase[0].id && p.id !== productWithSameCode1.id) || this.productDatabase[20];

                        const mockData = [{
                            reportId: 'RPT20250501001',
                            products: [ // 这两个商品现在应该有相同的 productCode
                                this.productDatabase[0],
                                productWithSameCode1
                            ],
                            activities: [{
                                type: 'promo',
                                name: '新品主推让利',
                                startDate: new Date('2025-05-10'),
                                endDate: new Date('2025-05-31'),
                                requireReview: false
                            }, {
                                type: 'race',
                                name: '新品赛马锁品',
                                startDate: new Date('2025-05-10'),
                                endDate: new Date('2025-06-30'),
                                requireReview: true,
                                reviewStatus: 'approved'
                            }],
                            creator: '张小明',
                            createTime: new Date('2025-05-01 09:30:25')
                        }, {
                            reportId: 'RPT20250501002',
                            products: [this.productDatabase[2]], // 单个商品，无需校验代码
                            activities: [{
                                type: 'support',
                                name: '新品亏损扶持',
                                startDate: new Date('2025-05-15'),
                                endDate: new Date('2025-06-15'),
                                requireReview: true,
                                reviewStatus: 'pending'
                            }],
                            creator: '李四',
                            createTime: new Date('2025-05-01 10:15:32')
                        }, {
                            reportId: 'RPT20250502003',
                            products: [ // 三个商品，确保前两个code相同，第三个不同，以便测试校验逻辑
                                this.productDatabase[0], // SKU-10000 (假设)
                                productWithSameCode1, // SKU-10000
                                this.productDatabase[3] // 另一个不同的code SKU-10003 (假设) -> 这个应该在新增时被校验阻止
                            ],
                            // 为了让初始数据能通过（因为校验是在提交时），我们让这个记录只有一个商品，或都同code
                            // products: [this.productDatabase[0]], // 或者
                            // products: [this.productDatabase[0], productWithSameCode1, productWithSameCode2],


                            activities: [{
                                type: 'promo',
                                name: '新品主推让利',
                                startDate: new Date('2025-05-03'),
                                endDate: new Date('2025-05-30'),
                                requireReview: false
                            }],
                            creator: '王五',
                            createTime: new Date('2025-05-02 14:22:45')
                        }];
                        // 修正 RPT20250502003 的初始数据，使其符合规则或仅包含一个商品，避免初始加载就显示“错误”数据
                        mockData[2].products = [this.productDatabase[0]]; // 例如，初始只有一个商品


                        let filteredData = mockData.map(item => ({...item,
                            activities: item.activities.map(act => ({...act,
                                startDate: act.startDate ? new Date(act.startDate) : null,
                                endDate: act.endDate ? new Date(act.endDate) : null
                            })),
                            createTime: new Date(item.createTime)
                        }));

                        if (this.searchForm.productKeyword) {
                            const keyword = this.searchForm.productKeyword.toLowerCase();
                            filteredData = filteredData.filter(item =>
                                item.products.some(p =>
                                    p.name.toLowerCase().includes(keyword) ||
                                    p.code.toLowerCase().includes(keyword) ||
                                    p.id.toLowerCase().includes(keyword)
                                )
                            );
                        }
                        if (this.searchForm.dateRange && this.searchForm.dateRange.length === 2) {
                            const startDateSearch = new Date(this.searchForm.dateRange[0]);
                            startDateSearch.setHours(0, 0, 0, 0);
                            const endDateSearch = new Date(this.searchForm.dateRange[1]);
                            endDateSearch.setHours(23, 59, 59, 999);
                            filteredData = filteredData.filter(item => new Date(item.createTime) >= startDateSearch && new Date(item.createTime) <= endDateSearch);
                        }
                        if (this.searchForm.activityType) filteredData = filteredData.filter(item => item.activities.some(a => a.type === this.searchForm.activityType));
                        if (this.searchForm.reviewStatus) filteredData = filteredData.filter(item => item.activities.some(a => a.requireReview && a.reviewStatus === this.searchForm.reviewStatus));

                        this.pagination.total = filteredData.length;
                        const start = (this.pagination.currentPage - 1) * this.pagination.pageSize;
                        const end = start + this.pagination.pageSize;
                        this.reportRecords = filteredData.slice(start, end);
                        this.tableLoading = false;

                        this.$nextTick(() => {
                            if (this.$refs.reportTable) {
                                this.$refs.reportTable.doLayout();
                            }
                        });
                    }, 500);
                },
                viewReportDetail(row) {
                    this.currentReport = JSON.parse(JSON.stringify(row));
                    this.detailDialogVisible = true;
                },
                handleDialogClose(done) {
                    if (this.formData.products.length > 0 || this.selectedActivities.length > 0) {
                        this.$confirm('确定要关闭吗？未保存的数据将丢失', '提示', {
                                confirmButtonText: '确定',
                                cancelButtonText: '取消',
                                type: 'warning'
                            })
                            .then(() => {
                                this.resetForm();
                                done();
                            }).catch(() => {});
                    } else {
                        this.resetForm();
                        done();
                    }
                },
                queryProductsForAdd(queryString, cb) {
                    const results = queryString ?
                        this.productDatabase.filter(product =>
                            (product.id.toLowerCase().includes(queryString.toLowerCase()) ||
                                product.name.toLowerCase().includes(queryString.toLowerCase()) ||
                                product.code.toLowerCase().includes(queryString.toLowerCase())) &&
                            !this.formData.products.some(selected => selected.id === product.id)
                        ) :
                        this.productDatabase.filter(product => !this.formData.products.some(selected => selected.id === product.id));
                    cb(results.map(product => ({
                        value: `${product.name} (ID: ${product.id}, 代码: ${product.code})`,
                        productFull: product
                    })));
                },
                handleProductSelectForAdd(item) {
                    if (item && item.productFull) {
                        if (this.formData.products.length < 3) {
                            this.formData.products.push(JSON.parse(JSON.stringify(item.productFull)));
                        } else {
                            this.$message.warning('最多只能选择3个商品。');
                        }
                        this.productIdInput = '';
                    }
                },
                removeProductFromAdd(index) {
                    this.formData.products.splice(index, 1);
                },
                validateProductCodeConsistency(products) {
                    if (products.length <= 1) return {
                        valid: true
                    };

                    const codes = products.map(p => p.code);
                    if (products.length === 2) {
                        if (codes[0] !== codes[1]) {
                            return {
                                valid: false,
                                message: `商品 "${products[0].name}" 和 "${products[1].name}" 的商品代码不一致 (${codes[0]} vs ${codes[1]})，请修改。`
                            };
                        }
                    } else if (products.length === 3) {
                        const codeCounts = {};
                        codes.forEach(code => {
                            codeCounts[code] = (codeCounts[code] || 0) + 1;
                        });
                        const uniqueCodes = Object.keys(codeCounts);

                        if (uniqueCodes.length === 3) {
                            return {
                                valid: false,
                                message: `所选三个商品的商品代码 (${uniqueCodes.join(', ')}) 均不相同，请确保商品代码一致。`
                            };
                        }
                        if (uniqueCodes.length === 2) {
                            let differingProduct = null;
                            let majorityCode = '';
                            for (const code in codeCounts) {
                                if (codeCounts[code] === 2) majorityCode = code;
                            }

                            // 如果没有明确的多数（例如，在添加第三个不同code的商品到两个已有不同code的商品中，这种情况不应发生，因为两两不同就应该先报错）
                            // 但如果逻辑是必须三者一致，或者两同一异，那么这里主要是找出那个“异”
                            if (!majorityCode && uniqueCodes.length === 2) { // 这种情况是两个商品代码各出现一次，还有一个商品代码是第三个。逻辑上不应该是这样的，但作为防御
                                // 实际上，如果两个商品已经代码不同，校验在两个商品时就应失败。
                                // 这里假设如果能到三个商品且uniqueCodes为2，必然是两同一异。
                                return {
                                    valid: false,
                                    message: `商品代码不一致，请确保所有选定商品的商品代码相同。`
                                };
                            }


                            for (const product of products) {
                                if (product.code !== majorityCode) {
                                    differingProduct = product;
                                    break;
                                }
                            }
                            if (differingProduct) {
                                return {
                                    valid: false,
                                    message: `商品 "${differingProduct.name}" (代码: ${differingProduct.code}) 与其他已选商品的商品代码 (${majorityCode}) 不一致，请修改。`
                                };
                            }
                        }
                        // uniqueCodes.length === 1 (all same) is implicitly valid
                    }
                    return {
                        valid: true
                    };
                },
                validateForm() {
                    this.showValidationErrors = true;
                    if (this.formData.products.length === 0) {
                        this.$message.error('请至少选择一个商品');
                        return false;
                    }
                    if (this.formData.products.length > 3) {
                        this.$message.error('最多选择3个商品');
                        return false;
                    }
                    const productCodeValidation = this.validateProductCodeConsistency(this.formData.products);
                    if (!productCodeValidation.valid) {
                        this.$message.error(productCodeValidation.message);
                        return false;
                    }
                    if (this.selectedActivities.length === 0) {
                        this.showValidationErrors = true;
                        return false;
                    }
                    for (const activityType of this.selectedActivities) {
                        const activity = this.activities.find(a => a.type === activityType);
                        if (!activity.startDate || !activity.endDate) {
                            this.$message.error(`请为 ${activity.name} 设置活动日期`);
                            return false;
                        }
                        if (new Date(activity.startDate) > new Date(activity.endDate)) {
                            this.$message.error(`${activity.name} 的开始日期不能晚于结束日期`);
                            return false;
                        }
                    }
                    return true;
                },
                submitForm() {
                    if (!this.validateForm()) return;
                    const newReport = {
                        reportId: 'RPT' + this.formatDate(new Date(), 'YYYYMMDD') + Math.floor(Math.random() * 900 + 100),
                        products: JSON.parse(JSON.stringify(this.formData.products)),
                        activities: this.selectedActivities.map(type => {
                            const ad = this.activities.find(a => a.type === type);
                            return {
                                name: ad.name,
                                type: ad.type,
                                requireReview: ad.requireReview,
                                startDate: new Date(ad.startDate),
                                endDate: new Date(ad.endDate),
                                reviewStatus: ad.requireReview ? 'pending' : null
                            }
                        }),
                        creator: this.formData.creator,
                        createTime: new Date(this.formData.createTime)
                    };
                    this.reportRecords.unshift(newReport);
                    this.pagination.total++;
                    this.$message.success('活动报备提交成功！');
                    if (newReport.activities.some(a => a.requireReview)) {
                        this.$notify({
                            title: '提交成功',
                            message: '您的部分活动需管理员审核后生效',
                            type: 'warning',
                            duration: 5000
                        });
                    }
                    this.dialogVisible = false;
                    this.resetForm();
                },
                resetForm() {
                    this.productIdInput = '';
                    this.formData.products = [];
                    this.selectedActivities = [];
                    this.isAutomaticDateCalculation = true;
                    this.manualDateSetting = false;
                    this.showValidationErrors = false;
                    this.activities.forEach(activity => {
                        this.$set(activity, 'startDate', null);
                        this.$set(activity, 'endDate', null);
                    });
                    this.calculateDefaultDates();
                },
                handleEditReport(row) {
                    if (row.products && row.products.length >= 3) {
                        this.$message.warning('该报备记录已有3个商品，无法继续追加！');
                        return;
                    }
                    this.editingReportOriginal = JSON.parse(JSON.stringify(row));
                    this.editForm.reportId = row.reportId;
                    this.editForm.existingProducts = row.products ? JSON.parse(JSON.stringify(row.products)) : [];
                    this.editForm.newlyAddedProducts = [];
                    this.editForm.creator = row.creator;
                    this.editForm.createTimeDisplay = this.formatDateTime(row.createTime);
                    this.editForm.activitiesData = JSON.parse(JSON.stringify(this.activities));
                    this.editForm.selectedActivities = row.activities.map(a => a.type);
                    this.editForm.activitiesData.forEach(activityInForm => {
                        const existingActivity = row.activities.find(ra => ra.type === activityInForm.type);
                        if (existingActivity) {
                            activityInForm.startDate = existingActivity.startDate ? new Date(existingActivity.startDate) : null;
                            activityInForm.endDate = existingActivity.endDate ? new Date(existingActivity.endDate) : null;
                            if (activityInForm.requireReview) activityInForm.reviewStatus = existingActivity.reviewStatus || 'pending';
                        } else {
                            activityInForm.startDate = null;
                            activityInForm.endDate = null;
                        }
                    });
                    const promoActivityInEditForm = this.editForm.activitiesData.find(a => a.type === 'promo');
                    if (promoActivityInEditForm && this.editForm.selectedActivities.includes('promo')) {
                        if (promoActivityInEditForm.startDate || promoActivityInEditForm.endDate) {
                            this.editManualDateSetting = true;
                            this.editIsAutomaticDateCalculation = false;
                        } else {
                            this.editManualDateSetting = false;
                            this.editIsAutomaticDateCalculation = true;
                            this.$nextTick(() => {
                                this.calculateEditPromoDates();
                            });
                        }
                    } else {
                        this.editManualDateSetting = false;
                        this.editIsAutomaticDateCalculation = true;
                    }

                    this.editProductIdInput = '';
                    this.editShowValidationErrors = false;
                    this.editDialogVisible = true;
                },
                queryProductsForEdit(queryString, cb) {
                    const currentProductIds = new Set([...this.editForm.existingProducts.map(p => p.id), ...this.editForm.newlyAddedProducts.map(p => p.id)]);
                    const results = queryString ?
                        this.productDatabase.filter(p => (p.id.toLowerCase().includes(queryString.toLowerCase()) || p.name.toLowerCase().includes(queryString.toLowerCase()) || p.code.toLowerCase().includes(queryString.toLowerCase())) && !currentProductIds.has(p.id)) :
                        this.productDatabase.filter(p => !currentProductIds.has(p.id));
                    cb(results.map(p => ({
                        value: `${p.name} (ID: ${p.id})`,
                        productFull: p
                    })));
                },
                handleAddProductToEditForm(item) {
                    if (item && item.productFull) {
                        const totalProducts = this.editForm.existingProducts.length + this.editForm.newlyAddedProducts.length;
                        if (totalProducts < 3) {
                            const allExistingProductsInDialog = [...this.editForm.existingProducts, ...this.editForm.newlyAddedProducts];
                            if (allExistingProductsInDialog.length > 0) {
                                const firstProductCode = allExistingProductsInDialog[0].code;
                                if (item.productFull.code !== firstProductCode) {
                                    this.$message.error(`追加失败：商品 "${item.productFull.name}" 的代码 (${item.productFull.code}) 与当前报备中已选商品的代码 (${firstProductCode}) 不一致。`);
                                } else {
                                    this.editForm.newlyAddedProducts.push(JSON.parse(JSON.stringify(item.productFull)));
                                }
                            } else {
                                this.editForm.newlyAddedProducts.push(JSON.parse(JSON.stringify(item.productFull)));
                            }
                        } else {
                            this.$message.warning('已达到3个商品上限。');
                        }
                        this.editProductIdInput = '';
                    }
                },
                removeNewlyAddedEditProduct(index) {
                    this.editForm.newlyAddedProducts.splice(index, 1);
                },
                isEditFormChanged() {
                    if (!this.editingReportOriginal) return false;
                    const newProductsAdded = this.editForm.newlyAddedProducts.length > 0;
                    const originalActivitiesState = JSON.stringify(this.editingReportOriginal.activities.map(a => ({
                        type: a.type,
                        startDate: this.formatDate(a.startDate),
                        endDate: this.formatDate(a.endDate),
                        reviewStatus: a.reviewStatus
                    })).sort((a, b) => a.type.localeCompare(b.type)));
                    const currentActivitiesState = JSON.stringify(this.editForm.selectedActivities.map(type => {
                        const actData = this.editForm.activitiesData.find(ad => ad.type === type);
                        return {
                            type: type,
                            startDate: this.formatDate(actData.startDate),
                            endDate: this.formatDate(actData.endDate),
                            reviewStatus: actData.reviewStatus
                        };
                    }).sort((a, b) => a.type.localeCompare(b.type)));
                    const activitiesActuallyChanged = originalActivitiesState !== currentActivitiesState;
                    return newProductsAdded || activitiesActuallyChanged;
                },
                validateEditForm() {
                    const allProducts = [...this.editForm.existingProducts, ...this.editForm.newlyAddedProducts];
                    if (allProducts.length === 0) {
                        this.$message.error('请至少保留或添加一个商品');
                        return false;
                    }
                    if (allProducts.length > 3) {
                        this.$message.error('最多关联3个商品');
                        return false;
                    }
                    const productCodeValidation = this.validateProductCodeConsistency(allProducts);
                    if (!productCodeValidation.valid) {
                        this.$message.error("编辑后商品代码校验失败：" + productCodeValidation.message);
                        return false;
                    }
                    if (this.isEditFormChanged() && this.editForm.selectedActivities.length === 0) {
                        this.$message.error('检测到更改，请至少选择一种活动类型');
                        return false;
                    }
                    for (const activityType of this.editForm.selectedActivities) {
                        const activity = this.editForm.activitiesData.find(a => a.type === activityType);
                        if (!activity.startDate || !activity.endDate) {
                            this.$message.error(`请为 ${activity.name} 设置活动日期`);
                            return false;
                        }
                        if (new Date(activity.startDate) > new Date(activity.endDate)) {
                            this.$message.error(`${activity.name} 的开始日期不能晚于结束日期`);
                            return false;
                        }
                    }
                    return true;
                },
                submitEditForm() {
                    if (!this.validateEditForm()) return;
                    if (!this.isEditFormChanged()) {
                        this.$message.info('未检测到商品追加或活动信息变更。');
                        this.editDialogVisible = false;
                        return;
                    }
                    const reportIndex = this.reportRecords.findIndex(r => r.reportId === this.editForm.reportId);
                    if (reportIndex === -1) {
                        this.$message.error('未找到记录');
                        return;
                    }
                    const finalProducts = [...this.editForm.existingProducts, ...this.editForm.newlyAddedProducts];
                    const updatedActivities = this.editForm.selectedActivities.map(type => {
                        const ad = this.editForm.activitiesData.find(a => a.type === type);
                        let newReviewStatus = ad.reviewStatus;
                        if (ad.requireReview) {
                            const originalActivity = this.editingReportOriginal.activities.find(oa => oa.type === type);
                            const datesChanged = originalActivity && (this.formatDate(originalActivity.startDate) !== this.formatDate(ad.startDate) || this.formatDate(originalActivity.endDate) !== this.formatDate(ad.endDate));
                            if ((originalActivity && originalActivity.reviewStatus === 'approved' && datesChanged) || (!originalActivity && this.editForm.selectedActivities.includes(ad.type))) {
                                newReviewStatus = 'pending';
                                if (datesChanged) this.$message.warning(`${ad.name} 的日期已更改，状态已重置为待审核。`);
                            }
                        } else {
                            newReviewStatus = null;
                        }
                        return {
                            name: ad.name,
                            type: ad.type,
                            requireReview: ad.requireReview,
                            startDate: new Date(ad.startDate),
                            endDate: new Date(ad.endDate),
                            reviewStatus: newReviewStatus
                        };
                    });
                    const updatedReport = {...this.editingReportOriginal,
                        products: finalProducts,
                        activities: updatedActivities
                    };
                    this.$set(this.reportRecords, reportIndex, updatedReport);
                    this.$message.success('报备信息更新成功！');
                    this.editDialogVisible = false;
                },
                handleEditDialogClose(done) {
                    if (this.isEditFormChanged()) {
                        this.$confirm('内容已修改，确定关闭吗？', '提示', {
                            type: 'warning'
                        }).then(done).catch(() => {});
                    } else {
                        done();
                    }
                },
                formatDate(date, formatStr) {
                    if (!date) return '—';
                    const d = new Date(date);
                    if (isNaN(d.getTime())) return '—';
                    const Y = d.getFullYear(),
                        M = (d.getMonth() + 1 + '').padStart(2, '0'),
                        D = (d.getDate() + '').padStart(2, '0');
                    if (formatStr === 'YYYYMMDD') return `${Y}${M}${D}`;
                    return `${Y}-${M}-${D}`;
                },
                formatDateTime(date) {
                    if (!date) return '—';
                    const d = new Date(date);
                    if (isNaN(d.getTime())) return '—';
                    return `${this.formatDate(d)} ${(d.getHours()+'').padStart(2,'0')}:${(d.getMinutes()+'').padStart(2,'0')}:${(d.getSeconds()+'').padStart(2,'0')}`;
                },
                getStatusType(status) {
                    switch (status) {
                        case 'pending':
                            return 'warning';
                        case 'approved':
                            return 'success';
                        case 'rejected':
                            return 'danger';
                        default:
                            return 'info';
                    }
                },
                getStatusText(status) {
                    switch (status) {
                        case 'pending':
                            return '待审核';
                        case 'approved':
                            return '已通过';
                        case 'rejected':
                            return '已拒绝';
                        default:
                            return '未知';
                    }
                },
                _updatePromoDate(targetActivityContainer, selectedActivitiesList, isAutoCalc) {
                    const today = new Date();
                    const promoActivity = targetActivityContainer.find(a => a.type === 'promo');
                    if (!promoActivity) return;
                    if (isAutoCalc && selectedActivitiesList.includes('promo')) {
                        const currentDay = today.getDate();
                        const currentMonth = today.getMonth();
                        const currentYear = today.getFullYear();
                        const lastDayOfMonth = new Date(currentYear, currentMonth + 1, 0);
                        const firstDayOfNextMonth = new Date(currentYear, currentMonth + 1, 1);
                        const lastDayOfNextMonth = new Date(currentYear, currentMonth + 2, 0);
                        const daysRemainingInMonth = lastDayOfMonth.getDate() - currentDay;
                        let newStartDate, newEndDate;
                        if (daysRemainingInMonth <= 5) {
                            newStartDate = new Date(firstDayOfNextMonth);
                        } else {
                            newStartDate = new Date(today);
                        }
                        if (selectedActivitiesList.includes('race')) {
                            newEndDate = new Date(lastDayOfNextMonth);
                        } else {
                            newEndDate = new Date(lastDayOfMonth);
                        }
                        if (!promoActivity.startDate || promoActivity.startDate.getTime() !== newStartDate.getTime()) {
                            this.$set(promoActivity, 'startDate', newStartDate);
                        }
                        if (!promoActivity.endDate || promoActivity.endDate.getTime() !== newEndDate.getTime()) {
                            this.$set(promoActivity, 'endDate', newEndDate);
                        }
                    } else if (isAutoCalc && !selectedActivitiesList.includes('promo')) {
                        if (promoActivity.startDate !== null) this.$set(promoActivity, 'startDate', null);
                        if (promoActivity.endDate !== null) this.$set(promoActivity, 'endDate', null);
                    }
                },
                calculateDefaultDates() {
                    this._updatePromoDate(this.activities, this.selectedActivities, this.isAutomaticDateCalculation);
                },
                toggleDateCalculation(value) {
                    this.isAutomaticDateCalculation = !value;
                    if (this.isAutomaticDateCalculation) {
                        this.calculateDefaultDates();
                    }
                },
                calculateEditPromoDates() {
                    this._updatePromoDate(this.editForm.activitiesData, this.editForm.selectedActivities, this.editIsAutomaticDateCalculation);
                },
                toggleEditDateCalculation(value) {
                    this.editIsAutomaticDateCalculation = !value;
                    if (this.editIsAutomaticDateCalculation) {
                        this.calculateEditPromoDates();
                    }
                },
                handleEditActivitiesChange() {
                    this.editShowValidationErrors = false;
                    if (this.editIsAutomaticDateCalculation) this.calculateEditPromoDates();
                },
            },
            watch: {
                selectedActivities: {
                    handler() {
                        this.calculateDefaultDates();
                        this.showValidationErrors = false;
                    },
                    deep: true
                },
                'formData.products': {
                    handler(newVal) { /* ... */ },
                    deep: true
                },
                'editForm.newlyAddedProducts': {
                    handler(newVal) { /* ... */ },
                    deep: true
                }
            }
        });
    </script>
</body>

</html>