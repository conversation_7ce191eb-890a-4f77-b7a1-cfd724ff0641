<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>成本增幅规则管理</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.1/font/bootstrap-icons.css">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/flatpickr/dist/flatpickr.min.css">
    <style>
        /* styles.css */
        
        body {
            background-color: #f8f9fa;
            color: #333;
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", <PERSON><PERSON>, "Helvetica Neue", <PERSON><PERSON>, sans-serif;
        }
        
        .card {
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
            border: none;
        }
        
        .card-body {
            padding: 1.5rem;
        }
        
        .table th {
            font-weight: 600;
            color: #495057;
        }
        
        .badge {
            font-weight: 500;
            padding: 0.5em 0.75em;
        }
        
        .badge-enabled {
            background-color: #d1e7dd;
            color: #0f5132;
        }
        
        .badge-expired {
            background-color: #f8d7da;
            color: #842029;
        }
        
        .badge-disabled {
            background-color: #e2e3e5;
            color: #41464b;
        }
        
        .version-link {
            color: #0d6efd;
            text-decoration: none;
            cursor: pointer;
            font-weight: 500;
        }
        
        .version-link:hover {
            text-decoration: underline;
        }
        
        .modal-header {
            background-color: #f8f9fa;
            border-bottom: 1px solid #e9ecef;
        }
        
        .range-item {
            background-color: #fcfcfc;
            transition: background-color 0.2s;
        }
        
        .range-item:hover {
            background-color: #f8f9fa;
        }
        
        .btn-icon {
            padding: 0.25rem 0.5rem;
            line-height: 1;
        }
        
        .table-hover tbody tr:hover {
            background-color: rgba(13, 110, 253, 0.05);
        }
        /* 分页样式 */
        
        .pagination .page-link {
            color: #0d6efd;
            border-color: #dee2e6;
        }
        
        .pagination .page-item.active .page-link {
            background-color: #0d6efd;
            border-color: #0d6efd;
        }
        /* 表单验证样式 */
        
        .was-validated .form-control:invalid,
        .form-control.is-invalid {
            border-color: #dc3545;
            background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 12 12' width='12' height='12' fill='none' stroke='%23dc3545'%3e%3ccircle cx='6' cy='6' r='4.5'/%3e%3cpath stroke-linejoin='round' d='M5.8 3.6h.4L6 6.5z'/%3e%3ccircle cx='6' cy='8.2' r='.6' fill='%23dc3545' stroke='none'/%3e%3c/svg%3e");
            background-repeat: no-repeat;
            background-position: right calc(0.375em + 0.1875rem) center;
            background-size: calc(0.75em + 0.375rem) calc(0.75em + 0.375rem);
        }
        
        .invalid-feedback {
            display: block;
            width: 100%;
            margin-top: 0.25rem;
            font-size: 0.875em;
            color: #dc3545;
        }
        /* 日期选择器样式覆盖 */
        
        .flatpickr-calendar {
            box-shadow: 0 3px 15px rgba(0, 0, 0, 0.15);
            border-radius: 8px;
        }
        
        .flatpickr-day.selected {
            background: #0d6efd;
            border-color: #0d6efd;
        }
        /* 响应式调整 */
        
        @media (max-width: 768px) {
            .card-body {
                padding: 1rem;
            }
            .btn-group-responsive {
                display: flex;
                flex-direction: column;
                width: 100%;
            }
            .btn-group-responsive .btn {
                margin-bottom: 0.5rem;
                width: 100%;
            }
            .col-responsive {
                margin-bottom: 1rem;
            }
        }
    </style>
</head>

<body>
    <div class="container-fluid">
        <!-- 页面标题 -->
        <div class="row mt-4 mb-4">
            <div class="col">
                <h2><i class="bi bi-gear-fill me-2"></i>成本增幅规则管理</h2>
                <p class="text-muted">管理不同电商平台的广告费用成本增幅规则</p>
            </div>
        </div>

        <!-- 筛选区域 -->
        <div class="card mb-4">
            <div class="card-body">
                <div class="row align-items-center">
                    <div class="col-md-2">
                        <label for="statusFilter" class="form-label">规则状态</label>
                        <select id="statusFilter" class="form-select">
                                <option value="enabled" selected>启用</option>
                                <option value="disabled">停用</option>
                                <option value="expired">已过期</option>
                                <option value="all">全部</option>
                            </select>
                    </div>
                    <div class="col-md-3">
                        <label for="platformFilter" class="form-label">电商平台</label>
                        <select id="platformFilter" class="form-select">
                                <option value="all" selected>全部平台</option>
                                <option value="tmall">天猫</option>
                                <option value="jd">京东</option>
                                <option value="pdd">拼多多</option>
                                <option value="douyin">抖音</option>
                            </select>
                    </div>
                    <div class="col-md-3">
                        <label for="versionFilter" class="form-label">版本名称</label>
                        <input type="text" id="versionFilter" class="form-control" placeholder="输入版本名称搜索">
                    </div>
                    <div class="col-md-4 d-flex align-items-end">
                        <button id="searchBtn" class="btn btn-primary me-2">
                                    <i class="bi bi-search me-1"></i>查询
                                </button>
                        <button id="resetBtn" class="btn btn-outline-secondary me-2">
                            <i class="bi bi-arrow-counterclockwise me-1"></i>重置
                        </button>
                        <button id="addRuleBtn" class="btn btn-success ms-auto">
                            <i class="bi bi-plus-lg me-1"></i>新增规则
                                </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- 规则列表 -->
        <div class="card">
            <div class="card-body">
                <div id="ruleVersionInfo" class="alert alert-info d-none">
                    <i class="bi bi-info-circle me-2"></i>
                    <span id="versionInfoText">您正在查看历史版本规则</span>
                    <button id="backToCurrentBtn" class="btn btn-sm btn-outline-primary ms-3">
                            返回当前版本
                        </button>
                </div>

                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead class="table-light">
                            <tr>
                                <th width="5%">编号</th>
                                <th width="15%">版本名称</th>
                                <th width="12%">起始金额(含)</th>
                                <th width="12%">结束金额</th>
                                <th width="12%">成本增幅比例</th>
                                <th width="12%">电商平台</th>
                                <th width="15%">日期范围</th>
                                <th width="8%">状态</th>
                                <th width="9%">操作</th>
                            </tr>
                        </thead>
                        <tbody id="rulesTableBody">
                            <!-- 表格数据将通过JavaScript动态生成 -->
                            <tr>
                                <td colspan="9" class="text-center py-4">加载中...</td>
                            </tr>
                        </tbody>
                    </table>
                </div>

                <!-- 分页控件 -->
                <div class="d-flex justify-content-between align-items-center mt-3">
                    <div class="text-muted">
                        共 <span id="totalRules">0</span> 条规则
                    </div>
                    <nav aria-label="规则分页">
                        <ul class="pagination mb-0">
                            <li class="page-item disabled">
                                <a class="page-link" href="#" tabindex="-1" aria-disabled="true">上一页</a>
                            </li>
                            <li class="page-item active"><a class="page-link" href="#">1</a></li>
                            <li class="page-item"><a class="page-link" href="#">2</a></li>
                            <li class="page-item"><a class="page-link" href="#">3</a></li>
                            <li class="page-item">
                                <a class="page-link" href="#">下一页</a>
                            </li>
                        </ul>
                    </nav>
                </div>
            </div>
        </div>
    </div>

    <!-- 新增/编辑规则模态框 -->
    <div class="modal fade" id="ruleModal" tabindex="-1" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="ruleModalTitle">新增成本增幅规则</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <form id="ruleForm">
                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label for="versionName" class="form-label">版本名称 <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" id="versionName" required placeholder="例如：2025年Q2成本增幅规则">
                            </div>
                            <div class="col-md-6">
                                <label for="platform" class="form-label">电商平台 <span class="text-danger">*</span></label>
                                <select class="form-select" id="platform" required>
                                    <option value="" selected disabled>请选择电商平台</option>
                                    <option value="tmall">天猫</option>
                                    <option value="jd">京东</option>
                                    <option value="pdd">拼多多</option>
                                    <option value="douyin">抖音</option>
                                </select>
                            </div>
                        </div>
                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label for="dateRange" class="form-label">日期范围 <span class="text-danger">*</span></label>
                                <div class="input-group">
                                    <input type="text" class="form-control" id="startDate" required placeholder="开始月份">
                                    <span class="input-group-text">至</span>
                                    <input type="text" class="form-control" id="endDate" required placeholder="结束月份">
                                </div>
                                <div class="form-text">格式：YYYY-MM</div>
                            </div>
                        </div>

                        <h6 class="mt-4 mb-3 border-bottom pb-2">广告费用区间设置</h6>
                        <p class="text-muted small">设置不同广告费用区间的成本增幅比例，区间不可重叠</p>

                        <div id="rangeContainer">
                            <!-- 区间项模板 -->
                            <div class="range-item mb-3 p-3 border rounded" data-index="0">
                                <div class="row align-items-center">
                                    <div class="col-md-4">
                                        <label class="form-label">起始金额(含) <span class="text-danger">*</span></label>
                                        <div class="input-group">
                                            <input type="number" class="form-control start-amount" placeholder="例如：0" required min="0" step="0.01">
                                            <span class="input-group-text">元</span>
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <label class="form-label">结束金额 <span class="text-danger">*</span></label>
                                        <div class="input-group">
                                            <input type="number" class="form-control end-amount" placeholder="例如：1000" required min="0" step="0.01">
                                            <span class="input-group-text">元</span>
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <label class="form-label">成本增幅比例 <span class="text-danger">*</span></label>
                                        <div class="input-group">
                                            <input type="number" class="form-control cost-rate" placeholder="例如：5" required min="0" max="100" step="0.01">
                                            <span class="input-group-text">%</span>
                                        </div>
                                    </div>
                                    <div class="col-md-1 d-flex align-items-end">
                                        <button type="button" class="btn btn-outline-danger btn-sm remove-range mt-4" disabled>
                                            <i class="bi bi-trash"></i>
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="text-center mb-3">
                            <button type="button" id="addRangeBtn" class="btn btn-outline-primary">
                                <i class="bi bi-plus-circle me-1"></i>添加区间
                            </button>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="button" id="saveRuleBtn" class="btn btn-primary">保存</button>
                </div>
            </div>
        </div>
    </div>

    <!-- 删除确认模态框 -->
    <div class="modal fade" id="deleteConfirmModal" tabindex="-1" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">确认删除</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <p>您确定要删除这条成本增幅规则吗？此操作不可撤销。</p>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="button" id="confirmDeleteBtn" class="btn btn-danger">确认删除</button>
                </div>
            </div>
        </div>
    </div>

    <!-- JS引用 -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/flatpickr"></script>
    <script src="https://cdn.jsdelivr.net/npm/flatpickr/dist/l10n/zh.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/flatpickr/dist/plugins/monthSelect/index.js"></script>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/flatpickr/dist/plugins/monthSelect/style.css">
    <script>
        // 使用立即执行函数表达式(IIFE)创建隔离的作用域，避免全局命名冲突
        (function() {
            // 全局变量和状态
            let currentRuleId = null;

            // 页面加载完成后初始化
            document.addEventListener('DOMContentLoaded', function() {
                // 初始化月份选择器
                initDatePickers();

                // 初始化界面数据
                loadRulesData();

                // 绑定事件处理
                bindEvents();

                // 初始化首个区间
                initRangeContainer();
            });

            // 初始化日期选择器
            function initDatePickers() {
                try {
                    // 确保月份选择插件已加载
                    if (typeof flatpickr === 'undefined') {
                        console.error('Flatpickr未加载');
                        showToast('日期选择器加载失败，请刷新页面重试', 'error');
                        return;
                    }

                    // 月份选择器配置
                    const monthPickerConfig = {
                        locale: 'zh',
                        dateFormat: "Y-m",
                        altInput: true,
                        altFormat: "Y年m月",
                        disableMobile: true,
                        static: true,
                        plugins: [new monthSelectPlugin({
                            shorthand: true,
                            dateFormat: "Y-m",
                            altFormat: "Y年m月"
                        })]
                    };

                    // 初始化起始月份选择器
                    const startDatePicker = flatpickr("#startDate", {
                        ...monthPickerConfig,
                        onChange: function(selectedDates, dateStr) {
                            try {
                                // 设置结束日期的最小值
                                if (window.endDatePicker && typeof window.endDatePicker.set === 'function') {
                                    window.endDatePicker.set('minDate', dateStr);
                                }
                            } catch (err) {
                                console.error('设置结束日期最小值出错:', err);
                            }
                        }
                    });

                    // 初始化结束月份选择器
                    const endDatePicker = flatpickr("#endDate", {
                        ...monthPickerConfig,
                        onChange: function(selectedDates, dateStr) {
                            try {
                                // 设置开始日期的最大值
                                if (window.startDatePicker && typeof window.startDatePicker.set === 'function') {
                                    window.startDatePicker.set('maxDate', dateStr);
                                }
                            } catch (err) {
                                console.error('设置开始日期最大值出错:', err);
                            }
                        }
                    });

                    // 将选择器实例保存到全局以便访问
                    window.startDatePicker = startDatePicker;
                    window.endDatePicker = endDatePicker;

                    console.log('日期选择器初始化成功');
                } catch (err) {
                    console.error('初始化日期选择器出错:', err);
                    showToast('日期选择器初始化失败，请刷新页面重试', 'error');
                }
            }

            // 绑定界面事件
            function bindEvents() {
                try {
                    // 新增规则按钮
                    const addRuleBtn = document.getElementById('addRuleBtn');
                    if (addRuleBtn) {
                        addRuleBtn.addEventListener('click', function() {
                            openRuleModal('add');
                        });
                    }

                    // 保存规则按钮
                    const saveRuleBtn = document.getElementById('saveRuleBtn');
                    if (saveRuleBtn) {
                        saveRuleBtn.addEventListener('click', saveRule);
                    }

                    // 添加区间按钮
                    const addRangeBtn = document.getElementById('addRangeBtn');
                    if (addRangeBtn) {
                        addRangeBtn.addEventListener('click', addNewRange);
                    }

                    // 状态筛选变化
                    const statusFilter = document.getElementById('statusFilter');
                    if (statusFilter) {
                        statusFilter.addEventListener('change', function() {
                            loadRulesData();
                        });
                    }

                    // 平台筛选变化
                    const platformFilter = document.getElementById('platformFilter');
                    if (platformFilter) {
                        platformFilter.addEventListener('change', function() {
                            loadRulesData();
                        });
                    }

                    // 搜索按钮
                    const searchBtn = document.getElementById('searchBtn');
                    if (searchBtn) {
                        searchBtn.addEventListener('click', function() {
                            loadRulesData();
                        });
                    }

                    // 重置按钮
                    const resetBtn = document.getElementById('resetBtn');
                    if (resetBtn) {
                        resetBtn.addEventListener('click', function() {
                            document.getElementById('statusFilter').value = 'active';
                            document.getElementById('platformFilter').value = 'all';
                            document.getElementById('versionFilter').value = '';
                            loadRulesData();
                        });
                    }

                    // 返回当前版本按钮
                    const backToCurrentBtn = document.getElementById('backToCurrentBtn');
                    if (backToCurrentBtn) {
                        backToCurrentBtn.addEventListener('click', function() {
                            document.getElementById('ruleVersionInfo').classList.add('d-none');
                            loadRulesData();
                        });
                    }

                    // 删除确认按钮
                    const confirmDeleteBtn = document.getElementById('confirmDeleteBtn');
                    if (confirmDeleteBtn) {
                        confirmDeleteBtn.addEventListener('click', function() {
                            // 获取待删除的规则ID
                            const ruleId = this.getAttribute('data-rule-id');
                            deleteRule(ruleId);
                        });
                    }

                    // 启用/停用按钮事件
                    const toggleButtons = document.querySelectorAll('.toggle-rule');
                    toggleButtons.forEach(button => {
                        button.addEventListener('click', function() {
                            const ruleId = this.getAttribute('data-rule-id');
                            const action = this.getAttribute('data-action');
                            if (ruleId && action) {
                                console.log(`点击${action === 'enable' ? '启用' : '停用'}按钮:`, ruleId);
                                toggleRuleStatus(ruleId, action);
                            }
                        });
                    });

                    console.log('事件绑定成功');
                } catch (err) {
                    console.error('绑定事件出错:', err);
                    showToast('初始化界面失败，请刷新页面重试', 'error');
                }
            }

            // 初始化区间容器
            function initRangeContainer() {
                const container = document.getElementById('rangeContainer');
                // 确保容器为空
                container.innerHTML = '';
                // 添加第一个区间
                addNewRange();
            }

            // 添加新区间
            function addNewRange() {
                const container = document.getElementById('rangeContainer');
                const rangeIndex = container.children.length;

                // 创建新区间元素
                const rangeElement = document.createElement('div');
                rangeElement.className = 'range-item mb-3 p-3 border rounded';
                rangeElement.setAttribute('data-index', rangeIndex);

                // 获取前一个区间的结束值（如果存在）
                let startValue = 0;
                if (rangeIndex > 0) {
                    const prevEndAmount = container.querySelector(`[data-index="${rangeIndex-1}"] .end-amount`);
                    if (prevEndAmount && prevEndAmount.value) {
                        startValue = parseFloat(prevEndAmount.value);
                    }
                }

                // 设置区间内容
                rangeElement.innerHTML = `
            <div class="row align-items-center">
                <div class="col-md-4">
                    <label class="form-label">起始金额(含) <span class="text-danger">*</span></label>
                <div class="input-group">
                        <input type="number" class="form-control start-amount" placeholder="例如：0" 
                               required min="0" step="0.01" value="${startValue}">
                        <span class="input-group-text">元</span>
                </div>
                </div>
                <div class="col-md-4">
                    <label class="form-label">结束金额 <span class="text-danger">*</span></label>
                <div class="input-group">
                        <input type="number" class="form-control end-amount" placeholder="例如：1000" 
                               required min="0" step="0.01">
                        <span class="input-group-text">元</span>
                </div>
                </div>
                <div class="col-md-3">
                    <label class="form-label">成本增幅比例 <span class="text-danger">*</span></label>
                <div class="input-group">
                        <input type="number" class="form-control cost-rate" placeholder="例如：5" 
                               required min="0" max="100" step="0.01">
                        <span class="input-group-text">%</span>
                </div>
                </div>
                <div class="col-md-1 d-flex align-items-end">
                    <button type="button" class="btn btn-outline-danger btn-sm remove-range mt-4"
                            ${rangeIndex === 0 ? 'disabled' : ''}>
                    <i class="bi bi-trash"></i>
                </button>
                </div>
            </div>
        `;

                // 添加到容器
                container.appendChild(rangeElement);

                // 绑定删除区间事件
                const removeBtn = rangeElement.querySelector('.remove-range');
                removeBtn.addEventListener('click', function() {
                    removeRange(rangeElement);
                });

                // 绑定输入事件，确保区间连续性
                const startInput = rangeElement.querySelector('.start-amount');
                const endInput = rangeElement.querySelector('.end-amount');

                startInput.addEventListener('input', function() {
                    validateRanges();
                });

                endInput.addEventListener('input', function() {
                    validateRanges();
                    // 如果存在下一个区间，更新其起始值
                    if (rangeIndex < container.children.length - 1) {
                        const nextStartInput = container.querySelector(`[data-index="${rangeIndex+1}"] .start-amount`);
                        if (nextStartInput && this.value) {
                            nextStartInput.value = this.value;
                        }
                    }
                });
            }

            // 删除区间
            function removeRange(rangeElement) {
                const container = document.getElementById('rangeContainer');
                const index = parseInt(rangeElement.getAttribute('data-index'));

                // 移除当前区间
                container.removeChild(rangeElement);

                // 更新后续区间的索引
                const remainingRanges = container.querySelectorAll('.range-item');
                remainingRanges.forEach((range, i) => {
                    range.setAttribute('data-index', i);
                    // 第一个区间不能删除
                    const removeBtn = range.querySelector('.remove-range');
                    if (i === 0) {
                        removeBtn.disabled = true;
                    } else {
                        removeBtn.disabled = false;
                    }
                });

                // 验证区间
                validateRanges();
            }

            // 验证区间是否连续且合法
            function validateRanges() {
                const container = document.getElementById('rangeContainer');
                const ranges = container.querySelectorAll('.range-item');
                let isValid = true;

                ranges.forEach((range, index) => {
                    const startInput = range.querySelector('.start-amount');
                    const endInput = range.querySelector('.end-amount');

                    // 移除之前的验证反馈
                    startInput.classList.remove('is-invalid');
                    endInput.classList.remove('is-invalid');

                    const start = parseFloat(startInput.value);
                    const end = parseFloat(endInput.value);

                    // 验证开始值小于结束值
                    if (startInput.value && endInput.value && start >= end) {
                        endInput.classList.add('is-invalid');
                        // 添加错误提示
                        let feedback = range.querySelector('.end-feedback');
                        if (!feedback) {
                            feedback = document.createElement('div');
                            feedback.className = 'invalid-feedback end-feedback';
                            endInput.parentNode.after(feedback);
                        }
                        feedback.textContent = '结束金额必须大于起始金额';
                        isValid = false;
                    }

                    // 如果不是第一个区间，验证与前一个区间的连续性
                    if (index > 0) {
                        const prevRange = ranges[index - 1];
                        const prevEndInput = prevRange.querySelector('.end-amount');
                        const prevEnd = parseFloat(prevEndInput.value);

                        if (prevEndInput.value && startInput.value && prevEnd !== start) {
                            startInput.classList.add('is-invalid');
                            // 添加错误提示
                            let feedback = range.querySelector('.start-feedback');
                            if (!feedback) {
                                feedback = document.createElement('div');
                                feedback.className = 'invalid-feedback start-feedback';
                                startInput.parentNode.after(feedback);
                            }
                            feedback.textContent = '起始金额必须等于上一区间的结束金额';
                            isValid = false;
                        }
                    }
                });

                return isValid;
            }

            // 打开规则模态框（新增或编辑）
            function openRuleModal(mode, ruleId) {
                try {
                    const modalTitle = document.getElementById('ruleModalTitle');
                    const form = document.getElementById('ruleForm');

                    if (!modalTitle || !form) {
                        console.error('找不到模态框元素');
                        showToast('打开模态框失败', 'error');
                        return;
                    }

                    // 重置表单
                    form.reset();
                    initRangeContainer();

                    // 保存当前规则ID
                    currentRuleId = ruleId;

                    if (mode === 'add') {
                        modalTitle.textContent = '新增成本增幅规则';
                    } else if (mode === 'edit') {
                        modalTitle.textContent = '编辑成本增幅规则';
                        // 加载规则数据
                        const rule = findRuleById(ruleId);
                        if (rule) {
                            fillRuleForm(rule);
                        }
                    }

                    // 使用Bootstrap API打开模态框
                    const modalElement = document.getElementById('ruleModal');
                    if (!modalElement) {
                        console.error('找不到模态框元素');
                        showToast('打开模态框失败', 'error');
                        return;
                    }

                    // 确保Bootstrap已加载
                    if (typeof bootstrap === 'undefined' || !bootstrap.Modal) {
                        console.error('Bootstrap未加载或Modal组件不可用');
                        showToast('页面组件加载失败，请刷新重试', 'error');
                        return;
                    }

                    const ruleModal = new bootstrap.Modal(modalElement);
                    ruleModal.show();

                    console.log(`${mode === 'add' ? '新增' : '编辑'}模态框打开成功`);
                } catch (err) {
                    console.error('打开规则模态框出错:', err);
                    showToast('打开模态框失败，请重试', 'error');
                }
            }

            // 根据ID查找规则
            function findRuleById(ruleId) {
                // 实际应用中应从服务器获取数据
                // 这里使用模拟数据
                return mockRulesData.find(rule => rule.id === ruleId);
            }

            // 填充规则表单（编辑模式）
            function fillRuleForm(rule) {
                try {
                    if (!rule) {
                        console.error('规则数据为空，无法填充表单');
                        return;
                    }

                    // 设置当前规则ID（已在openRuleModal中设置为全局变量）
                    console.log('填充表单，规则ID:', rule.id);

                    // 设置基本字段
                    const versionNameEl = document.getElementById('versionName');
                    const platformEl = document.getElementById('platform');

                    if (versionNameEl) versionNameEl.value = rule.versionName || '';
                    if (platformEl) platformEl.value = rule.platform || '';

                    // 安全地设置日期
                    try {
                        if (window.startDatePicker && typeof window.startDatePicker.setDate === 'function') {
                            window.startDatePicker.setDate(rule.startDate || '');
                        } else if (document.querySelector("#startDate") && document.querySelector("#startDate")._flatpickr) {
                            document.querySelector("#startDate")._flatpickr.setDate(rule.startDate || '');
                        } else {
                            const startDateEl = document.getElementById('startDate');
                            if (startDateEl) startDateEl.value = rule.startDate || '';
                        }
                    } catch (err) {
                        console.error('设置开始日期出错:', err);
                        // 回退到普通输入
                        const startDateEl = document.getElementById('startDate');
                        if (startDateEl) startDateEl.value = rule.startDate || '';
                    }

                    try {
                        if (window.endDatePicker && typeof window.endDatePicker.setDate === 'function') {
                            window.endDatePicker.setDate(rule.endDate || '');
                        } else if (document.querySelector("#endDate") && document.querySelector("#endDate")._flatpickr) {
                            document.querySelector("#endDate")._flatpickr.setDate(rule.endDate || '');
                        } else {
                            const endDateEl = document.getElementById('endDate');
                            if (endDateEl) endDateEl.value = rule.endDate || '';
                        }
                    } catch (err) {
                        console.error('设置结束日期出错:', err);
                        // 回退到普通输入
                        const endDateEl = document.getElementById('endDate');
                        if (endDateEl) endDateEl.value = rule.endDate || '';
                    }

                    // 清空现有区间
                    const container = document.getElementById('rangeContainer');
                    if (!container) {
                        console.error('找不到区间容器');
                        return;
                    }
                    container.innerHTML = '';

                    // 添加规则的所有区间
                    if (rule.ranges && Array.isArray(rule.ranges)) {
                        rule.ranges.forEach((range, index) => {
                            // 创建区间元素
                            const rangeElement = document.createElement('div');
                            rangeElement.className = 'range-item mb-3 p-3 border rounded';
                            rangeElement.setAttribute('data-index', index);

                            rangeElement.innerHTML = `
                <div class="row align-items-center">
                    <div class="col-md-4">
                        <label class="form-label">起始金额(含) <span class="text-danger">*</span></label>
                        <div class="input-group">
                            <input type="number" class="form-control start-amount" placeholder="例如：0" 
                                required min="0" step="0.01" value="${range.startAmount || 0}">
                            <span class="input-group-text">元</span>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <label class="form-label">结束金额 <span class="text-danger">*</span></label>
                        <div class="input-group">
                            <input type="number" class="form-control end-amount" placeholder="例如：1000" 
                                required min="0" step="0.01" value="${range.endAmount || 0}">
                            <span class="input-group-text">元</span>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <label class="form-label">成本增幅比例 <span class="text-danger">*</span></label>
                        <div class="input-group">
                            <input type="number" class="form-control cost-rate" placeholder="例如：5" 
                                required min="0" max="100" step="0.01" value="${range.costRate || 0}">
                            <span class="input-group-text">%</span>
                        </div>
                    </div>
                    <div class="col-md-1 d-flex align-items-end">
                        <button type="button" class="btn btn-outline-danger btn-sm remove-range mt-4"
                                ${index === 0 ? 'disabled' : ''}>
                            <i class="bi bi-trash"></i>
                        </button>
                    </div>
                </div>
            `;

                            // 添加到容器
                            container.appendChild(rangeElement);

                            // 绑定删除区间事件
                            const removeBtn = rangeElement.querySelector('.remove-range');
                            if (removeBtn) {
                                removeBtn.addEventListener('click', function() {
                                    removeRange(rangeElement);
                                });
                            }

                            // 绑定输入事件
                            const startInput = rangeElement.querySelector('.start-amount');
                            const endInput = rangeElement.querySelector('.end-amount');

                            if (startInput) {
                                startInput.addEventListener('input', function() {
                                    validateRanges();
                                });
                            }

                            if (endInput) {
                                endInput.addEventListener('input', function() {
                                    validateRanges();
                                    // 如果存在下一个区间，更新其起始值
                                    if (index < container.children.length - 1) {
                                        const nextStartInput = container.querySelector(`[data-index="${index+1}"] .start-amount`);
                                        if (nextStartInput && this.value) {
                                            nextStartInput.value = this.value;
                                        }
                                    }
                                });
                            }
                        });
                    } else {
                        console.warn('规则没有区间数据或格式不正确');
                        // 如果没有区间数据，添加一个默认区间
                        addNewRange();
                    }
                } catch (err) {
                    console.error('填充规则表单出错:', err);
                    showToast('加载规则数据失败', 'error');
                }
            }

            // 保存规则
            function saveRule() {
                try {
                    console.log('开始保存规则...');

                    // 表单验证
                    if (!validateRuleForm()) {
                        return;
                    }

                    // 获取表单数据
                    const formData = collectFormData();
                    formData.id = currentRuleId; // 使用保存的当前规则ID

                    console.log('收集的表单数据:', formData);

                    // 模拟保存操作
                    if (formData.id) {
                        // 更新规则
                        console.log('更新规则:', formData.id);
                        const index = window.mockRulesData.findIndex(rule => rule.id === formData.id);
                        if (index !== -1) {
                            window.mockRulesData[index] = formData;
                            console.log('规则更新成功');
                        } else {
                            console.warn('找不到要更新的规则:', formData.id);
                        }
                    } else {
                        // 新增规则
                        formData.id = 'rule_' + (window.mockRulesData.length + 1);
                        formData.createTime = new Date().toISOString();
                        window.mockRulesData.push(formData);
                        console.log('新规则添加成功:', formData.id);
                    }

                    // 安全地关闭模态框
                    try {
                        const modalElement = document.getElementById('ruleModal');
                        if (modalElement) {
                            const modalInstance = bootstrap.Modal.getInstance(modalElement);
                            if (modalInstance) {
                                modalInstance.hide();
                            } else {
                                console.warn('无法获取模态框实例，尝试手动关闭');
                                // 手动关闭
                                modalElement.classList.remove('show');
                                modalElement.style.display = 'none';
                                document.body.classList.remove('modal-open');
                                const backdrop = document.querySelector('.modal-backdrop');
                                if (backdrop) backdrop.remove();
                            }
                        }
                    } catch (modalErr) {
                        console.error('关闭模态框出错:', modalErr);
                    }

                    // 刷新规则列表
                    loadRulesData();

                    // 显示成功提示
                    showToast('规则保存成功');

                    // 清空当前规则ID
                    currentRuleId = null;
                } catch (err) {
                    console.error('保存规则出错:', err);
                    showToast('保存规则失败，请重试', 'error');
                }
            }

            // 收集表单数据
            function collectFormData() {
                try {
                    const form = document.getElementById('ruleForm');
                    if (!form) {
                        console.error('找不到规则表单');
                        throw new Error('表单元素不存在');
                    }

                    // 安全获取DOM元素值的辅助函数
                    function getElementValue(id, defaultValue = '') {
                        const element = document.getElementById(id);
                        return element && element.value ? element.value.trim() : defaultValue;
                    }

                    // 创建表单数据对象
                    const formData = {
                        id: currentRuleId, // 使用全局变量而不是DOM属性
                        versionName: getElementValue('versionName'),
                        platform: getElementValue('platform'),
                        startDate: getElementValue('startDate'),
                        endDate: getElementValue('endDate'),
                        status: new Date(getElementValue('endDate')) >= new Date() ? 'enabled' : 'expired',
                        ranges: []
                    };

                    console.log('收集表单数据，规则ID:', currentRuleId);

                    // 收集区间数据
                    const container = document.getElementById('rangeContainer');
                    if (!container) {
                        console.error('找不到区间容器');
                        return formData;
                    }

                    const ranges = container.querySelectorAll('.range-item');
                    ranges.forEach((range, index) => {
                        const startInput = range.querySelector('.start-amount');
                        const endInput = range.querySelector('.end-amount');
                        const rateInput = range.querySelector('.cost-rate');

                        if (!startInput || !endInput || !rateInput) {
                            console.warn(`区间 ${index+1} 的输入控件缺失`);
                            return;
                        }

                        const startAmount = parseFloat(startInput.value) || 0;
                        const endAmount = parseFloat(endInput.value) || 0;
                        const costRate = parseFloat(rateInput.value) || 0;

                        formData.ranges.push({
                            id: index + 1,
                            startAmount: startAmount,
                            endAmount: endAmount,
                            costRate: costRate
                        });
                    });

                    return formData;
                } catch (err) {
                    console.error('收集表单数据出错:', err);
                    throw err; // 重新抛出以便上层处理
                }
            }

            // 验证规则表单
            function validateRuleForm() {
                const form = document.getElementById('ruleForm');
                const versionName = document.getElementById('versionName').value.trim();
                const platform = document.getElementById('platform').value;
                const startDate = document.getElementById('startDate').value;
                const endDate = document.getElementById('endDate').value;

                // 验证基本字段
                if (!versionName || !platform || !startDate || !endDate) {
                    showToast('请填写所有必填字段', 'error');
                    return false;
                }

                // 验证日期范围
                if (new Date(startDate) > new Date(endDate)) {
                    showToast('开始月份不能晚于结束月份', 'error');
                    return false;
                }

                // 验证区间设置
                if (!validateRanges()) {
                    showToast('请检查区间设置，确保区间连续且金额设置正确', 'error');
                    return false;
                }

                return true;
            }

            // 显示提示消息
            function showToast(message, type = 'success') {
                // 创建提示元素
                const toastElement = document.createElement('div');
                toastElement.className = `toast align-items-center text-white bg-${type === 'success' ? 'success' : 'danger'} border-0`;
                toastElement.setAttribute('role', 'alert');
                toastElement.setAttribute('aria-live', 'assertive');
                toastElement.setAttribute('aria-atomic', 'true');

                toastElement.innerHTML = `
            <div class="d-flex">
                <div class="toast-body">
                    ${message}
                </div>
                <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast" aria-label="Close"></button>
            </div>
        `;

                // 添加到页面
                let toastContainer = document.querySelector('.toast-container');
                if (!toastContainer) {
                    // 创建toast容器
                    toastContainer = document.createElement('div');
                    toastContainer.className = 'toast-container position-fixed top-0 end-0 p-3';
                    document.body.appendChild(toastContainer);
                }

                // 添加toast到容器
                toastContainer.appendChild(toastElement);

                // 确保Bootstrap已加载
                if (typeof bootstrap !== 'undefined' && bootstrap.Toast) {
                    // 显示提示
                    const toast = new bootstrap.Toast(toastElement, {
                        delay: 3000
                    });
                    toast.show();

                    // 添加监听器在隐藏后移除元素
                    toastElement.addEventListener('hidden.bs.toast', function() {
                        this.remove();
                    });
                } else {
                    // Bootstrap未加载，使用setTimeout模拟
                    console.warn('Bootstrap未加载，无法使用Toast组件');
                    setTimeout(() => {
                        toastElement.remove();
                    }, 3000);
                }
            }

            // 加载规则数据
            function loadRulesData(versionId = null) {
                try {
                    console.log('开始加载规则数据...');

                    const tableBody = document.getElementById('rulesTableBody');
                    if (!tableBody) {
                        console.error('找不到表格主体元素 #rulesTableBody');
                        return;
                    }

                    // 获取筛选条件
                    const statusFilterEl = document.getElementById('statusFilter');
                    const platformFilterEl = document.getElementById('platformFilter');
                    const versionFilterEl = document.getElementById('versionFilter');

                    if (!statusFilterEl || !platformFilterEl || !versionFilterEl) {
                        console.error('找不到筛选控件元素');
                        return;
                    }

                    const statusFilter = statusFilterEl.value;
                    const platformFilter = platformFilterEl.value;
                    const versionFilter = versionFilterEl.value.trim().toLowerCase();

                    console.log('筛选条件:', {
                        状态: statusFilter,
                        平台: platformFilter,
                        版本名称: versionFilter
                    });

                    // 清空表格并显示加载中
                    tableBody.innerHTML = '<tr><td colspan="9" class="text-center py-4">加载中...</td></tr>';

                    // 在实际应用中，这里应该是发送AJAX请求获取数据
                    // 这里使用模拟数据
                    setTimeout(() => {
                        try {
                            // 确保mockRulesData存在
                            if (!window.mockRulesData || !Array.isArray(window.mockRulesData)) {
                                console.error('模拟数据不存在或格式不正确');
                                tableBody.innerHTML = '<tr><td colspan="9" class="text-center py-4">加载数据失败</td></tr>';
                                return;
                            }

                            // 复制数据以避免修改原始数据
                            let filteredData = [...window.mockRulesData];
                            console.log('原始数据条数:', filteredData.length);

                            // 如果指定了版本ID，仅显示该版本
                            if (versionId) {
                                console.log('按版本ID筛选:', versionId);
                                filteredData = filteredData.filter(rule => rule.id === versionId);

                                // 显示版本信息提示
                                const versionInfo = document.getElementById('ruleVersionInfo');
                                const versionInfoText = document.getElementById('versionInfoText');
                                if (versionInfo && versionInfoText) {
                                    if (filteredData.length > 0) {
                                        versionInfoText.textContent = `您正在查看 "${filteredData[0].versionName}" 版本的规则`;
                                        versionInfo.classList.remove('d-none');
                                    } else {
                                        versionInfo.classList.add('d-none');
                                    }
                                }
                            } else {
                                // 应用筛选条件
                                if (statusFilter && statusFilter !== 'all') {
                                    console.log('按状态筛选:', statusFilter);
                                    const beforeCount = filteredData.length;
                                    filteredData = filteredData.filter(rule => rule.status === statusFilter);
                                    console.log(`状态筛选前: ${beforeCount}, 筛选后: ${filteredData.length}`);
                                }

                                if (platformFilter && platformFilter !== 'all') {
                                    console.log('按平台筛选:', platformFilter);
                                    const beforeCount = filteredData.length;
                                    filteredData = filteredData.filter(rule => rule.platform === platformFilter);
                                    console.log(`平台筛选前: ${beforeCount}, 筛选后: ${filteredData.length}`);
                                }

                                if (versionFilter) {
                                    console.log('按版本名称筛选:', versionFilter);
                                    const beforeCount = filteredData.length;
                                    filteredData = filteredData.filter(rule => {
                                        const match = rule.versionName.toLowerCase().includes(versionFilter);
                                        console.log(`  版本 "${rule.versionName}" ${match ? '匹配' : '不匹配'}`);
                                        return match;
                                    });
                                    console.log(`版本名称筛选前: ${beforeCount}, 筛选后: ${filteredData.length}`);
                                }

                                // 隐藏版本信息提示
                                const versionInfo = document.getElementById('ruleVersionInfo');
                                if (versionInfo) {
                                    versionInfo.classList.add('d-none');
                                }
                            }

                            // 更新总条数
                            const totalRulesEl = document.getElementById('totalRules');
                            if (totalRulesEl) {
                                totalRulesEl.textContent = filteredData.length;
                            }
                            console.log('筛选后数据条数:', filteredData.length);

                            // 渲染数据
                            if (filteredData.length === 0) {
                                tableBody.innerHTML = '<tr><td colspan="9" class="text-center py-4">暂无数据</td></tr>';
                                return;
                            }

                            let tableHtml = '';
                            filteredData.forEach((rule, index) => {
                                const platformName = getPlatformName(rule.platform);

                                // 状态徽章显示
                                let statusBadge = '';
                                if (rule.status === 'enabled') {
                                    statusBadge = '<span class="badge badge-enabled">启用</span>';
                                } else if (rule.status === 'disabled') {
                                    statusBadge = '<span class="badge badge-disabled">停用</span>';
                                } else if (rule.status === 'expired') {
                                    statusBadge = '<span class="badge badge-expired">已过期</span>';
                                }

                                // 停用/启用按钮
                                let toggleButton = '';
                                if (rule.status === 'enabled') {
                                    toggleButton = `<button type="button" class="btn btn-outline-secondary toggle-rule" data-rule-id="${rule.id}" data-action="disable">
                                        <i class="bi bi-pause-fill"></i>
                                    </button>`;
                                } else if (rule.status === 'disabled') {
                                    toggleButton = `<button type="button" class="btn btn-outline-success toggle-rule" data-rule-id="${rule.id}" data-action="enable">
                                        <i class="bi bi-play-fill"></i>
                                    </button>`;
                                } else {
                                    // 已过期规则不显示启用/停用按钮
                                    toggleButton = `<button type="button" class="btn btn-outline-secondary" disabled>
                                        <i class="bi bi-dash"></i>
                                    </button>`;
                                }

                                // 创建表格行HTML
                                tableHtml += `
                <tr>
                    <td>${index + 1}</td>
                    <td><a href="javascript:void(0)" class="version-link" data-rule-id="${rule.id}">${rule.versionName}</a></td>
                    <td colspan="3">
                        <button class="btn btn-sm btn-outline-secondary mb-2" type="button" data-bs-toggle="collapse" data-bs-target="#rangeCollapse${rule.id}" aria-expanded="false">
                            查看区间明细 <i class="bi bi-chevron-down"></i>
                        </button>
                        <div class="collapse" id="rangeCollapse${rule.id}">
                            <div class="card card-body p-0">
                                <table class="table table-sm mb-0">
                                    <thead class="table-light">
                                        <tr>
                                            <th>起始金额(含)</th>
                                            <th>结束金额</th>
                                            <th>成本增幅比例</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        ${generateRangesTable(rule.ranges)}
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </td>
                    <td>${platformName}</td>
                    <td>${rule.startDate} 至 ${rule.endDate}</td>
                    <td>${statusBadge}</td>
                    <td>
                        <div class="btn-group btn-group-sm">
                            <button type="button" class="btn btn-outline-primary edit-rule" data-rule-id="${rule.id}">
                                <i class="bi bi-pencil"></i>
                            </button>
                            ${toggleButton}
                            <button type="button" class="btn btn-outline-danger delete-rule" data-rule-id="${rule.id}">
                                <i class="bi bi-trash"></i>
                            </button>
                        </div>
                    </td>
                </tr>
            `;
                            });

                            // 更新表格内容
                            tableBody.innerHTML = tableHtml;
                            console.log('表格更新完成');

                            // 绑定版本链接点击事件
                            const versionLinks = document.querySelectorAll('.version-link');
                            versionLinks.forEach(link => {
                                link.addEventListener('click', function() {
                                    const ruleId = this.getAttribute('data-rule-id');
                                    if (ruleId) {
                                        console.log('点击版本链接:', ruleId);
                                        loadRulesData(ruleId);
                                    }
                                });
                            });

                            // 绑定编辑按钮事件
                            const editButtons = document.querySelectorAll('.edit-rule');
                            editButtons.forEach(button => {
                                button.addEventListener('click', function() {
                                    const ruleId = this.getAttribute('data-rule-id');
                                    if (ruleId) {
                                        console.log('点击编辑按钮:', ruleId);
                                        openRuleModal('edit', ruleId);
                                    }
                                });
                            });

                            // 绑定删除按钮事件
                            const deleteButtons = document.querySelectorAll('.delete-rule');
                            deleteButtons.forEach(button => {
                                button.addEventListener('click', function() {
                                    const ruleId = this.getAttribute('data-rule-id');
                                    if (ruleId) {
                                        console.log('点击删除按钮:', ruleId);
                                        openDeleteConfirmModal(ruleId);
                                    }
                                });
                            });

                            // 绑定启用/停用按钮事件
                            const toggleButtons = document.querySelectorAll('.toggle-rule');
                            toggleButtons.forEach(button => {
                                button.addEventListener('click', function() {
                                    const ruleId = this.getAttribute('data-rule-id');
                                    const action = this.getAttribute('data-action');
                                    if (ruleId && action) {
                                        console.log(`点击${action === 'enable' ? '启用' : '停用'}按钮:`, ruleId);
                                        toggleRuleStatus(ruleId, action);
                                    }
                                });
                            });
                        } catch (err) {
                            console.error('渲染规则数据出错:', err);
                            tableBody.innerHTML = '<tr><td colspan="9" class="text-center py-4">加载数据失败</td></tr>';
                        }
                    }, 300);
                } catch (err) {
                    console.error('加载规则数据出错:', err);
                    showToast('加载数据失败，请刷新页面重试', 'error');
                }
            }

            // 生成区间表格HTML
            function generateRangesTable(ranges) {
                let html = '';
                ranges.forEach(range => {
                    html += `
                <tr>
                    <td>${range.startAmount.toFixed(2)}</td>
                    <td>${range.endAmount.toFixed(2)}</td>
                    <td>${range.costRate.toFixed(2)}%</td>
                </tr>
            `;
                });
                return html;
            }

            // 获取平台名称
            function getPlatformName(platformCode) {
                const platforms = {
                    'tmall': '天猫',
                    'jd': '京东',
                    'pdd': '拼多多',
                    'douyin': '抖音'
                };
                return platforms[platformCode] || platformCode;
            }

            // 打开删除确认模态框
            function openDeleteConfirmModal(ruleId) {
                try {
                    const confirmDeleteBtn = document.getElementById('confirmDeleteBtn');
                    if (!confirmDeleteBtn) {
                        console.error('找不到确认删除按钮');
                        return;
                    }

                    confirmDeleteBtn.setAttribute('data-rule-id', ruleId);

                    const modalElement = document.getElementById('deleteConfirmModal');
                    if (!modalElement) {
                        console.error('找不到删除确认模态框');
                        return;
                    }

                    const deleteModal = new bootstrap.Modal(modalElement);
                    deleteModal.show();
                } catch (err) {
                    console.error('打开删除确认模态框出错:', err);
                    showToast('操作失败，请重试', 'error');
                }
            }

            // 删除规则
            function deleteRule(ruleId) {
                // 实际应用中应发送到服务器
                // 这里使用模拟数据
                const index = mockRulesData.findIndex(rule => rule.id === ruleId);
                if (index !== -1) {
                    mockRulesData.splice(index, 1);
                }

                // 关闭模态框
                const deleteModal = bootstrap.Modal.getInstance(document.getElementById('deleteConfirmModal'));
                deleteModal.hide();

                // 刷新规则列表
                loadRulesData();

                // 显示成功提示
                showToast('规则删除成功');
            }

            // 切换规则状态（启用/停用）
            function toggleRuleStatus(ruleId, action) {
                try {
                    // 实际应用中应发送到服务器
                    // 这里使用模拟数据
                    const index = mockRulesData.findIndex(rule => rule.id === ruleId);
                    if (index === -1) {
                        console.error('找不到规则:', ruleId);
                        showToast('操作失败，找不到规则', 'error');
                        return;
                    }

                    const rule = mockRulesData[index];

                    // 已过期规则不能启用
                    if (action === 'enable' && rule.status === 'expired') {
                        showToast('已过期规则不能启用', 'error');
                        return;
                    }

                    // 设置新状态
                    if (action === 'enable') {
                        rule.status = 'enabled';
                        showToast('规则已启用');
                    } else if (action === 'disable') {
                        rule.status = 'disabled';
                        showToast('规则已停用');
                    }

                    // 刷新规则列表
                    loadRulesData();
                } catch (err) {
                    console.error('切换规则状态出错:', err);
                    showToast('操作失败，请重试', 'error');
                }
            }

            // 将所有函数暴露到全局，以便事件监听器可以访问
            window.openRuleModal = openRuleModal;
            window.saveRule = saveRule;
            window.addNewRange = addNewRange;
            window.removeRange = removeRange;
            window.validateRanges = validateRanges;
            window.openDeleteConfirmModal = openDeleteConfirmModal;
            window.deleteRule = deleteRule;
            window.toggleRuleStatus = toggleRuleStatus;
            window.loadRulesData = loadRulesData;
            window.initDatePickers = initDatePickers;
            window.initRangeContainer = initRangeContainer;
            window.showToast = showToast;
            window.findRuleById = findRuleById;
            window.fillRuleForm = fillRuleForm;
            window.collectFormData = collectFormData;
            window.validateRuleForm = validateRuleForm;
            window.generateRangesTable = generateRangesTable;
            window.getPlatformName = getPlatformName;

            // 将mockRulesData定义为全局变量，如果尚未定义
            if (!window.mockRulesData) {
                window.mockRulesData = [{
                    id: 'rule_1',
                    versionName: '2025年Q2成本增幅规则',
                    platform: 'tmall',
                    startDate: '2025-04',
                    endDate: '2025-06',
                    status: 'enabled',
                    createTime: '2025-03-15T10:00:00Z',
                    ranges: [{
                        id: 1,
                        startAmount: 0,
                        endAmount: 5000,
                        costRate: 5
                    }, {
                        id: 2,
                        startAmount: 5000,
                        endAmount: 10000,
                        costRate: 8
                    }, {
                        id: 3,
                        startAmount: 10000,
                        endAmount: 50000,
                        costRate: 10
                    }, {
                        id: 4,
                        startAmount: 50000,
                        endAmount: 100000,
                        costRate: 12
                    }]
                }, {
                    id: 'rule_2',
                    versionName: '2025年Q2京东成本规则',
                    platform: 'jd',
                    startDate: '2025-04',
                    endDate: '2025-06',
                    status: 'disabled',
                    createTime: '2025-03-10T09:30:00Z',
                    ranges: [{
                        id: 1,
                        startAmount: 0,
                        endAmount: 8000,
                        costRate: 4
                    }, {
                        id: 2,
                        startAmount: 8000,
                        endAmount: 15000,
                        costRate: 7
                    }, {
                        id: 3,
                        startAmount: 15000,
                        endAmount: 100000,
                        costRate: 9
                    }]
                }, {
                    id: 'rule_3',
                    versionName: '2025年Q1成本增幅规则',
                    platform: 'tmall',
                    startDate: '2025-01',
                    endDate: '2025-03',
                    status: 'expired',
                    createTime: '2024-12-20T14:15:00Z',
                    ranges: [{
                        id: 1,
                        startAmount: 0,
                        endAmount: 3000,
                        costRate: 4
                    }, {
                        id: 2,
                        startAmount: 3000,
                        endAmount: 8000,
                        costRate: 6
                    }, {
                        id: 3,
                        startAmount: 8000,
                        endAmount: 20000,
                        costRate: 8
                    }, {
                        id: 4,
                        startAmount: 20000,
                        endAmount: 50000,
                        costRate: 10
                    }]
                }, {
                    id: 'rule_4',
                    versionName: '2025年Q1拼多多成本规则',
                    platform: 'pdd',
                    startDate: '2025-01',
                    endDate: '2025-03',
                    status: 'expired',
                    createTime: '2024-12-18T11:45:00Z',
                    ranges: [{
                        id: 1,
                        startAmount: 0,
                        endAmount: 5000,
                        costRate: 3
                    }, {
                        id: 2,
                        startAmount: 5000,
                        endAmount: 15000,
                        costRate: 5
                    }, {
                        id: 3,
                        startAmount: 15000,
                        endAmount: 50000,
                        costRate: 7
                    }]
                }, {
                    id: 'rule_5',
                    versionName: '2025年Q2抖音成本规则',
                    platform: 'douyin',
                    startDate: '2025-04',
                    endDate: '2025-06',
                    status: 'enabled',
                    createTime: '2025-03-20T16:30:00Z',
                    ranges: [{
                        id: 1,
                        startAmount: 0,
                        endAmount: 10000,
                        costRate: 6
                    }, {
                        id: 2,
                        startAmount: 10000,
                        endAmount: 30000,
                        costRate: 9
                    }, {
                        id: 3,
                        startAmount: 30000,
                        endAmount: 100000,
                        costRate: 12
                    }]
                }];
            }
        })();
    </script>
</body>

</html>