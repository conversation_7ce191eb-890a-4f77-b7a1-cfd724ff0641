<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>快递费用管理系统</title>
    <link rel="icon" href="https://panhan.xin/assets/favicon-DfI5CYqk.ico">
    <script src="https://cdn.jsdelivr.net/npm/moment@2.29.1/min/moment-with-locales.min.js"></script>
    <script>
        // 初始化 moment 国际化配置
        window.onload = function() {
            moment.locale('zh-cn');
        };
    </script>
    <style>
        /* 全局样式 */
        
         :root {
            --primary-color: #1890ff;
            --success-color: #52c41a;
            --warning-color: #faad14;
            --error-color: #ff4d4f;
            --font-color: #000000d9;
            --border-color: #f0f0f0;
            --background-color: #f5f5f5;
            --header-height: 64px;
            --sidebar-width: 200px;
        }
        
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, 'Noto Sans', sans-serif;
            font-size: 14px;
            line-height: 1.5;
            color: var(--font-color);
            background-color: var(--background-color);
        }
        /* 容器样式 */
        
        .express-fee-container {
            padding: 24px;
            min-height: 100vh;
        }
        /* 页面头部 */
        
        .page-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 24px;
            padding: 16px 24px;
            background: #fff;
            border-radius: 4px;
            box-shadow: 0 1px 2px rgba(0, 0, 0, 0.03);
        }
        
        .header-title {
            display: flex;
            align-items: baseline;
        }
        
        .header-title h1 {
            font-size: 20px;
            font-weight: 500;
            margin: 0;
            margin-right: 12px;
        }
        
        .header-desc {
            color: rgba(0, 0, 0, 0.45);
            font-size: 14px;
        }
        
        .header-actions {
            display: flex;
            gap: 12px;
        }
        /* 按钮样式 */
        
        .btn-primary {
            height: 32px;
            padding: 4px 15px;
            background: var(--primary-color);
            border: none;
            border-radius: 4px;
            color: white;
            cursor: pointer;
            transition: all 0.3s;
        }
        
        .btn-primary:hover {
            background: #40a9ff;
        }
        
        .btn-text {
            height: 32px;
            padding: 4px 15px;
            background: transparent;
            border: none;
            color: var(--primary-color);
            cursor: pointer;
        }
        
        .btn-text:hover {
            color: #40a9ff;
            background: rgba(24, 144, 255, 0.1);
        }
        /* 内容区域 */
        
        .main-content {
            display: flex;
            flex-direction: column;
            gap: 24px;
        }
        
        .section-ladder,
        .section-rules {
            background: #fff;
            border-radius: 4px;
            padding: 24px;
            box-shadow: 0 1px 2px rgba(0, 0, 0, 0.03);
        }
        
        .section-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
        }
        
        .section-header h2 {
            font-size: 16px;
            font-weight: 500;
            margin: 0;
        }
        
        .section-tools {
            display: flex;
            gap: 12px;
        }
        /* 表格样式 */
        
        .ladder-table-container,
        .rules-table-container {
            overflow-x: auto;
        }
        
        .ladder-table,
        .rules-table {
            width: 100%;
            border-collapse: collapse;
        }
        
        .ladder-table th,
        .ladder-table td,
        .rules-table th,
        .rules-table td {
            padding: 16px;
            text-align: left;
            border-bottom: 1px solid var(--border-color);
        }
        
        .ladder-table th,
        .rules-table th {
            background: #fafafa;
            font-weight: 500;
            color: rgba(0, 0, 0, 0.85);
        }
        
        .ladder-table tr:hover,
        .rules-table tr:hover {
            background: #fafafa;
        }
        /* 弹窗样式 */
        
        .modal {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.45);
            z-index: 1000;
            justify-content: center;
            align-items: center;
        }
        
        .modal-content {
            width: 90%;
            max-width: 520px;
            background: #fff;
            border-radius: 4px;
            padding: 0;
            box-shadow: 0 3px 6px -4px rgba(0, 0, 0, 0.12), 0 6px 16px 0 rgba(0, 0, 0, 0.08), 0 9px 28px 8px rgba(0, 0, 0, 0.05);
        }
        
        .modal-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 16px 24px;
            border-bottom: 1px solid #f0f0f0;
        }
        
        .modal-header h3 {
            margin: 0;
            font-size: 16px;
            font-weight: 500;
        }
        
        .close-btn {
            background: transparent;
            border: none;
            font-size: 20px;
            cursor: pointer;
            color: #999;
        }
        
        .form-item {
            margin-bottom: 16px;
        }
        
        .form-item label {
            display: block;
            margin-bottom: 8px;
            font-weight: 500;
        }
        
        .form-item input,
        .form-item select {
            width: 100%;
            padding: 8px 12px;
            border: 1px solid #d9d9d9;
            border-radius: 4px;
        }
        
        .form-item select[multiple] {
            height: 80px;
        }
        
        .range-inputs {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .range-inputs input {
            flex: 1;
        }
        
        .date-range {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .date-range input {
            flex: 1;
        }
        
        .form-actions {
            display: flex;
            justify-content: flex-end;
            gap: 12px;
            margin-top: 24px;
            padding-top: 16px;
            border-top: 1px solid #f0f0f0;
        }
        
        #adjustForm,
        #ladderForm {
            padding: 24px;
        }
        
        .btn-cancel {
            padding: 8px 16px;
            background: white;
            border: 1px solid #d9d9d9;
            border-radius: 4px;
            cursor: pointer;
        }
        
        .btn-cancel:hover {
            color: #40a9ff;
            border-color: #40a9ff;
        }
    </style>
</head>

<body>
    <div id="app">
        <div class="express-fee-container">
            <!-- 页面头部 -->
            <header class="page-header">
                <div class="header-title">
                    <h1>快递费用管理</h1>
                    <span class="header-desc">配置快递费用阶梯价格与调整规则</span>
                </div>
                <div class="header-actions">
                    <button class="btn-primary" id="addLadderRule">新增阶梯规则</button>
                    <button class="btn-primary" id="addAdjustRule">新增调整规则</button>
                </div>
            </header>

            <!-- 主要内容区 -->
            <main class="main-content">
                <!-- 快递阶梯费用配置 -->
                <section class="section-ladder">
                    <div class="section-header">
                        <h2>阶梯费用配置</h2>
                        <div class="section-tools">
                            <button class="btn-text" id="batchImport">批量导入</button>
                            <button class="btn-text" id="exportTemplate">下载模板</button>
                        </div>
                    </div>
                    <div class="ladder-table-container">
                        <table class="ladder-table" id="ladderTable">
                            <thead>
                                <tr>
                                    <th>序号</th>
                                    <th>重量范围(kg)</th>
                                    <th>首重价格(元)</th>
                                    <th>续重价格(元/kg)</th>
                                    <th>操作</th>
                                </tr>
                            </thead>
                            <tbody>
                                <!-- 表格内容由JavaScript动态生成 -->
                            </tbody>
                        </table>
                    </div>
                </section>

                <!-- 调整规则配置 -->
                <section class="section-rules">
                    <div class="section-header">
                        <h2>调整规则配置</h2>
                        <div class="section-tools">
                            <button class="btn-text" id="batchEdit">批量编辑</button>
                            <button class="btn-text" id="viewHistory">历史记录</button>
                        </div>
                    </div>
                    <div class="rules-table-container">
                        <table class="rules-table" id="rulesTable">
                            <thead>
                                <tr>
                                    <th>规则名称</th>
                                    <th>增减类型</th>
                                    <th>增降幅度</th>
                                    <th>适用区域</th>
                                    <th>适用团队</th>
                                    <th>适用店铺</th>
                                    <th>重量阶梯</th>
                                    <th>生效时间</th>
                                    <th>状态</th>
                                    <th>操作</th>
                                </tr>
                            </thead>
                            <tbody>
                                <!-- 表格内容由JavaScript动态生成 -->
                            </tbody>
                        </table>
                    </div>
                </section>
            </main>
        </div>
    </div>

    <!-- 弹窗组件 -->
    <div class="modal" id="ladderModal">
        <div class="modal-content">
            <div class="modal-header">
                <h3 id="ladderModalTitle">新增阶梯规则</h3>
                <button class="close-btn" onclick="document.getElementById('ladderModal').style.display='none'">&times;</button>
            </div>
            <form id="ladderForm">
                <div class="form-item">
                    <label for="weightStart">重量范围(kg)</label>
                    <div class="range-inputs">
                        <input type="number" id="weightStart" name="weightStart" min="0" step="0.01" placeholder="起始重量">
                        <span>-</span>
                        <input type="number" id="weightEnd" name="weightEnd" min="0" step="0.01" placeholder="结束重量">
                    </div>
                </div>
                <div class="form-item">
                    <label for="firstWeight">首重价格(元)</label>
                    <input type="number" id="firstWeight" name="firstWeight" min="0" step="0.01" placeholder="首重价格">
                </div>
                <div class="form-item">
                    <label for="additionalWeight">续重价格(元/kg)</label>
                    <input type="number" id="additionalWeight" name="additionalWeight" min="0" step="0.01" placeholder="续重价格">
                </div>
                <div class="form-actions">
                    <button type="button" class="btn-cancel" onclick="document.getElementById('ladderModal').style.display='none'">取消</button>
                    <button type="button" class="btn-primary" onclick="ladderManager.saveLadderRule()">保存</button>
                </div>
            </form>
        </div>
    </div>
    <div class="modal" id="adjustModal">
        <div class="modal-content">
            <div class="modal-header">
                <h3 id="adjustModalTitle">新增调整规则</h3>
                <button class="close-btn" onclick="document.getElementById('adjustModal').style.display='none'">&times;</button>
            </div>
            <form id="adjustForm">
                <div class="form-item">
                    <label for="ruleName">规则名称</label>
                    <input type="text" id="ruleName" name="ruleName" required>
                </div>
                <div class="form-item">
                    <label for="adjustmentType">增减类型</label>
                    <select id="adjustmentType" name="adjustmentType">
                        <option value="INCREASE_RATIO">增浮比率</option>
                        <option value="DECREASE_RATIO">降浮比率</option>
                        <option value="INCREASE_AMOUNT">增浮金额</option>
                        <option value="DECREASE_AMOUNT">降浮金额</option>
                    </select>
                </div>
                <div class="form-item">
                    <label for="adjustmentValue">增降幅度</label>
                    <input type="number" id="adjustmentValue" name="adjustmentValue" min="0" step="0.01" required>
                </div>
                <div class="form-item">
                    <label for="region">适用区域</label>
                    <select id="region" name="region" multiple>
                        <option value="北京">北京</option>
                        <option value="上海">上海</option>
                        <option value="广州">广州</option>
                    </select>
                </div>
                <div class="form-item">
                    <label for="team">适用团队</label>
                    <select id="team" name="team" multiple>
                        <option value="团队A">团队A</option>
                        <option value="团队B">团队B</option>
                    </select>
                </div>
                <div class="form-item">
                    <label for="shop">适用店铺</label>
                    <select id="shop" name="shop" multiple>
                        <option value="店铺1">店铺1</option>
                        <option value="店铺2">店铺2</option>
                    </select>
                </div>
                <div class="form-item">
                    <label for="weightLadder">重量阶梯</label>
                    <select id="weightLadder" name="weightLadder" multiple>
                        <option value="0-1kg">0-1kg</option>
                        <option value="1-2kg">1-2kg</option>
                        <option value="2-5kg">2-5kg</option>
                    </select>
                </div>
                <div class="form-item">
                    <label>生效时间</label>
                    <div class="date-range">
                        <input type="datetime-local" id="startDate" name="startDate">
                        <span>-</span>
                        <input type="datetime-local" id="endDate" name="endDate">
                    </div>
                </div>
                <div class="form-actions">
                    <button type="button" class="btn-cancel" onclick="document.getElementById('adjustModal').style.display='none'">取消</button>
                    <button type="button" class="btn-primary" onclick="adjustmentManager.saveAdjustRule()">保存</button>
                </div>
            </form>
        </div>
    </div>
    <script>
        // 常量定义
        const ADJUSTMENT_TYPES = {
            INCREASE_RATIO: '增浮比率',
            DECREASE_RATIO: '降浮比率',
            INCREASE_AMOUNT: '增浮金额',
            DECREASE_AMOUNT: '降浮金额'
        };

        // 工具函数
        const utils = {
            formatDate(date) {
                return moment(date).format('YYYY-MM-DD HH:mm:ss');
            },

            formatMoney(amount) {
                return Number(amount).toFixed(2);
            },

            showMessage(type, message) {
                // 使用原生alert替代antd.message
                alert(message);
            }
        };

        // 阶梯费用管理类
        class LadderFeeManager {
            constructor() {
                this.ladderRules = [];
                this.initEventListeners();
            }

            initEventListeners() {
                document.getElementById('addLadderRule').addEventListener('click', () => this.showLadderModal());
                document.getElementById('batchImport').addEventListener('click', () => this.handleBatchImport());
                document.getElementById('exportTemplate').addEventListener('click', () => this.exportTemplate());
            }

            showLadderModal(data = null) {
                const modal = document.getElementById('ladderModal');
                const title = document.getElementById('ladderModalTitle');

                // 设置标题
                title.textContent = data ? '编辑阶梯规则' : '新增阶梯规则';

                // 重置表单
                document.getElementById('ladderForm').reset();

                // 如果是编辑，填充数据
                if (data) {
                    document.getElementById('weightStart').value = data.weightStart || '';
                    document.getElementById('weightEnd').value = data.weightEnd || '';
                    document.getElementById('firstWeight').value = data.firstWeight || '';
                    document.getElementById('additionalWeight').value = data.additionalWeight || '';

                    // 保存当前编辑的记录索引
                    this.currentEditIndex = this.ladderRules.indexOf(data);
                } else {
                    this.currentEditIndex = -1;
                }

                // 显示模态框
                modal.style.display = 'flex';
            }

            saveLadderRule() {
                // 保存阶梯规则逻辑
                const form = document.getElementById('ladderForm');
                const data = {
                    weightStart: Number(document.getElementById('weightStart').value),
                    weightEnd: Number(document.getElementById('weightEnd').value),
                    firstWeight: Number(document.getElementById('firstWeight').value),
                    additionalWeight: Number(document.getElementById('additionalWeight').value)
                };

                // 验证数据
                if (this.validateLadderRule(data)) {
                    if (this.currentEditIndex >= 0) {
                        // 编辑现有规则
                        this.ladderRules[this.currentEditIndex] = data;
                    } else {
                        // 添加新规则
                        this.ladderRules.push(data);
                    }
                    this.updateLadderTable();
                    utils.showMessage('success', '规则保存成功');
                    document.getElementById('ladderModal').style.display = 'none';
                }
            }

            validateLadderRule(data) {
                if (data.weightEnd <= data.weightStart) {
                    utils.showMessage('error', '结束重量必须大于起始重量');
                    return false;
                }
                if (data.firstWeight < 0 || data.additionalWeight < 0) {
                    utils.showMessage('error', '价格不能为负数');
                    return false;
                }
                return true;
            }

            updateLadderTable() {
                const tbody = document.querySelector('#ladderTable tbody');
                tbody.innerHTML = this.ladderRules.map((rule, index) => `
      <tr>
        <td>${index + 1}</td>
        <td>${rule.weightStart} - ${rule.weightEnd}</td>
        <td>${utils.formatMoney(rule.firstWeight)}</td>
        <td>${utils.formatMoney(rule.additionalWeight)}</td>
        <td>
          <button class="btn-text" onclick="ladderManager.editRule(${index})">编辑</button>
          <button class="btn-text" onclick="ladderManager.deleteRule(${index})">删除</button>
        </td>
      </tr>
    `).join('');
            }

            editRule(index) {
                this.showLadderModal(this.ladderRules[index]);
            }

            deleteRule(index) {
                if (confirm('确定要删除这条阶梯规则吗？')) {
                    this.ladderRules.splice(index, 1);
                    this.updateLadderTable();
                    utils.showMessage('success', '删除成功');
                }
            }

            handleBatchImport() {
                // 实现批量导入逻辑
            }

            exportTemplate() {
                // 实现导出模板逻辑
            }
        }

        // 调整规则管理类
        class AdjustmentRuleManager {
            constructor() {
                this.adjustmentRules = [];
                this.initEventListeners();
            }

            initEventListeners() {
                document.getElementById('addAdjustRule').addEventListener('click', () => this.showAdjustModal());
                document.getElementById('batchEdit').addEventListener('click', () => this.handleBatchEdit());
                document.getElementById('viewHistory').addEventListener('click', () => this.viewHistory());
            }

            showAdjustModal(data = null) {
                const modal = document.getElementById('adjustModal');
                const title = document.getElementById('adjustModalTitle');

                // 设置标题
                title.textContent = data ? '编辑调整规则' : '新增调整规则';

                // 重置表单
                document.getElementById('adjustForm').reset();

                // 如果是编辑，填充数据
                if (data) {
                    document.getElementById('ruleName').value = data.ruleName || '';
                    document.getElementById('adjustmentType').value = data.adjustmentType || 'INCREASE_RATIO';
                    document.getElementById('adjustmentValue').value = data.adjustmentValue || '';

                    // 多选框需要特殊处理
                    this.setMultiSelectValues('region', data.region || []);
                    this.setMultiSelectValues('team', data.team || []);
                    this.setMultiSelectValues('shop', data.shop || []);
                    this.setMultiSelectValues('weightLadder', data.weightLadder || []);

                    document.getElementById('startDate').value = data.startDate || '';
                    document.getElementById('endDate').value = data.endDate || '';

                    // 保存当前编辑的记录索引
                    this.currentEditIndex = this.adjustmentRules.indexOf(data);
                } else {
                    this.currentEditIndex = -1;
                }

                // 显示模态框
                modal.style.display = 'flex';
            }

            // 辅助方法：设置多选框的值
            setMultiSelectValues(selectId, values) {
                const select = document.getElementById(selectId);
                if (select && values && values.length) {
                    for (let i = 0; i < select.options.length; i++) {
                        const option = select.options[i];
                        option.selected = values.includes(option.value);
                    }
                }
            }

            // 辅助方法：获取多选框的值
            getMultiSelectValues(selectId) {
                const select = document.getElementById(selectId);
                const values = [];
                if (select) {
                    for (let i = 0; i < select.options.length; i++) {
                        if (select.options[i].selected) {
                            values.push(select.options[i].value);
                        }
                    }
                }
                return values;
            }

            saveAdjustRule() {
                // 保存调整规则逻辑
                const data = {
                    ruleName: document.getElementById('ruleName').value,
                    adjustmentType: document.getElementById('adjustmentType').value,
                    adjustmentValue: Number(document.getElementById('adjustmentValue').value),
                    region: this.getMultiSelectValues('region'),
                    team: this.getMultiSelectValues('team'),
                    shop: this.getMultiSelectValues('shop'),
                    weightLadder: this.getMultiSelectValues('weightLadder'),
                    startDate: document.getElementById('startDate').value,
                    endDate: document.getElementById('endDate').value,
                    status: 'active'
                };

                if (this.validateAdjustRule(data)) {
                    if (this.currentEditIndex >= 0) {
                        // 编辑现有规则
                        this.adjustmentRules[this.currentEditIndex] = data;
                    } else {
                        // 添加新规则
                        this.adjustmentRules.push(data);
                    }
                    this.updateAdjustTable();
                    utils.showMessage('success', '规则保存成功');
                    document.getElementById('adjustModal').style.display = 'none';
                }
            }

            validateAdjustRule(data) {
                if (!data.ruleName.trim()) {
                    utils.showMessage('error', '规则名称不能为空');
                    return false;
                }
                if (data.adjustmentValue <= 0) {
                    utils.showMessage('error', '增降幅度必须大于0');
                    return false;
                }
                return true;
            }

            updateAdjustTable() {
                const tbody = document.querySelector('#rulesTable tbody');
                tbody.innerHTML = this.adjustmentRules.map((rule, index) => `
      <tr>
        <td>${rule.ruleName}</td>
        <td>${ADJUSTMENT_TYPES[rule.adjustmentType]}</td>
        <td>${rule.adjustmentValue}</td>
        <td>${rule.region.join(', ')}</td>
        <td>${rule.team.join(', ')}</td>
        <td>${rule.shop.join(', ')}</td>
        <td>${rule.weightLadder.join(', ')}</td>
        <td>${rule.startDate} - ${rule.endDate}</td>
        <td>
          <span class="status-badge ${rule.status}">${rule.status}</span>
        </td>
        <td>
          <button class="btn-text" onclick="adjustmentManager.editRule(${index})">编辑</button>
          <button class="btn-text" onclick="adjustmentManager.deleteRule(${index})">删除</button>
        </td>
      </tr>
    `).join('');
            }

            editRule(index) {
                this.showAdjustModal(this.adjustmentRules[index]);
            }

            deleteRule(index) {
                if (confirm('确定要删除这条调整规则吗？')) {
                    this.adjustmentRules.splice(index, 1);
                    this.updateAdjustTable();
                    utils.showMessage('success', '删除成功');
                }
            }

            handleBatchEdit() {
                // 实现批量编辑逻辑
            }

            viewHistory() {
                // 实现查看历史记录逻辑
            }
        }

        // 初始化
        document.addEventListener('DOMContentLoaded', () => {
            // 确保moment已正确加载
            if (window.moment) {
                moment.locale('zh-cn');
            }

            window.ladderManager = new LadderFeeManager();
            window.adjustmentManager = new AdjustmentRuleManager();
        });
    </script>
</body>

</html>